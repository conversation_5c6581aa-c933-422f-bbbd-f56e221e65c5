(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-page-selectArea"],{1134:function(e,t,i){var n=i("36c4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("967d").default;a("137fe43c",n,!0,{sourceMap:!1,shadowMode:!1})},"36c4":function(e,t,i){var n=i("c86c");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */.area-container[data-v-6d895e36]{background-color:#f8f8f8;height:100vh;width:100%\n  /* 固定底部按钮区域 */}.area-container__head[data-v-6d895e36]{background-color:#fff;position:fixed;top:0;width:100%;z-index:100}.area-container__head .navigater[data-v-6d895e36]{position:relative}.area-container__head .navigater .back[data-v-6d895e36]{position:absolute;left:%?25?%;z-index:9999}.area-container__head .navigater .back uni-text[data-v-6d895e36]{font-size:%?36?%;font-weight:700}.area-container__head .navigater .text-center[data-v-6d895e36]{font-size:%?32?%;font-weight:500}.area-container .area-content[data-v-6d895e36]{background-color:#f8f8f8;padding:%?30?%;overflow-y:auto;padding-top:%?120?%;padding-bottom:%?150?%\n  /* 添加底部填充，为固定按钮留出空间 */}.area-container .area-content .current-location[data-v-6d895e36]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;margin-bottom:%?30?%}.area-container .area-content .current-location .location-header[data-v-6d895e36]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?15?%}.area-container .area-content .current-location .location-header .location-title[data-v-6d895e36]{font-size:%?28?%;color:#666}.area-container .area-content .current-location .location-header .location-status[data-v-6d895e36]{color:#ff7200;font-size:%?28?%}.area-container .area-content .current-location .location-name[data-v-6d895e36]{font-size:%?32?%;color:#999;padding:%?15?% 0}.area-container .area-content .current-location .location-name.has-location[data-v-6d895e36]{color:#ff7200;font-weight:500}.area-container .area-content .selected-region .selection-header[data-v-6d895e36]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?15?%}.area-container .area-content .selected-region .selection-header .location-title[data-v-6d895e36]{font-size:%?28?%;color:#666}.area-container .area-content .selected-region .selection-header .selection-status[data-v-6d895e36]{color:#4cd964;font-size:%?28?%}.area-container .area-content .selected-region .full-selection[data-v-6d895e36]{font-size:%?32?%;color:#999;padding:%?15?% 0}.area-container .area-content .selected-region .full-selection.has-selection[data-v-6d895e36]{color:#ff7200;font-weight:500}.area-container .area-content .selected-region[data-v-6d895e36]{background-color:#fff;border-radius:8px;padding:15px;margin-bottom:15px}.area-container .area-content .region-selector[data-v-6d895e36]{background-color:#fff;border-radius:%?16?%;padding:%?30?%;height:calc(100% - %?300?%);overflow:hidden;display:flex;flex-direction:column}.area-container .area-content .region-selector .region-title[data-v-6d895e36]{font-size:%?28?%;color:#666;margin-bottom:%?20?%}.area-container .area-content .region-selector .region-tabs[data-v-6d895e36]{display:flex;margin-bottom:%?20?%}.area-container .area-content .region-selector .region-tabs .tab[data-v-6d895e36]{padding:%?20?% %?30?%;font-size:%?28?%;color:#666;position:relative}.area-container .area-content .region-selector .region-tabs .tab.active[data-v-6d895e36]{color:#ff7200;font-weight:700}.area-container .area-content .region-selector .region-tabs .tab.active[data-v-6d895e36]:after{content:"";position:absolute;bottom:%?-2?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:40%;height:%?4?%;background-color:#ff7200;border-radius:%?4?%}.area-container .area-content .region-selector .region-list[data-v-6d895e36]{flex:1;overflow-y:auto}.area-container .area-content .region-selector .region-list .region-item[data-v-6d895e36]{padding:%?25?% %?20?%;font-size:%?28?%;color:#333;border-bottom:1px solid #f5f5f5}.area-container .area-content .region-selector .region-list .region-item uni-text.selected[data-v-6d895e36]{color:#ff7200;font-weight:700}.area-container .fixed-bottom-btn[data-v-6d895e36]{position:fixed;bottom:%?30?%;left:0;right:0;padding:0 %?30?%;box-sizing:border-box;z-index:100;display:flex;justify-content:center\n  /* 确认按钮 */}.area-container .fixed-bottom-btn .confirm-btn[data-v-6d895e36]{background-color:#ff7200;color:#fff;height:%?90?%;line-height:%?90?%;text-align:center;font-size:%?32?%;border-radius:%?45?%;width:90%;font-weight:500;box-shadow:0 %?5?% %?20?% rgba(255,114,0,.3);transition:all .3s}.area-container .fixed-bottom-btn .confirm-btn.active[data-v-6d895e36]{-webkit-transform:translateY(%?-5?%);transform:translateY(%?-5?%);box-shadow:0 %?10?% %?30?% rgba(255,114,0,.4)}',""]),e.exports=t},"3fa0":function(e,t,i){"use strict";(function(e){i("6a54");var n=i("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("2634")),c=n(i("2fdc"));i("fd3c"),i("bf0f"),i("2797"),i("c223"),i("d4b5");var o=n(i("097e")),s={data:function(){return{headHeight:0,windowHeight:0,headTop:0,contentHeight:0,currentCity:"",isRelocated:!1,activeTab:0,provinces:[],cities:[],districts:[],selectedProvince:null,selectedCity:null,selectedDistrict:null,selectedProvinceIndex:-1,selectedCityIndex:-1,selectedDistrictIndex:-1,regionData:[]}},computed:{fullRegionSelected:function(){return this.selectedProvince&&this.selectedCity&&this.selectedDistrict},canConfirm:function(){return null!==this.selectedProvince},currentSelectionName:function(){return this.selectedDistrict?this.selectedDistrict.name:this.selectedCity?this.selectedCity.name:this.selectedProvince?this.selectedProvince.name:""}},onLoad:function(){var e=this.$wanlshop.wanlsys();this.headTop=e.top,this.headHeight=e.height,this.windowHeight=e.windowHeight,this.contentHeight=this.windowHeight-this.headHeight,this.loadCurrentCity(),this.fetchAreaData()},methods:{loadCurrentCity:function(){var t=uni.getStorageSync("citydata");if(t)try{var i=JSON.parse(t);this.currentCity=i.shortname||i.name||"定位中"}catch(n){e.error("解析城市数据错误:",n),this.currentCity="定位中"}else this.currentCity="定位中"},fetchAreaData:function(){var t=this;uni.showLoading({title:"加载数据中..."}),uni.request({url:"/wanlshop/common/areaList",method:"GET",success:function(i){i.data&&Array.isArray(i.data)?(t.regionData=i.data,t.initProvinces(),t.findCurrentCityLocation()):(e.error("地区数据格式错误:",i.data),uni.showToast({title:"地区数据加载失败",icon:"none"}))},fail:function(t){e.error("获取地区数据失败:",t),uni.showToast({title:"地区数据加载失败",icon:"none"})},complete:function(){uni.hideLoading()}})},initProvinces:function(){this.provinces=this.regionData.map((function(e){return{name:e.name,code:e.code,longitude:e.lng||e.longitude,latitude:e.lat||e.latitude}}))},findCurrentCityLocation:function(){var t=this;if("定位中"!==this.currentCity){this.regionData.forEach((function(i,n){i.children&&Array.isArray(i.children)&&i.children.forEach((function(a,c){a.name===t.currentCity&&(e.log("找到匹配城市:",a.name,"在省份:",i.name),t.selectProvince(i,n),t.selectCity(a,c),t.activeTab=2,!0,!0)}))}))}},switchTab:function(e){1!==e||this.selectedProvince?2!==e||this.selectedCity?this.activeTab=e:uni.showToast({title:"请先选择城市",icon:"none"}):uni.showToast({title:"请先选择省份",icon:"none"})},selectProvince:function(e,t){this.isRelocated=!1,this.selectedProvince=e,this.selectedProvinceIndex=t,this.selectedCity=null,this.selectedCityIndex=-1,this.selectedDistrict=null,this.selectedDistrictIndex=-1;var i=this.regionData[t];i&&i.children&&(this.cities=i.children.map((function(e){return{name:e.name,code:e.code,longitude:e.lng||e.longitude,latitude:e.lat||e.latitude}}))),this.activeTab=1},selectCity:function(e,t){this.isRelocated=!1,this.selectedCity=e,this.selectedCityIndex=t,this.selectedDistrict=null,this.selectedDistrictIndex=-1;var i=this.regionData[this.selectedProvinceIndex];if(i&&i.children&&i.children[t]){var n=i.children[t];n.children&&(this.districts=n.children.map((function(e){return{name:e.name,code:e.code,longitude:e.lng||e.longitude,latitude:e.lat||e.latitude}})))}},selectDistrict:function(e,t){this.isRelocated=!1,this.selectedDistrict=e,this.selectedDistrictIndex=t},getFullSelectionText:function(){if(this.isRelocated&&this.currentCity)return this.currentCity;var e="";return this.selectedProvince&&(e+=this.selectedProvince.name),this.selectedCity&&(e+=" "+this.selectedCity.name),this.selectedDistrict&&(e+=" "+this.selectedDistrict.name),e||"请选择地区"},getDefaultCoordinates:function(){return e.log("使用默认经纬度数据"),{longitude:116.39747,latitude:39.908823}},confirmSelection:function(){var t=this.currentCity&&!this.selectedProvince;if(this.selectedProvince||t){var i="",n=null;if(this.currentCity&&!this.selectedProvince){i=this.currentCity,{shortname:this.currentCity,name:this.currentCity},{fullName:this.currentCity};var a=uni.getStorageSync("lnglat");if(a)try{var c="string"===typeof a?JSON.parse(a):a;n={longitude:c.lng||c.longitude,latitude:c.lat||c.latitude},e.log("使用定位的经纬度信息:",n)}catch(o){e.error("解析经纬度信息出错:",o),n=this.getDefaultCoordinates()}else n=this.getDefaultCoordinates()}this.selectedProvince&&this.selectedCity&&this.selectedDistrict?(i="".concat(this.selectedProvince.name," ").concat(this.selectedCity.name," ").concat(this.selectedDistrict.name),{shortname:this.selectedCity.name,name:this.selectedCity.name,code:this.selectedCity.code},{province:this.selectedProvince,city:this.selectedCity,county:this.selectedDistrict,fullName:i},this.selectedDistrict&&this.selectedDistrict.longitude&&this.selectedDistrict.latitude?(n={longitude:this.selectedDistrict.longitude,latitude:this.selectedDistrict.latitude},e.log("使用区县的经纬度信息:",this.selectedDistrict.name)):this.selectedCity&&this.selectedCity.longitude&&this.selectedCity.latitude?(n={longitude:this.selectedCity.longitude,latitude:this.selectedCity.latitude},e.log("使用城市的经纬度信息:",this.selectedCity.name)):this.selectedProvince&&this.selectedProvince.longitude&&this.selectedProvince.latitude?(n={longitude:this.selectedProvince.longitude,latitude:this.selectedProvince.latitude},e.log("使用省份的经纬度信息:",this.selectedProvince.name)):(n=this.getDefaultCoordinates(),e.log("没有找到经纬度信息，使用默认坐标"))):this.selectedProvince&&this.selectedCity?(i="".concat(this.selectedProvince.name," ").concat(this.selectedCity.name),{shortname:this.selectedCity.name,name:this.selectedCity.name,code:this.selectedCity.code},{province:this.selectedProvince,city:this.selectedCity,fullName:i},this.selectedCity&&this.selectedCity.longitude&&this.selectedCity.latitude?(n={longitude:this.selectedCity.longitude,latitude:this.selectedCity.latitude},e.log("使用城市的经纬度信息:",this.selectedCity.name)):this.selectedProvince&&this.selectedProvince.longitude&&this.selectedProvince.latitude?(n={longitude:this.selectedProvince.longitude,latitude:this.selectedProvince.latitude},e.log("使用省份的经纬度信息:",this.selectedProvince.name)):(n=this.getDefaultCoordinates(),e.log("没有找到经纬度信息，使用默认坐标"))):this.selectedProvince&&(i=this.selectedProvince.name,{shortname:this.selectedProvince.name,name:this.selectedProvince.name,code:this.selectedProvince.code},{province:this.selectedProvince,fullName:i},this.selectedProvince&&this.selectedProvince.longitude&&this.selectedProvince.latitude?(n={longitude:this.selectedProvince.longitude,latitude:this.selectedProvince.latitude},e.log("使用省份的经纬度信息:",this.selectedProvince.name)):(n=this.getDefaultCoordinates(),e.log("没有找到经纬度信息，使用默认坐标"))),uni.setStorageSync("lnglat",JSON.stringify(n)),e.log("更新定位信息:",n),uni.showLoading({title:"正在更新数据..."}),uni.request({url:"/common/init",data:{version:"1.1.11",lng:n.longitude,lat:n.latitude},success:function(t){e.log("数据更新成功:",t),t.data&&t.data.citydata&&uni.setStorageSync("citydata",JSON.stringify(t.data.citydata))},fail:function(t){e.error("数据更新失败:",t)},complete:function(){uni.hideLoading(),uni.showToast({title:"地区已更新",icon:"success",duration:1500,complete:function(){setTimeout((function(){uni.navigateBack()}),1e3)}})}})}else uni.showToast({title:"请至少选择省份",icon:"none"})},refreshLocation:function(){var t=this;return(0,c.default)((0,a.default)().mark((function n(){var c,s,r,l,d,u,h,v,g,f,y;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.isRelocated=!0,n.prev=1,uni.showLoading({title:"定位中..."}),n.next=5,(0,o.default)();case 5:if(c=n.sent,!(c&&c.lng&&c.lat)){n.next=13;break}e.log("高德地图定位成功:",c),uni.setStorageSync("lnglat",{lng:c.lng,lat:c.lat}),uni.setStorageSync("lnglatLocal",{lng:c.lng,lat:c.lat}),uni.request({url:"/common/init",data:{version:"1.1.11",lng:c.lng,lat:c.lat},success:function(e){uni.hideLoading(),t.currentCity=e.data.citydata.shortname,uni.setStorageSync("citydata",JSON.stringify(e.data.citydata))}}),n.next=31;break;case 13:return e.warn("高德定位结果无效，尝试使用IP定位"),s=i("097e"),r=s.getLocationByIP,l=s.loadAmapScript,n.prev=15,n.next=18,l();case 18:return d=n.sent,n.next=21,r(d);case 21:u=n.sent,e.log("IP定位成功:",u),uni.setStorageSync("lnglat",{lng:u.lng,lat:u.lat}),uni.setStorageSync("lnglatLocal",{lng:u.lng,lat:u.lat}),uni.request({url:"/common/init",data:{version:"1.1.11",lng:u.lng,lat:u.lat},success:function(e){t.currentCity=e.data.citydata.shortname,uni.setStorageSync("citydata",JSON.stringify(e.data.citydata))}}),n.next=31;break;case 28:n.prev=28,n.t0=n["catch"](15),e.error("IP定位也失败:",n.t0);case 31:n.next=53;break;case 33:return n.prev=33,n.t1=n["catch"](1),e.error("高德地图定位失败:",n.t1),h=i("097e"),v=h.getLocationByIP,g=h.loadAmapScript,n.prev=37,n.next=40,g();case 40:return f=n.sent,n.next=43,v(f);case 43:y=n.sent,e.log("降级使用IP定位成功:",y),uni.setStorageSync("lnglat",{lng:y.lng,lat:y.lat}),uni.setStorageSync("lnglatLocal",{lng:y.lng,lat:y.lat}),uni.request({url:"/common/init",data:{version:"1.1.11",lng:y.lng,lat:y.lat},success:function(e){t.currentCity=e.data.citydata.shortname,uni.setStorageSync("citydata",JSON.stringify(e.data.citydata))}}),n.next=53;break;case 50:n.prev=50,n.t2=n["catch"](37),e.error("IP定位也失败:",n.t2);case 53:case"end":return n.stop()}}),n,null,[[1,33],[15,28],[37,50]])})))()}}};t.default=s}).call(this,i("ba7c")["default"])},"858b":function(e,t,i){"use strict";var n=i("1134"),a=i.n(n);a.a},cf09:function(e,t,i){"use strict";i.r(t);var n=i("dbc9"),a=i("ff4c");for(var c in a)["default"].indexOf(c)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(c);i("858b");var o=i("828b"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"6d895e36",null,!1,n["a"],void 0);t["default"]=s.exports},dbc9:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"area-container"},[i("v-uni-view",{staticClass:"area-container__head bg-white",style:{height:e.headHeight+"px"}},[i("v-uni-view",{staticClass:"navigater flex align-center justify-center",style:{height:e.headHeight+"px",paddingTop:e.headTop+"px"}},[i("v-uni-view",{staticClass:"back",style:{height:e.headHeight+"px",lineHeight:e.headHeight+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$wanlshop.back(1)}}},[i("v-uni-text",{staticClass:"wlIcon-fanhui1"})],1),i("v-uni-view",{staticClass:"text-center"},[e._v("切换地址")])],1)],1),i("v-uni-view",{staticClass:"area-content",style:{height:e.contentHeight+"px"}},[i("v-uni-view",{staticClass:"current-location"},[i("v-uni-view",{staticClass:"location-header"},[i("v-uni-view",{staticClass:"location-title"},[e._v("当前定位")]),i("v-uni-view",{staticClass:"location-status flex align-center "},[i("v-uni-text",{staticClass:"wlIcon-weizhi1"}),i("v-uni-view",{staticClass:"location-name margin-left-xs",class:{"has-location":e.currentCity}},[e._v(e._s(e.currentCity||"定位中..."))]),i("v-uni-view",{staticClass:"margin-left text-sm",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.refreshLocation()}}},[e._v("重新定位")])],1)],1)],1),i("v-uni-view",{staticClass:"selected-region flex justify-between align-center"},[i("v-uni-view",{staticClass:"selection-header"},[i("v-uni-view",{staticClass:"location-title"},[e._v("当前选择")]),e.selectedProvince?i("v-uni-view",{staticClass:"selection-status"},[i("v-uni-text",{staticClass:"wlIcon-check-o"})],1):e._e()],1),i("v-uni-view",{staticClass:"full-selection",class:{"has-selection":e.selectedProvince}},[e._v(e._s(e.getFullSelectionText()||"请选择地区"))])],1),i("v-uni-view",{staticClass:"region-selector"},[i("v-uni-view",{staticClass:"region-tabs"},[i("v-uni-view",{staticClass:"tab",class:{active:0===e.activeTab},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchTab(0)}}},[e._v("省份")]),i("v-uni-view",{staticClass:"tab",class:{active:1===e.activeTab},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.switchTab(1)}}},[e._v("城市")])],1),0===e.activeTab?i("v-uni-scroll-view",{staticClass:"region-list",attrs:{"scroll-y":!0,"show-scrollbar":!0,"enable-scrollbar":!0}},e._l(e.provinces,(function(t,n){return i("v-uni-view",{key:"province_"+n+"_"+(t.code||n),staticClass:"region-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectProvince(t,n)}}},[i("v-uni-text",{class:{selected:e.selectedProvinceIndex===n}},[e._v(e._s(t.name))])],1)})),1):e._e(),1===e.activeTab?i("v-uni-scroll-view",{staticClass:"region-list",attrs:{"scroll-y":!0,"show-scrollbar":!0,"enable-scrollbar":!0}},e._l(e.cities,(function(t,n){return i("v-uni-view",{key:"city_"+n+"_"+(t.code||n),staticClass:"region-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectCity(t,n)}}},[i("v-uni-text",{class:{selected:e.selectedCityIndex===n}},[e._v(e._s(t.name))])],1)})),1):e._e(),2===e.activeTab?i("v-uni-scroll-view",{staticClass:"region-list",attrs:{"scroll-y":!0,"show-scrollbar":!0,"enable-scrollbar":!0}},e._l(e.districts,(function(t,n){return i("v-uni-view",{key:"district_"+n+"_"+(t.code||n),staticClass:"region-item",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectDistrict(t,n)}}},[i("v-uni-text",{class:{selected:e.selectedDistrictIndex===n}},[e._v(e._s(t.name))])],1)})),1):e._e()],1)],1),i("v-uni-view",{staticClass:"fixed-bottom-btn"},[i("v-uni-view",{staticClass:"confirm-btn",class:{active:e.selectedProvince},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmSelection.apply(void 0,arguments)}}},[e._v("确认选择")])],1)],1)},a=[]},ff4c:function(e,t,i){"use strict";i.r(t);var n=i("3fa0"),a=i.n(n);for(var c in n)["default"].indexOf(c)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(c);t["default"]=a.a}}]);