(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-auth-auth"],{"16c3":function(t,e,a){var r=a("c86c");e=r(!1),e.push([t.i,'@charset "UTF-8";\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* page {\n\tbackground-color: #fff\n} */.wanl-title[data-v-3d5bce6c]{padding-bottom:%?130?%;padding-top:%?170?%;font-size:%?68?%}.wanl-title .titleInfo[data-v-3d5bce6c]{font-size:%?56?%}.wanl-weixin-btn-info[data-v-3d5bce6c]{color:#b8b8b8!important}\n/* 1.1.5升级 */.auth[data-v-3d5bce6c]{\n\t/* margin: 0 25rpx; */background-color:#fff;position:relative;\n\t/* top: -140rpx; */z-index:888;border-radius:%?30?% %?30?% 0 0;padding:%?25?%\n\t/* box-shadow: 4rpx 4rpx 6rpx  rgba(26, 26, 26, 0.2); */}.auth-group[data-v-3d5bce6c]{padding:%?1?% %?30?%;display:flex;align-items:center;min-height:%?90?%;justify-content:space-between;margin-bottom:%?25?%}.auth-group uni-input[data-v-3d5bce6c]{flex:1;font-size:%?33?%;color:#250f00;padding-right:%?20?%}.top[data-v-3d5bce6c]{display:flex;flex-direction:column;align-items:center;\n\t/* background-color: #ff770f; */background-image:linear-gradient(180deg,#f96c00 0,#ff9000 40%,#ffbd6c);width:100%;height:%?660?%;position:absolute;top:0;.backIcon{left:%?30?%;position:absolute;color:#fff}.topText{text-align:center;letter-spacing:%?12?%}.img{margin-top:%?200?%;width:%?200?%;height:%?200?%}}.auth-group .placeholder[data-v-3d5bce6c]{color:#b3b3b3}.auth-button[data-v-3d5bce6c]{padding:%?25?% 0 %?50?% 0}.auth-button .cu-btn[data-v-3d5bce6c]{height:%?90?%}.text-center[data-v-3d5bce6c]{color:#3f2f21}.auth-clause[data-v-3d5bce6c]{display:flex;align-items:center;justify-content:start;font-size:%?25?%;color:#909090}.shake-horizontal[data-v-3d5bce6c]{-webkit-animation-name:shake-horizontal-data-v-3d5bce6c;animation-name:shake-horizontal-data-v-3d5bce6c;-webkit-animation-duration:.1s;animation-duration:.1s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;-webkit-animation-delay:0s;animation-delay:0s;-webkit-animation-play-state:running;animation-play-state:running}.auth-clause uni-checkbox[data-v-3d5bce6c]{margin-right:%?6?%;-webkit-transform:scale(.8);transform:scale(.8)}.auth-clause uni-text[data-v-3d5bce6c]{margin:0;color:#ffa001}.auth-foot[data-v-3d5bce6c]{width:100%;z-index:777;padding:0 %?60?%;padding-bottom:calc(env(safe-area-inset-bottom) / 2)}.auth-foot .oauth[data-v-3d5bce6c]{display:flex;flex-wrap:wrap;align-items:center;min-height:%?160?%;justify-content:space-around}.auth-foot .oauth uni-view[data-v-3d5bce6c]{border:%?2?% solid #fcf7e9}.auth-foot .menu[data-v-3d5bce6c]{display:flex;align-items:center;justify-content:center;margin-bottom:%?60?%;color:#3f2f21;line-height:%?28?%;font-size:%?28?%}.auth-foot uni-text[data-v-3d5bce6c]{width:%?180?%;text-align:center}.auth-foot uni-text[data-v-3d5bce6c]:nth-child(2){border-left:1px solid #ececec}\n/* 验证码 */.auth-title[data-v-3d5bce6c]{padding-bottom:%?30?%;padding-top:%?170?%;font-size:%?60?%}.auth-mobile[data-v-3d5bce6c]{color:#9a9a9a;padding-bottom:%?20?%}.auth-mobile uni-text[data-v-3d5bce6c]{margin-left:%?10?%}.codes[data-v-3d5bce6c]{display:flex;justify-content:space-around;flex-direction:row}.codes uni-input[data-v-3d5bce6c]{background:#fff;border-bottom:1px solid #c3c3c3;width:%?90?%;height:%?90?%;font-size:%?42?%;padding:0 %?20?%\n\t/* text-align: center; */}.codes .input[data-v-3d5bce6c]{display:flex;\n\t/* justify-content: center; */\n\t/* align-items: center; */background:#fff;border-bottom:1px solid #c3c3c3;width:%?90?%;height:%?90?%;font-size:%?42?%;font-weight:500;color:#333}.codes .input .shining[data-v-3d5bce6c]{border:1px solid #ffa001;height:%?50?%;animation:shining-data-v-3d5bce6c 1s linear infinite;\n\t/* 其它浏览器兼容性前缀 */-webkit-animation:shining-data-v-3d5bce6c 1s linear infinite;-moz-animation:shining-data-v-3d5bce6c 1s linear infinite;-ms-animation:shining-data-v-3d5bce6c 1s linear infinite;-o-animation:shining-data-v-3d5bce6c 1s linear infinite}.codes .active[data-v-3d5bce6c]{border-bottom:1px solid #ffa001;caret-color:#ffa001}.oneline-codes uni-input[data-v-3d5bce6c]{text-align:center}.auth-again[data-v-3d5bce6c]{padding-top:%?50?%}.auth-again uni-text[data-v-3d5bce6c]{color:#ffa001;margin-right:%?40?%}.auth-again .time[data-v-3d5bce6c]{color:#9a9a9a}@-webkit-keyframes shining-data-v-3d5bce6c{0%{opacity:1}50%{opacity:1}50.01%{opacity:0}100%{opacity:0}}.wlIcon-QQ[data-v-3d5bce6c]{color:#12b8f6}.wlIcon-WeChat[data-v-3d5bce6c]{color:#02dc6b}.wlIcon-WeiBo[data-v-3d5bce6c]{color:#d32820}.wlIcon-Xiaomi[data-v-3d5bce6c]{color:#ff6b00}uni-switch.orangeYellow[checked] .wx-switch-input[data-v-3d5bce6c],\nuni-checkbox.orangeYellow[checked] .wx-checkbox-input[data-v-3d5bce6c],\nuni-radio.orangeYellow[checked] .wx-radio-input[data-v-3d5bce6c],\nuni-switch.orangeYellow.checked .uni-switch-input[data-v-3d5bce6c],\nuni-checkbox.orangeYellow.checked .uni-checkbox-input[data-v-3d5bce6c],\nuni-radio.orangeYellow.checked .uni-radio-input[data-v-3d5bce6c]{background-color:#ffa001!important;border-color:#ffa001!important;color:#333!important}@-webkit-keyframes shake-horizontal-data-v-3d5bce6c{0%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}2%{-webkit-transform:translate(-4px) rotate(0deg);transform:translate(-4px) rotate(0deg)}4%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}6%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}8%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}10%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}12%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}14%{-webkit-transform:translate(-7px) rotate(0deg);transform:translate(-7px) rotate(0deg)}16%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}18%{-webkit-transform:translate(7px) rotate(0deg);transform:translate(7px) rotate(0deg)}20%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}22%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}24%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}26%{-webkit-transform:translate(3px) rotate(0deg);transform:translate(3px) rotate(0deg)}28%{-webkit-transform:translate(-5px) rotate(0deg);transform:translate(-5px) rotate(0deg)}30%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}32%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}34%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}36%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}38%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}40%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}42%{-webkit-transform:translate(8px) rotate(0deg);transform:translate(8px) rotate(0deg)}44%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}46%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}48%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}50%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}52%{-webkit-transform:translate(6px) rotate(0deg);transform:translate(6px) rotate(0deg)}54%{-webkit-transform:translate(-8px) rotate(0deg);transform:translate(-8px) rotate(0deg)}56%{-webkit-transform:translate(5px) rotate(0deg);transform:translate(5px) rotate(0deg)}58%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}60%{-webkit-transform:translate(7px) rotate(0deg);transform:translate(7px) rotate(0deg)}62%{-webkit-transform:translate(1px) rotate(0deg);transform:translate(1px) rotate(0deg)}64%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}66%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}68%{-webkit-transform:translate(-7px) rotate(0deg);transform:translate(-7px) rotate(0deg)}70%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}72%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}74%{-webkit-transform:translate(5px) rotate(0deg);transform:translate(5px) rotate(0deg)}76%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}78%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}80%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}82%{-webkit-transform:translate(8px) rotate(0deg);transform:translate(8px) rotate(0deg)}84%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}86%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}88%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}90%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}92%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}94%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}96%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}98%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}}@keyframes shake-horizontal-data-v-3d5bce6c{0%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}2%{-webkit-transform:translate(-4px) rotate(0deg);transform:translate(-4px) rotate(0deg)}4%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}6%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}8%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}10%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}12%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}14%{-webkit-transform:translate(-7px) rotate(0deg);transform:translate(-7px) rotate(0deg)}16%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}18%{-webkit-transform:translate(7px) rotate(0deg);transform:translate(7px) rotate(0deg)}20%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}22%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}24%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}26%{-webkit-transform:translate(3px) rotate(0deg);transform:translate(3px) rotate(0deg)}28%{-webkit-transform:translate(-5px) rotate(0deg);transform:translate(-5px) rotate(0deg)}30%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}32%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}34%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}36%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}38%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}40%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}42%{-webkit-transform:translate(8px) rotate(0deg);transform:translate(8px) rotate(0deg)}44%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}46%{-webkit-transform:translate(-10px) rotate(0deg);transform:translate(-10px) rotate(0deg)}48%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}50%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}52%{-webkit-transform:translate(6px) rotate(0deg);transform:translate(6px) rotate(0deg)}54%{-webkit-transform:translate(-8px) rotate(0deg);transform:translate(-8px) rotate(0deg)}56%{-webkit-transform:translate(5px) rotate(0deg);transform:translate(5px) rotate(0deg)}58%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}60%{-webkit-transform:translate(7px) rotate(0deg);transform:translate(7px) rotate(0deg)}62%{-webkit-transform:translate(1px) rotate(0deg);transform:translate(1px) rotate(0deg)}64%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}66%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}68%{-webkit-transform:translate(-7px) rotate(0deg);transform:translate(-7px) rotate(0deg)}70%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}72%{-webkit-transform:translate(-6px) rotate(0deg);transform:translate(-6px) rotate(0deg)}74%{-webkit-transform:translate(5px) rotate(0deg);transform:translate(5px) rotate(0deg)}76%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}78%{-webkit-transform:translate(9px) rotate(0deg);transform:translate(9px) rotate(0deg)}80%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}82%{-webkit-transform:translate(8px) rotate(0deg);transform:translate(8px) rotate(0deg)}84%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}86%{-webkit-transform:translate(-1px) rotate(0deg);transform:translate(-1px) rotate(0deg)}88%{-webkit-transform:translate(-3px) rotate(0deg);transform:translate(-3px) rotate(0deg)}90%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}92%{-webkit-transform:translate(0) rotate(0deg);transform:translate(0) rotate(0deg)}94%{-webkit-transform:translate(4px) rotate(0deg);transform:translate(4px) rotate(0deg)}96%{-webkit-transform:translate(2px) rotate(0deg);transform:translate(2px) rotate(0deg)}98%{-webkit-transform:translate(-2px) rotate(0deg);transform:translate(-2px) rotate(0deg)}}.password-input-container[data-v-3d5bce6c]{position:relative}.password-icon[data-v-3d5bce6c]{position:absolute;right:%?20?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:2}.bar[data-v-3d5bce6c]{padding-left:%?20?%;background-image:linear-gradient(180deg,#dd6100 0,#f96c00);color:#fff}.top[data-v-3d5bce6c]{display:flex;flex-direction:column;align-items:center;background-image:linear-gradient(180deg,#f96c00 0,#ff9000 40%,#ffbd6c);width:100%;height:%?660?%;position:absolute;top:0}.top .backIcon[data-v-3d5bce6c]{left:%?30?%;position:absolute;color:#fff}.top .topText[data-v-3d5bce6c]{text-align:center;letter-spacing:%?12?%}.top .img[data-v-3d5bce6c]{margin-top:%?200?%;width:%?200?%;height:%?200?%}.topSpace[data-v-3d5bce6c]{background-color:#fff;height:%?520?%}.authTopTitle[data-v-3d5bce6c]{width:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;letter-spacing:%?1?%;font-size:%?48?%;height:%?200?%}.loginSelect[data-v-3d5bce6c]{height:%?140?%;width:50%;line-height:%?160?%;text-align:center;color:#ffa001}.auth[data-v-3d5bce6c]{min-height:calc(100vh - %?520?%)}',""]),t.exports=e},23273:function(t,e,a){"use strict";var r=a("6fee"),n=a.n(r);n.a},"424b":function(t,e,a){"use strict";a.r(e);var r=a("abbc"),n=a.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},"6fee":function(t,e,a){var r=a("16c3");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=a("967d").default;n("4ee1ecae",r,!0,{sourceMap:!1,shadowMode:!1})},"935e":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r}));var r={uniIcons:a("d70c").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticStyle:{"background-color":"rgba(170, 170, 170, 0.1)",position:"relative"}},[a("v-uni-view",{staticClass:"top"},[a("v-uni-image",{staticClass:"img",attrs:{src:t.$wanlshop.imgstc("/user/logo01-1.png"),mode:"widfit"}})],1),a("v-uni-view",{staticClass:"topSpace"}),a("v-uni-view",{staticClass:"auth"},[a("v-uni-view",{staticClass:"authTopTitle"},[a("v-uni-view",{staticClass:"text-bold"},[t._v("Hello!")]),a("v-uni-view",{staticClass:"text-xl margin-top-xs"},[t._v("欢迎来到共店消费宝")])],1),t.$jssdk.isWechat()&&4==t.loginState?a("v-uni-view",{},[a("v-uni-view",{staticClass:"auth-button flex flex-direction"},t._l(t.providerList,(function(e,r){return a("v-uni-button",{staticClass:"cu-btn sl radius-bock text-white",staticStyle:{"background-color":"#ffa001"},attrs:{"form-type":"submit"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.tologin(e)}}},[t._v("微信授权登录")])})),1),a("v-uni-view",{staticClass:"flex justify-around align-center padding-bottom"},[a("v-uni-view",{staticClass:" loginSelect",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loginState=2}}},[t._v("短信验证码登录")]),a("v-uni-view",{staticClass:" loginSelect",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loginState=3}}},[t._v("账号密码登录")])],1)],1):a("v-uni-form",{on:{submit:function(e){arguments[0]=e=t.$handleEvent(e),t.formSubmit.apply(void 0,arguments)}}},[0!=t.loginState?a("v-uni-view",{staticClass:"auth-group radius-bock bg-gray wlian-grey-light"},[a("v-uni-input",{attrs:{placeholder:"请输入手机号 / 用户名","placeholder-class":"placeholder",name:"account",type:"text",maxlength:"16","confirm-type":"next"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onKeyInput.apply(void 0,arguments)}}})],1):t._e(),3==t.loginState?a("v-uni-view",{staticClass:"auth-group radius-bock bg-gray wlian-grey-light password-input-container"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入登录密码",password:!t.showPassword,"confirm-type":"done",maxlength:"16","placeholder-class":"placeholder",name:"password"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onKeyInput.apply(void 0,arguments)}}}),a("uni-icons",{staticClass:"password-icon",attrs:{type:t.showPassword?"eye-slash-filled":"eye",size:"20",color:"#999"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.togglePasswordVisibility.apply(void 0,arguments)}}})],1):t._e(),3==t.loginState?a("v-uni-view",{staticClass:"text-right text-gray text-min",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.retrieve.apply(void 0,arguments)}}},[t._v("忘记密码")]):t._e(),a("v-uni-view",{staticClass:"auth-button flex flex-direction"},[1==t.loginState?a("v-uni-button",{staticClass:"cu-btn sl radius-bock text-white",staticStyle:{"background-color":"#ffa001"},attrs:{"form-type":"submit",disabled:t.submitDisabled}},[t._v("下一步")]):t._e(),2==t.loginState?a("v-uni-button",{staticClass:"cu-btn sl radius-bock text-white",staticStyle:{"background-color":"#ffa001"},attrs:{"form-type":"submit"}},[t._v("获取验证码")]):t._e(),3==t.loginState?a("v-uni-button",{staticClass:"cu-btn sl radius-bock text-white",staticStyle:{"background-color":"#ffa001"},attrs:{"form-type":"submit"}},[t._v("登 录")]):t._e()],1),a("v-uni-checkbox-group",{staticClass:"auth-clause",class:1==t.checked?"shake-horizontal":"",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.CheckboxChange.apply(void 0,arguments)}}},[a("v-uni-checkbox",{staticClass:"orangeYellow",class:2==t.checked?"checked":"",attrs:{checked:2==t.checked,value:"2"}}),a("v-uni-view",{staticClass:"text-min"},[t._v("我已阅读共店消费宝"),a("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onDetails(t.$store.state.common.appConfig.user_agreement,"用户协议")}}},[t._v("《用户协议》")]),t._v("及"),a("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onDetails(t.$store.state.common.appConfig.privacy_protection,"隐私保护")}}},[t._v("《隐私权保护声明》")])],1)],1),a("v-uni-view",{staticClass:"flex align-center justify-around"},[2==t.loginState||0==t.loginState?a("v-uni-view",{staticClass:"loginSelect",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loginState=3}}},[t._v("账号密码登陆")]):t._e(),3==t.loginState||0==t.loginState?a("v-uni-view",{staticClass:"loginSelect",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loginState=2}}},[t._v("短信验证码登录")]):t._e(),t.$jssdk.isWechat()?a("v-uni-view",{staticClass:"loginSelect",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loginState=4}}},[t._v("微信授权登录")]):t._e()],1)],1),a("v-uni-view",{staticClass:"auth-foot"},[a("v-uni-view",{staticClass:"menu text-grey"},[a("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.register.apply(void 0,arguments)}}},[t._v("注册")]),a("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.help.apply(void 0,arguments)}}},[t._v("帮助")])],1)],1)],1)],1)},o=[]},a228:function(t,e,a){a("23f4"),a("7d2f"),a("5c47"),a("9c4e"),a("ab80"),a("0506"),a("64aa"),a("5ef2"),t.exports={error:"",check:function(t,e){for(var a=0;a<e.length;a++){if(!e[a].checkType)return!0;if(!e[a].name)return!0;if(!e[a].errorMsg)return!0;if(!t[e[a].name])return this.error=e[a].errorMsg,!1;switch(e[a].checkType){case"string":var r=new RegExp("^.{"+e[a].checkRule+"}$");if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"int":r=new RegExp("^(-[1-9]|[1-9])[0-9]{"+e[a].checkRule+"}$");if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"between":if(!this.isNumber(t[e[a].name]))return this.error=e[a].errorMsg,!1;var n=e[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[e[a].name]>n[1]||t[e[a].name]<n[0])return this.error=e[a].errorMsg,!1;break;case"betweenD":r=/^-?[1-9][0-9]?$/;if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;n=e[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[e[a].name]>n[1]||t[e[a].name]<n[0])return this.error=e[a].errorMsg,!1;break;case"betweenF":r=/^-?[0-9][0-9]?.+[0-9]+$/;if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;n=e[a].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[e[a].name]>n[1]||t[e[a].name]<n[0])return this.error=e[a].errorMsg,!1;break;case"same":if(t[e[a].name]!=e[a].checkRule)return this.error=e[a].errorMsg,!1;break;case"notsame":if(t[e[a].name]==e[a].checkRule)return this.error=e[a].errorMsg,!1;break;case"email":r=/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"phoneno":r=/^1[0-9]{10,10}$/;if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"zipcode":r=/^[0-9]{6}$/;if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"reg":r=new RegExp(e[a].checkRule);if(!r.test(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"in":if(-1==e[a].checkRule.indexOf(t[e[a].name]))return this.error=e[a].errorMsg,!1;break;case"notnull":if(null==t[e[a].name]||t[e[a].name].length<1)return this.error=e[a].errorMsg,!1;break}}return!0},isNumber:function(t){return/^-?[1-9][0-9]?.?[0-9]*$/.test(t)}}},abbc:function(t,e,a){"use strict";(function(t){a("6a54");var r=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("c223"),a("5c47"),a("af8f"),a("5ef2");var n=r(a("2634")),o=r(a("2fdc")),i=r(a("a228")),s={data:function(){return{submitDisabled:!0,providerList:[],loginRes:{},pageroute:"/pages/user",checked:0,invite_code:"",loginState:4,headTop:0,headHeight:0,windowHeight:0,doubleBack:!1,showPassword:!1}},onLoad:function(e){var a=this;return(0,o.default)((0,n.default)().mark((function r(){var o,i,s,c;return(0,n.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:a.$jssdk.isWechat()?a.loginState=4:a.loginState=3,o=a.$wanlshop.wanlsys(),a.headTop=o.top,a.headHeight=o.height,a.windowHeight=o.windowHeight,"inviteMerchant"===e.pagePath?a.doubleBack=!0:a.doubleBack=!1,e.m?i=e.m:e.pageroute&&(s=decodeURIComponent(e.pageroute),s&&(i=s.split("=")[2]),e.pageroute.scene&&(c=a.$wanlshop.getParam(decodeURIComponent(e.pageroute.scene)),t.log("data"),i=c.m)),i&&uni.setStorageSync("mark",i),a.pageroute=e.pageroute,t.log("pageroute======",a.pageroute),a.pageroute=a.pageroute?a.pageroute:"/pages/user";case 11:case"end":return r.stop()}}),r)})))()},onShow:function(){if(this.$jssdk.isWechat()){this.providerList=[{id:"weixin",name:"wlIcon-WeChat",platform:"h5_weixin"}],t.log(this.providerList,"  this.providerList"),t.log(this.pageroute,"-----------微信浏览器环境");var e=window.location.href,a=this.getUrlCode().code,r=uni.getStorageSync("wanlshop:code");if(null==a||""===a||"undefined"==a||null==r||""===r||"undefined"==r||a==r){var n=encodeURIComponent(e),o=this.$wanlshop.prePage().$mp.page;uni.setStorageSync("wanlshop:code",0),window.location.href="https://open.weixin.qq.com/connect/oauth2/authorize?appid=".concat(this.$wanlshop.config("appid"),"&redirect_uri=").concat(n,"&response_type=code&scope=snsapi_userinfo&state=").concat(encodeURIComponent("/".concat(o.route,"?").concat(this.$wanlshop.parseParams(o.options))),"#wechat_redirect")}else{this.getUrlCode().state;uni.setStorageSync("wanlshop:code",a)}}},methods:{goBack:function(){this.doubleBack?this.$wanlshop.back(2):this.$wanlshop.back(1)},CheckboxChange:function(t){this.checked=t.detail.value},tologin:function(e){var a=this;t.log(e),uni.showLoading({title:"登录中"}),this.$jssdk.isWechat()?(t.log(uni.getStorageSync("wanlshop:code")),uni.request({url:"/wanlshop/user/third",method:"POST",data:{platform:"h5_weixin",bind:2,code:uni.getStorageSync("wanlshop:code")?uni.getStorageSync("wanlshop:code"):null,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null,invite_code:uni.getStorageSync("invite_code")?uni.getStorageSync("invite_code"):null},success:function(t){5==t.res.code?a.$wanlshop.to("/pages/user/auth/bindWechat?pageroute=".concat(a.pageroute,"&bind_token=").concat(t.res.data.bind_token)):(a.$store.dispatch("user/login",t.data),a.$store.dispatch("cart/login"),a.$store.dispatch("chat/get"),uni.redirectTo({url:decodeURIComponent(a.pageroute)}))},complete:function(){uni.removeStorageSync("wanlshop:code")}})):uni.request({url:"/wanlshop/user/third_web",method:"POST",data:{platform:e.platform,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null,invite_code:uni.getStorageSync("invite_code")?uni.getStorageSync("invite_code"):null,mark:uni.getStorageSync("mark")?uni.getStorageSync("mark"):null},success:function(t){uni.hideLoading(),a.$store.dispatch("user/login",t.data),a.$store.dispatch("cart/login"),a.$store.dispatch("chat/get"),uni.redirectTo({url:decodeURIComponent(a.pageroute)})}})},onKeyInput:function(t){this.submitDisabled=!1},decryptPhoneNumber:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(2!=e.checked){a.next=6;break}if("getPhoneNumber:fail user deny"==t.detail.errMsg){a.next=4;break}return a.next=4,uni.request({url:"/wanlshop/user/phone",method:"POST",data:{encryptedData:t.detail.encryptedData,iv:t.detail.iv,code:e.loginRes.code,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null,invite_code:uni.getStorageSync("invite_code")?uni.getStorageSync("invite_code"):null,mark:uni.getStorageSync("mark")?uni.getStorageSync("mark"):null},success:function(t){e.$store.dispatch("user/login",t.data),e.$store.dispatch("cart/login"),uni.redirectTo({url:decodeURIComponent(e.pageroute)})}});case 4:a.next=8;break;case 6:e.checked=2,e.$wanlshop.msg("请先同意用户协议和隐私保护声明");case 8:case"end":return a.stop()}}),a)})))()},formSubmit:function(t){2==this.checked?this.islogin(t):(this.checked=2,this.$wanlshop.msg("请先同意用户协议和隐私保护声明"))},islogin:function(e){var a=this;if(2==this.loginState){var r=[{name:"account",checkType:"phoneno",errorMsg:"请输入正确的手机号"}],n=e.detail.value,o=i.default.check(n,r);o&&uni.request({url:"/wanlshop/validate/check_mobile_exist",data:{mobile:n.account},complete:function(e){0==e.res.code?uni.showModal({title:"提示",content:"账号不存在，是否注册？",success:function(e){e.confirm?a.$wanlshop.to("register?mobile=".concat(n.account,"&url=").concat(a.pageroute)):e.cancel&&t.log("取消")}}):a.$wanlshop.to("validcode?event=mobilelogin&mobile=".concat(n.account,"&url=").concat(a.pageroute),"slide-in-bottom",200)}})}else if(3==this.loginState){r=[{name:"account",checkType:"notnull",errorMsg:"请输入用户名"},{name:"password",checkType:"string",checkRule:"6,16",errorMsg:"密码至少6位"}],n=e.detail.value,o=i.default.check(n,r);this.checkState=o,o?uni.request({url:"/wanlshop/user/login",method:"POST",data:{account:n.account,password:n.password,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null,mark:uni.getStorageSync("mark")?uni.getStorageSync("mark"):null},success:function(e){a.$store.dispatch("user/login",e.data),a.$store.dispatch("cart/login"),a.$store.dispatch("chat/get"),a.get_h5_weixin_pay(),uni.redirectTo({url:decodeURIComponent(a.pageroute)}),t.log(decodeURIComponent(a.pageroute))},complete:function(){uni.removeStorageSync("wanlshop:code")}}):this.$wanlshop.msg(i.default.error)}},get_h5_weixin_pay:function(){return(0,o.default)((0,n.default)().mark((function t(){var e;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=uni.getStorageSync("wanlshop:code")?uni.getStorageSync("wanlshop:code"):null,!e){t.next=4;break}return t.next=4,uni.request({url:"/wanlshop/user/third",method:"POST",data:{platform:"h5_weixin_pay",code:e},complete:function(){uni.removeStorageSync("wanlshop:code")}});case 4:case"end":return t.stop()}}),t)})))()},retrieve:function(){this.$wanlshop.to("retrieve?url=".concat(this.pageroute))},getUrlCode:function(){var t=location.search,e=new Object;if(-1!=t.indexOf("?"))for(var a=t.substr(1),r=a.split("&"),n=0;n<r.length;n++)e[r[n].split("=")[0]]=r[n].split("=")[1];return e},register:function(){this.$wanlshop.to("register?url=".concat(this.pageroute))},help:function(){this.$wanlshop.to("/pages/user/help?url=".concat(this.pageroute))},togglePasswordVisibility:function(){this.showPassword=!this.showPassword}}};e.default=s}).call(this,a("ba7c")["default"])},c838:function(t,e,a){"use strict";a.r(e);var r=a("935e"),n=a("424b");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("23273");var i=a("828b"),s=Object(i["a"])(n["default"],r["b"],r["c"],!1,null,"3d5bce6c",null,!1,r["a"],void 0);e["default"]=s.exports}}]);