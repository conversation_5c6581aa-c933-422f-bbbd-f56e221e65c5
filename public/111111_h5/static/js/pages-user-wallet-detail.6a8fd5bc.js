(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-wallet-detail"],{"1f59":function(t,a,i){"use strict";i("7a82");var e=i("ee27").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var s=e(i("f07e")),n=e(i("c964")),v={data:function(){return{moneyData:{},data:null,bankList:{USDT:"USDT钱包",SDR:"SDR钱包",alipay:"支付宝",wechat:"微信",jssdk:"微信公众平台",ALIPAY:"支付宝",WECHAT:"微信",ICBC:"工商银行",ABC:"农业银行",PSBC:"邮储银行",CCB:"建设银行",CMB:"招商银行",BOC:"中国银行",COMM:"交通银行",SPDB:"浦发银行",GDB:"广发银行",CMBC:"民生银行",PAB:"平安银行",CEB:"光大银行",CIB:"兴业银行",CITIC:"中信银行"},withdrawStatus:{created:"申请中",successed:"成功",rejected:"已拒绝"}}},onLoad:function(t){this.moneyData=JSON.parse(t.data),this.loadData()},methods:{loadData:function(){var t=this;return(0,n.default)((0,s.default)().mark((function a(){var i;return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i=t.moneyData,t.$api.get({url:"/wanlshop/pay/details",data:{id:i.service_ids,type:i.type},success:function(a){t.data=a}});case 2:case"end":return a.stop()}}),a)})))()},getType:function(t){return["无需退货","退货退款"][t]},getReason:function(t){return["不喜欢","空包裹","一直未送达","颜色/尺码不符","质量问题","少件漏发","假冒品牌"][t]}}};a.default=v},"2a59":function(t,a,i){"use strict";var e=i("b31a"),s=i.n(e);s.a},3967:function(t,a,i){"use strict";i.r(a);var e=i("fd17"),s=i("c4e0");for(var n in s)["default"].indexOf(n)<0&&function(t){i.d(a,t,(function(){return s[t]}))}(n);i("2a59");var v=i("f0c5"),l=Object(v["a"])(s["default"],e["b"],e["c"],!1,null,"064538c4",null,!1,e["a"],void 0);a["default"]=l.exports},b31a:function(t,a,i){var e=i("b811");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var s=i("4f06").default;s("2826f287",e,!0,{sourceMap:!1,shadowMode:!1})},b811:function(t,a,i){var e=i("24fb");a=e(!1),a.push([t.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.detail[data-v-064538c4]{padding:%?30?% %?25?%}.detail .content .header[data-v-064538c4]{padding-bottom:%?50?%;border-bottom:%?1?% solid #f6f6f6}.detail .content .header uni-image[data-v-064538c4]{width:%?282.3?%;height:%?282.3?%}.detail .content .header .num[data-v-064538c4]{font-size:%?58?%}.detail .content .list .item[data-v-064538c4]{margin-bottom:%?40?%}.detail .content .list .name[data-v-064538c4]{margin-right:%?90?%;width:%?160?%}.detail .content .list .value[data-v-064538c4]{width:%?400?%;word-break:break-all;word-wrap:break-word}.detail .help[data-v-064538c4]{margin:%?100?% auto 0;text-align:center}',""]),t.exports=a},c4e0:function(t,a,i){"use strict";i.r(a);var e=i("1f59"),s=i.n(e);for(var n in e)["default"].indexOf(n)<0&&function(t){i.d(a,t,(function(){return e[t]}))}(n);a["default"]=s.a},fd17:function(t,a,i){"use strict";i.d(a,"b",(function(){return e})),i.d(a,"c",(function(){return s})),i.d(a,"a",(function(){}));var e=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("v-uni-view",{staticClass:"detail"},["pay"==t.moneyData.type&&t.data?t._l(t.data,(function(a,e){return i("v-uni-view",{key:a.id,staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(a.shop.shopname))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v("-"+t._s(a.pay.price))])],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.order_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.pay_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单价格")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.order_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付方式")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.memo))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.trade_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("实际支付")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.actual_payment))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.paymenttime_text))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("快递费")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.freight_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("优惠金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.discount_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("总金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.total_amount))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.createtime_text))])],1)],1)],1)})):t._e(),"groups"==t.moneyData.type&&t.data?t._l(t.data,(function(a,e){return i("v-uni-view",{key:a.id,staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(a.shop.shopname))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v("-"+t._s(a.pay.price))])],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.order_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.pay_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单价格")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.order_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付方式")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.memo))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.trade_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("实际支付")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.actual_payment))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.paymenttime_text))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("快递费")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.freight_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("优惠金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.discount_price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("总金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.pay.total_amount))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(a.createtime_text))])],1)],1)],1)})):t._e(),"recharge"==t.moneyData.type&&t.data?[i("v-uni-view",{staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(t.moneyData.memo))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v("+"+t._s(t.moneyData.money))])],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("状态")]),i("v-uni-view",{staticClass:"value text-df"},[t._v("充值成功")])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.orderid))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付类型")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.bankList[t.data.paytype]))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.memo))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动后")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.after))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动前")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.before))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("充值时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.moneyData.createtime,"yyyy-mm-dd hh:MM:ss")))])],1)],1)],1)]:t._e(),"withdraw"==t.moneyData.type&&t.data?[i("v-uni-view",{staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(t.moneyData.memo))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v(t._s(t.moneyData.money))])],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("状态")]),i("v-uni-view",{staticClass:"value text-df"},[t._v("回购"+t._s(t.withdrawStatus[t.data.status]))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("回购金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.money))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("回购手续费")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.handingfee))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("实到金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(Number(t.data.payamount).toFixed(2)))])],1),"successed"==t.data.status&&t.data.transfertime?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("转账时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.data.transfertime,"yyyy-mm-dd hh:MM:ss")))])],1):t._e(),"rejected"==t.data.status&&t.data.memo?i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("拒绝理由")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.memo))])],1):t._e(),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("类型")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.bankList[t.data.type]))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("账号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.account))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("交易号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.orderid))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动后")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.after))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动前")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.before))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("提交时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.moneyData.createtime,"yyyy-mm-dd hh:MM:ss")))])],1)],1)],1)]:t._e(),"refund"==t.moneyData.type&&t.data?[i("v-uni-view",{staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(t.moneyData.memo))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v(t._s(t.moneyData.money>0?"+"+t.moneyData.money:t.moneyData.money))]),i("v-uni-view",{on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.$wanlshop.to("/pages/user/refund/details?id="+t.data.refund.id)}}},[i("v-uni-button",{staticClass:"cu-btn sm radius-bock"},[t._v("查看退款")])],1)],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("商家")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.shop.shopname))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("订单号")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.order_no))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("下单时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.createtime_text))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("支付时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.paymenttime_text))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("退款金额")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.data.refund.price))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("退款类型")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.getType(t.data.refund.type)))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("退款理由")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.getReason(t.data.refund.reason)))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("退款时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.data.refund.createtime,"yyyy-mm-dd hh:MM:ss")))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("退款时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.data.refund.completetime,"yyyy-mm-dd hh:MM:ss")))])],1)],1)],1)]:[i("v-uni-view",{staticClass:"content radius-native bg-white padding-lr-xl"},[i("v-uni-view",{staticClass:"header margin-bottom-lg flex flex-direction justify-center align-center"},[i("v-uni-image",{staticClass:"margin-bottom-xs",attrs:{src:t.$wanlshop.imgstc("/user/pay-icon.png"),mode:""}}),i("v-uni-view",{staticClass:"text-lg margin-tb-xs"},[t._v(t._s(t.moneyData.memo))]),i("v-uni-view",{staticClass:"num wanl-pink text-bold6"},[t._v(t._s(t.moneyData.money>0?"+"+t.moneyData.money:t.moneyData.money))])],1),i("v-uni-view",{staticClass:"list padding-bottom-lg"},[i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动后")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.after))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("变动前")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.moneyData.before))])],1),i("v-uni-view",{staticClass:"item flex"},[i("v-uni-view",{staticClass:"name text-df wanl-gray-light"},[t._v("时间")]),i("v-uni-view",{staticClass:"value text-df"},[t._v(t._s(t.$wanlshop.timeFormat(t.moneyData.createtime,"yyyy-mm-dd hh:MM:ss")))])],1)],1)],1)],i("v-uni-view",{staticClass:"help text-co text-gray3"},[t._v("常见问题")])],2)},s=[]}}]);