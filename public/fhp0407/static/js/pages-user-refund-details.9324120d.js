(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-refund-details"],{"01e2":function(t,e,a){"use strict";var i=a("1b28"),n=a.n(i);n.a},"1b28":function(t,e,a){var i=a("3ef3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("415efc4b",i,!0,{sourceMap:!1,shadowMode:!1})},"340c":function(t,e,a){"use strict";a.r(e);var i=a("bdc5"),n=a("cbb8");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("01e2");var r=a("f0c5"),u=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"07e471a6",null,!1,i["a"],void 0);e["default"]=u.exports},"3ef3":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,".wanl-refund .header[data-v-07e471a6]{width:100%;height:%?180?%;position:relative;background-color:#f72b36}.wanl-refund .header .img-bg[data-v-07e471a6]{width:100%;height:%?180?%}.wanl-refund .header .content[data-v-07e471a6]{width:100%;height:%?180?%;position:absolute;z-index:10;left:0;top:0;display:flex;align-items:center;justify-content:space-between;padding:0 %?60?%;box-sizing:border-box}.wanl-refund .header .status-text[data-v-07e471a6]{font-size:%?34?%;line-height:%?34?%;color:#fefefe}.wanl-refund .header .reason[data-v-07e471a6]{font-size:%?24?%;line-height:%?24?%;color:hsla(0,0%,99.6%,.75);padding-top:%?15?%;display:flex;align-items:center}.wanl-refund .header .reason-text[data-v-07e471a6]{padding-right:%?12?%}.wanl-refund .header .status-img[data-v-07e471a6]{width:%?100?%;height:%?100?%;display:block}.wanl-refund .current .cu-btn[data-v-07e471a6]{width:%?140?%;font-size:%?26?%;padding:0 %?12?%}.wanl-refund .receipt[data-v-07e471a6]{display:flex;align-items:center}.wanl-refund .receipt .icon[data-v-07e471a6]{margin-right:%?25?%;font-weight:700}.wanl-refund .receipt .content[data-v-07e471a6]{flex:1}.wanl-refund .cu-form-group .title[data-v-07e471a6]{padding-right:%?25?%;font-size:%?28?%;height:%?55?%;line-height:%?55?%}.wanl-refund .cu-form-group uni-input[data-v-07e471a6]{font-size:%?28?%;color:#555;padding-right:%?10?%}",""]),t.exports=e},bdc5:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("v-uni-view",{staticClass:"edgeInsetTop"}),a("v-uni-view",{staticClass:"wanl-refund"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-image",{staticClass:"img-bg",attrs:{src:t.$wanlshop.appstc("/order/img_detail_bg.png")}}),a("v-uni-view",{staticClass:"content"},[a("v-uni-view",[a("v-uni-view",{staticClass:"status-text"},[t._v(t._s(t.getStateText(t.refundData.state)))]),a("v-uni-view",{staticClass:"reason"},[a("v-uni-text",{staticClass:"reason-text"},[t._v(t._s(t.refundData.statetime))])],1)],1)],1)],1),4!=t.refundData.state?a("v-uni-view",{staticClass:"bg-white padding-bj"},[t._v(t._s(t.getStateInfo(t.refundData.state)))]):t._e(),0==t.refundData.state?a("v-uni-view",{staticClass:"bg-white solid-top padding-bj current"},[a("v-uni-view",{staticClass:"wanl-gray text-sm"},[a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("卖家同意或超时未处理，系统将自动确认")],1),a("v-uni-view",{staticClass:"margin-top-xs"},[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("如果退款被拒绝，您可以修改申请重新发起")],1)],1),a("v-uni-view",{staticClass:"flex justify-end margin-top"},[a("v-uni-button",{staticClass:"cu-btn line-black margin-lr-xs",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeRefund(t.refundData.id)}}},[t._v("关闭退款")]),a("v-uni-button",{staticClass:"cu-btn line-orange",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editRefund(t.refundData.id)}}},[t._v("修改申请")])],1)],1):t._e(),1==t.refundData.state?a("v-uni-view",[a("v-uni-view",{staticClass:"bg-white solid-top padding-bj receipt"},[a("v-uni-view",{staticClass:"icon"},[a("v-uni-text",{staticClass:"wlIcon-guanzhu1"})],1),a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"flex justify-between"},[a("v-uni-text",[t._v("收件人："+t._s(t.refundData.shopConfig.returnName))]),a("v-uni-text",[t._v(t._s(t.refundData.shopConfig.returnPhoneNum))])],1),a("v-uni-view",{staticClass:"margin-top-xs"},[a("v-uni-view",{staticClass:"text-cut-2"},[t._v(t._s(t.refundData.shopConfig.returnAddr))])],1)],1)],1),a("v-uni-view",{staticClass:"bg-white padding-bj solid-top"},[a("v-uni-view",{staticClass:"wanl-gray text-sm"},[a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("请勿使用平邮或到付，以免商家无法收到退货")],1),a("v-uni-view",{staticClass:"margin-top-xs"},[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("请填写真实快递信息，如超时则关闭退款")],1)],1)],1),a("v-uni-view",{staticClass:"cu-form-group margin-top-bj"},[a("v-uni-view",{staticClass:"title"},[t._v("快递单号：")]),a("v-uni-input",{attrs:{placeholder:"请填写快递单号",name:"input"},model:{value:t.returnData.express_no,callback:function(e){t.$set(t.returnData,"express_no",e)},expression:"returnData.express_no"}})],1),a("v-uni-view",{staticClass:"cu-form-group"},[a("v-uni-view",{staticClass:"title"},[t._v("快递公司：")]),a("v-uni-picker",{attrs:{range:t.refundData.kuaidi,"range-key":"name"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.kuaidiChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"picker"},[t._v(t._s(t.kuaidiKey>-1?t.refundData.kuaidi[t.kuaidiKey].name:"请选择"))])],1)],1),a("v-uni-view",{staticClass:"bg-white padding-bj current"},[a("v-uni-view",{staticClass:"flex justify-end"},[a("v-uni-button",{staticClass:"cu-btn line-orange margin-lr-xs",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toExpress(t.refundData.id)}}},[t._v("确认退货")])],1)],1)],1):t._e(),6==t.refundData.state?a("v-uni-view",{staticClass:"bg-white current"},[a("v-uni-view",{staticClass:"bg-white solid-top padding-bj"},[t._v("退货物流："),a("v-uni-text",{staticClass:"wanl-gray"},[t._v(t._s(t.refundData.express_name)+"("+t._s(t.refundData.express_no)+")")])],1),a("v-uni-view",{staticClass:"bg-white solid-top padding-bj current"},[a("v-uni-view",{staticClass:"wanl-gray text-sm"},[a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("如果退款被拒绝，您可以修改申请重新发起")],1),a("v-uni-view",{staticClass:"margin-top-xs"},[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("卖家超时未处理，系统将自动确认")],1)],1)],1)],1):t._e(),2==t.refundData.state?a("v-uni-view",{staticClass:"bg-white solid-top padding-bj current "},[a("v-uni-view",{staticClass:"text-sm"},[t._v("拒绝理由："),a("v-uni-text",{staticClass:"wanl-gray"},[t._v(t._s(t.refundData.refuse_content))])],1),a("v-uni-view",{staticClass:"flex justify-end margin-top"},[a("v-uni-button",{staticClass:"cu-btn line-black",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.arbitrationRefund(t.refundData.id)}}},[t._v("平台介入")]),a("v-uni-button",{staticClass:"cu-btn line-orange margin-left-xs",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.editRefund(t.refundData.id)}}},[t._v("修改申请")])],1)],1):t._e(),3==t.refundData.state?a("v-uni-view",{staticClass:"bg-white solid-top padding-bj current"},[a("v-uni-view",{staticClass:"wanl-gray text-sm"},[a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("客服正在审核退款详情及退款历史记录")],1),a("v-uni-view",{staticClass:"margin-top-xs"},[a("v-uni-text",{staticClass:"wlIcon-dot margin-right-sm"}),t._v("大概1-3个工作日做出答复，请耐心等待")],1)],1)],1):t._e(),4==t.refundData.state?a("v-uni-view",{staticClass:"bg-white padding-bj flex justify-between align-center"},[a("v-uni-text",[t._v("退款总金额")]),a("v-uni-text",{staticClass:"text-price wanl-pink"},[t._v(t._s(t.refundData.price))])],1):t._e(),a("v-uni-view",{staticClass:"bg-white padding-bj margin-top-bj flex justify-between align-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.refundLog(t.refundData.id)}}},[a("v-uni-text",[t._v("退款历史")]),a("v-uni-text",{staticClass:"wlIcon-fanhui2"})],1),a("v-uni-view",{staticClass:"bg-white padding-bj margin-top-bj"},[t._v("退款详情")]),a("v-uni-view",{staticClass:"padding-bj flex"},[a("v-uni-view",{staticClass:"cu-avatar xl margin-right-bj",style:{backgroundImage:"url("+t.$wanlshop.oss(t.refundData.goods.image,70,70)+")"}}),a("v-uni-view",{staticClass:"text-sm",staticStyle:{width:"calc(100% - 128rpx)"}},[a("v-uni-view",{staticClass:"margin-bottom-xs"},[a("v-uni-view",{staticClass:"text-cut-2"},["groups"===t.refundData.order_type?a("v-uni-view",{staticClass:"cu-tag sm margin-right-xs bg-gradual-orange radius"},[a("v-uni-text",[t._v("拼团订单")])],1):t._e(),t._v(t._s(t.refundData.goods.title))],1)],1),a("v-uni-view",{staticClass:"wanl-gray"},[t._v("规格："+t._s(t.refundData.goods.difference))])],1)],1),a("v-uni-view",{staticClass:"bg-white padding-bj text-sm"},[a("v-uni-view",{staticClass:"item flex"},[a("v-uni-text",{staticClass:"wanl-gray"},[t._v("退款类型：")]),a("v-uni-text",[t._v(t._s(t.refundData.type_text))])],1),a("v-uni-view",{staticClass:"item flex margin-top-bj"},[a("v-uni-text",{staticClass:"wanl-gray"},[t._v("退款原因：")]),a("v-uni-text",[t._v(t._s(t.refundData.reason_text))])],1),a("v-uni-view",{staticClass:"item flex margin-top-bj"},[a("v-uni-text",{staticClass:"wanl-gray"},[t._v("退款金额：")]),a("v-uni-text",{staticClass:"text-price"},[t._v(t._s(t.refundData.price))])],1),a("v-uni-view",{staticClass:"item flex margin-top-bj"},[a("v-uni-text",{staticClass:"wanl-gray"},[t._v("物流状态：")]),a("v-uni-text",[t._v(t._s(t.refundData.expressType_text))])],1),a("v-uni-view",{staticClass:"item flex margin-top-bj"},[a("v-uni-text",{staticClass:"wanl-gray"},[t._v("退款时间：")]),a("v-uni-text",[t._v(t._s(t.refundData.createtime_text))])],1)],1),a("v-uni-view",{staticClass:"edgeInsetBottom"})],1)],1)},n=[]},cbb8:function(t,e,a){"use strict";a.r(e);var i=a("d8f4"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},d8f4:function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f07e")),s=i(a("c964")),r={data:function(){return{refundData:{statetime:"",goods:{}},returnData:{id:0,express_no:"",express_name:""},kuaidiKey:-1}},onLoad:function(t){this.loadData({id:t.id})},methods:{loadData:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.$api.get({url:"/wanlshop/refund/getRefundInfo",data:t,success:function(t){if(e.refundData=t,e.returnData.id=t.id,0==t.state){var a=t.createtime+86400*t.config.autoagree-Date.parse(new Date)/1e3,i=Math.floor(a/86400),n=Math.floor(a%86400/3600),s=Math.floor(a%86400%3600/60);s>0&&(e.refundData.statetime="还剩"+s+"分"),n>0&&(e.refundData.statetime="还剩"+n+"天"+s+"分"),i>0&&(e.refundData.statetime="还剩"+i+"天"+n+"小时"+s+"分")}else if(1==t.state){a=t.agreetime+86400*t.config.returntime-Date.parse(new Date)/1e3,i=Math.floor(a/86400),n=Math.floor(a%86400/3600),s=Math.floor(a%86400%3600/60);s>0&&(e.refundData.statetime="还剩"+s+"分"),n>0&&(e.refundData.statetime="还剩"+n+"天"+s+"分"),i>0&&(e.refundData.statetime="还剩"+i+"天"+n+"小时"+s+"分")}else if(2==t.state)e.refundData.statetime=t.rejecttime_text;else if(6==t.state){a=t.returntime+86400*t.config.receivingtime-Date.parse(new Date)/1e3,i=Math.floor(a/86400),n=Math.floor(a%86400/3600),s=Math.floor(a%86400%3600/60);s>0&&(e.refundData.statetime="还剩"+s+"分"),n>0&&(e.refundData.statetime="还剩"+n+"天"+s+"分"),i>0&&(e.refundData.statetime="还剩"+i+"天"+n+"小时"+s+"分")}else 3==t.state?e.refundData.statetime="等待平台处理":4==t.state?e.refundData.statetime=t.completetime_text:5==t.state&&(e.refundData.statetime=t.closingtime_text)}});case 1:case"end":return a.stop()}}),a)})))()},getStateText:function(t){return["等待卖家同意","等待买家退货","卖家拒绝退款","平台介入","退款完成","退款关闭","等待卖家收取退货"][t]},getStateInfo:function(t){return["您已成功发起退款，等待卖家同意","您发起的退款卖家已同意，请退货","您可以修改退货申请再次发起","您已申请平台介入，请等待平台对此判定","退款完成","您已关闭本次退款申请","如果商家确认收到货物，并核查没有问题，将退款给您"][t]},kuaidiChange:function(t){this.kuaidiKey=t.detail.value,this.returnData.express_name=this.refundData.kuaidi[t.detail.value].code},refundLog:function(t){this.$wanlshop.to("/pages/user/refund/log?id=".concat(t))},arbitrationRefund:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.$api.get({url:"/wanlshop/refund/arbitrationRefund",data:{id:t},success:function(a){e.loadData({id:t})}});case 1:case"end":return a.stop()}}),a)})))()},closeRefund:function(t){var e=this;return(0,s.default)((0,n.default)().mark((function a(){return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.$api.get({url:"/wanlshop/refund/closeRefund",data:{id:t},success:function(a){e.$store.commit("statistics/order",{customer:e.$store.state.statistics.order.customer-1}),e.loadData({id:t})}});case 1:case"end":return a.stop()}}),a)})))()},toExpress:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.returnData.express_no){e.next=3;break}return t.$wanlshop.msg("运单号不能为空"),e.abrupt("return",!1);case 3:if(t.returnData.express_name){e.next=6;break}return t.$wanlshop.msg("请选择快递公司"),e.abrupt("return",!1);case 6:t.$api.post({url:"/wanlshop/refund/toExpress",data:t.returnData,success:function(e){t.loadData({id:e})}});case 7:case"end":return e.stop()}}),e)})))()},editRefund:function(t){this.$wanlshop.to("/pages/user/refund/edit?id=".concat(t))}}};e.default=r}}]);