(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-dealer-paper"],{"172a":function(e,t,a){var i=a("9445");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var l=a("4f06").default;l("624c3e1a",i,!0,{sourceMap:!1,shadowMode:!1})},"17a8":function(e,t,a){var i=a("5f63");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var l=a("4f06").default;l("1983e844",i,!0,{sourceMap:!1,shadowMode:!1})},"30bf":function(e,t,a){"use strict";a.r(t);var i=a("68a0"),l=a("77ea");for(var n in l)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(n);a("cfc1");var s=a("f0c5"),c=Object(s["a"])(l["default"],i["b"],i["c"],!1,null,"493f6738",null,!1,i["a"],void 0);t["default"]=c.exports},"5f63":function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.checkBox[data-v-8664d8a8]{margin-right:%?10?%;height:%?36?%}.checkBox uni-image[data-v-8664d8a8]{width:%?36?%;height:%?36?%;box-sizing:border-box}.checkBox .check[data-v-8664d8a8]{box-sizing:border-box;width:%?36?%;height:%?36?%;background:#fff;border:%?2?% solid #ccc;border-radius:%?18?%}.certificate[data-v-8664d8a8]{padding-bottom:%?100?%}.certificate .switch-box[data-v-8664d8a8]{height:%?130?%;border-bottom:%?1?% solid #eee}.certificate .form .form-item[data-v-8664d8a8]{min-height:%?107?%;border-bottom:%?2?% solid #f6f6f6;padding-left:%?40?%;padding-right:%?35?%}.certificate .form .form-item .value[data-v-8664d8a8]{text-align:right}.certificate .form .form-item2[data-v-8664d8a8]{border-bottom:%?2?% solid #f6f6f6;padding-left:%?40?%;padding-right:%?35?%}.certificate .form .form-item2 .value[data-v-8664d8a8]{width:%?560?%;height:%?300?%;background:#f6f6f6;border-radius:%?10?%;border:%?1?% solid #eee}.certificate .form .form-item2 .value uni-image[data-v-8664d8a8]{width:%?338?%;height:%?200?%}.certificate .warn-img[data-v-8664d8a8]{width:%?26?%;height:%?23?%}.certificate .warn-note[data-v-8664d8a8]{width:calc(100% - %?40?%)}.certificate .btn[data-v-8664d8a8]{background:linear-gradient(167deg,#eb5d77,#e93323);border-radius:%?35?%;text-align:center;color:#fff;font-size:%?36?%;width:%?560?%;height:%?70?%;line-height:%?70?%;margin:%?90?% auto 0}',""]),e.exports=t},"68a0":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"wanl-check"},[this.select?t("v-uni-image",{attrs:{src:this.$wanlshop.imgstc("/login/check.png"),mode:""}}):t("v-uni-view",{staticClass:"check"})],1)},l=[]},"6ae8":function(e,t,a){"use strict";a.r(t);var i=a("73df"),l=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=l.a},"73df":function(e,t,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var l=i(a("f07e")),n=i(a("c964")),s=i(a("30bf")),c={data:function(){return{tab:2,svip:1,state:0,limg:"",lpname:"",cpname:"",mobile:"",city:"",yyzzFullImg:"",yyzzImg:""}},components:{wanlCheck:s.default},onShow:function(){},onLoad:function(){this.$store.state.user.isLogin&&this.loadData()},methods:{loadData:function(){var e=this;return(0,n.default)((0,l.default)().mark((function t(){return(0,l.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get({url:"/wanlshop/user/getDealerInfo",success:function(t){"ok"==t.msg&&(e.tab=Number(t.data.state)+1,e.limg=t.data.license_img,e.lpname=t.data.lpname,e.cpname=t.data.cpname,e.mobile=t.data.mobile,e.city=t.data.city)}});case 1:case"end":return t.stop()}}),t)})))()},apply:function(){return this.lpname?this.cpname?this.mobile?this.city?void this.$api.post({url:"/wanlshop/user/getDealerInfo",data:{state:this.tab-1,limg:this.yyzzImg,lpname:this.lpname,cpname:this.cpname,mobile:this.mobile,city:this.city},success:function(){uni.showToast({title:"提交成功",icon:"none",duration:2e3});var e=setTimeout((function(){uni.navigateTo({url:"/pages/dealer/order"}),clearTimeout(e)}),2e3)},fail:function(){}}):(this.$wanlshop.msg("商户所属地区不能为空"),!1):(this.$wanlshop.msg("联系电话不能为空"),!1):(this.$wanlshop.msg("签约商户名称不能为空"),!1):(this.$wanlshop.msg("法人姓名不能为空"),!1)},chooseImage:function(e){var t=this;uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:function(a){t.$api.get({url:"/wanlshop/common/uploadData",success:function(i){uni.getImageInfo({src:a.tempFilePaths[0],success:function(a){t.$api.upload({url:i.uploadurl,filePath:a.path,name:"file",formData:"local"==i.storage?null:i.multipart,success:function(a){console.log("options===>",a),3==e&&(t.yyzzFullImg=a.fullurl,t.yyzzImg=a.url)}})}})}})}})},changeTab:function(e){console.log("val===>",e),this.tab=e}}};t.default=c},"77ea":function(e,t,a){"use strict";a.r(t);var i=a("a1a3"),l=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(n);t["default"]=l.a},9445:function(e,t,a){var i=a("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.wanl-check[data-v-493f6738]{margin-right:%?5?%}.wanl-check uni-image[data-v-493f6738]{width:%?36?%;height:%?36?%}.wanl-check .check[data-v-493f6738]{box-sizing:border-box;width:%?36?%;height:%?36?%;background:#fff;border:%?2?% solid #ccc;border-radius:%?18?%}',""]),e.exports=t},a1a3:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={porps:{show:{type:Boolean,default:!1}},computed:{showWatcher:function(){return this.show}},watch:{showWatcher:function(e,t){console.log("props",e),this.select=newVal},immediate:!0},data:function(){return{select:!1}},methods:{onCheck:function(){this.select=!this.select}}};t.default=i},a385:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"certificate padding-top-xl padding-lr-bj"},[a("v-uni-view",{staticClass:"switch-box radius-native bg-white flex align-center justify-between padding-lr-sm"},[a("v-uni-view",{staticClass:"switch flex align-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeTab(2)}}},[a("v-uni-view",{staticClass:"checkBox"},[2==e.tab?a("v-uni-image",{attrs:{src:e.$wanlshop.imgstc("/login/check.png"),mode:""}}):a("v-uni-view",{staticClass:"check"})],1),a("v-uni-text",{staticClass:"margin-left-xs"},[e._v("个体资质")])],1),a("v-uni-view",{staticClass:"switch flex align-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeTab(3)}}},[a("v-uni-view",{staticClass:"checkBox"},[3==e.tab?a("v-uni-image",{attrs:{src:e.$wanlshop.imgstc("/login/check.png"),mode:""}}):a("v-uni-view",{staticClass:"check"})],1),a("v-uni-text",{staticClass:"margin-left-xs"},[e._v("企业资质")])],1),a("v-uni-view",{staticClass:"switch flex align-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeTab(1)}}},[a("v-uni-view",{staticClass:"checkBox"},[1==e.tab?a("v-uni-image",{attrs:{src:e.$wanlshop.imgstc("/login/check.png"),mode:""}}):a("v-uni-view",{staticClass:"check"})],1),a("v-uni-text",{staticClass:"margin-left-xs"},[e._v("个人资质")])],1)],1),1==e.tab?[a("v-uni-view",{staticClass:"form bg-white radius-native padding-lr-sm"},[a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("法人姓名：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入法人姓名","placeholder-class":"placeholder"},model:{value:e.lpname,callback:function(t){e.lpname=t},expression:"lpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("签约商户名称：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入签约商户名称","placeholder-class":"placeholder"},model:{value:e.cpname,callback:function(t){e.cpname=t},expression:"cpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("联系电话：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入联系电话","placeholder-class":"placeholder"},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("商户所属地区：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入商户所属地区","placeholder-class":"placeholder"},model:{value:e.city,callback:function(t){e.city=t},expression:"city"}})],1)],1)],1)]:e._e(),2==e.tab?[a("v-uni-view",{staticClass:"form bg-white radius-native padding-lr-sm"},[a("v-uni-view",{staticClass:"form-item2 flex flex-direction padding-tb-lg"},[a("v-uni-view",{staticClass:"label text-xl text-bold6 margin-bottom-lg"},[e._v("上传个体执照：")]),a("v-uni-view",{staticClass:"value text-df flex flex-direction align-center justify-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.chooseImage(3)}}},[e.yyzzFullImg?a("v-uni-image",{staticClass:"yyzz",attrs:{src:e.yyzzFullImg,mode:""}}):a("v-uni-image",{staticClass:"yyzz",attrs:{src:e.$wanlshop.imgstc("/pay/yyzz.png"),mode:""}}),a("v-uni-view",{staticClass:"upload-text text-df wanl-gray-light margin-top-sm"},[e._v("点击上传")])],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("法人姓名：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入法人姓名","placeholder-class":"placeholder"},model:{value:e.lpname,callback:function(t){e.lpname=t},expression:"lpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("签约商户名称：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入签约商户名称","placeholder-class":"placeholder"},model:{value:e.cpname,callback:function(t){e.cpname=t},expression:"cpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("联系电话：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入联系电话","placeholder-class":"placeholder"},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("商户所属地区：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入商户所属地区","placeholder-class":"placeholder"},model:{value:e.city,callback:function(t){e.city=t},expression:"city"}})],1)],1)],1)]:e._e(),3==e.tab?[a("v-uni-view",{staticClass:"form bg-white radius-native padding-lr-sm"},[a("v-uni-view",{staticClass:"form-item2 flex flex-direction padding-tb-lg"},[a("v-uni-view",{staticClass:"label text-xl text-bold6 margin-bottom-lg"},[e._v("上传营业执照：")]),a("v-uni-view",{staticClass:"value text-df flex flex-direction align-center justify-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.chooseImage(3)}}},[e.yyzzFullImg?a("v-uni-image",{staticClass:"yyzz",attrs:{src:e.yyzzFullImg,mode:""}}):a("v-uni-image",{staticClass:"yyzz",attrs:{src:e.$wanlshop.imgstc("/pay/yyzz.png"),mode:""}}),a("v-uni-view",{staticClass:"upload-text text-df wanl-gray-light margin-top-sm"},[e._v("点击上传")])],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("法人姓名：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入法人姓名","placeholder-class":"placeholder"},model:{value:e.lpname,callback:function(t){e.lpname=t},expression:"lpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("签约商户名称：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入签约商户名称","placeholder-class":"placeholder"},model:{value:e.cpname,callback:function(t){e.cpname=t},expression:"cpname"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("联系电话：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入联系电话","placeholder-class":"placeholder"},model:{value:e.mobile,callback:function(t){e.mobile=t},expression:"mobile"}})],1)],1),a("v-uni-view",{staticClass:"form-item flex align-center justify-between"},[a("v-uni-view",{staticClass:"label text-xl text-bold6"},[e._v("商户所属地区：")]),a("v-uni-view",{staticClass:"value text-df"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入商户所属地区","placeholder-class":"placeholder"},model:{value:e.city,callback:function(t){e.city=t},expression:"city"}})],1)],1)],1)]:e._e(),a("v-uni-view",{staticClass:"btn text-center text-lg text-white",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.apply.apply(void 0,arguments)}}},[e._v("提交资质证书")])],2)},l=[]},b357:function(e,t,a){"use strict";var i=a("17a8"),l=a.n(i);l.a},b7f0:function(e,t,a){"use strict";a.r(t);var i=a("a385"),l=a("6ae8");for(var n in l)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return l[e]}))}(n);a("b357");var s=a("f0c5"),c=Object(s["a"])(l["default"],i["b"],i["c"],!1,null,"8664d8a8",null,!1,i["a"],void 0);t["default"]=c.exports},cfc1:function(e,t,a){"use strict";var i=a("172a"),l=a.n(i);l.a}}]);