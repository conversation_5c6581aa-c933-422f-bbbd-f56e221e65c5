(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-lifeCircle~pages-lifeCircle-shopLists~pages-page-inputPiece~pages-page-shopSettled~pages-user-~8175312d"],{"15ab":function(e,t,n){"use strict";var r=n("7658"),a=n("57e7");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),a)},"20f3":function(e,t,n){"use strict";var r=n("8bdb"),a=n("5145");r({target:"Array",proto:!0,forced:a!==[].lastIndexOf},{lastIndexOf:a})},"2d47":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/home",style:{disableScroll:!0,navigationBarTitleText:"加载中..",navigationStyle:"custom"}},{path:"pages/index",style:{navigationBarTextStyle:"white",navigationStyle:"custom"}},{path:"pages/lifeCircle",style:{navigationBarTextStyle:"white",navigationStyle:"custom",enablePullDownRefresh:!0,onReachBottomDistance:50}},{path:"pages/category",style:{enablePullDownRefresh:!0,navigationBarTitleText:"分类",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}},{path:"pages/notice",style:{navigationBarTitleText:"消息中心",navigationStyle:"custom"}},{path:"pages/user",style:{enablePullDownRefresh:!0,navigationBarTitleText:"用户中心",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}},{path:"pages/authentication/index",style:{navigationBarTitleText:"实名认证"}}],subPackages:[{root:"pages/video",pages:[{path:"headlines",style:{navigationBarTitleText:"头条",navigationStyle:"custom"}},{path:"around",style:{navigationBarTitleText:"逛逛",navigationStyle:"custom"}}]},{root:"pages/source",pages:[{path:"video",style:{navigationBarTitleText:"商家入驻视频"}},{path:"article",style:{navigationBarTitleText:"学习宝"}}]},{root:"pages/lifeCircle",pages:[{path:"shopLists",style:{navigationBarTitleText:"线下商家列表"}},{path:"shop",style:{navigationBarTitleText:"联盟商家"}},{path:"pay",style:{navigationBarTitleText:"收银台"}},{path:"managerApply",style:{navigationBarTitleText:"商家入驻申请"}},{path:"settle",style:{navigationBarTitleText:"进件申请"}},{path:"license",style:{navigationBarTitleText:"商家资质"}},{path:"bindPay",style:{navigationBarTitleText:"绑定支付码"}}]},{root:"pages/article",pages:[{path:"list",style:{enablePullDownRefresh:!0,navigationBarTitleText:"通知","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"details",style:{navigationBarTitleText:"详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"advert",style:{navigationBarTitleText:"加载中..","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}}]},{root:"pages/notice",pages:[{path:"chat",style:{navigationBarTitleText:"IM连接中...","app-plus":{titleNView:{buttons:[{text:"",fontSrc:"/static/style/iconfont.ttf",fontSize:"21px",color:"#333",float:"right"}]}},h5:{titleNView:{buttons:[{text:"",fontSrc:"/static/style/iconfont.ttf",fontSize:"21px",color:"#333",float:"right"}]}}}},{path:"notify/notify",style:{enablePullDownRefresh:!0,navigationBarTitleText:"通知消息","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"system/system",style:{navigationBarTitleText:"系统消息",enablePullDownRefresh:!0,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"logistics/logistics",style:{enablePullDownRefresh:!0,navigationBarTitleText:"交易/物流","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"notice",style:{navigationStyle:"custom"}}]},{root:"pages/product",pages:[{path:"goods",style:{navigationStyle:"custom"}},{path:"list",style:{enablePullDownRefresh:!0}},{path:"comment",style:{navigationBarTitleText:"商品评论",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}}]},{root:"pages/page",pages:[{path:"index",style:{navigationBarTitleText:"加载中..",navigationStyle:"custom"}},{path:"selectArea",style:{navigationBarTitleText:"切换地址",navigationStyle:"custom"}},{path:"cart",style:{navigationBarTitleText:"购物车",navigationStyle:"custom"}},{path:"search",style:{navigationStyle:"custom"}},{path:"success",style:{navigationBarTitleText:"正在完成",enablePullDownRefresh:!0,navigationStyle:"custom"}},{path:"end_live",style:{navigationBarTextStyle:"white",navigationBarTitleText:"直播结束",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}},{path:"no_network",style:{navigationStyle:"custom",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"share",style:{navigationBarTitleText:"分享潮客"}},{path:"topUpGasCard/index",style:{navigationBarTitleText:"加油卡",navigationBarBackgroundColor:"#fff"}},{path:"topUpGasCard/add",style:{navigationBarTitleText:"添加油卡",navigationBarBackgroundColor:"#fff"}},{path:"rechargePhoneBill/index",style:{navigationBarTitleText:"充值话费",navigationBarBackgroundColor:"#fff"}},{path:"activationCode/index",style:{navigationBarTitleText:"激活码"}},{path:"activationCode/activate",style:{navigationBarTitleText:"激活码激活"}},{path:"activationCode/presenter",style:{navigationBarTitleText:"激活码赠送"}},{path:"inviteShop",style:{navigationBarTitleText:"商家邀请码"}},{path:"inviteMerchant",style:{navigationBarTitleText:"邀请成为商户"}},{path:"upgrade",style:{navigationBarTitleText:"升级合伙人"}},{path:"upgradesBR",style:{navigationBarTitleText:"升级商务代表",navigationStyle:"custom"}},{path:"myfrend",style:{navigationBarTitleText:"客户中心",navigationBarBackgroundColor:"#2c931b",navigationBarTextStyle:"white",enablePullDownRefresh:!1,onReachBottomDistance:50,disableScroll:!1}},{path:"webView",style:{navigationBarTitleText:"跳转地址"}},{path:"shopSettled",style:{navigationBarTitleText:"门店入驻"}},{path:"inputPiece",style:{navigationBarTitleText:"自助进件"}},{path:"authentication",style:{navigationBarTitleText:"认证"}},{path:"activationCode/activateFriend",style:{navigationBarTitleText:"帮好友激活"}},{path:"activationCode/industryCode",style:{navigationBarTitleText:"生态合伙人"}},{path:"lmj/index",style:{navigationBarTitleText:"利每家"}},{path:"lmj/sendbt",style:{navigationBarTitleText:"发放补贴"}},{path:"lvtu/index",style:{navigationBarTitleText:"旅途"}},{path:"lvtu/send",style:{navigationBarTitleText:"发放爱心券"}},{path:"piaowu/index",style:{navigationBarTitleText:"票务"}},{path:"piaowu/piao",style:{navigationBarTitleText:"我的入场券"}}]},{root:"pages/user",pages:[{path:"auth/auth",style:{navigationBarTextStyle:"white",navigationStyle:"custom"}},{path:"auth/bindWechat",style:{navigationBarTitleText:"绑定账号",navigationStyle:"custom",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/name",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/perfect",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/phone",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/register",style:{navigationBarTextStyle:"white",navigationBarTitleText:" ",navigationBarBackgroundColor:"#f96c00",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/retrieve",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/thirdreg",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/validcode",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/resetpwd",style:{navigationBarTitleText:" ",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"auth/resetpaypwd",style:{navigationBarTitleText:" "}},{path:"auth/resetphone",style:{navigationBarTitleText:"修改手机号码"}},{path:"bank/bank",style:{navigationBarTitleText:"银行卡","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"bank/add",style:{navigationBarTitleText:"添加银行卡","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"bank/details",style:{navigationBarTitleText:"查看账户","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"coupon/list",style:{navigationBarTitleText:"绿色消费券"}},{path:"coupon/mycard",style:{navigationBarTitleText:"我的卡券"}},{path:"coupon/details",style:{navigationBarTitleText:"卡券详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"coupon/apply",style:{navigationBarTitleText:"使用优惠券",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"help",style:{navigationBarTitleText:"客服中心",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"helpservice",style:{navigationBarTitleText:"帮助服务",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"service",style:{navigationBarTitleText:"智能小蜜"}},{path:"theme",style:{navigationBarTitleText:"主题中心",navigationStyle:"custom"}},{path:"footprint",style:{navigationBarTitleText:"足迹",navigationStyle:"custom",disableScroll:!0}},{path:"follow",style:{navigationBarTitleText:"关注店铺",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"collect",style:{navigationBarTitleText:"收藏夹",navigationStyle:"custom",disableScroll:!0}},{path:"signin/signin",style:{navigationBarTitleText:"积分签到","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"signin/log",style:{enablePullDownRefresh:!0,navigationBarTitleText:"积分明细","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"signin/rank",style:{navigationBarTitleText:"签到排行榜","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/notice",style:{navigationBarTitleText:"新消息通知","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/privacy",style:{navigationBarTitleText:"隐私","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/security",style:{navigationBarTitleText:"账户与安全","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/currency",style:{navigationBarTitleText:"通用","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/securityCenter",style:{navigationBarTitleText:"安全中心","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/about",style:{navigationBarTitleText:"",navigationBarBackgroundColor:"#ffffff",backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#ffffff"}}}},{path:"setting/setting",style:{navigationBarTitleText:"设置","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"setting/user",style:{navigationBarTitleText:"账户设置","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"feedback/feedback",style:{navigationBarTitleText:"意见反馈","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"feedback/lists",style:{navigationBarTitleText:"我的反馈","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"complaint/complaint",style:{navigationBarTitleText:"投诉举报","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"complaint/lists",style:{navigationBarTitleText:"我的举报","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/order",style:{navigationBarTitleText:"我的订单"}},{path:"order/addorder",style:{navigationBarTitleText:"确认订单","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/details",style:{navigationBarTitleText:"订单详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"money/money",style:{navigationBarTitleText:"钱包",navigationBarBackgroundColor:"#fe6600",navigationBarTextStyle:"white"}},{path:"money/currencytransfer",style:{navigationBarTitleText:"转赠"}},{path:"money/account",style:{navigationBarTitleText:"账户总览"}},{path:"money/list",style:{navigationBarTitleText:"账单明细","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"money/pay",style:{navigationBarTitleText:"收银台","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#ffffff"}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"money/recharge",style:{navigationBarTitleText:"充值","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#ffffff"}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"money/exchange",style:{navigationBarTitleText:"兑换","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#ffffff"}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"money/withdraw",style:{navigationBarTitleText:"提现","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#ffffff"}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"money/witlist",style:{navigationBarTitleText:"提现日志","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"money/quota",style:{navigationBarTitleText:"剩余配额","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{}},backgroundColorTop:"#ffffff",backgroundColorBottom:"#ffffff"}},{path:"address/address",style:{navigationBarTitleText:"收货地址",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{}}}},{path:"address/addressManage",style:{navigationBarTitleText:"收货管理","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light",buttons:[{text:"保存",fontSize:"15px",color:"#f40",float:"right",width:"35px",fontWeight:"bold"}]}},h5:{titleNView:{backgroundColor:"#f7f7f7",buttons:[{text:"保存",fontSize:"15px",color:"#f40",float:"right",width:"35px",fontWeight:"bold"}]}},"mp-weixin":{titleNView:{backgroundColor:"#f7f7f7",buttons:[{text:"保存",fontSize:"15px",color:"#f40",float:"right",width:"35px",fontWeight:"bold"}]}},"mp-qq":{titleNView:{backgroundColor:"#f7f7f7",buttons:[{text:"保存",fontSize:"15px",color:"#f40",float:"right",width:"35px",fontWeight:"bold"}]}}}},{path:"distribution/distribution",style:{}},{path:"switchSass",style:{navigationBarTitleText:"账户切换"}},{path:"bindWeixinList",style:{navigationBarTitleText:"微信授权管理"}},{path:"portrait/portrait",style:{navigationBarTitleText:"头像","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/logistics",style:{navigationBarTitleText:"查看物流","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/comment",style:{navigationBarTitleText:"商品评论","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/search",style:{navigationBarTitleText:"搜索订单","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/offlinedetails",style:{navigationBarTitleText:"订单详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"comment/comment",style:{navigationBarTitleText:"我的评论",enablePullDownRefresh:!0,onReachBottomDistance:150,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"refund/lists",style:{navigationBarTitleText:"售后/退款",enablePullDownRefresh:!0,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"refund/apply",style:{navigationBarTitleText:"申请售后","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"refund/details",style:{navigationBarTitleText:"退款详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"refund/log",style:{navigationBarTitleText:"退款历史","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"refund/edit",style:{navigationBarTitleText:"修改退款","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"money/details",style:{enablePullDownRefresh:!0,navigationBarTitleText:"余额详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"question/question",style:{enablePullDownRefresh:!0,navigationBarTitleText:"问题详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}}]},{root:"pages/shop",pages:[{path:"index",style:{navigationBarTitleText:"店铺首页",navigationStyle:"custom",disableScroll:!0}},{path:"info",style:{navigationBarTitleText:"店铺详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"product/list",style:{enablePullDownRefresh:!0,navigationStyle:"custom"}},{path:"live/live",style:{navigationBarTextStyle:"white",navigationBarTitleText:"直播",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}}]},{root:"pages/apps/find",pages:[{path:"list",style:{navigationBarTitleText:"发现列表",navigationStyle:"custom"}},{path:"user",style:{navigationBarTitleText:"用户中心",navigationStyle:"custom"}},{path:"user/add",style:{navigationBarTitleText:"发布","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"user/follow",style:{navigationBarTitleText:"关注列表",enablePullDownRefresh:!0,"app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"user/fans",style:{navigationBarTitleText:"粉丝列表","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"details/details",style:{navigationBarTitleText:"发现详情",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}},{path:"details/live",style:{navigationBarTextStyle:"white",navigationBarTitleText:"直播间",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}},{path:"details/video",style:{navigationBarTextStyle:"white",navigationBarTitleText:"发现视频",navigationStyle:"custom","app-plus":{scrollIndicator:"none"}}}]},{root:"pages/apps/groups",pages:[{path:"index",style:{navigationBarTitleText:"拼团活动",navigationStyle:"custom"}},{path:"goods",style:{navigationStyle:"custom"}},{path:"order/order",style:{navigationBarTitleText:"拼团订单"}},{path:"order/confirm",style:{navigationBarTitleText:"确认订单","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/details",style:{navigationBarTitleText:"订单详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/comment",style:{navigationBarTitleText:"商品评论","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"order/logistics",style:{navigationBarTitleText:"查看物流","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}},{path:"team",style:{navigationBarTitleText:"拼团详情","app-plus":{titleNView:{type:"float",backgroundColor:"",blurEffect:"light"}},h5:{titleNView:{backgroundColor:"#f7f7f7"}}}}]}],preloadRule:{},globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"共店",navigationBarBackgroundColor:"#F7F7F7",backgroundColor:"#F7F7F7",backgroundColorTop:"#F7F7F7",backgroundColorBottom:"#F7F7F7","app-plus":{scrollIndicator:"none",animationDuration:200}}}},3471:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=(0,r.default)(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var a=0,i=function(){};return{s:i,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){u=!0,o=e},f:function(){try{s||null==n["return"]||n["return"]()}finally{if(u)throw o}}}},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("5d6b"))},4085:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1");r({global:!0,forced:a.globalThis!==a},{globalThis:a})},4986:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__8E03CE1"}},"57e7":function(e,t,n){"use strict";var r=n("e37c"),a=n("e4ca"),i=n("a74c"),o=n("ae5c"),s=n("b720"),u=n("1eb8"),c=n("50757"),l=n("0cc2"),f=n("97ed"),d=n("437f"),h=n("ab4a"),p=n("d0b1").fastKey,g=n("235c"),v=g.set,y=g.getterFor;e.exports={getConstructor:function(e,t,n,l){var f=e((function(e,a){s(e,d),v(e,{type:t,index:r(null),first:void 0,last:void 0,size:0}),h||(e.size=0),u(a)||c(a,e[l],{that:e,AS_ENTRIES:n})})),d=f.prototype,g=y(t),m=function(e,t,n){var r,a,i=g(e),o=k(e,t);return o?o.value=n:(i.last=o={index:a=p(t,!0),key:t,value:n,previous:r=i.last,next:void 0,removed:!1},i.first||(i.first=o),r&&(r.next=o),h?i.size++:e.size++,"F"!==a&&(i.index[a]=o)),e},k=function(e,t){var n,r=g(e),a=p(t);if("F"!==a)return r.index[a];for(n=r.first;n;n=n.next)if(n.key===t)return n};return i(d,{clear:function(){var e=g(this),t=e.first;while(t)t.removed=!0,t.previous&&(t.previous=t.previous.next=void 0),t=t.next;e.first=e.last=void 0,e.index=r(null),h?e.size=0:this.size=0},delete:function(e){var t=g(this),n=k(this,e);if(n){var r=n.next,a=n.previous;delete t.index[n.index],n.removed=!0,a&&(a.next=r),r&&(r.previous=a),t.first===n&&(t.first=r),t.last===n&&(t.last=a),h?t.size--:this.size--}return!!n},forEach:function(e){var t,n=g(this),r=o(e,arguments.length>1?arguments[1]:void 0);while(t=t?t.next:n.first){r(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!k(this,e)}}),i(d,n?{get:function(e){var t=k(this,e);return t&&t.value},set:function(e,t){return m(this,0===e?0:e,t)}}:{add:function(e){return m(this,e=0===e?0:e,e)}}),h&&a(d,"size",{configurable:!0,get:function(){return g(this).size}}),f},setStrong:function(e,t,n){var r=t+" Iterator",a=y(t),i=y(r);l(e,t,(function(e,t){v(this,{type:r,target:e,state:a(e),kind:t,last:void 0})}),(function(){var e=i(this),t=e.kind,n=e.last;while(n&&n.removed)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?f("keys"===t?n.key:"values"===t?n.value:[n.key,n.value],!1):(e.target=void 0,f(void 0,!0))}),n?"entries":"values",!n,!0),d(t)}}},6730:function(e,t,n){"use strict";var r=n("8bdb"),a=n("71e9");r({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return a(URL.prototype.toString,this)}})},"67f2":function(e,t,n){"use strict";n.r(t);var r=n("6efa"),a=n("a613");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("d025");var o=n("828b"),s=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"12f438c0",null,!1,r["a"],void 0);t["default"]=s.exports},"68ef":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=s(n("f1f8")),a=s(n("e668")),i=s(n("d441")),o=s(n("d2c4"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e){var n="function"===typeof Map?new Map:void 0;return t.default=u=function(e){if(null===e||!(0,i.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return(0,o.default)(e,arguments,(0,r.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,a.default)(t,e)},u(e)}},"6efa":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-load-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[!e.webviewHide&&("circle"===e.iconType||"auto"===e.iconType&&"android"===e.platform)&&"loading"===e.status&&e.showIcon?n("svg",{staticClass:"uni-load-more__img uni-load-more__img--android-H5",style:{width:e.iconSize+"px",height:e.iconSize+"px"},attrs:{width:"24",height:"24",viewBox:"25 25 50 50"}},[n("circle",{style:{color:e.color},attrs:{cx:"50",cy:"50",r:"20",fill:"none","stroke-width":3}})]):!e.webviewHide&&"loading"===e.status&&e.showIcon?n("v-uni-view",{staticClass:"uni-load-more__img uni-load-more__img--ios-H5",style:{width:e.iconSize+"px",height:e.iconSize+"px"}},[n("v-uni-image",{attrs:{src:"data:image/png;base64,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",mode:"widthFix"}})],1):e._e(),n("v-uni-text",{staticClass:"uni-load-more__text",style:{color:e.color}},[e._v(e._s("more"===e.status?e.contentText.contentdown:"loading"===e.status?e.contentText.contentrefresh:e.contentText.contentnomore))])],1)},a=[]},"85f0":function(e,t,n){var r=n("c86c");t=r(!1),t.push([e.i,".uni-load-more[data-v-12f438c0]{\ndisplay:flex;\nflex-direction:row;height:%?70?%;align-items:center;justify-content:center}.uni-load-more__text[data-v-12f438c0]{font-size:%?26?%}.uni-load-more__img[data-v-12f438c0]{width:24px;height:24px;margin-right:8px}.uni-load-more__img--nvue[data-v-12f438c0]{color:#666}.uni-load-more__img--android[data-v-12f438c0],\n.uni-load-more__img--ios[data-v-12f438c0]{width:24px;height:24px;-webkit-transform:rotate(0deg);transform:rotate(0deg)}\n.uni-load-more__img--android[data-v-12f438c0]{-webkit-animation:loading-ios 1s 0s linear infinite;animation:loading-ios 1s 0s linear infinite}@-webkit-keyframes loading-android-data-v-12f438c0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.uni-load-more__img--ios-H5[data-v-12f438c0]{position:relative;-webkit-animation:loading-ios-H5-data-v-12f438c0 1s 0s step-end infinite;animation:loading-ios-H5-data-v-12f438c0 1s 0s step-end infinite}.uni-load-more__img--ios-H5>uni-image[data-v-12f438c0]{position:absolute;width:100%;height:100%;left:0;top:0}@-webkit-keyframes loading-ios-H5-data-v-12f438c0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}8%{-webkit-transform:rotate(30deg);transform:rotate(30deg)}16%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}24%{-webkit-transform:rotate(90deg);transform:rotate(90deg)}32%{-webkit-transform:rotate(120deg);transform:rotate(120deg)}40%{-webkit-transform:rotate(150deg);transform:rotate(150deg)}48%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}56%{-webkit-transform:rotate(210deg);transform:rotate(210deg)}64%{-webkit-transform:rotate(240deg);transform:rotate(240deg)}73%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}82%{-webkit-transform:rotate(300deg);transform:rotate(300deg)}91%{-webkit-transform:rotate(330deg);transform:rotate(330deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-ios-H5-data-v-12f438c0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}8%{-webkit-transform:rotate(30deg);transform:rotate(30deg)}16%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}24%{-webkit-transform:rotate(90deg);transform:rotate(90deg)}32%{-webkit-transform:rotate(120deg);transform:rotate(120deg)}40%{-webkit-transform:rotate(150deg);transform:rotate(150deg)}48%{-webkit-transform:rotate(180deg);transform:rotate(180deg)}56%{-webkit-transform:rotate(210deg);transform:rotate(210deg)}64%{-webkit-transform:rotate(240deg);transform:rotate(240deg)}73%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}82%{-webkit-transform:rotate(300deg);transform:rotate(300deg)}91%{-webkit-transform:rotate(330deg);transform:rotate(330deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}\n.uni-load-more__img--android-H5[data-v-12f438c0]{-webkit-animation:loading-android-H5-rotate-data-v-12f438c0 2s linear infinite;animation:loading-android-H5-rotate-data-v-12f438c0 2s linear infinite;-webkit-transform-origin:center center;transform-origin:center center}.uni-load-more__img--android-H5>circle[data-v-12f438c0]{display:inline-block;-webkit-animation:loading-android-H5-dash-data-v-12f438c0 1.5s ease-in-out infinite;animation:loading-android-H5-dash-data-v-12f438c0 1.5s ease-in-out infinite;stroke:currentColor;stroke-linecap:round}@-webkit-keyframes loading-android-H5-rotate-data-v-12f438c0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes loading-android-H5-rotate-data-v-12f438c0{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes loading-android-H5-dash-data-v-12f438c0{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-40}100%{stroke-dasharray:90,150;stroke-dashoffset:-120}}@keyframes loading-android-H5-dash-data-v-12f438c0{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:90,150;stroke-dashoffset:-40}100%{stroke-dasharray:90,150;stroke-dashoffset:-120}}\n\n\n\n\n\n",""]),e.exports=t},"861b":function(e,t,n){"use strict";(function(e,r){var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var i=a(n("f478")),o=a(n("5de6")),s=a(n("fcf3")),u=a(n("b7c7")),c=a(n("3471")),l=a(n("2634")),f=a(n("2fdc")),d=a(n("9b1b")),h=a(n("acb1")),p=a(n("cad9")),g=a(n("68ef")),v=a(n("80b1")),y=a(n("efe5"));n("4085"),n("7a76"),n("c9b5"),n("bf0f"),n("ab80"),n("f7a5"),n("aa9c"),n("e966"),n("c223"),n("dd2b"),n("5ef2"),n("2797"),n("dc8a"),n("473f"),n("4626"),n("5ac7"),n("4100"),n("5c47"),n("d4b5"),n("0c26"),n("0506"),n("fd3c"),n("6a54"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("af8f"),n("64aa"),n("8f71"),n("23f4"),n("7d2f"),n("9c4e"),n("4db2"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("01a2"),n("e39c"),n("e062"),n("aa77"),n("2c10"),n("f555"),n("dc69"),n("9370"),n("6730"),n("08eb"),n("15d1"),n("d5c6"),n("5a56"),n("f074"),n("20f3");var m=a(n("2d47"));function k(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var w=k((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},a=r.lib={},i=a.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=a.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,a=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<a;i++){var o=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=o<<24-(r+i)%4*8}else for(i=0;i<a;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=a,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],a=function(t){var n=987654321,r=4294967295;return function(){var a=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return a/=4294967296,(a+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=a(4294967296*(n||e.random()));n=987654071*s(),r.push(4294967296*s()|0)}return new o.init(r,t)}}),s=r.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new o.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new o.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},f=a.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,a=n.sigBytes,i=this.blockSize,s=a/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*u,a);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(r,l);var f=r.splice(0,u);n.sigBytes-=c}return new o.init(f,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=r.algo={};return r}(Math),n)})),b=w,T=(k((function(e,t){var n;e.exports=(n=b,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=o.MD5=i.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,a=e[r];e[r]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}var i=this._hash.words,o=e[t+0],u=e[t+1],h=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],y=e[t+6],m=e[t+7],k=e[t+8],w=e[t+9],b=e[t+10],T=e[t+11],x=e[t+12],_=e[t+13],S=e[t+14],C=e[t+15],I=i[0],A=i[1],E=i[2],P=i[3];I=c(I,A,E,P,o,7,s[0]),P=c(P,I,A,E,u,12,s[1]),E=c(E,P,I,A,h,17,s[2]),A=c(A,E,P,I,p,22,s[3]),I=c(I,A,E,P,g,7,s[4]),P=c(P,I,A,E,v,12,s[5]),E=c(E,P,I,A,y,17,s[6]),A=c(A,E,P,I,m,22,s[7]),I=c(I,A,E,P,k,7,s[8]),P=c(P,I,A,E,w,12,s[9]),E=c(E,P,I,A,b,17,s[10]),A=c(A,E,P,I,T,22,s[11]),I=c(I,A,E,P,x,7,s[12]),P=c(P,I,A,E,_,12,s[13]),E=c(E,P,I,A,S,17,s[14]),I=l(I,A=c(A,E,P,I,C,22,s[15]),E,P,u,5,s[16]),P=l(P,I,A,E,y,9,s[17]),E=l(E,P,I,A,T,14,s[18]),A=l(A,E,P,I,o,20,s[19]),I=l(I,A,E,P,v,5,s[20]),P=l(P,I,A,E,b,9,s[21]),E=l(E,P,I,A,C,14,s[22]),A=l(A,E,P,I,g,20,s[23]),I=l(I,A,E,P,w,5,s[24]),P=l(P,I,A,E,S,9,s[25]),E=l(E,P,I,A,p,14,s[26]),A=l(A,E,P,I,k,20,s[27]),I=l(I,A,E,P,_,5,s[28]),P=l(P,I,A,E,h,9,s[29]),E=l(E,P,I,A,m,14,s[30]),I=f(I,A=l(A,E,P,I,x,20,s[31]),E,P,v,4,s[32]),P=f(P,I,A,E,k,11,s[33]),E=f(E,P,I,A,T,16,s[34]),A=f(A,E,P,I,S,23,s[35]),I=f(I,A,E,P,u,4,s[36]),P=f(P,I,A,E,g,11,s[37]),E=f(E,P,I,A,m,16,s[38]),A=f(A,E,P,I,b,23,s[39]),I=f(I,A,E,P,_,4,s[40]),P=f(P,I,A,E,o,11,s[41]),E=f(E,P,I,A,p,16,s[42]),A=f(A,E,P,I,y,23,s[43]),I=f(I,A,E,P,w,4,s[44]),P=f(P,I,A,E,x,11,s[45]),E=f(E,P,I,A,C,16,s[46]),I=d(I,A=f(A,E,P,I,h,23,s[47]),E,P,o,6,s[48]),P=d(P,I,A,E,m,10,s[49]),E=d(E,P,I,A,S,15,s[50]),A=d(A,E,P,I,v,21,s[51]),I=d(I,A,E,P,x,6,s[52]),P=d(P,I,A,E,p,10,s[53]),E=d(E,P,I,A,b,15,s[54]),A=d(A,E,P,I,u,21,s[55]),I=d(I,A,E,P,k,6,s[56]),P=d(P,I,A,E,C,10,s[57]),E=d(E,P,I,A,y,15,s[58]),A=d(A,E,P,I,_,21,s[59]),I=d(I,A,E,P,g,6,s[60]),P=d(P,I,A,E,T,10,s[61]),E=d(E,P,I,A,h,15,s[62]),A=d(A,E,P,I,w,21,s[63]),i[0]=i[0]+I|0,i[1]=i[1]+A|0,i[2]=i[2]+E|0,i[3]=i[3]+P|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;n[a>>>5]|=128<<24-a%32;var i=e.floor(r/4294967296),o=r;n[15+(a+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(a+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,a,i,o){var s=e+(t&n|~t&r)+a+o;return(s<<i|s>>>32-i)+t}function l(e,t,n,r,a,i,o){var s=e+(t&r|n&~r)+a+o;return(s<<i|s>>>32-i)+t}function f(e,t,n,r,a,i,o){var s=e+(t^n^r)+a+o;return(s<<i|s>>>32-i)+t}function d(e,t,n,r,a,i,o){var s=e+(n^(t|~r))+a+o;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),k((function(e,t){var n;e.exports=(n=b,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,a=4*n;t.sigBytes>a&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,u=o.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),k((function(e,t){e.exports=b.HmacMD5}))),x=k((function(e,t){e.exports=b.enc.Utf8})),_=k((function(e,t){var n;e.exports=(n=b,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var a=[],i=0,o=0;o<n;o++)if(o%4){var s=r[e.charCodeAt(o-1)]<<o%4*2,u=r[e.charCodeAt(o)]>>>6-o%4*2;a[i>>>2]|=(s|u)<<24-i%4*8,i++}return t.create(a,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var a=[],i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)a.push(r.charAt(o>>>6*(3-s)&63));var u=r.charAt(64);if(u)for(;a.length%4;)a.push(u);return a.join("")},parse:function(e){var t=e.length,n=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<n.length;i++)a[n.charCodeAt(i)]=i}var o=n.charAt(64);if(o){var s=e.indexOf(o);-1!==s&&(t=s)}return r(e,t,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),S="uni_id_token",C="uni_id_token_expired",I={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},A="pending",E="fulfilled",P="rejected";function N(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function O(e){return"object"===N(e)}function B(e){return"function"==typeof e}function R(e){return function(){try{return e.apply(e,arguments)}catch(e){r.error(e)}}}var L="REJECTED",D="NOT_PENDING",U=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,a=void 0===r?L:r;(0,v.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=a}return(0,y.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case L:return this.status===P;case D:return this.status!==A}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=A,this.promise=this.createPromise().then((function(t){return e.status=E,Promise.resolve(t)}),(function(t){return e.status=P,Promise.reject(t)})),this.promise):this.promise}}]),e}(),M=function(){function e(){(0,v.default)(this,e),this._callback={}}return(0,y.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,r)}}]),e}();function F(e){return e&&"string"==typeof e?JSON.parse(e):e}var V=F([]),j="web",q=(F(void 0),F([])||[]);try{(n("4986").default||n("4986")).appid}catch(kr){}var J,H={};function K(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=H,n=e,Object.prototype.hasOwnProperty.call(t,n)||(H[e]=r),H[e]}"app"===j&&(H=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var W=["invoke","success","fail","complete"],z=K("_globalUniCloudInterceptor");function G(e,t){z[e]||(z[e]={}),O(t)&&Object.keys(t).forEach((function(n){W.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];r||(r=z[e][t]=[]),-1===r.indexOf(n)&&B(n)&&r.push(n)}(e,n,t[n])}))}function Y(e,t){z[e]||(z[e]={}),O(t)?Object.keys(t).forEach((function(n){W.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];if(r){var a=r.indexOf(n);a>-1&&r.splice(a,1)}}(e,n,t[n])})):delete z[e]}function Q(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function Z(e,t){return z[e]&&z[e][t]||[]}function X(e){G("callObject",e)}var $=K("_globalUniCloudListener"),ee={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},te={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ne(e){return $[e]||($[e]=[]),$[e]}function re(e,t){var n=ne(e);n.includes(t)||n.push(t)}function ae(e,t){var n=ne(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function ie(e,t){for(var n=ne(e),r=0;r<n.length;r++)(0,n[r])(t)}var oe,se=!1;function ue(){return oe||(oe=new Promise((function(e){se&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(se=!0,e())}se||setTimeout((function(){t()}),30)}()})),oe)}function ce(e){var t={};for(var n in e){var r=e[n];B(r)&&(t[n]=R(r))}return t}var le=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(e){var r;return(0,v.default)(this,n),r=t.call(this,e.message),r.errMsg=e.message||e.errMsg||"unknown system error",r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,y.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,g.default)(Error));t.UniCloudError=le;var fe,de,he={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function pe(){return{token:he.getStorageSync(S)||he.getStorageSync("uniIdToken"),tokenExpired:he.getStorageSync(C)}}function ge(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&he.setStorageSync(S,t),n&&he.setStorageSync(C,n)}function ve(){return fe||(fe=uni.getSystemInfoSync()),fe}var ye={};function me(){var e=uni.getLocale&&uni.getLocale()||"en";if(de)return(0,d.default)((0,d.default)((0,d.default)({},ye),de),{},{locale:e,LOCALE:e});var t=ve(),n=t.deviceId,r=t.osName,a=t.uniPlatform,i=t.appId,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var s in t)Object.hasOwnProperty.call(t,s)&&-1===o.indexOf(s)&&delete t[s];return de=(0,d.default)((0,d.default)({PLATFORM:a,OS:r,APPID:i,DEVICEID:n},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=uni.getLaunchOptionsSync(),r=n.scene,a=n.channel;e=a,t=r}}catch(e){}return{channel:e,scene:t}}()),t),(0,d.default)((0,d.default)((0,d.default)({},ye),de),{},{locale:e,LOCALE:e})}var ke,we={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),T(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var a=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new le({code:a,message:i,requestId:t}))}var o=e.data;if(o.error)return r(new le({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},toBase64:function(e){return _.stringify(x.parse(e))}},be=function(){function e(t){var n=this;(0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=he,this._getAccessTokenPromiseHub=new U({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new le({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:D})}return(0,y.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return we.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=we.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=we.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,d.default)((0,d.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,a=e.name,i=e.filePath,o=e.fileType,s=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:r,name:a,filePath:i,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,f,d,h,p,g,v,y,m,k,w,b,T,x,_;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=t.fileType,i=void 0===a?"image":a,o=t.cloudPathAsRealPath,s=void 0!==o&&o,u=t.onUploadProgress,c=t.config,"string"===N(r)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(f=c&&c.envType||this.config.envType,!(s&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new le({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:f,filename:s?r.split("/").pop():r,fileId:s?r:void 0});case 12:return d=e.sent.result,h="https://"+d.cdnDomain+"/"+d.ossPath,p=d.securityToken,g=d.accessKeyId,v=d.signature,y=d.host,m=d.ossPath,k=d.id,w=d.policy,b=d.ossCallbackUrl,T={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:v,host:y,id:k,key:m,policy:w,success_action_status:200},p&&(T["x-oss-security-token"]=p),b&&(x=JSON.stringify({callbackUrl:b,callbackBody:JSON.stringify({fileId:k,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),T.callback=we.toBase64(x)),_={url:"https://"+d.host,formData:T,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},_,{onUploadProgress:u}));case 27:if(!b){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 29:return e.next=31,this.reportOSSUpload({id:k});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 33:throw new le({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList;return new Promise((function(e,n){Array.isArray(t)&&0!==t.length||n(new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e({fileList:t.map((function(e){return{fileID:e,tempFileURL:e}}))})}))}},{key:"getFileInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),Te={init:function(e){var t=new be(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},xe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(ke||(ke={}));var _e,Se=function(){},Ce=k((function(e,t){var n;e.exports=(n=b,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,o=t.algo,s=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,a=0;a<64;)t(r)&&(a<8&&(s[a]=n(e.pow(r,.5))),u[a]=n(e.pow(r,1/3)),a++),r++}();var c=[],l=o.SHA256=i.extend({_doReset:function(){this._hash=new a.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],a=n[1],i=n[2],o=n[3],s=n[4],l=n[5],f=n[6],d=n[7],h=0;h<64;h++){if(h<16)c[h]=0|e[t+h];else{var p=c[h-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=c[h-2],y=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[h]=g+c[h-7]+y+c[h-16]}var m=r&a^r&i^a&i,k=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),w=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&f)+u[h]+c[h];d=f,f=l,l=s,s=o+w|0,o=i,i=a,a=r,r=w+(k+m)|0}n[0]=n[0]+r|0,n[1]=n[1]+a|0,n[2]=n[2]+i|0,n[3]=n[3]+o|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;return n[a>>>5]|=128<<24-a%32,n[14+(a+64>>>9<<4)]=e.floor(r/4294967296),n[15+(a+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Ie=Ce,Ae=k((function(e,t){e.exports=b.HmacSHA256})),Ee=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new le({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Pe(e){return void 0===e}function Ne(e){return"[object Null]"===Object.prototype.toString.call(e)}function Oe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Be(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(_e||(_e={}));var Re={adapter:null,runtime:void 0},Le=["anonymousUuidKey"],De=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),Re.adapter.root.tcbObject||(Re.adapter.root.tcbObject={}),e}return(0,y.default)(n,[{key:"setItem",value:function(e,t){Re.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Re.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Re.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Re.adapter.root.tcbObject}}]),n}(Se);function Ue(e,t){switch(e){case"local":return t.localStorage||new De;case"none":return new De;default:return t.sessionStorage||new De}}var Me=function(){function e(t){if((0,v.default)(this,e),!this._storage){this._persistence=Re.adapter.primaryStorage||t.persistence,this._storage=Ue(this._persistence,Re.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),a="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),o="login_type_".concat(t.env),s="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:a,anonymousUuidKey:i,loginTypeKey:o,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,y.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=Ue(e,Re.adapter);for(var r in this.keys){var a=this.keys[r];if(!t||!Le.includes(r)){var i=this._storage.getItem(a);Pe(i)||Ne(i)||(n.setItem(a,i),this._storage.removeItem(a))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},a=JSON.stringify(r);try{this._storage.setItem(e,a)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),Fe={},Ve={};function je(e){return Fe[e]}var qe=(0,y.default)((function e(t,n){(0,v.default)(this,e),this.data=n||null,this.name=t})),Je=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(e,r){var a;return(0,v.default)(this,n),a=t.call(this,"error",{error:e,data:r}),a.error=e,a}return(0,y.default)(n)}(qe),He=new(function(){function e(){(0,v.default)(this,e),this._listeners={}}return(0,y.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof Je)return r.error(e.error),this;var n="string"==typeof e?new qe(e,t||{}):e,a=n.name;if(this._listens(a)){n.target=this;var i,o=this._listeners[a]?(0,u.default)(this._listeners[a]):[],s=(0,c.default)(o);try{for(s.s();!(i=s.n()).done;){var l=i.value;l.call(this,n)}}catch(f){s.e(f)}finally{s.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function Ke(e,t){He.on(e,t)}function We(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};He.fire(e,t)}function ze(e,t){He.off(e,t)}var Ge,Ye="loginStateChanged",Qe="loginStateExpire",Ze="loginTypeChanged",Xe="anonymousConverted",$e="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Ge||(Ge={}));var et=function(){function e(){(0,v.default)(this,e),this._fnPromiseMap=new Map}return(0,y.default)(e,[{key:"run",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,f.default)((0,l.default)().mark((function e(r,i){var o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a._runIdlePromise();case 3:return o=n(),e.t0=r,e.next=7,o;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,a._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),tt=function(){function e(t){(0,v.default)(this,e),this._singlePromise=new et,this._cache=je(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Re.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,y.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Be(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=u.length>2&&void 0!==u[2]?u[2]:{},a={"x-request-id":Be(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:o=e.sent,s=this._cache.getStore(i),a.authorization="".concat(s," ").concat(o);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:a}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u,c,d,h=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=t.tokenTypeKey,o=this._cache.getStore(n),!o||o===Ge.ANONYMOUS){e.next=3;break}throw new le({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return s=e.sent,u=s.access_token,c=s.expires_in,d=s.token_type,e.abrupt("return",(this._cache.setStore(i,d),this._cache.setStore(r,u),this._cache.setStore(a,Date.now()+1e3*c),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=this._cache.getStore(n),i=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(a,i)?this._fetchAccessToken():a);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,Ge.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),nt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],rt={"X-SDK-Version":"1.3.5"};function at(e,t,n){var r=e[t];e[t]=function(t){var a={},i={};n.forEach((function(n){var r=n.call(e,t),o=r.data,s=r.headers;Object.assign(a,o),Object.assign(i,s)}));var o=t.data;return o&&function(){var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,d.default)((0,d.default)({},o),a);else for(var n in a)o.append(n,a[n])}(),t.headers=(0,d.default)((0,d.default)({},t.headers||{}),i),r.call(e,t)}}function it(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,d.default)((0,d.default)({},rt),{},{"x-seqid":e})}}var ot=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,v.default)(this,e),this.config=n,this._reqClass=new Re.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=je(this.config.env),this._localCache=(t=this.config.env,Ve[t]),this.oauth=new tt(this.config),at(this._reqClass,"post",[it]),at(this._reqClass,"upload",[it]),at(this._reqClass,"download",[it])}return(0,y.default)(e,[{key:"post",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u,c,f,d,h,p;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,i=t.loginTypeKey,o=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),s=this._cache.getStore(a),s){e.next=5;break}throw new le({message:"未登录CloudBase"});case 5:return u={refresh_token:s},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(c=e.sent,!c.data.code){e.next=21;break}if(f=c.data.code,"SIGN_PARAM_INVALID"!==f&&"REFRESH_TOKEN_EXPIRED"!==f&&"INVALID_REFRESH_TOKEN"!==f){e.next=20;break}if(this._cache.getStore(i)!==Ge.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==f){e.next=19;break}return d=this._cache.getStore(o),h=this._cache.getStore(a),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:d,refresh_token:h});case 17:return p=e.sent,e.abrupt("return",(this.setRefreshToken(p.refresh_token),this._refreshAccessToken()));case 19:We(Qe),this._cache.removeStore(a);case 20:throw new le({code:c.data.code,message:"刷新access token失败：".concat(c.data.code)});case 21:if(!c.data.access_token){e.next=23;break}return e.abrupt("return",(We($e),this._cache.setStore(n,c.data.access_token),this._cache.setStore(r,c.data.access_token_expire+Date.now()),{accessToken:c.data.access_token,accessTokenExpire:c.data.access_token_expire}));case 23:c.data.refresh_token&&(this._cache.removeStore(a),this._cache.setStore(a,c.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,this._cache.getStore(a)){e.next=3;break}throw new le({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),o=this._cache.getStore(r),s=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,o);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}s=!1;case 12:return e.abrupt("return",(!i||!o||o<Date.now())&&s?this.refreshAccessToken():{accessToken:i,accessTokenExpire:o});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n,r){var a,i,o,s,u,c,f,h,p,g,v,y,m,k,w;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",o=(0,d.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===nt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:o.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in s=new FormData,s)s.hasOwnProperty(u)&&void 0!==s[u]&&s.append(u,o[u]);i="multipart/form-data",e.next=17;break;case 15:for(c in i="application/json",s={},o)void 0!==o[c]&&(s[c]=o[c]);case 17:return f={headers:{"content-type":i}},r&&r.timeout&&(f.timeout=r.timeout),r&&r.onUploadProgress&&(f.onUploadProgress=r.onUploadProgress),h=this._localCache.getStore(a),h&&(f.headers["X-TCB-Trace"]=h),p=n.parse,g=n.inQuery,v=n.search,y={env:this.config.env},p&&(y.parse=!0),g&&(y=(0,d.default)((0,d.default)({},g),y)),m=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=a)?t:"".concat(e).concat(t)}(xe,"//tcb-api.tencentcloudapi.com/web",y),v&&(m+=v),e.next=28,this.post((0,d.default)({url:m,data:s},f));case 28:if(k=e.sent,w=k.header&&k.header["x-tcb-trace"],w&&this._localCache.setStore(a,w),(200===Number(k.status)||200===Number(k.statusCode))&&k.data){e.next=32;break}throw new le({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",k);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},r=o.length>2&&void 0!==o[2]?o[2]:{},e.next=4,this.request(t,n,(0,d.default)((0,d.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(a=e.sent,"ACCESS_TOKEN_DISABLED"!==a.data.code&&"ACCESS_TOKEN_EXPIRED"!==a.data.code||-1!==nt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,(0,d.default)((0,d.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new le({code:i.data.code,message:Oe(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!a.data.code){e.next=16;break}throw new le({code:a.data.code,message:Oe(a.data.message)});case 16:return e.abrupt("return",a.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}}]),e}(),st={};function ut(e){return st[e]}var ct=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=je(t.env),this._request=ut(t.env)}return(0,y.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,a=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(a,t)}},{key:"refreshUserInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),lt=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=je(this._envId),this._request=ut(this._envId),this.setUserInfo()}return(0,y.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,a=n.users,e.abrupt("return",(a.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:a,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,a=t.avatarUrl,i=t.province,o=t.country,s=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:a,province:i,country:o,city:s});case 8:u=e.sent,c=u.data,this.setLocalUserInfo(c);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),ft=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=je(t);var n=this._cache.keys,r=n.refreshTokenKey,a=n.accessTokenKey,i=n.accessTokenExpireKey,o=this._cache.getStore(r),s=this._cache.getStore(a),u=this._cache.getStore(i);this.credential={refreshToken:o,accessToken:s,accessTokenExpire:u},this.user=new lt(t)}return(0,y.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===Ge.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===Ge.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===Ge.WECHAT||this.loginType===Ge.WECHAT_OPEN||this.loginType===Ge.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),dt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return We(Ye),We(Ze,{env:this.config.env,loginType:Ge.ANONYMOUS,persistence:"local"}),t=new ft(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,a=n.refreshTokenKey,i=this._cache.getStore(r),o=this._cache.getStore(a),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:o,ticket:t});case 7:if(s=e.sent,!s.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(s.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return We(Xe,{env:this.config.env}),We(Ze,{loginType:Ge.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:s.refresh_token}});case 16:throw new le({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,Ge.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(ct),ht=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return We(Ye),We(Ze,{env:this.config.env,loginType:Ge.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new ft(this.config.env));case 15:throw new le({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(ct),pt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(a=e.sent,i=a.refresh_token,o=a.access_token,s=a.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!o||!s){e.next=15;break}this.setAccessToken(o,s),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return We(Ye),We(Ze,{env:this.config.env,loginType:Ge.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new ft(this.config.env));case 22:throw a.code?new le({code:a.code,message:"邮箱登录失败: ".concat(a.message)}):new le({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),gt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var a,i,o,s,u;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",r.warn("password is empty")),a=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:Ge.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(a)||""});case 6:if(i=e.sent,o=i.refresh_token,s=i.access_token_expire,u=i.access_token,!o){e.next=23;break}if(this.setRefreshToken(o),!u||!s){e.next=16;break}this.setAccessToken(u,s),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return We(Ye),We(Ze,{env:this.config.env,loginType:Ge.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new ft(this.config.env));case 23:throw i.code?new le({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new le({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),vt=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=je(t.env),this._request=ut(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ke(Ze,this._onLoginTypeChanged)}return(0,y.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new dt(this.config)}},{key:"customAuthProvider",value:function(){return new ht(this.config)}},{key:"emailAuthProvider",value:function(){return new pt(this.config)}},{key:"usernameAuthProvider",value:function(){return new gt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new dt(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new pt(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new gt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new dt(this.config)),Ke(Xe,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==Ge.ANONYMOUS){e.next=2;break}throw new le({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return o=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(a),We(Ye),We(Ze,{env:this.config.env,loginType:Ge.NULL,persistence:this.config.persistence}),o));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;Ke(Ye,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){Ke(Qe,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){Ke($e,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){Ke(Xe,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;Ke(Ze,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),a=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,a)?null:new ft(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,d.default)((0,d.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,a=t.env;a===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),yt=function(e,t){t=t||Ee();var n=ut(this.config.env),r=e.cloudPath,a=e.filePath,i=e.onUploadProgress,o=e.fileType,s=void 0===o?"image":o;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var o=e.data,u=o.url,c=o.authorization,l=o.token,f=o.fileId,d=o.cosFileId,h=e.requestId,p={key:r,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:p,file:a,name:r,fileType:s,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:f,requestId:h}):t(new le({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},mt=function(e,t){t=t||Ee();var n=ut(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},kt=function(e,t){var n=e.fileList;if(t=t||Ee(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,a=(0,c.default)(n);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){a.e(s)}finally{a.f()}var o={fileid_list:n};return ut(this.config.env).send("storage.batchDeleteFile",o).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},wt=function(e,t){var n=e.fileList;t=t||Ee(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,a=[],i=(0,c.default)(n);try{for(i.s();!(r=i.n()).done;){var o=r.value;"object"==(0,s.default)(o)?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),a.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?a.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(l){i.e(l)}finally{i.f()}var u={file_list:a};return ut(this.config.env).send("storage.batchGetDownloadUrl",u).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},bt=function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,wt.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(a=e.sent.fileList[0],"SUCCESS"===a.code){e.next=6;break}return e.abrupt("return",n?n(a):new Promise((function(e){e(a)})));case 6:if(i=ut(this.config.env),o=a.download_url,o=encodeURI(o),n){e.next=10;break}return e.abrupt("return",i.download({url:o}));case 10:return e.t0=n,e.next=13,i.download({url:o});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),Tt=function(e,t){var n,r=e.name,a=e.data,i=e.query,o=e.parse,s=e.search,u=e.timeout,c=t||Ee();try{n=a?JSON.stringify(a):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new le({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:o,search:s,function_name:r,request_data:n};return ut(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(o)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new le({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},xt={timeout:15e3,persistence:"session"},_t={},St=function(){function e(t){(0,v.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,y.default)(e,[{key:"init",value:function(t){switch(Re.adapter||(this.requestClient=new Re.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,d.default)((0,d.default)({},xt),t),!0){case this.config.timeout>6e5:r.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:r.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||Re.adapter.primaryStorage||xt.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;Fe[t]=new Me(e),Ve[t]=new Me((0,d.default)((0,d.default)({},e),{},{persistence:"local"}))}(this.config),n=this.config,st[n.env]=new ot(n),this.authObj=new vt(this.config),this.authObj}},{key:"on",value:function(e,t){return Ke.apply(this,[e,t])}},{key:"off",value:function(e,t){return ze.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return Tt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return kt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return wt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return bt.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return yt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return mt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){_t[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=_t[t],r){e.next=3;break}throw new le({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),a=(0,c.default)(r);try{for(a.s();!(n=a.n()).done;){var i=n.value,o=i.isMatch,s=i.genAdapter,u=i.runtime;if(o())return{adapter:s(),runtime:u}}}catch(l){a.e(l)}finally{a.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(Re.adapter=n),r&&(Re.runtime=r)}}]),e}(),Ct=new St;function It(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=a)?t:""+e+t}var At=function(){function e(){(0,v.default)(this,e)}return(0,y.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){he.request({url:It("https:",t),data:n,method:"GET",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){he.request({url:It("https:",t),data:n,method:"POST",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,a=e.file,i=e.data,o=e.headers,s=e.fileType,u=he.uploadFile({url:It("https:",r),name:"file",formData:Object.assign({},i),filePath:a,fileType:s,header:o,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Et={setItem:function(e,t){he.setStorageSync(e,t)},getItem:function(e){return he.getStorageSync(e)},removeItem:function(e){he.removeStorageSync(e)},clear:function(){he.clearStorageSync()}},Pt={genAdapter:function(){return{root:{},reqClass:At,localStorage:Et,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Ct.useAdapters(Pt);var Nt=Ct,Ot=Nt.init;Nt.init=function(e){e.env=e.spaceId;var t=Ot.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ce(e),r=t.success,a=t.fail,i=t.complete;if(!(r||a||i))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),i&&i(e)}),(function(e){a&&a(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Bt=Nt;function Rt(e,t){return Lt.apply(this,arguments)}function Lt(){return Lt=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:r,timeout:500},new Promise((function(e,t){he.request((0,d.default)((0,d.default)({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return a=e.sent,e.abrupt("return",!(!a.data||0!==a.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Lt.apply(this,arguments)}function Dt(e,t){return Ut.apply(this,arguments)}function Ut(){return Ut=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=0;case 1:if(!(a<t.length)){e.next=11;break}return i=t[a],e.next=5,Rt(i,n);case 5:if(!e.sent){e.next=8;break}return r=i,e.abrupt("break",11);case 8:a++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),Ut.apply(this,arguments)}var Mt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},Ft=function(){function e(t){if((0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=he}return(0,y.default)(e,[{key:"request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r=this,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(a.length>1&&void 0!==a[1])||a[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):we.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",a=e.data&&e.data.message||"request:fail";return r(new le({code:t,message:a}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=we.sign(t,this.config.clientSecret);var r=me();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var a=pe(),i=a.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,f;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=me(),r=pe(),a=r.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:a}),o=this.__dev__&&this.__dev__.debugInfo||{},s=o.address,u=o.servePort,e.next=9,Dt(s,u);case 9:return c=e.sent,f=c.address,e.abrupt("return",{url:"http://".concat(f,":").concat(u,"/").concat(Mt[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,a=e.cloudPath,i=e.fileType,o=void 0===i?"image":i,s=e.onUploadProgress;if(!a)throw new le({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:a}).then((function(e){var a=e.result,i=a.url,u=a.formData,c=a.name;return t=e.result.fileUrl,new Promise((function(e,t){var a=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:r,fileType:o,success:function(n){n&&n.statusCode<400?e(n):t(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:a})})).then((function(e){return new Promise((function(n,a){e.success?n({success:!0,filePath:r,fileID:t}):a(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new le({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new le({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Vt={init:function(e){var t=new Ft(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},jt=k((function(e,t){e.exports=b.enc.Hex}));function qt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Jt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,a=t.method,i=t.headers,s=t.signHeaderKeys,u=void 0===s?[]:s,c=t.config,l=String(Date.now()),f=qt(),d=Object.assign({},i,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":f,"x-alipay-callid":f,"x-trace-id":f}),h=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(u),p=e.split("?")||[],g=(0,o.default)(p,2),v=g[0],y=void 0===v?"":v,m=g[1],k=void 0===m?"":m,w=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),a=Ie(e.body).toString(jt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(a,"\n"),o=Ie(i).toString(jt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(o,"\n"),u=Ae(s,e.secretKey).toString(jt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:y,query:k,method:a,headers:d,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:h.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},d,{Authorization:w})}}function Ht(e){var t=e.url,n=e.data,r=e.method,a=void 0===r?"POST":r,i=e.headers,o=void 0===i?{}:i,u=e.timeout;return new Promise((function(e,r){he.request({url:t,method:a,data:"object"==(0,s.default)(n)?JSON.stringify(n):n,header:o,dataType:"json",timeout:u,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=o["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var a=t.data||{},i=a.message,s=a.errMsg,u=a.trace_id;return r(new le({code:"SYS_ERR",message:i||s||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Kt(e,t){var n=e.path,r=e.data,a=e.method,i=void 0===a?"GET":a,o=Jt(n,{functionName:"",data:r,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=o.url,u=o.headers;return Ht({url:s,data:r,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Wt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new le({code:"INVALID_PARAM",message:"fileID不合法"});var a=t.substring(0,n),i=t.substring(n+1);return a!==this.config.spaceId&&r.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var Gt=function(){function e(t){(0,v.default)(this,e),this.config=t}return(0,y.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),a=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:qt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return a[e]?"".concat(e,"=").concat(a[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),o=["HMAC-SHA256",Ie(i).toString(jt)].join("\n"),s=Ae(o,this.config.secretKey).toString(jt),u=Object.keys(a).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(a[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(s)}}]),e}(),Yt=function(){function e(t){if((0,v.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new Gt(this.config)}return(0,y.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,a=e.async,i=void 0!==a&&a,o=e.timeout,s="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=Jt("/functions/invokeFunction",{functionName:n,data:r,method:s,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,f=c.headers;return Ht({url:l,data:r,method:s,headers:f,timeout:o}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new le({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,a=e.formData,i=e.onUploadProgress;return new Promise((function(e,o){var s=he.uploadFile({url:t,filePath:n,fileType:r,formData:a,name:"file",success:function(t){t&&t.statusCode<400?e(t):o(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){o(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,o,s,u,c,f,d,h;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=void 0===r?"":r,i=t.fileType,o=void 0===i?"image":i,s=t.onUploadProgress,"string"===N(a)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(a=a.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(a)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Kt({path:"/".concat(a.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,c=u.file_id,f=u.upload_url,d=u.form_data,h=d&&d.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:f,filePath:n,fileType:o,formData:h,onUploadProgress:s}).then((function(){return{fileID:c}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,o=[],s=(0,c.default)(n);try{for(s.s();!(i=s.n()).done;){var u=i.value,l=void 0;"string"!==N(u)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{l=Wt.call(a,u)}catch(e){r.warn(e.errCode,e.errMsg),l=u}o.push({file_id:l,expire:600})}}catch(f){s.e(f)}finally{s.f()}Kt({path:"/?download_url",data:{file_list:o},method:"POST"},a.config).then((function(t){var n=t.file_list,r=void 0===n?[]:n;e({fileList:r.map((function(e){return{fileID:zt.call(a,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",he.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Qt={init:function(e){e.provider="alipay";var t=new Yt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Zt(e){var t,n=e.data;t=me();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var a=pe(),i=a.token;i&&(r.uniIdToken=i)}return r}var Xt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],$t=/[\\^$.*+?()[\]{}|]/g,en=RegExp($t.source);function tn(e,t,n){return e.replace(new RegExp((r=t)&&en.test(r)?r.replace($t,"\\$&"):r,"g"),n);var r}var nn={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},rn="_globalUniCloudStatus",an="_globalUniCloudSecureNetworkCache__{spaceId}";var on;on="0123456789abcdef";var sn="uni-secure-network",un={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function cn(e){var t=e||{},n=t.errSubject,r=t.subject,a=t.errCode,i=t.errMsg,o=t.code,s=t.message,u=t.cause;return new le({subject:n||r||sn,code:a||o||un.SYSTEM_ERROR.code,message:i||s,cause:u})}var ln;function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===nn.REQUEST||t===nn.RESPONSE||t===nn.BOTH}function dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===j&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function hn(e){e.functionName,e.result,e.logPvd}function pn(e){var t=e.callFunction,n=function(n){var r=this,a=n.name;n.data=Zt.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],o=fn(n),s=dn(n),u=o||s;return t.call(this,n).then((function(e){return e.errCode=0,!u&&hn.call(r,{functionName:a,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&hn.call(r,{functionName:a,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,a=void 0===r?{}:r,i=e.formatter,o=void 0===i?[]:i,s=0;s<o.length;s++){var u=o[s],c=u.rule,l=u.content,f=u.mode,d=n.match(c);if(d){for(var h=l,p=1;p<d.length;p++)h=tn(h,"{$".concat(p,"}"),d[p]);for(var g in a)h=tn(h,"{".concat(g,"}"),a[g]);return"replace"===f?h:n+h}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:Xt,extraInfo:{functionName:a}})),Promise.reject(e)}))};e.callFunction=function(t){var a,i,o=e.config,s=o.provider,u=o.spaceId,c=t.name;return t.data=t.data||{},a=n,a=a.bind(e),i=dn(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===j&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?a.call(e,t):fn(t)?new ln({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,a=e.functionName,i=ve(),o=i.appId,s=i.uniPlatform,u=i.osName,c=s;"app"===s&&(c=u);var l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=V;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var a=r.find((function(e){return e.provider===t&&e.spaceId===n}));return a&&a.config}({provider:t,spaceId:n});if(!l||!l.accessControl||!l.accessControl.enable)return!1;var f=l.accessControl.function||{},d=Object.keys(f);if(0===d.length)return!0;var h=function(e,t){for(var n,r,a,i=0;i<e.length;i++){var o=e[i];o!==t?"*"!==o?o.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=o):a=o:n=o}return n||r||a}(d,a);if(!h)return!1;if((f[h]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===o&&(e.platform||"").toLowerCase()===c.toLowerCase()})))return!0;throw r.error("此应用[appId: ".concat(o,", platform: ").concat(c,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),cn(un.APP_INFO_INVALID)}({provider:s,spaceId:u,functionName:c})?new ln({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):a(t),Object.defineProperty(i,"result",{get:function(){return r.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return"undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e}))}}ln="mp-weixin"!==j&&"app"!==j?function(){return(0,y.default)((function e(){throw(0,v.default)(this,e),cn({message:"Platform ".concat(j," is not supported by secure network")})}))}():function(){return(0,y.default)((function e(){throw(0,v.default)(this,e),cn({message:"Platform ".concat(j," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var gn=Symbol("CLIENT_DB_INTERNAL");function vn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=gn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,s.default)(n))return e[n];if(n in e||"string"!=typeof n){var a=e[n];return"function"==typeof a?a.bind(e):a}return t.get(e,n,r)}})}function yn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var mn=["db.Geo","db.command","command.aggregate"];function kn(e,t){return mn.indexOf("".concat(e,".").concat(t))>-1}function wn(e){switch(N(e)){case"array":return e.map((function(e){return wn(e)}));case"object":return e._internalType===gn||Object.keys(e).forEach((function(t){e[t]=wn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function bn(e){return e&&e.content&&e.content.$method}var Tn=function(){function e(t,n,r){(0,v.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,y.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:wn(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=bn(e),n=bn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===bn(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=bn(e),n=bn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return xn({$method:e,$param:wn(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:wn(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function xn(e,t,n){return vn(new Tn(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),kn(r,t)?xn({$method:t},e,n):function(){return xn({$method:t,$param:wn(Array.from(arguments))},e,n)}}})}function _n(e){var t=e.path,n=e.method;return function(){function e(){(0,v.default)(this,e),this.param=Array.from(arguments)}return(0,y.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,u.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Sn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,a=t.isJQL,i=void 0!==a&&a;(0,v.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=K("_globalUniCloudDatabaseCallback")),i||(this.auth=yn(this._authCallBacks)),this._isJQL=i,Object.assign(this,yn(this._dbCallBacks)),this.env=vn({},{get:function(e,t){return{$env:t}}}),this.Geo=vn({},{get:function(e,t){return _n({path:["Geo"],method:t})}}),this.serverDate=_n({path:[],method:"serverDate"}),this.RegExp=_n({path:[],method:"RegExp"})}return(0,y.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function Cn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return vn(new e(t),{get:function(e,t){return kn("db",t)?xn({$method:t},null,e):function(){return xn({$method:t,$param:wn(Array.from(arguments))},null,e)}}})}var In=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,y.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,a=e.command,i=e.multiCommand,o=e.queryList;function s(e,t){if(i&&o)for(var n=0;n<o.length;n++){var r=o[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var u=this,c=this._isJQL?"databaseForJQL":"database";function l(e){return u._callback("error",[e]),Q(Z(c,"fail"),e).then((function(){return Q(Z(c,"complete"),e)})).then((function(){return s(null,e),ie(ee.RESPONSE,{type:te.CLIENT_DB,content:e}),Promise.reject(e)}))}var f=Q(Z(c,"invoke")),d=this._uniClient;return f.then((function(){return d.callFunction({name:"DCloud-clientDB",type:I.CLIENT_DB,data:{action:n,command:a,multiCommand:i}})})).then((function(e){var n=e.result,a=n.code,i=n.message,o=n.token,f=n.tokenExpired,d=n.systemInfo,h=void 0===d?[]:d;if(h)for(var p=0;p<h.length;p++){var g=h[p],v=g.level,y=g.message,m=g.detail,k="[System Info]"+y;m&&(k="".concat(k,"\n详细信息：").concat(m)),(r["app"===j&&"warn"===v?"error":v]||r.log)(k)}if(a)return l(new le({code:a,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&f&&(ge({token:o,tokenExpired:f}),t._callbackAuth("refreshToken",[{token:o,tokenExpired:f}]),t._callback("refreshToken",[{token:o,tokenExpired:f}]),ie(ee.REFRESH_TOKEN,{token:o,tokenExpired:f}));for(var w=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],b=function(t){var n=w[t],a=n.prop,i=n.tips;if(a in e.result){var o=e.result[a];Object.defineProperty(e.result,a,{get:function(){return r.warn(i),o}})}},T=0;T<w.length;T++)b(T);return function(e){return Q(Z(c,"success"),e).then((function(){return Q(Z(c,"complete"),e)})).then((function(){s(e,null);var t=u._parseResult(e);return ie(ee.RESPONSE,{type:te.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&r.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),l(new le({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Sn),An="token无效，跳转登录页面",En="token过期，跳转登录页面",Pn={TOKEN_INVALID_TOKEN_EXPIRED:En,TOKEN_INVALID_INVALID_CLIENTID:An,TOKEN_INVALID:An,TOKEN_INVALID_WRONG_TOKEN:An,TOKEN_INVALID_ANONYMOUS_USER:An},Nn={"uni-id-token-expired":En,"uni-id-check-token-failed":An,"uni-id-token-not-exist":An,"uni-id-check-device-feature-failed":An};function On(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Bn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(On(t,e.path)):!1===e.needLogin&&r.push(On(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function Rn(e){return e.split("?")[0].replace(/^\//,"")}function Ln(){return function(e){var t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Dn(){return Rn(Ln())}function Un(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=Rn(e);return n.some((function(e){return e.pagePath===r}))}var Mn,Fn=!!m.default.uniIdRouter,Vn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,a=void 0===r?[]:r,i=e.uniIdRouter,o=void 0===i?{}:i,s=e.tabBar,c=void 0===s?{}:s,l=o.loginPage,f=o.needLogin,d=void 0===f?[]:f,h=o.resToLogin,p=void 0===h||h,g=Bn(n),v=g.needLoginPage,y=g.notNeedLoginPage,k=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,a=e.pages,i=void 0===a?[]:a,o=Bn(i,r),s=o.needLoginPage,c=o.notNeedLoginPage;t.push.apply(t,(0,u.default)(s)),n.push.apply(n,(0,u.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(a),w=k.needLoginPage,b=k.notNeedLoginPage;return{loginPage:l,routerNeedLogin:d,resToLogin:p,needLoginPage:[].concat((0,u.default)(v),(0,u.default)(w)),notNeedLoginPage:[].concat((0,u.default)(y),(0,u.default)(b)),loginPageInTabBar:Un(l,c)}}(),jn=Vn.loginPage,qn=Vn.routerNeedLogin,Jn=Vn.resToLogin,Hn=Vn.needLoginPage,Kn=Vn.notNeedLoginPage,Wn=Vn.loginPageInTabBar;if(Hn.indexOf(jn)>-1)throw new Error("Login page [".concat(jn,'] should not be "needLogin", please check your pages.json'));function zn(e){var t=Dn();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,o.default)(n,2),a=r[0],i=r[1],s=a.replace(/^\//,"").split("/"),u=t.split("/");u.pop();for(var c=0;c<s.length;c++){var l=s[c];".."===l?u.pop():"."!==l&&u.push(l)}return""===u[0]&&u.shift(),"/"+u.join("/")+(i?"?"+i:"")}function Gn(e){var t=Rn(zn(e));return!(Kn.indexOf(t)>-1)&&(Hn.indexOf(t)>-1||qn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function Yn(e){var t=e.redirect,n=Rn(t),r=Rn(jn);return Dn()!==r&&n!==r}function Qn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&Yn({redirect:n})){var r=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(jn,n);Wn?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var a={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){a[t]({url:r})}),0)}}function Zn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=pe(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var a="uni-id-token-expired";e={errCode:a,errMsg:Nn[a]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:Nn[i]}}return e}();if(Gn(t)&&r){if(r.uniIdRedirectUrl=t,ne(ee.NEED_LOGIN).length>0)return setTimeout((function(){ie(ee.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function Xn(){!function(){var e=Ln(),t=Zn({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&Qn({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];uni.addInterceptor(n,{invoke:function(e){var t=Zn({url:e.url}),r=t.abortLoginPageJump,a=t.autoToLoginPage;return r?e:a?(Qn({api:n,redirect:zn(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function $n(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in Nn}(n);break;case"clientdb":r=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in Pn}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ne(ee.NEED_LOGIN);ue().then((function(){var n=Ln();if(n&&Yn({redirect:n}))return t.length>0?ie(ee.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(jn&&Qn({api:"navigateTo",redirect:n}))}))}(n)}))}function er(e){!function(e){e.onResponse=function(e){re(ee.RESPONSE,e)},e.offResponse=function(e){ae(ee.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){re(ee.NEED_LOGIN,e)},e.offNeedLogin=function(e){ae(ee.NEED_LOGIN,e)},Fn&&(K(rn).needLoginInit||(K(rn).needLoginInit=!0,ue().then((function(){Xn.call(e)})),Jn&&$n.call(e)))}(e),function(e){e.onRefreshToken=function(e){re(ee.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){ae(ee.REFRESH_TOKEN,e)}}(e)}var tr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",nr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function rr(){var e,t,n=pe().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(Mn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}Mn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!nr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,a="",i=0;i<e.length;)t=tr.indexOf(e.charAt(i++))<<18|tr.indexOf(e.charAt(i++))<<12|(n=tr.indexOf(e.charAt(i++)))<<6|(r=tr.indexOf(e.charAt(i++))),a+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return a}:atob;var ar=k((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function i(e,t,r){var a=r.onChooseFile,i=r.onUploadProgress;return t.then((function(e){if(a){var t=a(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,o=i.length,s=0;return new Promise((function(n){for(;s<r;)u();function u(){var r=s++;if(r>=o)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[r];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=c,e.tempFilePath=c.path,a&&a(e)}}).then((function(e){c.url=e.fileID,r<o&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,r<o&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?i(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,o=void 0===i?["album","camera"]:i,s=e.extension;return new Promise((function(e,i){uni.chooseImage({count:t,sizeType:n,sourceType:o,extension:s,success:function(t){e(a(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}(t),t):"video"===t.type?i(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,o=e.sourceType,s=void 0===o?["album","camera"]:o,u=e.extension;return new Promise((function(e,o){uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:u,success:function(t){var n=t.tempFilePath,r=t.duration,i=t.size,o=t.height,s=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}(t),t):i(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,i){var o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return i({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){i({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}(t),t)}}})),ir=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ar),or={auto:"auto",onready:"onready",manual:"manual"};function sr(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==or.manual){for(var r=!1,a=[],i=2;i<t.length;i++)t[i]!==n[i]&&(a.push(t[i]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,a)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,a=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,o=n.count;e.getcount&&(e.mixinDatacomPage.count=o),e.mixinDatacomHasMore=i.length<e.pageSize;var s=r?i.length?i[0]:void 0:i;e.mixinDatacomResData=s,a&&a(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var a=r.action||this.action;a&&(n=n.action(a));var i=r.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,u.default)(i)):n.collection(i);var o=r.where||this.where;o&&Object.keys(o).length&&(n=n.where(o));var s=r.field||this.field;s&&(n=n.field(s));var c=r.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var l=r.groupby||this.groupby;l&&(n=n.groupBy(l));var f=r.groupField||this.groupField;f&&(n=n.groupField(f)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var d=r.orderby||this.orderby;d&&(n=n.orderBy(d));var h=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,p=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,g=void 0!==r.getcount?r.getcount:this.getcount,v=void 0!==r.gettree?r.gettree:this.gettree,y=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,m={getCount:g},k={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return v&&(m.getTree=k),y&&(m.getTreePath=k),n=n.skip(p*(h-1)).limit(p).get(m),n}}}}function ur(e){return K(an.replace("{spaceId}",e.config.spaceId))}function cr(){return lr.apply(this,arguments)}function lr(){return lr=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r,i=ur(this),"mp-weixin"===j){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(j,"`"));case 4:if(!n||!a){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(i.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return o=e.sent,s=this.importObject("uni-id-co",{customUI:!0}),e.next=14,s.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:a});case 14:return i.mpWeixinCode=o,e.abrupt("return",{code:o});case 16:case"end":return e.stop()}}),e,this)}))),lr.apply(this,arguments)}function fr(e){return dr.apply(this,arguments)}function dr(){return dr=(0,f.default)((0,l.default)().mark((function e(t){var n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ur(this),e.abrupt("return",(n.initPromise||(n.initPromise=cr.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),dr.apply(this,arguments)}function hr(e){!function(e){ye=e}(e)}function pr(e){var t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise((function(r,a){t[e]((0,d.default)((0,d.default)({},n),{},{success:function(e){r(e)},fail:function(e){a(e)}}))}))}}var gr=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,i.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,y.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([pr("getSystemInfo")(),pr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,o.default)(t,2),r=n[0];r=void 0===r?{}:r;var a=r.appId,i=n[1];i=void 0===i?{}:i;var s=i.cid;if(!a)throw new Error("Invalid appId, please check the manifest.json file");if(!s)throw new Error("Invalid push client id");e._appId=a,e._pushClientId=s,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,a=t.message;this._payloadQueue.push({action:n,messageId:r,message:a}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(M);var vr={tcb:Bt,tencent:Bt,aliyun:Te,private:Vt,dcloud:Vt,alipay:Qt},yr=new(function(){function e(){(0,v.default)(this,e)}return(0,y.default)(e,[{key:"init",value:function(e){var t={},n=vr[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new U({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),pn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=Cn(In,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=Cn(In,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=rr,e.chooseAndUploadFile=ir.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return sr(e)}}),e.SSEChannel=gr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r;return fr.call(e,{openid:n,callLoginByWeixin:a})}}(e),e.setCustomClientInfo=hr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,s.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var r=n,a=r.customUI,i=r.loadingOptions,o=r.errorOptions,u=r.parseSystemError,c=!a;return new Proxy({},{get:function(r,a){switch(a){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,f.default)((0,l.default)().mark((function e(){var a,i,o,s,u,c,f=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=f.length,i=new Array(a),o=0;o<a;o++)i[o]=f[o];return s=r?r({params:i}):{},e.prev=2,e.next=5,Q(Z(n,"invoke"),(0,d.default)({},s));case 5:return e.next=7,t.apply(void 0,i);case 7:return u=e.sent,e.next=10,Q(Z(n,"success"),(0,d.default)((0,d.default)({},s),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),c=e.t0,e.next=18,Q(Z(n,"fail"),(0,d.default)((0,d.default)({},s),{},{error:c}));case 18:throw c;case 19:return e.prev=19,e.next=22,Q(Z(n,"complete"),c?(0,d.default)((0,d.default)({},s),{},{error:c}):(0,d.default)((0,d.default)({},s),{},{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var r=(0,f.default)((0,l.default)().mark((function r(){var p,g,v,y,m,k,w,b,T,x,_,S,C,A,E,P=arguments;return(0,l.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:for(c&&uni.showLoading({title:i.title,mask:i.mask}),g=P.length,v=new Array(g),y=0;y<g;y++)v[y]=P[y];return m={name:t,type:I.OBJECT,data:{method:a,params:v}},"object"==(0,s.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},a=r[n]||r["*"];a&&(t.secretType=a)}(n,m),k=!1,r.prev=5,r.next=8,e.callFunction(m);case 8:p=r.sent,r.next=14;break;case 11:r.prev=11,r.t0=r["catch"](5),k=!0,p={result:new le(r.t0)};case 14:if(w=p.result||{},b=w.errSubject,T=w.errCode,x=w.errMsg,_=w.newToken,c&&uni.hideLoading(),_&&_.token&&_.tokenExpired&&(ge(_),ie(ee.REFRESH_TOKEN,(0,d.default)({},_))),!T){r.next=39;break}if(S=x,!k||!u){r.next=24;break}return r.next=20,u({objectName:t,methodName:a,params:v,errSubject:b,errCode:T,errMsg:x});case 20:if(r.t1=r.sent.errMsg,r.t1){r.next=23;break}r.t1=x;case 23:S=r.t1;case 24:if(!c){r.next=37;break}if("toast"!==o.type){r.next=29;break}uni.showToast({title:S,icon:"none"}),r.next=37;break;case 29:if("modal"===o.type){r.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(o.type));case 31:return r.next=33,(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,o,s=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},n=t.title,r=t.content,a=t.showCancel,i=t.cancelText,o=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:n,content:r,showCancel:a,cancelText:i,confirmText:o,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:S,showCancel:o.retry,cancelText:"取消",confirmText:o.retry?"重试":"确定"});case 33:if(C=r.sent,A=C.confirm,!o.retry||!A){r.next=37;break}return r.abrupt("return",h.apply(void 0,v));case 37:throw E=new le({subject:b,code:T,message:x,requestId:p.requestId}),E.detail=p.result,ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:E}),E;case 39:return r.abrupt("return",(ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:p.result}),p.result));case 40:case"end":return r.stop()}}),r,null,[[5,11]])})));function h(){return r.apply(this,arguments)}return h}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:a,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,a=!1;if("callFunction"===t){var i=n&&n.type||I.DEFAULT;a=i!==I.DEFAULT}var o="callFunction"===t&&!a,s=this._initPromiseHub.exec();n=n||{};var u=ce(n),c=u.success,l=u.fail,f=u.complete,d=s.then((function(){return a?Promise.resolve():Q(Z(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return a?Promise.resolve(e):Q(Z(t,"success"),e).then((function(){return Q(Z(t,"complete"),e)})).then((function(){return o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return a?Promise.reject(e):Q(Z(t,"fail"),e).then((function(){return Q(Z(t,"complete"),e)})).then((function(){return ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||f))return d;d.then((function(e){c&&c(e),f&&f(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),f&&f(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=yr,function(){var e=q,n={};if(e&&1===e.length)n=e[0],t.uniCloud=yr=yr.init(n),yr._isDefault=!0;else{var a;a=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"].forEach((function(e){yr[e]=function(){return r.error(a),Promise.reject(new le({code:"SYS_ERR",message:a}))}}))}if(Object.assign(yr,{get mixinDatacom(){return sr(yr)}}),er(yr),yr.addInterceptor=G,yr.removeInterceptor=Y,yr.interceptObject=X,"app"===j&&(uni.__uniCloud=yr),"app"===j||"web"===j){var i=function(){return J||(J=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),J)}();i.uniCloud=yr,i.UniCloudError=le}}();var mr=yr;t.default=mr}).call(this,n("0ee4"),n("ba7c")["default"])},9370:function(e,t,n){"use strict";var r=n("8bdb"),a=n("af9e"),i=n("1099"),o=n("c215"),s=a((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}));r({target:"Date",proto:!0,arity:1,forced:s},{toJSON:function(e){var t=i(this),n=o(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},a613:function(e,t,n){"use strict";n.r(t);var r=n("ec12"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},c1a3:function(e,t,n){"use strict";n("15ab")},d025:function(e,t,n){"use strict";var r=n("db73"),a=n.n(r);a.a},d441:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},n("5ef2"),n("c9b5"),n("bf0f"),n("ab80")},db73:function(e,t,n){var r=n("85f0");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var a=n("967d").default;a("642d7bc7",r,!0,{sourceMap:!1,shadowMode:!1})},e062:function(e,t,n){"use strict";var r=n("8bdb");r({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:9007199254740991})},ec12:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var r={name:"UniLoadMore",props:{status:{type:String,default:"more"},showIcon:{type:Boolean,default:!0},iconType:{type:String,default:"auto"},iconSize:{type:Number,default:24},color:{type:String,default:"#777777"},contentText:{type:Object,default:function(){return{contentdown:"上拉显示更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"}}}},data:function(){return{webviewHide:!1,platform:""}},computed:{iconSnowWidth:function(){return 2*(Math.floor(this.iconSize/24)||1)}},mounted:function(){this.platform=uni.getSystemInfoSync().platform},methods:{onClick:function(){this.$emit("clickLoadMore",{detail:{status:this.status}})}}};t.default=r},f555:function(e,t,n){"use strict";var r=n("85c1"),a=n("ab4a"),i=n("e4ca"),o=n("471d"),s=n("af9e"),u=r.RegExp,c=u.prototype,l=a&&s((function(){var e=!0;try{u(".","d")}catch(l){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",a=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in e&&(i.hasIndices="d"),i)a(o,i[o]);var s=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return s!==r||n!==r}));l&&i(c,"flags",{configurable:!0,get:o})}}]);