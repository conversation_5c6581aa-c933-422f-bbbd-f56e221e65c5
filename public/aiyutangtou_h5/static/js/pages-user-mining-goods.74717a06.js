(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-mining-goods"],{"0181":function(t,i,a){var e=a("a08a");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var n=a("4f06").default;n("4928fa54",e,!0,{sourceMap:!1,shadowMode:!1})},"10eb":function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("d9e2"),a("d401")},"2e9a":function(t,i,a){"use strict";a.d(i,"b",(function(){return n})),a.d(i,"c",(function(){return s})),a.d(i,"a",(function(){return e}));var e={wanlDivider:a("d7c5").default,wanlDirect:a("25aa").default,wanlShare:a("7bdc").default,uniNumberBox:a("6aa3").default},n=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",{staticClass:"wanl-goods"},[a("v-uni-view",{staticClass:"cu-custom",style:{height:t.wanlsys.height+"px"}},[a("v-uni-view",{staticClass:"cu-bar fixed",style:{height:t.wanlsys.height+"px",paddingTop:t.wanlsys.top+"px"}},[a("v-uni-view",{staticClass:"action",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.$wanlshop.back(1)}}},[a("v-uni-text",{staticClass:"wlIcon-fanhui1"})],1),a("v-uni-view",{staticClass:"action"},[a("v-uni-text",{staticClass:"wlIcon-fenxiangcopy",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.showModal("share")}}})],1)],1)],1),a("v-uni-view",{staticClass:"swiper-box",attrs:{id:"swiper"}},[a("v-uni-swiper",{attrs:{circular:"true",autoplay:"true"},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.swiperChange.apply(void 0,arguments)}}},t._l(t.goodsData.images,(function(i,e){return a("v-uni-swiper-item",{key:e},[a("v-uni-image",{attrs:{src:t.$wanlshop.oss(i,400,0),mode:"aspectFill"}})],1)})),1),a("v-uni-view",{staticClass:"indicator"},[t._v(t._s(t.currentSwiper+1)+"/"+t._s(t.goodsData.images.length))])],1),a("v-uni-view",{staticClass:"bg-white",staticStyle:{"padding-top":"-100upx"}},["seckill"===t.goodsData.activity_type?a("v-uni-view",{staticClass:"price-rush bg-gradual-red margin-bottom-sm"},[a("v-uni-view",{staticClass:"price align-center  padding-lr-bj"},[a("v-uni-view",[a("v-uni-view",{staticClass:"text-price text-white text-xxl text-bold margin-right-xs"},[t._v("0"),a("v-uni-text",{staticClass:"text-lg"},[t._v(".00")]),a("v-uni-text",{staticClass:"margin-left-sm text-sm"},[t._v("已抢"),a("v-uni-text",{staticClass:"amount"},[t._v("0")]),t._v("件")],1)],1),a("v-uni-view",{staticClass:"text-sm text-white text-price"},[a("v-uni-text",{staticClass:"text-dec"},[t._v("0.00")])],1)],1),a("v-uni-view",[a("v-uni-view",{staticClass:"text-xs margin-bottom-xs"},[t._v("距结束还剩：")]),a("wanl-countdown",{attrs:{time:1,height:40,width:40,size:28,colonsize:32,color:"#ec008c",bcolor:"#ffffff",bgcolor:"#ffffff",coloncolor:"#ffffff"}})],1)],1)],1):a("v-uni-view",{staticClass:"price margin-bottom-sm padding-lr-bj padding-top-bj align-start"},[a("v-uni-view",[a("v-uni-view",{staticClass:"wanl-orange text-xxl margin-right-xs"},[t._v(t._s(t.goodsData.interval_price||"0.00")+"USDT")])],1)],1),a("v-uni-view",{staticClass:"title padding-lr-bj padding-bottom-bj"},[a("v-uni-view",{staticClass:"name wanl-black text-lg text-cut-2"},[1==t.goodsData.shop.isself?a("v-uni-view",{staticClass:"cu-tag radius margin-right-xs sm bg-red"},[t._v("自营")]):t._e(),t._v(t._s(t.goodsData.title||"加载中..."))],1),a("v-uni-view",{staticClass:"share",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.showModal("share")}}},[a("v-uni-view",{staticClass:"button wanl-gray-light margin-bottom"},[a("v-uni-text",{staticClass:"wlIcon-fenxiangcopy margin-right-xs"}),a("v-uni-text",{staticClass:"text-sm"},[t._v("分享")])],1)],1)],1),a("v-uni-view",{staticClass:"title padding-lr-bj padding-bottom-bj"},[a("v-uni-view",{staticClass:"name wanl-gray text-sm text-cut-2"},[t._v(t._s(t.goodsData.description))])],1)],1),a("v-uni-view",{staticClass:"bg-white",staticStyle:{"padding-top":"-100upx"}},[a("v-uni-view",{staticClass:"block text-min padding-lr padding-bottom-bj"},[a("v-uni-view",{staticClass:"wanl-gray"},[t._v("矿机费用"),a("br"),a("v-uni-text",{},[t._v(t._s(t.goodsData.interval_price)+"USDT")])],1),a("v-uni-view",{staticClass:"wanl-gray"},[t._v("GAS费用"),a("br"),a("v-uni-text",{},[t._v(t._s(t.goodsData.gas)+"FIL/T")])],1),a("v-uni-view",{staticClass:"wanl-gray"},[t._v("质押费用"),a("br"),a("v-uni-text",{},[t._v(t._s(t.goodsData.fil)+"FIL/T")])],1)],1)],1),a("v-uni-view",{staticClass:"bg-white"},[a("v-uni-view",{staticClass:"block text-min padding-lr padding-bottom-bj"},[a("v-uni-view",{staticClass:"wanl-gray"},[t._v("合约周期"),a("br"),a("v-uni-text",{},[t._v("540日")])],1),a("v-uni-view",{staticClass:"wanl-gray"},[t._v("封装周期"),a("br"),a("v-uni-text",{},[t._v("预计10天")])],1)],1)],1),a("v-uni-view",{staticClass:"choice margin-bottom-bj padding-bj text-sm bg-white"},[a("v-uni-view",{staticClass:"commodity  margin-bottom-bj",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.showModal("option")}}},[a("v-uni-view",{staticClass:"opt"},[a("v-uni-view",{staticClass:"title wanl-gray"},[t._v("选择")]),a("v-uni-view",{staticClass:"option"},[a("v-uni-view",{staticClass:"selected"},[t.canCount?a("v-uni-view",[t._v("选择"),t._l(t.goodsData.spu,(function(i,e){return a("v-uni-text",{key:i.id,staticClass:"margin-lr-xs"},[t._v(t._s(i.name))])}))],2):a("v-uni-view",[t._v("已选"),a("v-uni-text",[t._v(t._s(t.selectArr.join(" / ")))])],1)],1),a("v-uni-view",{staticClass:"option-list"},t._l(t.goodsData.spu,(function(i,e){return a("v-uni-view",{key:i.id},[t._l(i.item,(function(i){return 0==e?a("v-uni-text",{key:i.name,staticClass:"cu-tag"},[t._v(t._s(i.name))]):t._e()})),0==e?a("v-uni-text",{staticClass:"cu-tag"},[t._v("更多规格可选")]):t._e()],2)})),1)],1)],1),a("v-uni-view",{staticClass:"text-sm wanl-gray"},[a("v-uni-text",{staticClass:"wlIcon-fanhui2"})],1)],1)],1),a("wanl-divider",{attrs:{width:"60%"}},[t._v("产品详情")]),a("v-uni-view",{staticClass:"wanl-goods-content bg-white",staticStyle:{padding:"20upx"},attrs:{id:"details"}},[a("v-uni-rich-text",{attrs:{nodes:t.goodsData.content}})],1),a("v-uni-view",{staticClass:"safeAreaBottom"}),a("v-uni-view",{staticClass:"WANL-MODAL text-sm",on:{touchmove:function(i){i.stopPropagation(),i.preventDefault(),arguments[0]=i=t.$handleEvent(i),t.moveHandle.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-modal top-modal",class:"menu"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"wanl-modal-menu cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("wanl-direct",{on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"promotion"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("v-uni-view",{staticClass:"wanl-modal"},[a("v-uni-view",{staticClass:"head padding-bj"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"text-lg"},[t._v("促销")])],1),a("v-uni-view",{staticClass:"close wlIcon-31guanbi",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}})],1)],1)],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"coupon"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog bg-bgcolor",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("v-uni-view",{staticClass:"wanl-modal"},[a("v-uni-view",{staticClass:"head padding-bj"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"text-lg"},[t._v("优惠券")])],1),a("v-uni-view",{staticClass:"close wlIcon-31guanbi",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}})],1),a("v-uni-scroll-view",{staticClass:"wanl-coupon scroll-y",attrs:{"scroll-y":"true"}},t._l(t.goodsData.coupon,(function(i,e){return a("v-uni-view",{key:e,staticClass:"item margin-bottom-bj radius-bock",class:i.type},[a("v-uni-image",{staticClass:"coupon-bg",attrs:{src:t.$wanlshop.appstc("/coupon/bg_coupon_3x.png")}}),i.state?a("v-uni-image",{staticClass:"coupon-sign",attrs:{src:t.$wanlshop.appstc("/coupon/img_couponcentre_received_3x.png")}}):t._e(),a("v-uni-view",{staticClass:"item-left"},["reduction"==i.type||"vip"==i.type&&"reduction"==i.usertype?[a("v-uni-view",{staticClass:"colour"},[a("v-uni-text",{staticClass:"text-price"}),a("v-uni-text",{staticClass:"prices"},[t._v(t._s(Number(i.price)))])],1),a("v-uni-view",{staticClass:"cu-tag wanl-gray-dark radius text-sm bg-white"},[t._v("满"+t._s(Number(i.limit))+"减"+t._s(Number(i.price)))])]:t._e(),"discount"==i.type||"vip"==i.type&&"discount"==i.usertype?[a("v-uni-view",{staticClass:"colour"},[a("v-uni-text",{staticClass:"prices"},[t._v(t._s(Number(i.discount)))]),a("v-uni-text",{staticClass:"discount"},[t._v("折")])],1),a("v-uni-view",{staticClass:"cu-tag wanl-gray-dark radius text-sm bg-white"},[t._v("满"+t._s(Number(i.limit))+"打"+t._s(Number(i.discount))+"折")])]:t._e(),"shipping"==i.type?[a("v-uni-view",{staticClass:"colour"},[a("v-uni-text",{staticClass:"prices"},[t._v("包邮")])],1),a("v-uni-view",{staticClass:"cu-tag wanl-gray-dark radius text-sm bg-white"},[t._v("满"+t._s(Number(i.limit))+"元包邮")])]:t._e()],2),a("v-uni-view",{staticClass:"item-right padding-bj"},[a("v-uni-view",{staticClass:"title"},[a("v-uni-view",{staticClass:"cu-tag sm radius margin-right-xs tagstyle"},[t._v(t._s(i.type_text))]),a("v-uni-text",{staticClass:"text-cut wanl-gray text-min"},[t._v(t._s(i.name))])],1),a("v-uni-view",{staticClass:"content text-min"},[a("v-uni-view",{staticClass:"wanl-gray"},["-1"!=i.grant?a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("目前仅剩余 "+t._s(i.surplus)+" 张")],1):t._e(),0!=i.drawlimit?a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("每人仅限领取 "+t._s(i.drawlimit)+" 张")],1):t._e(),"fixed"==i.pretype?[a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("生效 "+t._s(i.startdate))],1),a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("结束 "+t._s(i.enddate))],1)]:t._e(),"appoint"==i.pretype?[0==i.validity?a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("未使用前 永久 有效")],1):a("v-uni-view",[a("v-uni-text",{staticClass:"wlIcon-dot"}),t._v("领取后 "+t._s(i.validity)+" 天有效")],1)]:t._e()],2),i.state?t._e():a("v-uni-view",{staticClass:"cu-btn sm round",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onReceive(e)}}},[a("v-uni-text",[t._v("立即领取")])],1)],1)],1)],1)})),1),a("v-uni-view",{staticClass:"foot padding-lr-bj"},[a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round text-bold complete",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[t._v("完成")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"attribute"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("v-uni-view",{staticClass:"wanl-modal"},[a("v-uni-view",{staticClass:"head padding-bj"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"text-lg"},[t._v("产品参数")])],1)],1),a("v-uni-scroll-view",{staticClass:"scroll-y",attrs:{"scroll-y":"true"}},[a("v-uni-view",{staticClass:"table solid-bottom"},[a("v-uni-view",{staticClass:"name wanl-gray"},[t._v("品牌")]),a("v-uni-view",{staticClass:"value"},[t._v(t._s(t.goodsData.brand.name))])],1),t._l(t.goodsData.category_attribute,(function(i,e){return a("v-uni-view",{key:e,staticClass:"table solid-bottom"},[a("v-uni-view",{staticClass:"name wanl-gray"},[t._v(t._s(e))]),a("v-uni-view",{staticClass:"value"},[t._v(t._s(i))])],1)}))],2),a("v-uni-view",{staticClass:"foot padding-lr-bj"},[a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round text-bold complete",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[t._v("完成")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"share"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("wanl-share",{attrs:{scrollAnimation:t.scrollAnimation,shareTitle:t.goodsData.title,shareText:t.goodsData.description,image:t.$wanlshop.oss(t.goodsData.image,50,50),href:t.common.appConfig.domain+"/pages/product/goods?id="+t.goodsData.id+"&QRtype=goods",isReport:!0},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.wanlShare.apply(void 0,arguments)}}})],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"service"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("v-uni-view",{staticClass:"wanl-modal"},[a("v-uni-view",{staticClass:"head padding-bj"},[a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"text-lg"},[t._v("基础服务保障")])],1)],1),a("v-uni-view",{staticClass:"listbox padding-bj"},t._l(t.goodsData.shop.service_ids,(function(i,e){return a("v-uni-view",{key:i.id},[a("v-uni-view",{staticClass:"name"},[t._v(t._s(i.name)),a("v-uni-text",{staticClass:"wlIcon-fuwuxing"})],1),a("v-uni-view",{staticClass:"description wanl-gray text-min"},[t._v(t._s(i.description))])],1)})),1),a("v-uni-view",{staticClass:"foot padding-lr-bj"},[a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round text-bold complete",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[t._v("完成")])],1)],1)],1)],1),a("v-uni-view",{staticClass:"cu-modal bottom-modal",class:"option"==t.modalName?"show":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"cu-dialog",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i)}}},[a("v-uni-view",{staticClass:"option wanl-modal"},[a("v-uni-view",{staticClass:"head text-left padding-bj solid-bottom"},[a("v-uni-view",{staticClass:"cu-avatar radius-bock margin-right",style:{backgroundImage:"url("+t.$wanlshop.oss(t.selectshop.thumbnail||t.goodsData.image,100,100)+")"}}),a("v-uni-view",[a("v-uni-view",{staticClass:"wanl-orange text-xl margin-bottom-xs"},[t._v(t._s(t.selectshop.price||t.goodsData.interval_price)+"USDT")]),a("v-uni-view",{staticClass:"wanl-orange text-xl margin-bottom-xs"},[t._v(t._s(t.selectshop.fil?1*t.selectshop.fil+1*t.selectshop.gas:1*t.goodsData.fil+1*t.goodsData.gas)+"FIL")]),t.selectArr.join("")?a("v-uni-view",{staticClass:"wanl-gray margin-bottom-xs"},[t._v("库存"),a("v-uni-text",{staticClass:"margin-lr-xs"},[t._v(t._s(t.selectshop.stock||0))]),t._v("件")],1):t._e(),a("v-uni-view",{staticClass:"text-sm"},[t.selectArr.join("")?a("v-uni-view",[t._v("已选择："+t._s(t.selectArr.join(" ")))]):a("v-uni-view",[t._v("请选择："),t._l(t.goodsData.spu,(function(i,e){return a("v-uni-text",{key:i.id,staticClass:"wanl-gray-light"},[0!=e?[t._v("-")]:t._e(),t._v(t._s(i.name))],2)}))],2)],1)],1),a("v-uni-view",{staticClass:"close wlIcon-31guanbi",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.hideModal.apply(void 0,arguments)}}})],1),a("v-uni-scroll-view",{style:{maxHeight:t.wanlsys.screenHeight/2+"px"},attrs:{"scroll-y":"true"}},[t._l(t.goodsData.spu,(function(i,e){return a("v-uni-view",{key:i.id,staticClass:"opt text-left padding-bj solid-bottom"},[a("v-uni-view",{staticClass:"text-df"},[t._v(t._s(i.name))]),a("v-uni-view",{staticClass:"tag"},t._l(i.item,(function(i,n){return a("v-uni-view",{key:n,staticClass:"cu-tag text-sm",class:[i.ishow?"":"noactive",t.subIndex[e]==n?"active":""],on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.skuClick(i,e,a,n)}}},[t._v(t._s(i.name))])})),1)],1)})),a("v-uni-view",{staticClass:"number padding-bj"},[a("v-uni-view",{staticClass:"text-df"},[t._v("购买数量")]),a("uni-number-box",{attrs:{min:1,max:t.selectshop.stock,value:t.selectNum,disabled:t.canCount},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.changeNum.apply(void 0,arguments)}}})],1)],2),a("v-uni-view",{staticClass:"foot padding-lr-bj"},[t.isChoice?a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round text-bold complete",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.completeSelection.apply(void 0,arguments)}}},[t._v("完成")]):[a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round text-bold",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.SubmitData("placeOrder",1)}}},[t._v("立即订购")])]],2)],1)],1)],1)],1),a("v-uni-view",{staticClass:"wanlian cu-bar tabbar shop solid-top foot bg-blackgreen-light"},[1!=t.goodsData.shop_id?a("v-uni-view",{staticClass:"action",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onShop(t.goodsData.shop_id)}}},[a("v-uni-view",{staticClass:"wlIcon-dianpu1 wanl-orange"}),t._v("店铺")],1):t._e(),1!=t.goodsData.shop_id?a("v-uni-view",{staticClass:"action",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.toCart.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"wlIcon-gouwuche"},[t.cart.cartnum>0?a("v-uni-view",{staticClass:"cu-tag badge"},[t._v(t._s(t.cart.cartnum))]):t._e()],1),t._v("购物车")],1):t._e(),a("v-uni-view",{staticClass:"btn-group"},[a("v-uni-button",{staticClass:"cu-btn bg-gradual-orange round",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.SubmitData("placeOrder")}}},[t._v("立即购买")])],1)],1)],1)},s=[]},3210:function(t,i,a){var e=a("24fb");i=e(!1),i.push([t.i,".wanl-divider[data-v-fa809d04]{width:100%;position:relative;text-align:center;display:flex;justify-content:center;align-items:center;box-sizing:border-box;overflow:hidden}.wanl-divider-line[data-v-fa809d04]{position:absolute;height:%?1?%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.wanl-divider-text[data-v-fa809d04]{position:relative;text-align:center;padding:0 %?18?%;z-index:1}",""]),t.exports=i},3360:function(t,i,a){var e=a("24fb");i=e(!1),i.push([t.i,"toast[data-v-29ef2694],\r\nuni-toast[data-v-29ef2694]{z-index:9999}.cu-custom .cu-bar .search-form[data-v-29ef2694]{background-color:#fff}.cu-custom .cu-bar[data-v-29ef2694]{background-color:#f3f3f3}.cu-custom .bar-bg[data-v-29ef2694]{background-color:hsla(0,0%,95.3%,.96)}.cu-custom .cu-bar[data-v-29ef2694]{z-index:99}\n.cu-custom .cu-bar .nav .cu-item[data-v-29ef2694]{line-height:%?70?%;height:%?70?%}.swiper-box[data-v-29ef2694]{position:relative;width:100%;height:100vw}.swiper-box uni-swiper[data-v-29ef2694]{width:100%;height:100vw}.swiper-box uni-swiper uni-swiper-item uni-image[data-v-29ef2694]{width:100%;height:100vw}.swiper-box .indicator[data-v-29ef2694]{display:flex;justify-content:center;align-items:center;padding:0 %?25?%;height:%?40?%;border-radius:%?40?%;font-size:%?22?%;position:absolute;bottom:%?20?%;right:%?20?%;color:#fff;background-color:rgba(0,0,0,.2)}",""]),t.exports=i},"3d54":function(t,i,a){var e=a("3360");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var n=a("4f06").default;n("188ffcb1",e,!0,{sourceMap:!1,shadowMode:!1})},4053:function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){if(Array.isArray(t))return(0,e.default)(t)};var e=function(t){return t&&t.__esModule?t:{default:t}}(a("b680"))},4252:function(t,i,a){"use strict";a.r(i);var e=a("47de"),n=a.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(s);i["default"]=n.a},"42d7":function(t,i,a){"use strict";a.r(i);var e=a("2e9a"),n=a("4252");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return n[t]}))}(s);a("6b30");var o=a("f0c5"),c=Object(o["a"])(n["default"],e["b"],e["c"],!1,null,"29ef2694",null,!1,e["a"],void 0);i["default"]=c.exports},"47de":function(t,i,a){"use strict";a("7a82");var e=a("ee27").default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("d3b7"),a("ac1f"),a("5319"),a("a9e3"),a("14d9"),a("d81d"),a("99af"),a("4de4"),a("13d5"),a("e25e"),a("e9c4");var n=e(a("d0ff")),s=e(a("f07e")),o=e(a("54f8")),c=e(a("c964")),l=e(a("f3f3")),u=a("26cb"),r={data:function(){return{TabCur:0,wanlsys:{},modalName:null,durect:0,headerOpacity:0,currentSwiper:0,anchorlist:[{name:"主图",top:0},{name:"评价",top:0},{name:"详情",top:0},{name:"推荐",top:0}],selectAnchor:0,goodsData:{id:0,category_id:0,shop_id:0,brand_id:0,freight_id:0,title:"",image:"",images:"",flag:"",content:"",category_attribute:[],activity_type:"goods",price:null,interval_price:null,market_price:null,sales:0,brand:{},freight:{name:"",isdelivery:1},payment:0,comment:0,praise:0,moderate:0,negative:0,like:0,views:0,status:"",category:{},follow:!0,sku:[],spu:[],promotion:[],coupon:[],comment_list:{data:[],figure:0,tag:[]},shop:{find_user:{fans:0},city:""},shop_recommend:{}},reload:!0,likeData:[],current_page:1,last_page:1,status:"loading",contentText:{contentdown:" ",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"},shopItemInfo:{},selectArr:[],subIndex:[],selectshop:{},selectNum:1,isChoice:"",scrollAnimation:100}},onLoad:function(t){this.wanlsys=this.$wanlshop.wanlsys(),this.loadData(t),this.durect=this.$store.state.statistics.notice.notice+this.$store.state.statistics.notice.order+this.$store.state.statistics.notice.chat+this.$store.state.statistics.order.pay+this.$store.state.statistics.order.delive+this.$store.state.statistics.order.receiving+this.$store.state.statistics.order.evaluate},onPageScroll:function(t){t.scrollTop=t.scrollTop>150?150:t.scrollTop,this.headerOpacity=t.scrollTop*(1/150)},onReachBottom:function(){this.current_page>=this.last_page?this.status="noMore":(this.reload=!1,this.current_page=this.current_page+1,this.status="loading")},watch:{selectshop:function(t,i){t.price!=i.price&&this.queryCoupon(t.price)}},computed:(0,l.default)({canCount:function(){return this.subIndex.some((function(t){return-1===t}))}},(0,u.mapState)(["cart","common"])),methods:{loadData:function(t){var i=this;return(0,c.default)((0,s.default)().mark((function a(){return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i.$api.get({url:"/wanlshop/product/goods",data:t,success:function(t){if(t.content=t.content.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/gi,(function(t,a){var e='<img style="display: block; max-width: 100%;" src="'.concat(i.$wanlshop.oss(a,500,0),'">');return e})),0==t.sku.length)t.interval_price=t.price,t.market_price=t.price;else{var a,e=[],n=[],s=(0,o.default)(t.sku);try{for(s.s();!(a=s.n()).done;){var c=a.value;e.push(c.price),n.push(c.market_price)}}catch(d){s.e(d)}finally{s.f()}var l=Math.min.apply(null,e),u=Math.max.apply(null,e),r=Math.max.apply(null,n);t.interval_price=l==u?Number(l).toFixed(2):l+"-"+Number(u).toFixed(2),t.market_price=Number(r).toFixed(2)}i.goodsData=t,i.goodsData.spu.map((function(t){i.selectArr.push(""),i.subIndex.push(-1)})),i.checkItem(),i.checkInpath(-1),setTimeout((function(){i.calcAnchor()}),1e3)}});case 1:case"end":return a.stop()}}),a)})))()},loadlikeData:function(){var t=this;return(0,c.default)((0,s.default)().mark((function i(){return(0,s.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:t.$api.get({url:"/wanlshop/product/likes?pages=goods",data:{page:t.current_page},success:function(i){t.likeData=t.reload?i.data:t.likeData.concat(i.data),t.current_page=i.current_page,t.last_page=i.last_page,t.status="more"}});case 1:case"end":return i.stop()}}),i)})))()},queryCoupon:function(t){var i=this;return(0,c.default)((0,s.default)().mark((function a(){return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i.$api.post({url:"/wanlshop/coupon/query",data:{shop_id:i.goodsData.shop_id,goods_id:i.goodsData.id,shop_category_id:i.goodsData.shop_category_id,price:t},success:function(t){i.goodsData.coupon=t}});case 1:case"end":return a.stop()}}),a)})))()},onReceive:function(t){var i=this;return(0,c.default)((0,s.default)().mark((function a(){var e;return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e=i.goodsData.coupon[t],i.$api.post({url:"/wanlshop/coupon/receive",loadingTip:"领取中",data:{id:e.id},success:function(t){e.id=t.id,e.state=!0,i.$wanlshop.msg(t.msg),i.$store.commit("statistics/dynamic",{coupon:i.$store.state.statistics.dynamic.coupon+1})}});case 2:case"end":return a.stop()}}),a)})))()},skuClick:function(t,i,a,e){t.ishow&&(this.selectArr[i]!=t.name?(this.$set(this.selectArr,i,t.name),this.$set(this.subIndex,i,e)):(this.$set(this.selectArr,i,""),this.$set(this.subIndex,i,-1)),this.checkInpath(i),this.selectArr.every((function(t){return""!=t}))&&(this.selectshop=this.shopItemInfo[this.selectArr],this.selectNum=1))},checkInpath:function(t){for(var i=0,a=this.goodsData.spu.length;i<a;i++)if(i!=t)for(var e=this.goodsData.spu[i].item.length,s=0;s<e;s++)if(-1==this.subIndex[i]||s!=this.subIndex[i]){var o=(0,n.default)(this.selectArr);this.$set(o,i,this.goodsData.spu[i].item[s].name);var c=o.filter((function(t){return""!==t&&"undefined"!==typeof t}));this.shopItemInfo.hasOwnProperty(c)?this.$set(this.goodsData.spu[i].item[s],"ishow",!0):this.$set(this.goodsData.spu[i].item[s],"ishow",!1)}},checkItem:function(){var t=this;this.goodsData.sku.reduce((function(i,a){return i.concat(a.difference.reduce((function(i,e){return i.concat(i.map((function(i){return t.shopItemInfo.hasOwnProperty([].concat((0,n.default)(i),[e]))||(t.shopItemInfo[[].concat((0,n.default)(i),[e])]=a),[].concat((0,n.default)(i),[e])})))}),[[]]))}),[[]])},changeNum:function(t){if(this.selectshop.stock){var i=parseInt(t);i>this.selectshop.stock?(this.$wanlshop.msg("数量不能超过库存 ".concat(this.selectshop.stock," 件")),this.selectNum=parseInt(this.selectshop.stock)):this.selectNum=parseInt(t)}},swiperChange:function(t){this.currentSwiper=t.detail.current},showModal:function(t){var i=this;this.isChoice="","share"==t&&"share"!=this.modalName&&setTimeout((function(){i.scrollAnimation=0}),300),this.modalName=t},hideModal:function(t){t?this.showModal(t):this.modalName=null},wanlShare:function(t){t?this.$wanlshop.auth("/pages/user/complaint/complaint?id=".concat(this.goodsData.id,"&type=1")):this.modalName=null},moveHandle:function(){},follow:function(){var t=this;this.goodsData.follow=!this.goodsData.follow,this.goodsData.follow?this.$store.commit("statistics/dynamic",{collection:this.$store.state.statistics.dynamic.collection+1}):this.$store.commit("statistics/dynamic",{collection:this.$store.state.statistics.dynamic.collection-1}),this.$api.post({url:"/wanlshop/product/follow",data:{id:this.goodsData.id},success:function(i){t.goodsData.follow=i}})},toAnchor:function(t){this.selectAnchor=t,uni.pageScrollTo({scrollTop:this.anchorlist[t].top,duration:200})},calcAnchor:function(){var t=this,i=null;i=uni.createSelectorQuery().in(this),i.select("#evaluate").boundingClientRect((function(i){t.anchorlist[1].top=i.top-t.wanlsys.height-45})).exec(),i.select("#details").boundingClientRect((function(i){t.anchorlist[2].top=i.top-t.wanlsys.height-45})).exec(),i.select("#recommend").boundingClientRect((function(i){t.anchorlist[3].top=i.top-t.wanlsys.height-45})).exec()},onTag:function(t){this.$wanlshop.to("/pages/product/comment?id="+this.goodsData.id+"&praise="+this.goodsData.praise+"&tag="+t)},toCart:function(){uni.reLaunch({url:"/pages/cart"})},completeSelection:function(){this.canCount?this.$wanlshop.msg("请选择完成规格"):this.ProcessingData()},SubmitData:function(t,i){1==i?this.canCount?this.$wanlshop.msg("请选择完成规格"):(this.isChoice=t,this.ProcessingData()):(this.modalName="option",this.isChoice=t)},ProcessingData:function(){if("addToCart"==this.isChoice)this.$store.dispatch("cart/add",{shop_id:this.goodsData.shop_id,shop_name:this.goodsData.shop.shopname,goods_id:this.goodsData.id,title:this.goodsData.title,number:this.selectNum,image:this.goodsData.image,sku:this.selectshop,sku_id:this.selectshop.id,sum:this.$wanlshop.bcmul(this.selectshop.price,this.selectNum)}),this.$wanlshop.msg("已加购物车");else if("placeOrder"==this.isChoice){var t=[{goods_id:this.goodsData.id,number:this.selectNum,sku_id:this.selectshop.id}];this.$store.state.user.isLogin?uni.redirectTo({url:"/pages/user/mining/addorder?order_type=groups&sid=".concat(this.goodsData.shop_id,"&data=").concat(JSON.stringify(t))}):this.$wanlshop.to("/pages/auth/auth")}else this.$wanlshop.msg("数据异常");this.hideModal()},productSearch:function(t){this.$wanlshop.to("/pages/page/search?type=goods&keywords=".concat(t),"fade-in",100)}}};i.default=r},"542b":function(t,i,a){"use strict";var e=a("9bc2"),n=a.n(e);n.a},"54f8":function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t,i){var a="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!a){if(Array.isArray(t)||(a=(0,e.default)(t))||i&&t&&"number"===typeof t.length){a&&(t=a);var n=0,s=function(){};return{s:s,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,l=!1;return{s:function(){a=a.call(t)},n:function(){var t=a.next();return c=t.done,t},e:function(t){l=!0,o=t},f:function(){try{c||null==a["return"]||a["return"]()}finally{if(l)throw o}}}},a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("d9e2"),a("d401");var e=function(t){return t&&t.__esModule?t:{default:t}}(a("dde1"))},"6aa3":function(t,i,a){"use strict";a.r(i);var e=a("7f21"),n=a("fffa");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return n[t]}))}(s);a("eeb5");var o=a("f0c5"),c=Object(o["a"])(n["default"],e["b"],e["c"],!1,null,"77a19b44",null,!1,e["a"],void 0);i["default"]=c.exports},"6b30":function(t,i,a){"use strict";var e=a("3d54"),n=a.n(e);n.a},"7f21":function(t,i,a){"use strict";a.d(i,"b",(function(){return e})),a.d(i,"c",(function(){return n})),a.d(i,"a",(function(){}));var e=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",{staticClass:"uni-numbox"},[a("v-uni-view",{staticClass:"uni-numbox__minus",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t._calcValue("minus")}}},[a("v-uni-text",{staticClass:"uni-numbox--text wlIcon-jian",class:{"uni-numbox--disabled":t.inputValue<=t.min||t.disabled}})],1),a("v-uni-input",{staticClass:"uni-numbox__value",attrs:{disabled:t.disabled,type:"number"},on:{blur:function(i){arguments[0]=i=t.$handleEvent(i),t._onBlur.apply(void 0,arguments)}},model:{value:t.inputValue,callback:function(i){t.inputValue=i},expression:"inputValue"}}),a("v-uni-view",{staticClass:"uni-numbox__plus",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t._calcValue("plus")}}},[a("v-uni-text",{staticClass:"uni-numbox--text wlIcon-tianjia",class:{"uni-numbox--disabled":t.inputValue>=t.max||t.disabled}})],1)],1)},n=[]},8016:function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("a9e3");var e={name:"UniNumberBox",props:{value:{type:[Number,String],default:1},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},disabled:{type:Boolean,default:!1}},data:function(){return{inputValue:0}},watch:{value:function(t){this.inputValue=+t},inputValue:function(t,i){+t!==+i&&this.$emit("change",t)}},created:function(){this.inputValue=+this.value},methods:{_calcValue:function(t){if(!this.disabled){var i=this._getDecimalScale(),a=this.inputValue*i,e=this.step*i;if("minus"===t){if(a-=e,a<this.min*i)return;a>this.max*i&&(a=this.max*i)}else if("plus"===t){if(a+=e,a>this.max*i)return;a<this.min*i&&(a=this.min*i)}this.inputValue=String(a/i)}},_getDecimalScale:function(){var t=1;return~~this.step!==this.step&&(t=Math.pow(10,(this.step+"").split(".")[1].length)),t},_onBlur:function(t){var i=t.detail.value;i&&(i=+i,i>this.max?i=this.max:i<this.min&&(i=this.min),this.inputValue=i)}}};i.default=e},"82d8":function(t,i,a){"use strict";a.r(i);var e=a("d9cb"),n=a.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(s);i["default"]=n.a},"9b64":function(t,i,a){"use strict";a.d(i,"b",(function(){return e})),a.d(i,"c",(function(){return n})),a.d(i,"a",(function(){}));var e=function(){var t=this,i=t.$createElement,a=t._self._c||i;return a("v-uni-view",{staticClass:"wanl-divider",style:{height:t.height+"rpx"}},[a("v-uni-view",{staticClass:"wanl-divider-line",style:{width:t.width,background:t.getBgColor(t.gradual,t.gradualColor,t.dividerColor)}}),a("v-uni-view",{staticClass:"wanl-divider-text",style:{color:t.color,fontSize:t.size+"rpx",lineHeight:t.size+"rpx",background:t.bgcolor}},[t._t("default")],2)],1)},n=[]},"9bc2":function(t,i,a){var e=a("3210");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var n=a("4f06").default;n("7eb8e0ed",e,!0,{sourceMap:!1,shadowMode:!1})},a08a:function(t,i,a){var e=a("24fb");i=e(!1),i.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* solid */.uni-numbox[data-v-77a19b44]{\ndisplay:flex;\nflex-direction:row;border-radius:%?1000?%;padding:%?4?%;border:%?1?% solid #f2f2f2}.uni-numbox__value[data-v-77a19b44]{width:%?80?%;height:%?60?%;line-height:%?60?%;text-align:center;font-size:%?32?%}.uni-numbox__minus[data-v-77a19b44]{\ndisplay:flex;\nflex-direction:row;align-items:center;justify-content:center;width:%?60?%;height:%?60?%;color:#333;background-color:#f2f2f2;border-radius:%?1000?%;border-right-width:0}.uni-numbox__plus[data-v-77a19b44]{\ndisplay:flex;\nflex-direction:row;align-items:center;justify-content:center;width:%?60?%;height:%?60?%;border-radius:%?1000?%;background-color:#f2f2f2}.uni-numbox--text[data-v-77a19b44]{font-size:%?32?%;color:#333}.uni-numbox--disabled[data-v-77a19b44]{color:#b8b8b8}",""]),t.exports=i},a9e0:function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("a630")},d0ff:function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){return(0,e.default)(t)||(0,n.default)(t)||(0,s.default)(t)||(0,o.default)()};var e=c(a("4053")),n=c(a("a9e0")),s=c(a("dde1")),o=c(a("10eb"));function c(t){return t&&t.__esModule?t:{default:t}}},d7c5:function(t,i,a){"use strict";a.r(i);var e=a("9b64"),n=a("82d8");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return n[t]}))}(s);a("542b");var o=a("f0c5"),c=Object(o["a"])(n["default"],e["b"],e["c"],!1,null,"fa809d04",null,!1,e["a"],void 0);i["default"]=c.exports},d9cb:function(t,i,a){"use strict";a("7a82"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0,a("a9e3");var e={name:"WanlDivider",props:{height:{type:Number,default:86},width:{type:String,default:"100%"},dividerColor:{type:String,default:"#cecece"},color:{type:String,default:"#333"},size:{type:Number,default:28},bgcolor:{type:String,default:"#f5f5f5"},gradual:{type:Boolean,default:!1},gradualColor:{type:Array,default:function(){return["#eee","#ccc"]}}},methods:{getBgColor:function(t,i,a){var e=a;return t&&(e="linear-gradient(to right,"+i[0]+","+i[1]+","+i[1]+","+i[0]+")"),e}}};i.default=e},eeb5:function(t,i,a){"use strict";var e=a("0181"),n=a.n(e);n.a},fffa:function(t,i,a){"use strict";a.r(i);var e=a("8016"),n=a.n(e);for(var s in e)["default"].indexOf(s)<0&&function(t){a.d(i,t,(function(){return e[t]}))}(s);i["default"]=n.a}}]);