/*** WanlShop - 全局样式表 {@link http://www.wanlshop.com}* <AUTHOR> <<EMAIL>> < 本程序仅用作FastAdmin付费插件（多用户分销商城）测试使用，未经版权所有权人书面许可，不能自行用于商业用途！>* @2019年9月10日12:52:20* @version 1.0.1*/
body {
	background: #F7F7F7;
	font-size: 28rpx;
	color: #222222;
	/* background-image: linear-gradient(#3b5eaa, #e599c0); */
}

page {
	/* background: #F7F7F7; */
	font-size: 28rpx;
	color: #222222;
}

image {
	will-change: transform
}

/* 全局图片加速 */
/* image{will-change: transform;} */
/* APP 磨砂玻璃状态栏高度 */
/* #ifdef APP-PLUS */
.edgeInsetTop {
	height: var(--window-top);
}

.edgeInsetBottom {
	height: var(--window-bottom);
}

/* #endif */
/* 绝对定位的视图需要考虑 tabBar 遮挡的问题，bottom 应该加上 tabBar 的高度 */
.fixedView {
	position: fixed;
	bottom: var(--window-bottom);
}

.safeAreaBottom {
	height: 100rpx;
	height: calc(env(safe-area-inset-bottom) + 100rpx);
	width: 100%;
}

[class*="wlIcon-"] {
	font-family: "wlIcon";
	font-size: inherit;
	font-style: normal;
}


.white-back {
	width: 80rpx;
	height: 80rpx;
}

.omit1 {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.omit2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}


.cu-dialog {
	background-color: #ffffff;
}

.cu-modal {
	z-index: 990;
}

.cu-modal.bottom-modal .cu-dialog {
	border-radius: 20rpx 20rpx 0 0;
}

.margin-bj {
	margin: 25rpx;
}

.margin-top-bj {
	margin-top: 25rpx;
}

.margin-right-bj {
	margin-right: 25rpx;
}

.margin-bottom-bj {
	margin-bottom: 25rpx;
}

.margin-left-bj {
	margin-left: 25rpx;
}

.margin-lr-bj {
	margin-left: 25rpx;
	margin-right: 25rpx;
}

.margin-tb-bj {
	margin-top: 25rpx;
	margin-bottom: 25rpx;
}

.padding-bj {
	padding: 25rpx;
}

.padding-top-bj {
	padding-top: 25rpx;
}

.padding-right-bj {
	padding-right: 25rpx;
}

.padding-left-bj {
	padding-left: 25rpx;
}

.padding-lr-bj {
	padding-left: 25rpx;
	padding-right: 25rpx;
}

.padding-tb-bj {
	padding-top: 25rpx;
	padding-bottom: 25rpx;
}

.padding-bottom-bj {
	padding-bottom: 25rpx;
}

.text-min {
	font-size: 24rpx;
}

.text-wl {
	font-size: 26rpx;
}

.text-bold1 {
	font-weight: 100;
}

.text-bold2 {
	font-weight: 200;
}

.text-bold4 {
	font-weight: 400;
}

.text-bold5 {
	font-weight: 500;
}

.text-bold6 {
	font-weight: 600;
}

.text-bold7 {
	font-weight: 700;
}

.text-bold8 {
	font-weight: 800;
}

.text-dec {
	text-decoration: line-through;
}

.text-cut-2 {
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

.radius-bock {
	border-radius: 18rpx;
	overflow: hidden;
}

.bg-gradual-yellow {
	background-image: linear-gradient(to left, #ff9700, #ffca00);
	color: white;
}

.bg-transparent {
	background-color: transparent !important;
}

.bg-white {
	color: #222222;
}

.bg-nav {
	background-color: #f9f9f9;
}

.bg-bgcolor {
	background-color: #F7F7F7;
}

.wanl-black {
	color: black;
}

/* 标准黑 */
.wanl-pip {
	color: #222222;
}

/* 灰色 */
.wanl-gray {
	color: #828282;
}

/* 深灰 */
.wanl-gray-dark {
	color: #666666;
}

/* 浅灰 */
.wanl-gray-light {
	color: #999999;
}

.wanl-orange {
	color: #fe6600;
}

.wanl-red {
	color: #ff243a;
}

.wanl-pink {
	color: #E12430;

}

/* 字体渐变 - 红色 */
.wanl-text-red {
	background-image: -webkit-linear-gradient(right, #f43f3b, #ec008c);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 黄色 */
.wanl-text-yellow {
	background-image: -webkit-linear-gradient(right, #ff9700, #ffca00);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 橙色 */
.wanl-text-orange {
	background-image: -webkit-linear-gradient(right, #ff9700, #ed1c24);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 紫罗兰 */
.wanl-text-violet {
	background-image: -webkit-linear-gradient(right, #709cff, #9000ff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 蓝色 */
.wanl-text-blue {
	background-image: -webkit-linear-gradient(right, #0081ff, #1cbbb4);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 浅蓝色 */
.wanl-text-light-blue {
	background-image: -webkit-linear-gradient(right, #55c9fc, #0c79ff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 粉色 */
.wanl-text-pink {
	background-image: -webkit-linear-gradient(right, #ec008c, #6739b6);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 绿色 */
.wanl-text-green {
	background-image: -webkit-linear-gradient(right, #39b54a, #8dc63f);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 紫色 */
.wanl-text-purple {
	background-image: -webkit-linear-gradient(right, #9000ff, #5e00ff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

/* 白灰 */
.wanl-text-white {
	background-image: -webkit-linear-gradient(top, #ffffff, #cccccc);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.cu-custom .cu-tag.badge {
	top: 10rpx;
	right: -15rpx;
}

.cu-custom .cu-bar {
	background-size: 100%;
}

.cu-custom .bar-bg {
	background-size: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
}

.cu-custom .search-swiper {
	width: 100%;
	height: 100%;
}

.cu-bar .search-form {
	font-size: 28rpx;
}


.cu-bar .action:first-child>text[class*="wlIcon-"] {
	margin-left: 0;
	margin-right: -0.1em;
}

.placeholder {
	color: #999;
	font-size: 28rpx;
}

/* 万联渐变背景色*/
.wanl-bg-redblack {
	background: #7e2505;
	background-image: linear-gradient(90deg, #7e2505 0, #7e2505 97%);
	color: white;
}

/* 橘红色 */
.wanl-bg-redorange {
	background: #e12430;
	/* background-image: linear-gradient(90deg, #ff6333 0, #fe6600 97%); */
	color: white;
}

.wanl-bg-redorange-light {
	background-image: linear-gradient(90deg, #ffc0ae 0, #ffa7b0 97%);
	color: white;
}

/* 橙色 */
.wanl-bg-orange {
	color: white;
	background-image: linear-gradient(-90deg, #ff4950 0, #ff8123 100%);
}

/* 粉色 */
.wanl-bg-pink {
	color: white;
	background-image: linear-gradient(-90deg, #fa3b26 3%, #ff4d8a 96%);
}

/* 蓝色 */
.wanl-bg-blue {
	color: white;
	background-image: linear-gradient(-90deg, #2676fa 0, #23d7ff 100%);
}

/* 蓝色 */
.wanl-bg-nav {
	background-color: #f8f8f8;
	color: #000000;
}

/* 吸顶容器 */
.wanl-sticky-box {
	/* #ifndef APP-PLUS-NVUE */
	display: flex;
	position: -webkit-sticky;
	/* #endif */
	position: sticky;
	top: var(--window-top);
	z-index: 1025;
	flex-direction: row;
}

.wanl-sticky-box .bg-white {
	background-color: rgba(255, 255, 255, .9);
}

/* 数字框 */
.wanl-numberBox {
	width: 200rpx;
	height: 58rpx;
	border-radius: 100rpx;
	padding: 8rpx 6rpx 6rpx 6rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.wanl-numberBox text {
	background-color: #f2f2f2;
	padding: 8rpx;
	font-size: 30rpx;
}

.wanl-numberBox.solid:after {
	border: 1px solid #dddddd;
}

/* 优惠券 */
.wanl-ticket {
	display: flex;
	align-items: center;
	margin-right: 18rpx;
}

.wanl-ticket .ticket-price {
	height: 60rpx;
	line-height: 60rpx;
	position: relative;
	background: #ff5700;
	color: white;
	/* border-radius: 3rpx 0 0 3rpx; */
	padding-left: 12rpx;
	padding-right: 10rpx;
	font-weight: bold;
}

.wanl-ticket .ticket-title {
	font-size: 20rpx;
	display: flex;
	align-items: center;
	color: #ff5700;
	height: 60rpx;
	position: relative;
	background-color: #fff;
	border-radius: 0 6rpx 6rpx 0;
	border: #ff5700 solid 1px;
	border-left: 0;
	padding: 0 8rpx;
}


.wanl-ticket .ticket-price:before {
	content: "";
	width: 18rpx;
	height: 16rpx;
	background-color: #F7F7F7;
	position: absolute;
	top: 50%;
	left: -10rpx;
	margin-top: -8rpx;
	border-radius: 50%;
	z-index: 1;
}

.position-relative {
	position: relative;
}


.position-absolute {
	position: absolute;
}
	
/* 弹出层 */
.wanl-modal {
	position: relative;
	min-height: 500rpx;
	max-height: 1200rpx;
	padding-bottom: 128rpx;
	padding-bottom: calc(118rpx + env(safe-area-inset-bottom));
}

.wanl-modal .head {
	display: flex;
	position: relative;
	align-items: center;
}

.wanl-modal .head .content {
	display: flex;
	justify-content: center;
	width: 100%;
}

.wanl-modal .head .close {
	position: absolute;
	right: 25rpx;
}

.wanl-modal .listbox {
	text-align: left;
}

.wanl-modal .listbox .name {
	position: relative;
	padding-left: 60rpx;
	color: #333;
}

.wanl-modal .listbox .name>text[class*="wlIcon-"] {
	position: absolute;
	top: 5rpx;
	left: 5rpx;
	color: #ff243a;
}

.wanl-modal .listbox .description {
	padding: 20rpx 0 40rpx 60rpx;
}

.wanl-modal .scroll-y {
	text-align: left;
	width: 100%;
	height: 800rpx;
	padding: 25rpx;
	padding-top: 0;
}

.wanl-modal .scroll-y .table {
	display: flex;
	justify-content: space-between;
	width: 100%;
}

.wanl-modal .scroll-y .table .name {
	width: 23%;
	padding: 25rpx 0;
}

.wanl-modal .scroll-y .table .value {
	width: 75%;
	padding: 25rpx 0;
}

.wanl-modal .foot {
	position: absolute;
	padding-top: 25rpx;
	bottom: 0;
	width: 100%;
	height: 128rpx;
	height: calc(118rpx + env(safe-area-inset-bottom));
}

.wanl-modal .foot .cu-btn {
	padding: 0;
	width: 50%;
	height: 78rpx;
}

.wanl-modal .foot .cu-btn.complete {
	width: 100%;
}

/* 规格 */
.wanl-modal.option .head {
	text-align: left;
}

.wanl-modal.option .head .close {
	top: 25rpx;
	line-height: 45rpx;
}

.wanl-modal.option .head .cu-avatar {
	width: 200rpx;
	height: 220rpx;
}

.wanl-modal.option .opt .tag {
	display: flex;
	flex-wrap: wrap;
}

.wanl-modal.option .opt .tag .cu-tag {
	margin-right: 25rpx;
	margin-top: 20rpx;
	padding: 0px 25rpx;
	height: 65rpx;
	background-color: #f6f6f6;
	border-radius: 12rpx;
	color: #222222;
}

.wanl-modal.option .opt .tag .cu-tag.active {
	color: #eb5d2a;
	background-color: #fdf9f9;
}

.wanl-modal.option .opt .tag .cu-tag.active:after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border: 1upx solid currentColor;
	transform: scale(0.5);
	transform-origin: 0 0;
	box-sizing: border-box;
	border-radius: 25rpx;
	z-index: 1;
	pointer-events: none;
}

.wanl-modal.option .opt .tag .cu-tag.disabled {
	color: #bbb;
	background-color: #f9f9f9;
}

.wanl-modal.option .opt .tag .cu-tag.noactive {
	color: #cacaca;
	background-color: #efefef;
}

.wanl-modal.option .opt .tag .cu-tag+.cu-tag {
	margin-left: 0;
}

.wanl-modal.option .number {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

/* 菜单 */
.wanl-modal-menu {
	padding: 0 25rpx;
	box-sizing: border-box;
	padding-top: 18rpx;
}

/* grid 1-2-2布局 */
.grid.col-1-2-2>view {
	width: 50%;
}

.grid.col-1-2-2>view:nth-child(1) {
	width: 100%;
}

/* grid 1-1_2布局 */
.grid.col-1-1_2>view {
	width: 50%;
}

.grid.col-1-1_2>view:nth-child(1) {
	width: 100%;
}

.grid.col-1-1_2>view:nth-child(2) {
	width: 50%;
}

.grid.col-1-1_2>view:nth-child(3),
.grid.col-1-1_2>view:nth-child(4) {
	width: 25%;
}

/* grid 2-1_2布局 */
.grid.col-2-1_2>view {
	width: 50%;
}

.grid.col-2-1_2>view:nth-child(1),
.grid.col-2-1_2>view:nth-child(2),
.grid.col-2-1_2>view:nth-child(3) {
	width: 50%;
}

.grid.col-2-1_2>view:nth-child(4),
.grid.col-2-1_2>view:nth-child(5) {
	width: 25%;
}

/* grid 2-2_1布局 */
.grid.col-2-2_1>view {
	width: 50%;
}

.grid.col-2-2_1>view:nth-child(1),
.grid.col-2-2_1>view:nth-child(2) {
	width: 50%;
}

.grid.col-2-2_1>view:nth-child(3),
.grid.col-2-2_1>view:nth-child(4) {
	width: 25%;
}

/* grid 2-2-1_2布局 */
.grid.col-2-2-1_2>view {
	width: 50%;
}

.grid.col-2-2-1_2>view:nth-child(1),
.grid.col-2-2-1_2>view:nth-child(2),
.grid.col-2-2-1_2>view:nth-child(3),
.grid.col-2-2-1_2>view:nth-child(4),
.grid.col-2-2-1_2>view:nth-child(5) {
	width: 50%;
}

.grid.col-2-2-1_2>view:nth-child(6),
.grid.col-2-2-1_2>view:nth-child(7) {
	width: 25%;
}

/* grid 2-4布局 */
.grid.col-2-4>view {
	width: 50%;
}

.grid.col-2-4>view:nth-child(3),
.grid.col-2-4>view:nth-child(4),
.grid.col-2-4>view:nth-child(5),
.grid.col-2-4>view:nth-child(6) {
	width: 25%;
}

/* grid 2-2-4布局 */
.grid.col-2-2-4>view {
	width: 50%;
}

.grid.col-2-2-4>view:nth-child(5),
.grid.col-2-2-4>view:nth-child(6),
.grid.col-2-2-4>view:nth-child(7),
.grid.col-2-2-4>view:nth-child(8) {
	width: 25%;
}

/* 万联标签 */
.wanl-tag {
	display: flex;
	align-items: center;
}

.wanl-tag .triangle {
	width: 0;
	height: 0;
	border-top: 18rpx solid transparent;
	border-bottom: 18rpx solid transparent;
	border-right: 18rpx solid #ffdb00;
}

.wanl-tag .content {
	background-color: #ffdb00;
	border-radius: 0 6rpx 6rpx 0;
	height: 36rpx;
	line-height: 36rpx;
	vertical-align: inherit;
	font-size: 24rpx;
	padding-right: 16rpx;
	padding-left: 8rpx;
	color: #5c1d10;
}

/* 左右圆角 */
.round-left {
	border-radius: 5000rpx 0 0 5000rpx;
}

.round-right {
	border-radius: 0 5000rpx 5000rpx 0;
}

.round-bottom-50 {
	border-radius: 0 0 50rpx 50rpx;
}

.cu-bar .action.transparent:first-child {
	margin-left: 18rpx;
	font-size: 40rpx;
}

.cu-bar .action.transparent:last-child {
	margin-right: 18rpx;
	font-size: 40rpx;
}

.cu-tag:empty:not([class*="wlIcon-"]) {
	z-index: 2;
}

.wanl-list .drawer .scroll {
	width: 100%;
	height: 100%;
	padding-bottom: 116rpx;
	padding-bottom: calc(116rpx + env(safe-area-inset-bottom));
	padding-top: 15rpx;
	padding-top: calc(5rpx + env(safe-area-inset-top));
}

.wanl-list .drawer .scroll .title {
	color: #666666;
	font-size: 24rpx;
	padding: 25rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.wanl-list .drawer .scroll .from {
	display: flex;
	padding: 0 20rpx 25rpx 20rpx;
}

.wanl-list .drawer .scroll .from>text {
	padding: 0 25rpx;
	line-height: 60rpx;
	color: #f1f1f1;
}

.wanl-list .drawer .scroll .from>input {
	background: #f8f8f8;
	font-size: 24rpx;
	color: #999;
	height: 60rpx;
	text-align: center;
	border-radius: 999rpx;
}

.wanl-list .drawer .scroll .list {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	/* 相当于 1fr 1fr 1fr */
	grid-gap: 16rpx;
	/* grid-column-gap 和 grid-row-gap的简写 */
	grid-auto-flow: row;
	font-size: 25rpx;
	color: #666666;
	padding: 0 16rpx 25rpx 16rpx;
}

.wanl-list .drawer .scroll .list>text {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f8f8;
	border-radius: 5rpx;
	min-height: 70rpx;
	position: relative;
	overflow: hidden;
}

.wanl-list .drawer .scroll .list .active {
	background: #feeae1;
	color: #ff4f00;
}

.wanl-list .drawer .scroll .list .active::before {
	font-family: "wlIcon";
	content: "\e6db";
	position: absolute;
	right: -1px;
	top: -1px;
	font-size: 30rpx;
}

.wanl-list .drawer .footer {
	position: fixed;
	bottom: 0;
	background: #fff;
	display: flex;
	justify-content: flex-end;
	padding: 20rpx 26rpx 0 0;
	width: 100%;
	height: 116rpx;
	height: calc(116rpx + env(safe-area-inset-bottom));
}

.wanl-list .drawer .footer .cu-btn {
	height: 76rpx;
	padding: 0 50rpx;
}

/* 底部导航 */
.wanlian.cu-bar.tabbar {
	background-color: #f8f8f8;
	padding: 0;
	height: calc(100rpx + env(safe-area-inset-bottom));
	padding-bottom: env(safe-area-inset-bottom);
	z-index: 2;
}

.wanlian.cu-bar.tabbar-token {
	background-color: #191a2e;
	padding: 0;
	height: calc(100rpx + env(safe-area-inset-bottom));
	padding-bottom: env(safe-area-inset-bottom);
	z-index: 2;
}

.wanlian.cu-bar.tabbar .action .badge {
	top: -8rpx;
	right: 0;
	background-color: #fe6600;
}

.wanlian.cu-bar.tabbar.shop .action {
	width: 106rpx;
}

.wanlian.cu-bar.tabbar .btn-group {
	justify-content: center;
}

.wanlian.cu-bar.tabbar .btn-group .cu-btn {
	padding: 0;
	width: 190rpx;
	height: 78rpx;
}

/* ==================直播==================== */
.wan-live {}

/* 商品详情页 直播图标 */
.wan-live .tag {
	display: flex;
	align-items: center;
	border-radius: 18rpx;
	position: absolute;
	right: 25rpx;
	font-size: 20rpx;
	flex-wrap: wrap;
	background-color: rgba(0, 0, 0, 0.2);
	padding-bottom: 8rpx;
}

.wan-live .live {
	width: 100%;
	height: 52rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.wanLive-icon {
	width: 7%;
	height: 70%;
	position: relative;
	--color: #ff8d40;
	background-color: var(--color);
	transform-origin: bottom;
	animation: wanLive 0.6s 0.2s infinite ease-in-out;
}

.wanLive-icon::after {
	right: 200%;
	animation: wanLive 0.6s 0.4s infinite ease-in-out;
}

.wanLive-icon::before {
	left: 200%;
	animation: wanLive 0.6s 0s infinite ease-in-out;
}

.wanLive-icon::after,
.wanLive-icon::before {
	width: 100%;
	height: 100%;
	content: '';
	position: absolute;
	bottom: 0;
	background-color: var(--color);
	transform-origin: bottom;
}

@keyframes wanLive {

	0%,
	100% {
		transform: scaleY(1);
	}

	50% {
		transform: scaleY(0.6);
	}
}

/* ==================自定义页面==================== */
.wanl-page {
	width: 100%;
	overflow: hidden;
}

/* ==================商品列表==================== */
/* 全局样式 */
.wanl-product .content {
	display: flex;
	flex: 1;
	flex-wrap: wrap;
	align-content: space-between;
}

.wanl-product .content>view {
	width: 100%;
}

.wanl-product .content .goods-tag {
	margin-top: 4rpx;
	margin-bottom: 10rpx;
}

/* 普通布局 */
.wanl-product .product_list {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
}

.wanl-product .product_list .item {
	border-radius: 16rpx;
	background-color: #fff;
	overflow: hidden;
}

.wanl-product .product_list .item .img-wrap,
.wanl-product .product_list .item image {
	height: 250rpx;
	width: 250rpx;
}

/* 瀑布流布局 */
.wanl-product .product_warter .warter {
	border-radius: 16rpx !important;
	background-color: #ffffff;
	overflow: hidden;
}

.wanl-product .product_warter .warter .image {
	border-radius: 16rpx 16rpx 0 0 !important;
	width: 100%;
	overflow: hidden;
}

.wanl-product .product_warter .warter .img-wrap {
	margin-bottom: -4px;
}

/* 一列布局 */
.wanl-product [class*='col-1-'] .item image,
.wanl-product [class*='col-1-'] .item .img-wrap {
	border-radius: 16rpx;
}

.wanl-product .product_list.col-1-10 {
	margin: 0 10rpx;
	padding-top: 10rpx;
}

.wanl-product .product_list.col-1-10 .item {
	display: flex;
	width: 100%;
	margin-bottom: 10rpx;
}

.wanl-product .product_list.col-1-15 {
	margin: 0 15rpx;
	padding-top: 15rpx;
}

.wanl-product .product_list.col-1-15 .item {
	display: flex;
	width: 100%;
	margin-bottom: 15rpx;
}

.wanl-product .product_list.col-1-20 {
	margin: 0 20rpx;
	padding-top: 20rpx;
}

.wanl-product .product_list.col-1-20 .item {
	display: flex;
	width: 100%;
	margin-bottom: 20rpx;
}

.wanl-product .product_list.col-1-25 {
	margin: 0 25rpx;
	padding-top: 25rpx;
}

.wanl-product .product_list.col-1-25 .item {
	display: flex;
	width: 100%;
	margin-bottom: 25rpx;
}

.wanl-product .product_list.col-1-30 {
	margin: 0 30rpx;
	padding-top: 30rpx;
}

.wanl-product .product_list.col-1-30 .item {
	display: flex;
	width: 100%;
	margin-bottom: 30rpx;
}

/* 瀑布流 二列布局 */
.wanl-product .product_warter.col-2-10 {
	margin: 0 10rpx;
	padding-top: 10rpx;
}

.wanl-product .product_warter.col-2-10 .warter {
	margin-bottom: 10rpx;
}

.wanl-product .product_warter.col-2-10 .warter.right {
	margin-left: 5rpx;
}

.wanl-product .product_warter.col-2-10 .warter.left {
	margin-right: 5rpx;
}

.wanl-product .product_warter.col-2-15 {
	margin: 0 15rpx;
	padding-top: 15rpx;
}

.wanl-product .product_warter.col-2-15 .warter {
	margin-bottom: 15rpx;
}

.wanl-product .product_warter.col-2-15 .warter.right {
	margin-left: 7.5rpx;
}

.wanl-product .product_warter.col-2-15 .warter.left {
	margin-right: 7.5rpx;
}

.wanl-product .product_warter.col-2-20 {
	margin: 0 20rpx;
	padding-top: 20rpx;
}

.wanl-product .product_warter.col-2-20 .warter {
	margin-bottom: 20rpx;
}

.wanl-product .product_warter.col-2-20 .warter.right {
	margin-left: 10rpx;
}

.wanl-product .product_warter.col-2-20 .warter.left {
	margin-right: 10rpx;
}

.wanl-product .product_warter.col-2-25 {
	margin: 0 25rpx;
	padding-top: 25rpx;
}

.wanl-product .product_warter.col-2-25 .warter {
	margin-bottom: 25rpx;
}

.wanl-product .product_warter.col-2-25 .warter.right {
	margin-left: 12.5rpx;
}

.wanl-product .product_warter.col-2-25 .warter.left {
	margin-right: 12.5rpx;
}

.wanl-product .product_warter.col-2-30 {
	margin: 0 30rpx;
	padding-top: 30rpx;
}

.wanl-product .product_warter.col-2-30 .warter {
	margin-bottom: 30rpx;
}

.wanl-product .product_warter.col-2-30 .warter.right {
	margin-left: 15rpx;
}

.wanl-product .product_warter.col-2-30 .warter.left {
	margin-right: 15rpx;
}

/* 二列布局 */
.wanl-product .product_list[class*='col-2-'] .item image,
.wanl-product .product_list[class*='col-2-'] .item .img-wrap {
	width: 100%;
	height: 400rpx;
}

.wanl-product .product_list[class*='col-2-'] .comment {
	display: none;
}

.wanl-product .product_list[class*='col-2-'] .content {
	height: 200rpx;
}

.wanl-product .product_list.col-2-10 {
	margin: 0 10rpx;
	padding-top: 10rpx;
}

.wanl-product .product_list.col-2-10:after {
	content: '';
	width: calc((100% - 10rpx) / 2);
}

.wanl-product .product_list.col-2-10 .item {
	width: calc((100% - 10rpx) / 2);
	margin-bottom: 10rpx;
}

.wanl-product .product_list.col-2-15 {
	margin: 0 15rpx;
	padding-top: 15rpx;
}

.wanl-product .product_list.col-2-15:after {
	content: '';
	width: calc((100% - 15rpx) / 2);
}

.wanl-product .product_list.col-2-15 .item {
	width: calc((100% - 15rpx) / 2);
	margin-bottom: 15rpx;
}

.wanl-product .product_list.col-2-20 {
	margin: 0 20rpx;
	padding-top: 20rpx;
}

.wanl-product .product_list.col-2-20:after {
	content: '';
	width: calc((100% - 20rpx) / 2);
}

.wanl-product .product_list.col-2-20 .item {
	width: calc((100% - 20rpx) / 2);
	margin-bottom: 20rpx;
}

.wanl-product .product_list.col-2-25 {
	margin: 0 25rpx;
	padding-top: 25rpx;
}

.wanl-product .product_list.col-2-25:after {
	content: '';
	width: calc((100% - 25rpx) / 2);
}

.wanl-product .product_list.col-2-25 .item {
	width: calc((100% - 25rpx) / 2);
	margin-bottom: 25rpx;
}

.wanl-product .product_list.col-2-30 {
	margin: 0 30rpx;
	padding-top: 30rpx;
}

.wanl-product .product_list.col-2-30:after {
	content: '';
	width: calc((100% - 30rpx) / 2);
}

.wanl-product .product_list.col-2-30 .item {
	width: calc((100% - 30rpx) / 2);
	margin-bottom: 30rpx;
}

/* 三列布局 */
.wanl-product [class*='col-3-'] .item image,
.wanl-product [class*='col-3-'] .item .img-wrap {
	width: 100%;
}

.wanl-product .product_list.col-3-10 {
	margin: 0 10rpx;
	padding-top: 10rpx;
}

.wanl-product .product_list.col-3-10:after {
	content: '';
	width: calc((100% - 20rpx) / 3);
}

.wanl-product .product_list.col-3-10 .item {
	width: calc((100% - 20rpx) / 3);
	margin-bottom: 10rpx;
}

.wanl-product .product_list.col-3-15 {
	margin: 0 15rpx;
	padding-top: 15rpx;
}

.wanl-product .product_list.col-3-15:after {
	content: '';
	width: calc((100% - 30rpx) / 3);
}

.wanl-product .product_list.col-3-15 .item {
	width: calc((100% - 30rpx) / 3);
	margin-bottom: 15rpx;
}

.wanl-product .product_list.col-3-20 {
	margin: 0 20rpx;
	padding-top: 20rpx;
}

.wanl-product .product_list.col-3-20:after {
	content: '';
	width: calc((100% - 40rpx) / 3);
}

.wanl-product .product_list.col-3-20 .item {
	width: calc((100% - 40rpx) / 3);
	margin-bottom: 20rpx;
}

.wanl-product .product_list.col-3-25 {
	margin: 0 25rpx;
	padding-top: 25rpx;
}

.wanl-product .product_list.col-3-25:after {
	content: '';
	width: calc((100% - 50rpx) / 3);
}

.wanl-product .product_list.col-3-25 .item {
	width: calc((100% - 50rpx) / 3);
	margin-bottom: 25rpx;
}

.wanl-product .product_list.col-3-30 {
	margin: 0 30rpx;
	padding-top: 30rpx;
}

.wanl-product .product_list.col-3-30:after {
	content: '';
	width: calc((100% - 60rpx) / 3);
}

.wanl-product .product_list.col-3-30 .item {
	width: calc((100% - 60rpx) / 3);
	margin-bottom: 30rpx;
}

/* ==================评论页==================== */
.wanl-comment .subject .goods {
	display: flex;
	align-items: center;
	width: 100%;
}

.wanl-comment .subject .goods .cu-avatar {
	width: 130rpx;
	height: 130rpx;
	margin-right: 20rpx;
}

.wanl-comment .subject .goods .content {
	width: calc(100% - 150rpx);
}

.wanl-comment .subject .comment {
	display: flex;
	align-items: center;
	justify-items: center;
}

.wanl-comment .subject .comment .comment-title {
	width: 160rpx;
	color: #666666;
}

.wanl-comment .subject .comment .comment-operate {
	display: flex;
}

.wanl-comment .subject .comment .comment-operate .item {
	display: flex;
	align-items: center;
	justify-items: center;
	width: 150rpx;
	color: #c3c2ca;
}

.wanl-comment .subject .comment .comment-operate .item text {
	margin-right: 15rpx;
	font-size: 35rpx;
}

.wanl-comment .subject .comment .comment-operate .item.action {
	color: #ff4e02;
	font-weight: 500;
}

.wanl-comment .subject .comment .comment-operate .item.action .wlIcon-haoping:before {
	content: "\e6e7";
}

.wanl-comment .subject .comment .comment-operate .item.action .wlIcon-chaping:before {
	content: "\e6e6";
}

.wanl-comment .subject .describe {
	background-color: #fafafa;
}

.wanl-comment .subject .describe textarea {
	margin: 0;
}

.wanl-comment .subject .upload {}

.wanl-comment .operate .score {
	display: flex;
	align-items: center;
	margin-top: 25rpx;
}

.wanl-comment .operate .score .title {
	width: 150rpx;
}

/* ==================消息通知==================== */
.wanl-notice {
	position: relative;
}

.wanl-notice .tool {
	display: flex;
	align-items: center;
	padding-bottom: 190rpx;
	margin-left: 25rpx;
	padding-top: 6rpx;
}

.wanl-notice .tool .cu-tag {
	width: 40rpx;
	height: 40rpx;
	font-size: 25rpx;
	background-color: rgba(0, 0, 0, .05);
}

.wanl-notice .mode {
	position: absolute;
	width: 100%;
	bottom: -60rpx;
}

.wanl-notice .mode .shadow-warp {
	box-shadow: rgba(0, 0, 0, 0.02) 0px 0px 10rpx;
}

.wanl-notice .mode .flex {
	background-color: white;
	border-radius: 25rpx;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 70rpx;
}

.wanl-notice .mode .flex>view {
	position: relative;
}

.wanl-notice .mode .flex>view .cu-tag.badge {
	top: 0;
	right: 0;
	height: 32rpx;
}

.wanl-notice .mode .flex>view .cu-tag.badge:not([class*="bg-"]) {
	background-color: red;
}

.wanl-notice .mode .flex text {
	display: block;
	width: 100rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
	font-size: 48rpx;
	color: white;
	background-color: red;
	border-radius: 5000rpx;
	margin-bottom: 10rpx;
}

.wanl-notice .mode .flex .logistics text {
	background-image: linear-gradient(to right, #0081ff, #56ade0)
}

.wanl-notice .mode .flex .notice text {
	background-image: linear-gradient(to right, #ff9700, #f9ec50);
}

.wanl-notice .mode .flex .Interaction text {
	background-image: linear-gradient(to right, #3edc53, #8dc63f)
}

/* 消息列表 */
.wanl-msg {
	padding-top: 66rpx;
	background-color: white;
}

.wanl-msg .cu-list.menu-avatar>.cu-item>.cu-avatar {
	left: 25rpx;
}

.wanl-msg .cu-list.menu-avatar>.cu-item .content {
	left: 145rpx;
}

.wanl-msg .cu-list.menu-avatar>.cu-item .action .cu-tag {
	border-radius: 1000rpx;
	font-size: 24rpx;
	padding: 0px 11rpx;
	height: 35rpx;
	line-height: 35rpx;
}

.wanl-msg .cu-avatar {
	background-color: transparent;
}

/* ==================物流通知==================== */
/* 列表 */
.wanl-logistics-list .item .title {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-logistics-list .item .action {
	background-color: #f7f7f7;
	display: flex;
	overflow: hidden;
	border-radius: 10rpx;
}

.wanl-logistics-list .item .action image {
	width: 150rpx;
	height: 150rpx;
}

.wanl-logistics-list .item .action .padding-xs {
	width: calc(100% - 150rpx);
}

/* 详情 */
/* ==================产品详情==================== */
/* 价格-普通 */
.wanl-goods .price {
	display: flex;
	justify-content: space-between;
}

.wanl-goods .price .text-xxl {
	font-size: 52rpx;
}

.wanl-goods .price .follow {
	text-align: center;
}

.wanl-goods .price .follow text {
	display: block;
}

.wanl-goods .price .follow text[class*="wlIcon-"] {
	font-size: 36rpx;
}







/* 价格-拼团 */
.wanl-goods .price-pintuan {
	background-image: linear-gradient(90deg, #ff5f00 0, #ff243a 97%);
}

.wanl-goods .price-pintuan .price {
	padding-bottom: 20rpx;
}

.wanl-goods .price-pintuan .price .cu-capsule {
	vertical-align: 0.25em;
}

.wanl-goods .price-pintuan .price .cu-capsule .cu-tag[class*="line-"]:after {
	border-width: 3rpx;
}

/* 价格-抢购 */
.wanl-goods .price-rush .price {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.wanl-goods .price-rush .price .title {
	display: flex;
}

.wanl-goods .price-rush .price .amount {
	margin: 0 6rpx;
	color: #ffdb00;
}

















/* 标题 */
.wanl-goods .title {
	display: flex;
	position: relative;
}

.wanl-goods .title .name {
	width: 80%;
}

.wanl-goods .title .share .button {
	position: absolute;
	top: 6rpx;
	right: 0;
	background-color: #f4f4f4;
	border-radius: 500rpx 0 0 500rpx;
	padding: 7rpx 12rpx;
	display: flex;
	align-items: center;
	white-space: nowrap;
}

/* 物流 */
.wanl-goods .block {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-goods .block .margin-right-xs {
	margin-right: 5rpx;
}

/* 营销 */
.wanl-goods .promotion .item {
	display: flex;
	position: relative;
	padding: 20rpx 25rpx;
}

.wanl-goods .promotion .item:after {
	position: absolute;
	top: 0;
	left: 0;
	box-sizing: border-box;
	width: 200%;
	height: 200%;
	border-bottom: 0.5px solid #ddd;
	border-radius: inherit;
	content: " ";
	-webkit-transform: scale(.5);
	transform: scale(.5);
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	pointer-events: none;
}

.wanl-goods .promotion .item:last-child:after {
	border: none;
}

.wanl-goods .promotion .item .label {
	height: 24px;
	line-height: 24px;
	width: 80rpx;
}

.wanl-goods .promotion .item .conten {
	width: 80%;
}

.wanl-goods .promotion .item .conten .promotion-header {
	padding-top: 6rpx;
	margin-bottom: 16rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	white-space: nowrap;
}

.wanl-goods .promotion .item .conten .promotion-header:last-child {
	margin-bottom: 10rpx;
}

.wanl-goods .promotion .item .conten .promotion-header .cu-tag {
	font-size: 22rpx;
	padding: 0px 8rpx;
	color: #ff243a;
}

.wanl-goods .promotion .item .bnt-quan {
	display: block;
	vertical-align: top;
	position: absolute;
	right: 25rpx;
	top: 26rpx;
}

/* 活动 */
.wanl-goods .partake .item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-goods .partake .item .info {
	display: flex;
	align-items: center;
}

/* 选择 */
.wanl-goods .choice {}

.wanl-goods .choice .commodity {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.wanl-goods .choice .commodity .opt {
	display: flex;
}

.wanl-goods .choice .commodity .opt .title {
	width: 80rpx;
	flex-shrink: 0;
}

.wanl-goods .choice .commodity .opt .option {}

.wanl-goods .choice .commodity .opt .option .selected {
	display: flex;
}

.wanl-goods .choice .commodity .opt .option .option-list .cu-tag {
	background-color: #f7f7f7;
	color: #999;
	border-radius: 12rpx;
	height: 60rpx;
	margin-right: 15rpx;
	margin-top: 25rpx;
	padding: 0px 20rpx;
	font-size: 24rpx;
}

.wanl-goods .choice .commodity .opt .option .option-list .cu-tag+.cu-tag {
	margin-left: 0;
}

/* 评价 */
.wanl-goods .comment {}

.wanl-goods .comment .head {
	display: flex;
	justify-content: space-between;
}

.wanl-goods .comment .label .cu-tag {
	background-color: #fff5ef;
	margin-right: 18rpx;
	margin-top: 25rpx;
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 22rpx;
	color: #272727;
	font-size: 26rpx;
}

.wanl-goods .comment .label .cu-tag+.cu-tag {
	margin-left: 0;
}

.wanl-goods .comment .user {
	margin-bottom: 25rpx;
}

.wanl-goods .comment .user .userinfo {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-goods .comment .user .userinfo .avatar {
	display: flex;
	align-items: center;
}

.wanl-goods .comment .user .grid.col-1>view {
	width: 50%;
}

.wanl-goods .comment .user .details {
	margin: 20rpx 0;
}

.wanl-goods .comment .more {
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 店铺 */
.wanl-goods .shop .shopinfo {
	display: flex;
	align-items: center;
	position: relative;
}

.wanl-goods .shop .shopinfo .cu-avatar {}

.wanl-goods .shop .shopinfo .shopname {
	position: absolute;
	left: 116rpx;
}

.wanl-goods .shop .shopinfo .bnt {
	position: absolute;
	right: 0;
}

.wanl-goods .shop .shopinfo .bnt .cu-btn {
	padding: 0 16rpx;
	font-size: 24rpx;
	height: 54rpx;
}

.wanl-goods .shop .quality {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-goods .shop .quality .cu-tag {
	vertical-align: baseline;
	font-size: 20rpx;
	padding: 0 6rpx;
	height: 30rpx;
}

.wanl-goods .shop .quality text {
	margin: 0 15rpx;
}

.wanl-goods .shop .quality .cu-tag.gao {
	background-color: #feebe2;
	color: #ff6601;
}

/* 店铺推荐 */
.wanl-goods .shop-recom .head {
	display: flex;
	justify-content: space-between;
}

.wanl-goods .shop-recom .recommend {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}

.wanl-goods .shop-recom .recommend:after {
	content: '';
	width: 32%;
	border: 1px solid transparent;
}

.wanl-goods .shop-recom .recommend .item {
	width: 32%;
	margin-top: 25rpx;
}

.wanl-goods .shop-recom .recommend .item image {
	height: 220rpx;
	overflow: hidden;
	border-radius: 8rpx;
}

.wanl-goods-content {
	width: 100%;
	overflow: hidden;
}

/* 评论页 */
.wanl-goods-comment .head .cu-tag {
	height: 60rpx;
	background-color: #ffeee3;
	color: #383736;
	padding: 0 25rpx;
	margin: 25rpx 15rpx 0 0;
}

.wanl-goods-comment .head .cu-tag.active {
	background-color: #fe6600;
	color: white;
}

.wanl-goods-comment .list {
	margin-bottom: 25rpx;
	background-color: white;
	padding-bottom: 0;
}

.wanl-goods-comment .list .userinfo {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.wanl-goods-comment .list .userinfo .avatar {
	display: flex;
	align-items: center;
}

.wanl-goods-comment .list .grid.col-1>view {
	width: 450rpx;
}

.wanl-goods-comment .list .details {
	margin: 20rpx 0;
}

.wanl-goods-comment .list .goods {
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.wanl-goods-comment .list .goods .cu-avatar {
	width: 120rpx;
	height: 120rpx;
}

.wanl-goods-comment .list .goods .content {
	width: calc(100% - 145rpx);
}


/* ==================搜索页面==================== */
.wanl-search .history>view {
	margin-bottom: 50rpx;
}

.wanl-search .history .title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 30rpx;
	margin-bottom: 10rpx;
}

.wanl-search .list {
	display: flex;
	flex-wrap: wrap;
}

.wanl-search .list>view {
	background: #f8f8f8;
	color: #666666;
	margin: 20rpx 20rpx 0 0;
	padding: 10rpx 25rpx;
	border-radius: 9999rpx;
	position: relative;
}

.wanl-search .cu-list.menu>.cu-item {
	min-height: 86rpx;
}

/* ==================搜索列表==================== */
.wanl-list .headtop {
	height: 80rpx;
}

.wanl-list .headtop .cue {
	position: fixed;
	width: 100%;
	animation-name: fluctuate;
	animation-duration: .2s;
	z-index: 1000;
}

.wanl-list .bar {
	padding: 0 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 80rpx;
	font-size: 28rpx;
	background-color: #f7f7f7;
}

.wanl-list .bar .item {
	color: #555;
	display: flex;
	justify-content: center;
	align-items: center;
}

.wanl-list .bar .item.current {
	color: #ff4f00;
}

.wanl-list .bar .item .box {
	display: flex;
	flex-direction: column;
	height: 42rpx;
	line-height: 1;
}

.wanl-list .bar .item .box text[class*="wlIcon-"] {
	color: #cccccc;
	margin: 0 10rpx;
	font-weight: bold;
	font-size: 22rpx;
}

.wanl-list .bar .item .box text[class*="wlIcon-"].active {
	color: #ff4f00;
}

.wanl-list .menus {
	height: 80rpx;
	background-color: white;
	display: flex;
	align-items: center;
}

/* ==================聊天窗口==================== */
.wanl-chat .chatfoot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
}


/* ==================订单列表==================== */
.wanl-order-list {
	height: 100%;
}

.wanl-order-list .list-scroll-content {
	height: 100%;
}

.wanl-order-list .uni-swiper-item {
	height: auto;
}

/* 菜单 */
.wanl-order-list .navbar {
	display: flex;
	height: 60rpx;
	color: #3d4144;
	font-size: 26rpx;
	z-index: 10;
}

.wanl-order-list .navbar .nav-item {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
	position: relative;
}

.wanl-order-list .navbar .nav-item.current {
	color: #E12430;
}

.wanl-order-list .navbar .nav-item.current:after {
	content: "";
	position: absolute;
	left: 50%;
	bottom: 0;
	transform: translateX(-50%);
	width: 40%;
	height: 0;
	border-bottom: 2px solid #E12430;
}

/* 商品 */
.wanl-order-list .order-item {
	display: flex;
	flex-direction: column;
	padding-left: 25rpx;
	background: #fff;
	margin: 0 18rpx;
	margin-top: 25rpx;
}

.wanl-order-list .order-item .head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
}

.wanl-order-list .order-item .goods-box {
	display: flex;
	padding-top: 25rpx;
}

.wanl-order-list .order-item .goods-box .content {
	flex: 1;
	display: flex;
}

.wanl-order-list .order-item .goods-box .content .describe {
	flex: 1;
}

.wanl-order-list .order-item .goods-box .content .parameter {
	width: 140rpx;
	text-align: right;
}

.wanl-order-list .order-item .price-box {
	display: flex;
	justify-content: flex-end;
	align-items: baseline;
	padding: 28rpx;
	padding-top: 50rpx;
}

.wanl-order-list .order-item .action-box {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	position: relative;
}

.wanl-order-list .order-item .action-box .cu-btn {
	padding: 0 30rpx;
	font-size: 24rpx;
	height: 60rpx;
}

/* ================== 优惠券 ==================== */
.wanl-coupon .item {
	height: 210rpx;
	position: relative;
	overflow: hidden;
	display: flex;
}

/* 样式开始 */
.wanl-coupon .item.reduction .colour {
	color: #E12430;
}

.wanl-coupon .item.reduction .tagstyle {
	background-color: #fff0e6;
	color: #E12430;
}

.wanl-coupon .item.reduction .cu-btn {
	color: #ffffff;
	background-color: #E12430;
}

.wanl-coupon .item.reduction .cu-btn.line-colour {
	color: #ff4f00;
	background-color: transparent;
}

.wanl-coupon .item.discount .colour {
	color: #39b54a;
}

.wanl-coupon .item.discount .tagstyle {
	background-color: #e9ffec;
	color: #39b54a;
}

.wanl-coupon .item.discount .cu-btn {
	color: #ffffff;
	background-color: #39b54a;
}

.wanl-coupon .item.discount .cu-btn.line-colour {
	color: #39b54a;
	background-color: transparent;
}

.wanl-coupon .item.shipping .colour {
	color: #0081ff;
}

.wanl-coupon .item.shipping .tagstyle {
	background-color: #e1f0ff;
	color: #0081ff;
}

.wanl-coupon .item.shipping .cu-btn {
	color: #ffffff;
	background-color: #0081ff;
}

.wanl-coupon .item.shipping .cu-btn.line-colour {
	color: #0081ff;
	background-color: transparent;
}

.wanl-coupon .item.vip .colour {
	color: #f5a623;
}

.wanl-coupon .item.vip .tagstyle {
	background-color: #fff7e9;
	color: #f5a623;
}

.wanl-coupon .item.vip .cu-btn {
	color: #ffffff;
	background-color: #f5a623;
}

.wanl-coupon .item.vip .cu-btn.line-colour {
	color: #f5a623;
	background-color: transparent;
}

/* 样式结束 */
.wanl-coupon .item .coupon-bg {
	width: 100%;
	height: 210rpx;
	position: absolute;
	left: 0;
	top: 0;
	z-index: 1;
}

.wanl-coupon .item .coupon-sign {
	height: 110rpx;
	width: 110rpx;
	position: absolute;
	z-index: 99;
	top: -30rpx;
	right: 30rpx;
}

.wanl-coupon .item .item-left {
	width: 218rpx;
	height: 210rpx;
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	flex-shrink: 0;
}

.wanl-coupon .item .item-left .price {
	font-size: 52rpx;
	font-weight: bold;
}

.wanl-coupon .item .item-left .prices {
	font-size: 50rpx;
	font-weight: bold;
}

.wanl-coupon .item .item-left .discount {
	font-size: 22rpx;
}

.wanl-coupon .item .item-left .cu-tag {
	margin-top: 8rpx;
}

.wanl-coupon .item .item-right {
	display: flex;
	flex-wrap: wrap;
	flex: 1;
	height: 210rpx;
	z-index: 2;
	overflow: hidden;
	align-content: space-between;
}

.wanl-coupon .item .item-right .shop {
	display: flex;
	width: 100%;
	justify-content: space-between;
}

.wanl-coupon .item .item-right .shop .name {
	flex: 1;
}

.wanl-coupon .item .item-right .title {
	display: flex;
	align-items: center;
	width: 100%;
}

.wanl-coupon .item .item-right .content {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	width: 100%;
}

.wanl-coupon .item .item-right .cu-btn.sm {
	padding: 0 25rpx;
	font-size: 24rpx;
	height: 60rpx;
}

/* ==================订单列表==================== */
.wanl-order .address .cu-avatar {
	width: 82rpx;
	height: 82rpx;
	font-size: 1.6em;
}

.wanl-order .address.cu-list.menu-avatar>.cu-item>.cu-avatar {
	left: 25rpx;
}

.wanl-order .address.cu-list.menu-avatar>.cu-item .content {
	left: 130rpx;
	width: calc(100% - 200rpx);
}

.wanl-order .address.cu-list.menu-avatar>.cu-item .action {
	width: 40rpx;
}

.wanl-order .lists .shopname {
	margin: 20rpx 25rpx 30rpx 25rpx;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item {
	padding-right: 25rpx;
	height: 160rpx;
	align-items: flex-start;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item:after,
.wanl-order .lists .cu-list.menu>.cu-item:after {
	border-bottom: 0;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item .content {
	position: absolute;
	left: 186rpx;
	width: calc(100% - 350rpx);
	line-height: 1.2em;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item .content>view:first-child {
	font-size: 26rpx;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item>.cu-avatar {
	position: absolute;
	left: 25rpx;
	width: 140rpx;
	height: 140rpx;
}

.wanl-order .lists .cu-list.menu-avatar>.cu-item .action {
	width: 180rpx;
	text-align: right;
}

/* 操作 */
.wanl-order form .cu-form-group+.cu-form-group {
	border-top: 0
}

.wanl-order form .cu-form-group {
	padding: 0 25rpx;
}

.wanl-order form .cu-form-group .title {
	font-size: 26rpx;
	height: 52rpx;
	line-height: 52rpx;
	padding-right: 25rpx;
}

.wanl-order form .cu-form-group:first-child {
	margin-top: 25rpx;
}

.wanl-order form .cu-form-group {
	min-height: 76rpx;
}

.wanl-order form .cu-form-group picker:after {
	line-height: 76rpx;
	font-size: 30rpx;
	color: #333333;
}

.wanl-order form .cu-form-group picker .picker {
	line-height: 76rpx;
	font-size: 26rpx;
}

.wanl-order form .wanl-numberBox {
	width: 180rpx;
	height: 54rpx;
	padding: 6rpx;
}

.wanl-order form .wanl-numberBox text {
	padding: 8rpx;
	font-size: 30rpx;
}

/* 文本行高度 */
.wanl-order form .cu-form-group.align-start .title {
	margin-top: 14rpx;
}

.wanl-order form .cu-form-group textarea {
	font-size: 26rpx;
	margin: 24rpx 0 0 0;
	height: 3.5em;
}

.wanl-order .wanlian.cu-bar.tabbar {
	background-color: white;
	justify-content: flex-end;
}

.wanl-order .wanlian.cu-bar.tabbar .cu-btn {
	font-size: 28rpx;
	height: 76rpx;
}

/* ==================sku组件==================== */
.wanl_sku {
	background-color: #fafafa;
	color: #9c9c9c;
	margin-top: 10rpx;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	display: inline-flex;
}

/* ==================支付页面==================== */
.wanl-pay .price-box {
	background-color: #fff;
	height: 300rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-size: 80rpx;
	color: #666666;
}

.wanl-pay .list-box {
	margin-top: 20rpx;
	background-color: #fff;
	padding-left: 60rpx;
}

.wanl-pay .list-box .item {
	height: 120rpx;
	padding: 20upx 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-right: 60rpx;
	font-size: 30rpx;
	position: relative;
}

.wanl-pay .list-box .item .wlIcon-balance-pay {
	color: #fe8e2e;
}

.wanl-pay .list-box .item .wlIcon-points-pay {
	color: #fe8e2e;
}

.wanl-pay .list-box .item .wlIcon-alipay-pay {
	color: #06a0f8;
}

.wanl-pay .list-box .item .wlIcon-jssdk-pay {
	color: #26af41;
}

.wanl-pay .list-box .item .wlIcon-wechat-pay {
	color: #26af41;
}

.wanl-pay .list-box .item .wlIcon-baidu-pay {
	color: #e84330;
}

.wanl-pay .list-box .item>text[class*="wlIcon-"] {
	width: 100rpx;
	font-size: 52rpx;
}

.wanl-pay .list-box .item .action {
	flex: 1;
	display: flex;
	flex-direction: column;
	font-size: 24rpx;
	color: #999999;
}

.wanl-pay .iconimg {
	width: 64upx;
	height: 64upx;
}

.wanl-pay .coinaddr {
	width: 400upx;
	height: 400upx;
}

.wanl-pay .alipayimg {
	width: 400upx;
	height: 600upx;
}

.wanl-pay .list-box .item .action .title {
	font-size: 32rpx;
	margin-bottom: 4rpx;
}

.wanl-pay .mix-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 630rpx;
	height: 80rpx;
	margin: 80upx auto 30rpx;
	font-size: 24px;
	color: #fff;
	border-radius: 10rpx;
}

.wanl-pay .footer {
	/* position: fixed; */
	width: 100%;
	bottom: 0;
	color: #eaeaea;
	z-index: 1;
	height: 140rpx;
	height: calc(140rpx + env(safe-area-inset-bottom) / 2);
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

/* ==================客服服务==================== */
.wanl-service {
	display: flex;
	justify-content: space-around;
	align-items: center;
	align-content: center;
	text-align: center;
	height: 280rpx;
}

.wanl-service .cu-avatar.lg {
	width: 100rpx;
	height: 100rpx;
	font-size: 50rpx;
}

.wanl-you-like {
	background-size: 239rpx;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	width: 100%;
	height: 50rpx;
	margin-top: 20rpx;
}

/* CSS动画 */
[class*=animation-] {
	animation-duration: .35s;
	animation-timing-function: ease-out;
	animation-fill-mode: both
}

.animation-fade {
	animation-name: fade;
	animation-duration: .8s;
	animation-timing-function: linear
}

.animation-scale-up {
	animation-name: scale-up
}

.animation-scale-down {
	animation-name: scale-down
}

.animation-slide-top {
	animation-name: slide-top
}

.animation-slide-bottom {
	animation-name: slide-bottom
}

.animation-slide-left {
	animation-name: slide-left
}

.animation-slide-right {
	animation-name: slide-right
}

.animation-shake {
	animation-name: shake
}

.animation-reverse {
	animation-direction: reverse
}

.wlIconfont-spin {
	-webkit-animation: wlIcon-spin 2s infinite linear;
	animation: wlIcon-spin 2s infinite linear;
	display: inline-block;
}

.wlIconfont-pulse {
	-webkit-animation: wlIcon-spin 1s infinite steps(8);
	animation: wlIcon-spin 1s infinite steps(8);
	display: inline-block;
}

@keyframes wlIcon-spin {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(359deg);
		transform: rotate(359deg);
	}
}

@keyframes fluctuate {
	0% {
		opacity: 0.5;
		transform: translateY(-50rpx);
	}

	100% {
		opacity: 1;
		transform: translateY(0)
	}
}

@keyframes fade {
	0% {
		opacity: 0
	}

	100% {
		opacity: 1
	}
}

@keyframes scale-up {
	0% {
		opacity: 0;
		transform: scale(.2)
	}

	100% {
		opacity: 1;
		transform: scale(1)
	}
}

@keyframes scale-down {
	0% {
		opacity: 0;
		transform: scale(1.8)
	}

	100% {
		opacity: 1;
		transform: scale(1)
	}
}

@keyframes slide-top {
	0% {
		opacity: 0;
		transform: translateY(-100%)
	}

	100% {
		opacity: 1;
		transform: translateY(0)
	}
}

@keyframes slide-bottom {
	0% {
		opacity: 0;
		transform: translateY(100%)
	}

	100% {
		opacity: 1;
		transform: translateY(0)
	}
}

@keyframes shake {

	0%,
	100% {
		transform: translateX(0)
	}

	10% {
		transform: translateX(-9px)
	}

	20% {
		transform: translateX(8px)
	}

	30% {
		transform: translateX(-7px)
	}

	40% {
		transform: translateX(6px)
	}

	50% {
		transform: translateX(-5px)
	}

	60% {
		transform: translateX(4px)
	}

	70% {
		transform: translateX(-3px)
	}

	80% {
		transform: translateX(2px)
	}

	90% {
		transform: translateX(-1px)
	}
}

@keyframes slide-left {
	0% {
		opacity: 0;
		transform: translateX(-100%)
	}

	100% {
		opacity: 1;
		transform: translateX(0)
	}
}

@keyframes slide-right {
	0% {
		opacity: 0;
		transform: translateX(100%)
	}

	100% {
		opacity: 1;
		transform: translateX(0)
	}
}