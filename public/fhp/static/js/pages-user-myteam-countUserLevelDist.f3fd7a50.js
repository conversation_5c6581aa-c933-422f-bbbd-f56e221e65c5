(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-myteam-countUserLevelDist"],{"0abb":function(t,e,n){"use strict";n.r(e);var i=n("37ea"),r=n("66eed");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("84b1");var a=n("828b"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"138f5475",null,!1,i["a"],void 0);e["default"]=s.exports},1043:function(t,e,n){var i=n("7d56");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("967d").default;r("fcdf72c4",i,!0,{sourceMap:!1,shadowMode:!1})},1197:function(t,e,n){var i=n("2479");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("967d").default;r("61ee344b",i,!0,{sourceMap:!1,shadowMode:!1})},"144f":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,".lime-echart[data-v-138f5475]{position:relative;\r\nwidth:100%;height:100%;\n}.lime-echart__canvas[data-v-138f5475]{\r\nwidth:100%;height:100%;\n}\n.lime-echart__mask[data-v-138f5475]{position:absolute;width:100%;height:100%;left:0;top:0;z-index:1}\r\n",""]),t.exports=e},"15ab":function(t,e,n){"use strict";var i=n("7658"),r=n("57e7");i("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),r)},1851:function(t,e,n){"use strict";var i=n("8bdb"),r=n("84d6"),o=n("1cb5");i({target:"Array",proto:!0},{fill:r}),o("fill")},"1ea2":function(t,e,n){"use strict";var i=n("af9e"),r=n("1c06"),o=n("ada5"),a=n("5d6e"),s=Object.isExtensible,l=i((function(){s(1)}));t.exports=l||a?function(t){return!!r(t)&&((!a||"ArrayBuffer"!==o(t))&&(!s||s(t)))}:s},"20f3":function(t,e,n){"use strict";var i=n("8bdb"),r=n("5145");i({target:"Array",proto:!0,forced:r!==[].lastIndexOf},{lastIndexOf:r})},2479:function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 首页头部标题图片  120*30 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.bg[data-v-a69d2974]{width:%?750?%;height:calc(100% - %?80?%);background:no-repeat 0 0;background-size:contain;overflow:hidden}.teamListInfo[data-v-a69d2974]{width:%?700?%;margin:%?350?% auto 0}.teamListInfo .screen[data-v-a69d2974]{height:%?102?%;border-radius:%?20?% %?20?% 0 0;background-color:#ecebec;position:relative}.teamListInfo .screen > uni-view[data-v-a69d2974]{width:50%;height:%?102?%;border-radius:%?20?% %?20?% 0 0;background-color:#ecebec}.teamListInfo .screen .active[data-v-a69d2974]{font-weight:700;background-color:#fff;border-radius:%?20?% %?20?% 0 0;color:#e12430}.teamListInfo .screen .active[data-v-a69d2974]::after{content:"";width:%?70?%;height:%?6?%;background-color:#e12430;position:absolute;bottom:0}.type[data-v-a69d2974]{min-height:%?800?%}.type .Box[data-v-a69d2974]{border-bottom:%?1?% solid #f6f6f6;margin:auto;padding:%?35?% 0}',""]),t.exports=e},"247d":function(t,e,n){"use strict";var i=n("6a50");i("Uint16",(function(t){return function(e,n,i){return t(this,e,n,i)}}))},"295e":function(t,e,n){"use strict";var i=n("6a50");i("Uint32",(function(t){return function(e,n,i){return t(this,e,n,i)}}))},"30f7":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("7a76"),n("c9b5")},3639:function(t,e,n){n("bf0f"),n("18f7"),n("d0af"),n("de6c"),n("6a54"),n("9a2c");var i=n("bdbb")["default"];function r(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(r=function(t){return t?n:e})(t)}t.exports=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==i(t)&&"function"!==typeof t)return{default:t};var n=r(e);if(n&&n.has(t))return n.get(t);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&Object.prototype.hasOwnProperty.call(t,s)){var l=a?Object.getOwnPropertyDescriptor(t,s):null;l&&(l.get||l.set)?Object.defineProperty(o,s,l):o[s]=t[s]}return o["default"]=t,n&&n.set(t,o),o},t.exports.__esModule=!0,t.exports["default"]=t.exports},"37ea":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.canvasId?n("v-uni-view",{ref:"limeEchart",staticClass:"lime-echart",style:t.customStyle,attrs:{"aria-label":t.ariaLabel}},[t.use2dCanvas?n("v-uni-canvas",{staticClass:"lime-echart__canvas",style:t.canvasStyle,attrs:{type:"2d",id:t.canvasId,"disable-scroll":t.isDisableScroll},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}):n("v-uni-canvas",{staticClass:"lime-echart__canvas",style:t.canvasStyle,attrs:{width:t.nodeWidth,height:t.nodeHeight,"canvas-id":t.canvasId,id:t.canvasId,"disable-scroll":t.isDisableScroll},on:{touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}),t.isPC?n("v-uni-view",{staticClass:"lime-echart__mask",on:{mousedown:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},mousemove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},mouseup:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)},touchstart:function(e){arguments[0]=e=t.$handleEvent(e),t.touchStart.apply(void 0,arguments)},touchmove:function(e){arguments[0]=e=t.$handleEvent(e),t.touchMove.apply(void 0,arguments)},touchend:function(e){arguments[0]=e=t.$handleEvent(e),t.touchEnd.apply(void 0,arguments)}}}):t._e(),t.isOffscreenCanvas?n("v-uni-canvas",{style:t.offscreenStyle,attrs:{"canvas-id":t.offscreenCanvasId}}):t._e()],1):t._e()},r=[]},"3f75":function(t,e,n){var i,r,o,a=n("bdbb").default;n("4085"),n("8a8d"),n("7a76"),n("c9b5"),n("5ef2"),n("5c47"),n("2c10"),n("0506"),n("bf0f"),n("2797"),n("8f71"),n("f7a5"),n("fd3c"),n("7f48"),n("aa9c"),n("dc8a"),n("c223"),n("0c26"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("08eb"),n("c24b"),n("dd2b"),n("e838"),n("e966"),n("ab80"),n("d5c6"),n("5a56"),n("f074"),n("4100"),n("20f3"),n("1851"),n("6a54"),n("64aa"),n("dfcf"),n("4db2"),n("4f9b"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("3efd"),n("295e"),n("247d"),n("c02e"),n("825c"),n("a578"),n("dc69"),n("23f4"),n("7d2f"),n("9c4e"),function(n,s){"object"==a(e)&&"undefined"!=typeof t?s(e):(r=[e],i=s,o="function"===typeof i?i.apply(e,r):i,void 0===o||(t.exports=o))}(0,(function(t){"use strict";var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var i=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new i,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==("undefined"===typeof wx?"undefined":a(wx))&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:"undefined"==typeof navigator||0===navigator.userAgent.indexOf("Node.js")?(r.node=!0,r.svgSupported=!0):(w=navigator.userAgent,Bt=(wt=r).browser,I=w.match(/Firefox\/([\d.]+)/),g=w.match(/MSIE\s([\d.]+)/)||w.match(/Trident\/.+?rv:(([\d.]+))/),x=w.match(/Edge?\/([\d.]+)/),w=/micromessenger/i.test(w),I&&(Bt.firefox=!0,Bt.version=I[1]),g&&(Bt.ie=!0,Bt.version=g[1]),x&&(Bt.edge=!0,Bt.version=x[1],Bt.newEdge=18<+x[1].split(".")[0]),w&&(Bt.weChat=!0),wt.svgSupported="undefined"!=typeof SVGRect,wt.touchEventsSupported="ontouchstart"in window&&!Bt.ie&&!Bt.edge,wt.pointerEventsSupported="onpointerdown"in window&&(Bt.edge||Bt.ie&&11<=+Bt.version),wt.domSupported="undefined"!=typeof document,I=document.documentElement.style,wt.transform3dSupported=(Bt.ie&&"transition"in I||Bt.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in I)&&!("OTransition"in I),wt.transformSupported=wt.transform3dSupported||Bt.ie&&9<=+Bt.version);var o,s,l="12px sans-serif",u=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),h={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(o||(n=h.createCanvas(),o=n&&n.getContext("2d")),o)return s!==e&&(s=o.font=e||l),o.measureText(t);t=t||"",e=e||l;var n=/(\d+)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var a=0;a<t.length;a++){var c=u[t[a]];r+=null==c?i:c*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function c(t){for(var e in h)t[e]&&(h[e]=t[e])}var p=E(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),d=E(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),f=Object.prototype.toString,g=Array.prototype,y=g.forEach,m=g.filter,v=g.slice,_=g.map,x=function(){}.constructor,w=x?x.prototype:null,b="__proto__",S=2311;function M(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function T(t){if(null==t||"object"!=a(t))return t;var e=t,n=f.call(t);if("[object Array]"===n){if(!lt(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(d[n]){if(!lt(t)){var o=t.constructor;if(o.from)e=o.from(t);else for(e=new o(t.length),i=0,r=t.length;i<r;i++)e[i]=t[i]}}else if(!p[n]&&!lt(t)&&!Z(t))for(var s in e={},t)t.hasOwnProperty(s)&&s!==b&&(e[s]=T(t[s]));return e}function C(t,e,n){if(!X(e)||!X(t))return n?T(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==b&&(r=t[i],!X(o=e[i])||!X(r)||H(o)||H(r)||Z(o)||Z(r)||Y(o)||Y(r)||lt(o)||lt(r)?!n&&i in t||(t[i]=T(e[i])):C(r,o,n))}return t}function k(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==b&&(t[n]=e[n]);return t}function D(t,e,n){for(var i=B(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var I=h.createCanvas;function A(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function O(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function L(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else D(t,e,n)}function P(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function R(t,e,n){if(t&&e)if(t.forEach&&t.forEach===y)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function N(t,e,n){if(!t)return[];if(!e)return nt(t);if(t.map&&t.map===_)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function E(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function z(t,e,n){if(!t)return[];if(!e)return nt(t);if(t.filter&&t.filter===m)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function B(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var F=w&&W(w.bind)?w.call.bind(w.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(v.call(arguments)))}};function V(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(v.call(arguments)))}}function H(t){return Array.isArray?Array.isArray(t):"[object Array]"===f.call(t)}function W(t){return"function"==typeof t}function G(t){return"string"==typeof t}function U(t){return"[object String]"===f.call(t)}function j(t){return"number"==typeof t}function X(t){var e=a(t);return"function"==e||!!t&&"object"==e}function Y(t){return!!p[f.call(t)]}function q(t){return!!d[f.call(t)]}function Z(t){return"object"==a(t)&&"number"==typeof t.nodeType&&"object"==a(t.ownerDocument)}function $(t){return null!=t.colorStops}function K(t){return null!=t.image}function Q(t){return"[object RegExp]"===f.call(t)}function J(t){return t!=t}function tt(t,e){return null!=t?t:e}function et(t,e,n){return null!=t?t:null!=e?e:n}function nt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return v.apply(t,e)}function it(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function rt(t,e){if(!t)throw new Error(e)}function ot(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var at="__ec_primitive__";function st(t){t[at]=!0}function lt(t){return t[at]}ht.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},ht.prototype.has=function(t){return this.data.hasOwnProperty(t)},ht.prototype.get=function(t){return this.data[t]},ht.prototype.set=function(t,e){return this.data[t]=e,this},ht.prototype.keys=function(){return B(this.data)},ht.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var ut=ht;function ht(){this.data={}}var ct="function"==typeof Map;dt.prototype.hasKey=function(t){return this.data.has(t)},dt.prototype.get=function(t){return this.data.get(t)},dt.prototype.set=function(t,e){return this.data.set(t,e),e},dt.prototype.each=function(t,e){this.data.forEach((function(n,i){t.call(e,n,i)}))},dt.prototype.keys=function(){var t=this.data.keys();return ct?Array.from(t):t},dt.prototype.removeKey=function(t){this.data.delete(t)};var pt=dt;function dt(t){var e=H(t),n=(this.data=new(ct?Map:ut),this);function i(t,i){e?n.set(t,i):n.set(i,t)}t instanceof dt?t.each(i):t&&R(t,i)}function ft(t){return new pt(t)}function gt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function yt(t,e){var n;t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&k(t,e),t}function mt(t){t=t.style,t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function vt(t,e){return t.hasOwnProperty(e)}function _t(){}var xt=180/Math.PI,wt=Object.freeze({__proto__:null,HashMap:pt,RADIAN_TO_DEGREE:xt,assert:rt,bind:F,clone:T,concatArray:gt,createCanvas:I,createHashMap:ft,createObject:yt,curry:V,defaults:D,disableUserSelect:mt,each:R,eqNaN:J,extend:k,filter:z,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:function(){return S++},hasOwn:vt,indexOf:A,inherits:O,isArray:H,isArrayLike:P,isBuiltInObject:Y,isDom:Z,isFunction:W,isGradientObject:$,isImagePatternObject:K,isNumber:j,isObject:X,isPrimitive:lt,isRegExp:Q,isString:G,isStringSafe:U,isTypedArray:q,keys:B,logError:M,map:N,merge:C,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=C(n,t[i],e);return n},mixin:L,noop:_t,normalizeCssArray:it,reduce:E,retrieve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]},retrieve2:tt,retrieve3:et,setAsPrimitive:st,slice:nt,trim:ot});function bt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function St(t){return[t[0],t[1]]}function Mt(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Tt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Ct(t){return Math.sqrt(kt(t))}function kt(t){return t[0]*t[0]+t[1]*t[1]}function Dt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function It(t,e){var n=Ct(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function At(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Ot=At;function Lt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Pt=Lt;function Rt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function Nt(t,e,n){var i=e[0];e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function Et(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function zt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Bt=Object.freeze({__proto__:null,add:Mt,applyTransform:Nt,clone:St,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:bt,dist:Ot,distSquare:Pt,distance:At,distanceSquare:Lt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Ct,lenSquare:kt,length:Ct,lengthSquare:kt,lerp:Rt,max:zt,min:Et,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:It,scale:Dt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Tt}),Ft=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Vt=(Ht.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Ft(e,t),"dragstart",t.event))},Ht.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new Ft(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new Ft(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new Ft(i,t),"dragenter",t.event)},Ht.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Ft(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Ft(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},Ht);function Ht(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}Gt.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e],i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},Gt.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},Gt.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},Gt.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},Gt.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var Wt=Gt;function Gt(t){t&&(this._$eventProcessor=t)}var Ut=Math.log(2);function jt(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/Ut),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*jt(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function Xt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=jt(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*jt(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var Yt="___zrEVENTSAVED";function qt(t){return"CANVAS"===t.nodeName.toUpperCase()}var Zt=/([&<>"'])/g,$t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Kt(t){return null==t?"":(t+"").replace(Zt,(function(t,e){return $t[e]}))}var Qt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Jt=[],te=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function ee(t,e,n,i){return n=n||{},i?ne(t,e,n):te&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):ne(t,e,n),n}function ne(t,e,n){if(r.domSupported&&t.getBoundingClientRect){var i,o=e.clientX;e=e.clientY;if(qt(t))return i=t.getBoundingClientRect(),n.zrX=o-i.left,void(n.zrY=e-i.top);if(function(t,e,n,i,o){if(e.getBoundingClientRect&&r.domSupported&&!qt(e)){var a=e[Yt]||(e[Yt]={});e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left;h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?Xt(s,a):Xt(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,a),a,o);if(e)return e(t,n,i),1}}(Jt,t,o,e))return n.zrX=Jt[0],void(n.zrY=Jt[1])}n.zrX=n.zrY=0}function ie(t){return t||window.event}function re(t,e,n){var i;return null==(e=ie(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&ee(t,i,e,n):(ee(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX;t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&Qt.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var oe=(ae.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},ae.prototype.clear=function(){return this._track.length=0,this},ae.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=ee(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},ae.prototype._recognize=function(t){for(var e in le)if(le.hasOwnProperty(e)&&(e=le[e](this._track,t),e))return e},ae);function ae(){this._track=[]}function se(t){var e=t[1][0]-t[0][0];t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var le={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=se(n)/se(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function ue(){return[1,0,0,1,0,0]}function he(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ce(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function pe(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4];n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function de(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function fe(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],u=(e=e[5],Math.sin(n));n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function ge(t,e,n){var i=n[0];n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function ye(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=(e=e[5],n*a-o*i);return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var me=Object.freeze({__proto__:null,clone:function(t){var e=ue();return ce(e,t),e},copy:ce,create:ue,identity:he,invert:ye,mul:pe,rotate:fe,scale:ge,translate:de}),ve=(_e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},_e.prototype.clone=function(){return new _e(this.x,this.y)},_e.prototype.set=function(t,e){return this.x=t,this.y=e,this},_e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},_e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},_e.prototype.scale=function(t){this.x*=t,this.y*=t},_e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},_e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},_e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},_e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},_e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},_e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},_e.prototype.distance=function(t){var e=this.x-t.x;t=this.y-t.y;return Math.sqrt(e*e+t*t)},_e.prototype.distanceSquare=function(t){var e=this.x-t.x;t=this.y-t.y;return e*e+t*t},_e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},_e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},_e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},_e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},_e.set=function(t,e,n){t.x=e,t.y=n},_e.copy=function(t,e){t.x=e.x,t.y=e.y},_e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},_e.lenSquare=function(t){return t.x*t.x+t.y*t.y},_e.dot=function(t,e){return t.x*e.x+t.y*e.y},_e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},_e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},_e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},_e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},_e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},_e);function _e(t,e){this.x=t||0,this.y=e||0}var xe=Math.min,we=Math.max,be=new ve,Se=new ve,Me=new ve,Te=new ve,Ce=new ve,ke=new ve,De=(Ie.prototype.union=function(t){var e=xe(t.x,this.x),n=xe(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=we(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=we(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},Ie.prototype.applyTransform=function(t){Ie.applyTransform(this,this,t)},Ie.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=ue();return de(i,i,[-this.x,-this.y]),ge(i,i,[e,n]),de(i,i,[t.x,t.y]),i},Ie.prototype.intersect=function(t,e){if(!t)return!1;t instanceof Ie||(t=Ie.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,f=(h=h.y+h.height,t.x),g=t.x+t.width,y=t.y,m=(t=t.y+t.height,!(p<f||g<c||h<y||t<d));return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?ve.set(ke,-r,0):ve.set(ke,o,0)):l<n&&(n=l,r<o?ve.set(Ce,r,0):ve.set(Ce,-o,0)),h<y||t<d?i<u&&(i=u,a<s?ve.set(ke,0,-a):ve.set(ke,0,s)):l<n&&(n=l,a<s?ve.set(Ce,0,a):ve.set(Ce,0,-s))),e&&ve.copy(e,m?Ce:ke),m},Ie.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},Ie.prototype.clone=function(){return new Ie(this.x,this.y,this.width,this.height)},Ie.prototype.copy=function(t){Ie.copy(this,t)},Ie.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},Ie.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},Ie.prototype.isZero=function(){return 0===this.width||0===this.height},Ie.create=function(t){return new Ie(t.x,t.y,t.width,t.height)},Ie.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},Ie.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(be.x=Me.x=e.x,be.y=Te.y=e.y,Se.x=Te.x=e.x+e.width,Se.y=Me.y=e.y+e.height,be.transform(n),Te.transform(n),Se.transform(n),Me.transform(n),t.x=xe(be.x,Se.x,Me.x,Te.x),t.y=xe(be.y,Se.y,Me.y,Te.y),o=we(be.x,Se.x,Me.x,Te.x),a=we(be.y,Se.y,Me.y,Te.y),t.width=o-t.x,t.height=a-t.y):t!==e&&Ie.copy(t,e)},Ie);function Ie(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}function Ae(){(function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0})(this.event)}n(Pe,Oe=Wt),Pe.prototype.dispose=function(){},Pe.prototype.setCursor=function(){};var Oe,Le=Pe;function Pe(){var t=null!==Oe&&Oe.apply(this,arguments)||this;return t.handler=null,t}var Re,Ne=function(t,e){this.x=t,this.y=e},Ee=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ze=new De(0,0,0,0),Be=(n(Fe,Re=Wt),Fe.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(R(Ee,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},Fe.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=He(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),i=this._hovered=i?new Ne(e,n):this.findHover(e,n),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},Fe.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},Fe.prototype.resize=function(){this._hovered=new Ne(0,0)},Fe.prototype.dispatch=function(t,e){t=this[t],t&&t.call(this,e)},Fe.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},Fe.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},Fe.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Ae};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},Fe.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new Ne(t,e);if(Ve(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new De(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(ze.copy(h.getBoundingRect()),h.transform&&ze.applyTransform(h.transform),ze.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(Ve(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},Fe.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new oe);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new Ne).target=i.target,this.dispatchToElement(n,e,i.event))},Fe);function Fe(t,e,n,i,r){var o=Re.call(this)||this;return o._hovered=new Ne(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new Le,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new Vt(o),o}function Ve(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=!!i.ignoreClip||o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0),a=i.__hostTarget,i=a||i.parent}return!r||"silent"}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),"silent"!==s)){e.target=a;break}}}function He(t,e,n){return t=t.painter,e<0||e>t.getWidth()||n<0||n>t.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Be.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=He(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||4<Ot(this._downPoint,[e.zrX,e.zrY]))return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));function We(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function Ge(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Ue(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function je(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a;a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function Xe(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<32)Ge(t,n,i,n+(o=We(t,n,i,e)),e);else{var a,s=function(t,e){var n,i,r=7,o=0,a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--,s=je(t[h],t,l,u,0,e);if(l+=s,0!=(u-=s)&&0!==(c=Ue(t[l+u-1],t,h,c,c-1,e)))if(u<=c){var p=l,d=u,f=(s=h,c),g=0;for(g=0;g<d;g++)a[g]=t[p+g];var y=0,m=s,v=p;if(t[v++]=t[m++],0==--f)for(g=0;g<d;g++)t[v+g]=a[y+g];else if(1===d){for(g=0;g<f;g++)t[v+g]=t[m+g];t[v+f]=a[y]}else{for(var _,x,w,b=r;;){x=_=0,w=!1;do{if(e(t[m],a[y])<0){if(t[v++]=t[m++],x++,(_=0)==--f){w=!0;break}}else if(t[v++]=a[y++],_++,x=0,1==--d){w=!0;break}}while((_|x)<b);if(w)break;do{if(0!==(_=je(t[m],a,y,d,0,e))){for(g=0;g<_;g++)t[v+g]=a[y+g];if(v+=_,y+=_,(d-=_)<=1){w=!0;break}}if(t[v++]=t[m++],0==--f){w=!0;break}if(0!==(x=Ue(a[y],t,m,f,0,e))){for(g=0;g<x;g++)t[v+g]=t[m+g];if(v+=x,m+=x,0===(f-=x)){w=!0;break}}if(t[v++]=a[y++],1==--d){w=!0;break}}while(b--,7<=_||7<=x);if(w)break;b<0&&(b=0),b+=2}if((r=b)<1&&(r=1),1===d){for(g=0;g<f;g++)t[v+g]=t[m+g];t[v+f]=a[y]}else{if(0===d)throw new Error;for(g=0;g<d;g++)t[v+g]=a[y+g]}}}else{var S=l,M=u,T=h,C=c,k=0;for(k=0;k<C;k++)a[k]=t[T+k];var D=S+M-1,I=C-1,A=T+C-1,O=0,L=0;if(t[A--]=t[D--],0==--M)for(O=A-(C-1),k=0;k<C;k++)t[O+k]=a[k];else if(1===C){for(L=1+(A-=M),O=1+(D-=M),k=M-1;0<=k;k--)t[L+k]=t[O+k];t[A]=a[I]}else{for(var P=r;;){var R=0,N=0,E=!1;do{if(e(a[I],t[D])<0){if(t[A--]=t[D--],R++,(N=0)==--M){E=!0;break}}else if(t[A--]=a[I--],N++,R=0,1==--C){E=!0;break}}while((R|N)<P);if(E)break;do{if(0!==(R=M-je(a[I],t,S,M,M-1,e))){for(M-=R,L=1+(A-=R),O=1+(D-=R),k=R-1;0<=k;k--)t[L+k]=t[O+k];if(0===M){E=!0;break}}if(t[A--]=a[I--],1==--C){E=!0;break}if(0!==(N=C-Ue(t[D],a,0,C,C-1,e))){for(C-=N,L=1+(A-=N),O=1+(I-=N),k=0;k<N;k++)t[L+k]=a[O+k];if(C<=1){E=!0;break}}if(t[A--]=t[D--],0==--M){E=!0;break}}while(P--,7<=R||7<=N);if(E)break;P<0&&(P=0),P+=2}if((r=P)<1&&(r=1),1===C){for(L=1+(A-=M),O=1+(D-=M),k=M-1;0<=k;k--)t[L+k]=t[O+k];t[A]=a[I]}else{if(0===C)throw new Error;for(O=A-(C-1),k=0;k<C;k++)t[O+k]=a[k]}}}}return n=[],i=[],{mergeRuns:function(){for(;1<o;){var t=o-2;if(1<=t&&i[t-1]<=i[t]+i[t+1]||2<=t&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;1<o;){var t=o-2;0<t&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}(t,e),l=function(t){for(var e=0;32<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=We(t,n,i,e))<l&&(Ge(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var Ye=!1;function qe(){Ye||(Ye=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Ze(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Ke.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Ke.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Ke.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Xe(n,Ze)},Ke.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=1),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else i=t,e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(qe(),i.z=0),isNaN(i.z2)&&(qe(),i.z2=0),isNaN(i.zlevel)&&(qe(),i.zlevel=0),this._displayList[this._displayListLen++]=i;i=t.getDecalElement&&t.getDecalElement(),i&&this._updateAndAddDisplayable(i,e,n),i=t.getTextGuideLine(),i&&this._updateAndAddDisplayable(i,e,n),i=t.getTextContent(),i&&this._updateAndAddDisplayable(i,e,n)}},Ke.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Ke.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=A(this._roots,t);0<=i&&this._roots.splice(i,1)}},Ke.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Ke.prototype.getRoots=function(){return this._roots},Ke.prototype.dispose=function(){this._displayList=null,this._roots=null};var $e=Ke;function Ke(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Ze}var Qe=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Je={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-Je.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Je.bounceIn(2*t):.5*Je.bounceOut(2*t-1)+.5}},tn=Math.pow,en=Math.sqrt,nn=en(3),rn=bt(),on=bt(),an=bt();function sn(t){return-1e-8<t&&t<1e-8}function ln(t){return 1e-8<t||t<-1e-8}function un(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function hn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function cn(t,e,n,i,r,o){i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t-=r,r=n*n-3*i*e;var a,s,l=n*e-9*i*t,u=(t=e*e-3*n*t,0);return sn(r)&&sn(l)?sn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):sn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=en(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-tn(-t,1/3):tn(t,1/3))+(e=e<0?-tn(-e,1/3):tn(e,1/3))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*en(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=en(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+nn*Math.sin(e)))/(3*i),r=(-n+l*(t-nn*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function pn(t,e,n,i,r){var o,a=6*n-12*e+6*t;i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return sn(i)?ln(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):sn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=en(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function dn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(e=(n-e)*r+e,n=(i-n)*r+n,(e-a)*r+a);e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function fn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;rn[0]=l,rn[1]=u;for(var m=0;m<1;m+=.05)on[0]=un(t,n,r,a,m),on[1]=un(e,i,o,s,m),(d=Pt(rn,on))<y&&(c=m,y=d);y=1/0;for(var v=0;v<32&&!(g<1e-4);v++)p=c+g,on[0]=un(t,n,r,a,f=c-g),on[1]=un(e,i,o,s,f),d=Pt(on,rn),0<=f&&d<y?(c=f,y=d):(an[0]=un(t,n,r,a,p),an[1]=un(e,i,o,s,p),f=Pt(an,rn),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=un(t,n,r,a,c),h[1]=un(e,i,o,s,c)),en(y)}function gn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function yn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function mn(t,e,n){return n=t+n-2*e,0==n?.5:(t-e)/n}function vn(t,e,n,i,r){var o=(e-t)*i+t;e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function _n(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;rn[0]=a,rn[1]=s;for(var p=0;p<1;p+=.05)on[0]=gn(t,n,r,p),on[1]=gn(e,i,o,p),(y=Pt(rn,on))<c&&(u=p,c=y);c=1/0;for(var d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(on[0]=gn(t,n,r,f),on[1]=gn(e,i,o,f),Pt(on,rn));0<=f&&y<c?(u=f,c=y):(an[0]=gn(t,n,r,g),an[1]=gn(e,i,o,g),f=Pt(an,rn),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=gn(t,n,r,u),l[1]=gn(e,i,o,u)),en(c)}var xn=/cubic-bezier\(([0-9,\.e ]+)\)/;function wn(t){if(t=t&&xn.exec(t),t){t=t[1].split(",");var e,n=+ot(t[0]),i=+ot(t[1]),r=+ot(t[2]),o=+ot(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:cn(0,n,r,1,t,e)&&un(0,i,o,1,e[0])}}}Sn.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc);o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},Sn.prototype.pause=function(){this._paused=!0},Sn.prototype.resume=function(){this._paused=!1},Sn.prototype.setEasing=function(t){this.easing=t,this.easingFunc=W(t)?t:Je[t]||wn(t)};var bn=Sn;function Sn(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||_t,this.ondestroy=t.ondestroy||_t,this.onrestart=t.onrestart||_t,t.easing&&this.setEasing(t.easing)}var Mn=function(t){this.value=t},Tn=(Cn.prototype.insert=function(t){return t=new Mn(t),this.insertEntry(t),t},Cn.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Cn.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Cn.prototype.len=function(){return this._len},Cn.prototype.clear=function(){this.head=this.tail=null,this._len=0},Cn);function Cn(){this._len=0}Dn.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Mn(e),n.key=t,r.insertEntry(n),o[t]=n),a},Dn.prototype.get=function(t){t=this._map[t];var e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},Dn.prototype.clear=function(){this._list.clear(),this._map={}},Dn.prototype.len=function(){return this._list.len()};var kn=Dn;function Dn(t){this._list=new Tn,this._maxSize=10,this._map={},this._maxSize=t}var In={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function An(t){return(t=Math.round(t))<0?0:255<t?255:t}function On(t){return t<0?0:1<t?1:t}function Ln(t){return t.length&&"%"===t.charAt(t.length-1)?An(parseFloat(t)/100*255):An(parseInt(t,10))}function Pn(t){return t.length&&"%"===t.charAt(t.length-1)?On(parseFloat(t)/100):On(parseFloat(t))}function Rn(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function Nn(t,e,n){return t+(e-t)*n}function En(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function zn(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Bn=new kn(20),Fn=null;function Vn(t,e){Fn&&zn(Fn,e),Fn=Bn.put(t,Fn||e.slice())}function Hn(t,e){if(t){e=e||[];var n=Bn.get(t);if(n)return zn(e,n);if(n=(t+="").replace(/ /g,"").toLowerCase(),n in In)return zn(e,In[n]),Vn(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(En(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),Vn(t,e),e):void En(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(En(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),Vn(t,e),e):void En(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){i=n.substr(0,r);var a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?En(e,+a[0],+a[1],+a[2],1):En(e,0,0,0,1);s=Pn(a.pop());case"rgb":return 3<=a.length?(En(e,Ln(a[0]),Ln(a[1]),Ln(a[2]),3===a.length?s:Pn(a[3])),Vn(t,e),e):void En(e,0,0,0,1);case"hsla":return 4!==a.length?void En(e,0,0,0,1):(a[3]=Pn(a[3]),Wn(a,e),Vn(t,e),e);case"hsl":return 3!==a.length?void En(e,0,0,0,1):(Wn(a,e),Vn(t,e),e);default:return}}En(e,0,0,0,1)}}function Wn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Pn(t[1]),r=Pn(t[2]);i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return En(e=e||[],An(255*Rn(r,i,n+1/3)),An(255*Rn(r,i,n)),An(255*Rn(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Gn(t,e){var n=Hn(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return qn(n,4===n.length?"rgba":"rgb")}}function Un(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t*=e.length-1,i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=An(Nn(r[0],e[0],o=t-i)),n[1]=An(Nn(r[1],e[1],o)),n[2]=An(Nn(r[2],e[2],o)),n[3]=On(Nn(r[3],e[3],o)),n}var jn=Un;function Xn(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t*=e.length-1,i=Math.floor(t),r=Math.ceil(t),a=Hn(e[i]),e=Hn(e[r]),a=qn([An(Nn(a[0],e[0],o=t-i)),An(Nn(a[1],e[1],o)),An(Nn(a[2],e[2],o)),On(Nn(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var Yn=Xn;function qn(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function Zn(t,e){return t=Hn(t),t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var $n=new kn(100);function Kn(t){var e;return G(t)?((e=$n.get(t))||(e=Gn(t,-.1),$n.put(t,e)),e):$(t)?((e=k({},t)).colorStops=N(t.colorStops,(function(t){return{offset:t.offset,color:Gn(t.color,-.1)}})),e):t}jn=Object.freeze({__proto__:null,fastLerp:Un,fastMapToColor:jn,lerp:Xn,lift:Gn,liftColor:Kn,lum:Zn,mapToColor:Yn,modifyAlpha:function(t,e){if((t=Hn(t))&&null!=e)return t[3]=On(e),qn(t,"rgba")},modifyHSL:function(t,e,n,i){var r=Hn(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=Pn(n)),null!=i&&(r[2]=Pn(i)),qn(Wn(r),"rgba")},parse:Hn,random:function(){return qn([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:qn,toHex:function(t){if(t=Hn(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}}),r.hasGlobalWindow&&W(window.btoa);var Qn=Array.prototype.slice;function Jn(t,e,n){return(e-t)*n+t}function ti(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Jn(e[o],n[o],i);return t}function ei(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function ni(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function ii(t){if(P(t)){var e=t.length;if(P(t[0])){for(var n=[],i=0;i<e;i++)n.push(Qn.call(t[i]));return n}return Qn.call(t)}return t}function ri(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function oi(t){return 4===t||5===t}function ai(t){return 1===t||2===t}var si=[0,0,0,0],li=(ui.prototype.isFinished=function(){return this._finished},ui.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},ui.prototype.needsAnimate=function(){return 1<=this.keyframes.length},ui.prototype.getAdditiveTrack=function(){return this._additiveTrack},ui.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(P(e)?(1==(s=i=P((i=e)&&i[0])?2:1)&&!j(e[0])||2==i&&!j(e[0][0]))&&(a=!0):j(e)&&!J(e)?s=0:G(e)?isNaN(+e)?(i=Hn(e))&&(l=i,s=3):s=0:$(e)&&((u=k({},l)).colorStops=N(e.colorStops,(function(t){return{offset:t.offset,color:Hn(t.color)}})),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=W(n)?n:Je[n]||wn(n)),r.push(u),u},ui.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort((function(t,e){return t.time-e.time})),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=ai(i),l=oi(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=g=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){h=g.length;var m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Qn.call(y[v]));var _=g[0]&&g[0].length;for(v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=h=d=void 0;d=c.colorStops,h=p.colorStops;for(var w=d.length,b=h.length,S=b<w?h:d,M=(h=Math.min(w,b),S[h-1]||{color:[0,0,0,0],offset:0}),T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var C=n[0].value;for(u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=ei([],n[u].value,C,-1):ai(i)&&(n[u].additiveValue=(1===i?ei:ni)([],n[u].value,C,-1))}},ui.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?si:t[c],(ai(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:ai(l)?(1===l?ti:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Jn(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):oi(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Jn(d.x,o.x,r),y:Jn(d.y,o.y,r),colorStops:N(d.colorStops,(function(t,e){return e=o.colorStops[e],{offset:Jn(t.offset,e.offset,r),color:ri(ti([],t.color,e.color,r))}})),global:o.global},l?(t[c].x2=Jn(d.x2,o.x2,r),t[c].y2=Jn(d.y2,o.y2,r)):t[c].r=Jn(d.r,o.r,r)):p?(ti(f,n[s],i[s],r),a||(t[c]=ri(f))):(l=Jn(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},ui.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(Hn(t[n],si),ei(si,si,i,1),t[n]=ri(si)):1===e?ei(t[n],t[n],i,1):2===e&&ni(t[n],t[n],i,1)},ui);function ui(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}ci.prototype.getMaxTime=function(){return this._maxTime},ci.prototype.getDelay=function(){return this._delay},ci.prototype.getLoop=function(){return this._loop},ci.prototype.getTarget=function(){return this._target},ci.prototype.changeTarget=function(t){this._target=t},ci.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,B(e),n)},ci.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new li(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&ri(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,ii(u),i),this._trackKeys.push(a)}l.addKeyframe(t,ii(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},ci.prototype.pause=function(){this._clip.pause(),this._paused=!0},ci.prototype.resume=function(){this._clip.resume(),this._paused=!1},ci.prototype.isPaused=function(){return!!this._paused},ci.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},ci.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},ci.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},ci.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},ci.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},ci.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,n=this,i=[],r=this._maxTime||0,o=0;o<this._trackKeys.length;o++){var a=this._trackKeys[o],s=this._tracks[a],l=(a=this._getAdditiveTrack(a),s.keyframes),u=l.length;s.prepare(r,a),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((a=l[u-1])&&(n._target[s.propName]=a.rawValue),s.setFinished()):i.push(s))}return i.length||this._force?(e=new bn({life:r,loop:this._loop,delay:this._delay||0,onframe:function(t){n._started=2;var e=n._additiveAnimators;if(e){for(var r=!1,o=0;o<e.length;o++)if(e[o]._clip){r=!0;break}r||(n._additiveAnimators=null)}for(o=0;o<i.length;o++)i[o].step(n._target,t);var a=n._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](n._target,t)},ondestroy:function(){n._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},ci.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},ci.prototype.delay=function(t){return this._delay=t,this},ci.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},ci.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},ci.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},ci.prototype.getClip=function(){return this._clip},ci.prototype.getTrack=function(t){return this._tracks[t]},ci.prototype.getTracks=function(){var t=this;return N(this._trackKeys,(function(e){return t._tracks[e]}))},ci.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},ci.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=ii(o.rawValue))}}},ci.prototype.__changeFinalValue=function(t,e){e=e||B(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var hi=ci;function ci(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?M("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function pi(){return(new Date).getTime()}n(gi,di=Wt),gi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},gi.prototype.addAnimator=function(t){t.animation=this,t=t.getClip(),t&&this.addClip(t)},gi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},gi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},gi.prototype.update=function(t){for(var e=pi()-this._pausedTime,n=e-this._time,i=this._head;i;){var r=i.next;i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r)}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},gi.prototype._startLoop=function(){var t=this;this._running=!0,Qe((function e(){t._running&&(Qe(e),t._paused||t.update())}))},gi.prototype.start=function(){this._running||(this._time=pi(),this._pausedTime=0,this._startLoop())},gi.prototype.stop=function(){this._running=!1},gi.prototype.pause=function(){this._paused||(this._pauseStart=pi(),this._paused=!0)},gi.prototype.resume=function(){this._paused&&(this._pausedTime+=pi()-this._pauseStart,this._paused=!1)},gi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},gi.prototype.isFinished=function(){return null==this._head},gi.prototype.animate=function(t,e){return e=e||{},this.start(),t=new hi(t,e.loop),this.addAnimator(t),t};var di,fi=gi;function gi(t){var e=di.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var yi,mi=r.domSupported,vi=(yi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Yn=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:N(Yn,(function(t){var e=t.replace("mouse","pointer");return yi.hasOwnProperty(e)?e:t}))}),_i=["mousemove","mouseup"],xi=["pointermove","pointerup"],wi=!1;function bi(t){return t=t.pointerType,"pen"===t||"touch"===t}function Si(t){t&&(t.zrByTouch=!0)}function Mi(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var Ti=function(t,e){this.stopPropagation=_t,this.stopImmediatePropagation=_t,this.preventDefault=_t,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Ci={mousedown:function(t){t=re(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=re(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=re(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Mi(this,(t=re(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){wi=!0,t=re(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){wi||(t=re(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){Si(t=re(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Ci.mousemove.call(this,t),Ci.mousedown.call(this,t)},touchmove:function(t){Si(t=re(this.dom,t)),this.handler.processGesture(t,"change"),Ci.mousemove.call(this,t)},touchend:function(t){Si(t=re(this.dom,t)),this.handler.processGesture(t,"end"),Ci.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Ci.click.call(this,t)},pointerdown:function(t){Ci.mousedown.call(this,t)},pointermove:function(t){bi(t)||Ci.mousemove.call(this,t)},pointerup:function(t){Ci.mouseup.call(this,t)},pointerout:function(t){bi(t)||Ci.mouseout.call(this,t)}},ki=(R(["click","dblclick","contextmenu"],(function(t){Ci[t]=function(e){e=re(this.dom,e),this.trigger(t,e)}})),{pointermove:function(t){bi(t)||ki.mousemove.call(this,t)},pointerup:function(t){ki.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function Di(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function Ii(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var Ai,Oi=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Li=(n(Pi,Ai=Wt),Pi.prototype.dispose=function(){Ii(this._localHandlerScope),mi&&Ii(this._globalHandlerScope)},Pi.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},Pi.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,mi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?function(t,e){function n(n){Di(e,n,(function(i){var r;i=ie(i),Mi(t,i.target)||(r=i,i=re(t.dom,new Ti(t,r),!0),e.domHandlers[n].call(t,i))}),{capture:!0})}r.pointerEventsSupported?R(xi,n):r.touchEventsSupported||R(_i,n)}(this,e):Ii(e))},Pi);function Pi(t,e){var n=Ai.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new Oi(t,Ci),mi&&(n._globalHandlerScope=new Oi(document,ki)),function(t,e){var n=e.domHandlers;r.pointerEventsSupported?R(vi.pointer,(function(i){Di(e,i,(function(e){n[i].call(t,e)}))})):(r.touchEventsSupported&&R(vi.touch,(function(i){Di(e,i,(function(r){var o;n[i].call(t,r),(o=e).touching=!0,null!=o.touchTimer&&(clearTimeout(o.touchTimer),o.touchTimer=null),o.touchTimer=setTimeout((function(){o.touching=!1,o.touchTimer=null}),700)}))})),R(vi.mouse,(function(i){Di(e,i,(function(r){r=ie(r),e.touching||n[i].call(t,r)}))})))}(n,n._localHandlerScope),n}Yn=1;var Ri=Yn=r.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):Yn,Ni="#333",Ei="#ccc",zi=he;function Bi(t){return 5e-5<t||t<-5e-5}var Fi=[],Vi=[],Hi=ue(),Wi=Math.abs,Gi=(Ui.prototype.getLocalTransform=function(t){return Ui.getLocalTransform(this,t)},Ui.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},Ui.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},Ui.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},Ui.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},Ui.prototype.needLocalTransform=function(){return Bi(this.rotation)||Bi(this.x)||Bi(this.y)||Bi(this.scaleX-1)||Bi(this.scaleY-1)||Bi(this.skewX)||Bi(this.skewY)},Ui.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||ue(),e?this.getLocalTransform(n):zi(n),t&&(e?pe(n,t,n):ce(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(zi(n),this.invTransform=null)},Ui.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(Fi),n=((Fi[1]-(n=Fi[1]<0?-1:1))*i+n)/Fi[1]||0,t[0]*=i=((Fi[0]-(e=Fi[0]<0?-1:1))*i+e)/Fi[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||ue(),ye(this.invTransform,t)},Ui.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},Ui.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},Ui.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||ue(),pe(Vi,e.invTransform,t),t=Vi),e=this.originX,n=this.originY,(e||n)&&(Hi[4]=e,Hi[5]=n,pe(Vi,t,Hi),Vi[4]-=e,Vi[5]-=n,t=Vi),this.setLocalTransform(t))},Ui.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},Ui.prototype.transformCoordToLocal=function(t,e){return t=[t,e],e=this.invTransform,e&&Nt(t,t,e),t},Ui.prototype.transformCoordToGlobal=function(t,e){return t=[t,e],e=this.transform,e&&Nt(t,t,e),t},Ui.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<Wi(t[0]-1)&&1e-10<Wi(t[3]-1)?Math.sqrt(Wi(t[0]*t[3]-t[2]*t[1])):1},Ui.prototype.copyTransform=function(t){for(var e=t,n=0;n<ji.length;n++){var i=ji[n];this[i]=e[i]}},Ui.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0;t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&fe(e,e,l),e[4]+=n+u,e[5]+=i+h,e},Ui.initDefaultProps=((Yn=Ui.prototype).scaleX=Yn.scaleY=Yn.globalScaleRatio=1,void(Yn.x=Yn.y=Yn.originX=Yn.originY=Yn.skewX=Yn.skewY=Yn.rotation=Yn.anchorX=Yn.anchorY=0)),Ui);function Ui(){}var ji=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"],Xi={};function Yi(t,e){var n=Xi[e=e||l],i=(n=n||(Xi[e]=new kn(500))).get(t);return null==i&&(i=h.measureText(t,e).width,n.put(t,i)),i}function qi(t,e,n,i){return t=Yi(t,e),e=Qi(e),n=$i(0,t,n),i=Ki(0,e,i),new De(n,i,t,e)}function Zi(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return qi(r[0],e,n,i);for(var o=new De(0,0,0,0),a=0;a<r.length;a++){var s=qi(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function $i(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Ki(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Qi(t){return Yi("国",t)}function Ji(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function tr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=Ji(i[0],n.width),u+=Ji(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var er,nr="__zr_normal__",ir=ji.concat(["ignore"]),rr=E(ji,(function(t,e){return t[e]=!0,t}),{ignore:!1}),or={},ar=new De(0,0,0,0);sr.prototype._init=function(t){this.attr(t)},sr.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},sr.prototype.beforeUpdate=function(){},sr.prototype.afterUpdate=function(){},sr.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},sr.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=ar,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(or,t,u):tr(or,t,u),e.x=or.x,e.y=or.y,n=or.align,i=or.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=Ji(o[0],u.width),Ji(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=1,r&&c.dirtyStyle(!0))},sr.prototype.canBeInsideText=function(){return!0},sr.prototype.getInsideTextFill=function(){return"#fff"},sr.prototype.getInsideTextStroke=function(t){return"#000"},sr.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Ei:Ni},sr.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&Hn(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,qn(n,"rgba")},sr.prototype.traverse=function(t,e){},sr.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},sr.prototype.hide=function(){this.ignore=!0,this.markRedraw()},sr.prototype.show=function(){this.ignore=!1,this.markRedraw()},sr.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(X(t))for(var n=B(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},sr.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==nr||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},sr.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,ir)},sr.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},sr.prototype.hasState=function(){return 0<this.currentStates.length},sr.prototype.getState=function(t){return this.states[t]},sr.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},sr.prototype.clearStates=function(t){this.useState(nr,!1,t)},sr.prototype.useState=function(t,e,n,i){var r=t===nr,o=this.hasState();if(o||!r){o=this.currentStates;var a,s=this.stateTransition;if(!(0<=A(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),a;M("State "+t+" not exists.")}}},sr.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],c=(h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),this.stateTransition);n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}}else this.clearStates()},sr.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},sr.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},sr.prototype.removeState=function(t){var e;t=A(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},sr.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=(t=A(i,t),0<=A(i,e));0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},sr.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},sr.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];k(n,r),r.textConfig&&k(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},sr.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=k({},(i?this:n).textConfig),k(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<ir.length;u++){var h=ir[u],c=r&&rr[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},sr.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},sr.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},sr.prototype.getClipPath=function(){return this._clipPath},sr.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},sr.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},sr.prototype.getTextContent=function(){return this._textContent},sr.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Gi,this._attachComponent(t),this._textContent=t,this.markRedraw())},sr.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},sr.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},sr.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},sr.prototype.getTextGuideLine=function(){return this._textGuide},sr.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},sr.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},sr.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},sr.prototype.dirty=function(){this.markRedraw()},sr.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},sr.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},sr.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},sr.prototype.animate=function(t,e,n){var i=t?this[t]:this;i=new hi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},sr.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=A(e,t);0<=n&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},sr.prototype.updateDuringAnimation=function(t){this.markRedraw()},sr.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},sr.prototype.animateTo=function(t,e,n){ur(this,t,e,n)},sr.prototype.animateFrom=function(t,e,n){ur(this,t,e,n,!0)},sr.prototype._transitionState=function(t,e,n,i){for(var r=ur(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},sr.prototype.getBoundingRect=function(){return null},sr.prototype.getPaintRect=function(){return null},sr.initDefaultProps=((er=sr.prototype).type="element",er.name="",er.ignore=er.silent=er.isGroup=er.draggable=er.dragging=er.ignoreClip=er.__inHover=!1,er.__dirty=1,void(Object.defineProperty&&(lr("position","_legacyPos","x","y"),lr("scale","_legacyScale","scaleX","scaleY"),lr("origin","_legacyOrigin","originX","originY")))),Yn=sr;function sr(t){this.id=S++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function lr(t,e,n,i){function r(t,e){Object.defineProperty(e,0,{get:function(){return t[n]},set:function(e){t[n]=e}}),Object.defineProperty(e,1,{get:function(){return t[i]},set:function(e){t[i]=e}})}Object.defineProperty(er,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function ur(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(function t(e,n,i,r,o,a,s,l){for(var u=B(r),h=o.duration,c=o.delay,p=o.additive,d=o.setToFinal,f=!X(a),g=e.animators,y=[],m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!X(_)||P(_)||$(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=A(g,S),g.splice(w,1));if(o.force||(y=z(y,(function(t){return!function(t,e){return t===e||P(t)&&P(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}(r[t],i[t])})),x=y.length),0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l)for(T={},d&&(M={}),b=0;b<x;b++)v=y[b],T[v]=i[v],d?M[v]=r[v]:i[v]=r[v];else if(d)for(C={},b=0;b<x;b++)v=y[b],C[v]=ii(i[v]),cr(i,r,v);(S=new hi(i,!1,!1,p?z(g,(function(t){return t.targetName===n})):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during((function(t,e){n.during(e)}));for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function hr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function cr(t,e,n){if(P(e[n]))if(P(t[n])||(t[n]=[]),q(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),hr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(P(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?hr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else hr(o,r,a);o.length=r.length}else t[n]=e[n]}L(Yn,Wt),L(Yn,Gi),n(fr,pr=Yn),fr.prototype.childrenRef=function(){return this._children},fr.prototype.children=function(){return this._children.slice()},fr.prototype.childAt=function(t){return this._children[t]},fr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},fr.prototype.childCount=function(){return this._children.length},fr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},fr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},fr.prototype.replace=function(t,e){return t=A(this._children,t),0<=t&&this.replaceAt(e,t),this},fr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},fr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},fr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=A(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},fr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},fr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},fr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},fr.prototype.addSelfToZr=function(t){pr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},fr.prototype.removeSelfFromZr=function(t){pr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},fr.prototype.getBoundingRect=function(t){for(var e=new De(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(De.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var pr,dr=fr;function fr(t){var e=pr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}dr.prototype.type="group";var gr={},yr={};_r.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},_r.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},_r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},_r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return Zn(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Zn(e[r].color,1);return(n/=i)<.4}}return!1}(t))},_r.prototype.getBackgroundColor=function(){return this._backgroundColor},_r.prototype.setDarkMode=function(t){this._darkMode=t},_r.prototype.isDarkMode=function(){return this._darkMode},_r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},_r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},_r.prototype.flush=function(){this._disposed||this._flush(!1)},_r.prototype._flush=function(t){var e,n=pi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),t=pi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},_r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},_r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},_r.prototype.refreshHover=function(){this._needsRefreshHover=!0},_r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},_r.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},_r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},_r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},_r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},_r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},_r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},_r.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},_r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},_r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},_r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof dr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},_r.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete yr[t])};var mr,vr=_r;function _r(t,e,n){var i,o=this,a=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new $e),s=n.renderer||"canvas",l=(s=(gr[s]||(s=B(gr)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new gr[s](e,a,n,t)),e=n.ssr||s.ssrOnly,t=(this.storage=a,this.painter=s,r.node||r.worker||e?null:new Li(s.getViewportRoot(),s.root)),n.useCoarsePointer);(null==l||"auto"===l?r.touchEventsSupported:!!l)&&(i=tt(n.pointerSize,44)),this.handler=new Be(a,s,t,s.root,i),this.animation=new fi({stage:{update:e?null:function(){return o._flush(!0)}}}),e||this.animation.start()}function xr(t,e){return t=new vr(S++,t,e),yr[t.id]=t}function wr(t,e){gr[t]=e}function br(t){mr=t}var Sr=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in yr)yr.hasOwnProperty(t)&&yr[t].dispose();yr={}},getElementSSRData:function(t){if("function"==typeof mr)return mr(t)},getInstance:function(t){return yr[t]},init:xr,registerPainter:wr,registerSSRDataGetter:br,version:"5.5.0"});function Mr(t,e,n,i){var r=e[0],o=(e=e[1],n[0]),a=(n=n[1],e-r),s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function Tr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return G(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function Cr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function kr(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return Dr(t)}function Dr(t){t=t.toString().toLowerCase();var e=t.indexOf("e"),n=0<e?+t.slice(e+1):0;e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function Ir(t,e){var n=Math.log,i=Math.LN10;t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function Ar(t,e){var n=E(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===n)return[];for(var i=Math.pow(10,e),r=(e=N(t,(function(t){return(isNaN(t)?0:t)/n*i*100})),100*i),o=N(e,(function(t){return Math.floor(t)})),a=E(o,(function(t,e){return t+e}),0),s=N(e,(function(t,e){return t-o[e]}));a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return N(o,(function(t){return t/i}))}var Or=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Lr(t){var e,n;return t instanceof Date?t:G(t)?(e=Or.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function Pr(t){return Math.pow(10,Rr(t))}function Rr(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function Nr(t,e){var n=Rr(t),i=Math.pow(10,n),r=t/i;e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function Er(t){var e=parseFloat(t);return e==t&&(0!==e||!G(t)||t.indexOf("x")<=0)?e:NaN}function zr(t){return!isNaN(Er(t))}function Br(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function Fr(t){throw new Error(t)}function Vr(t,e,n){return(e-t)*n+t}var Hr="series\0";function Wr(t){return t instanceof Array?t:null==t?[]:[t]}function Gr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var Ur=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function jr(t){return!X(t)||H(t)||t instanceof Date?t:t.value}function Xr(t,e,n){return e=qr(e[t],null),n=qr(n[t],null),null!=e&&null!=n&&e===n}function Yr(t){return qr(t,"")}function qr(t,e){return null==t?e:G(t)?t:j(t)||U(t)?t+"":e}function Zr(t){return t=t.name,!(!t||!t.indexOf(Hr))}function $r(t){return t&&null!=t.id&&0===Yr(t.id).indexOf("\0_ec_\0")}function Kr(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?H(e.dataIndex)?N(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?H(e.name)?N(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function Qr(){var t="__ec_inner_"+Jr++;return function(e){return e[t]||(e[t]={})}}var Jr=Math.round(9*Math.random());function to(t,e,n){e=eo(e,n);var i=e.mainTypeSpecified,r=e.queryOptionMap,o=e.others,a=n?n.defaultMainType:null;return!i&&a&&r.set(a,{}),r.each((function(e,i){e=io(t,i,e,{useDefault:a===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone}),o[i+"Models"]=e.models,o[i+"Model"]=e.models[0]})),o}function eo(t,e){var n=G(t)?((n={})[t+"Index"]=0,n):t,i=ft(),r={},o=!1;return R(n,(function(t,n){var a;"dataIndex"===n||"dataIndexInside"===n?r[n]=t:(a=(n=n.match(/^(\w+)(Index|Id|Name)$/)||[])[1],n=(n[2]||"").toLowerCase(),!a||!n||e&&e.includeMainTypes&&A(e.includeMainTypes,a)<0||(o=o||!!a,(i.get(a)||i.set(a,{}))[n]=t))})),{mainTypeSpecified:o,queryOptionMap:i,others:r}}var no={useDefault:!0,enableAll:!1,enableNone:!1};function io(t,e,n,i){i=i||no;var r=n.index,o=n.id,a=(n=n.name,{models:null,specified:null!=r||null!=o||null!=n});return a.specified?"none"===r||!1===r?(rt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(rt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function ro(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var oo="___EC__COMPONENT__CONTAINER___",ao="___EC__EXTENDED_CLASS___";function so(t){var e={main:"",sub:""};return t&&(t=t.split("."),e.main=t[0]||"",e.sub=t[1]||""),e}function lo(t){(t.$constructor=t).extend=function(t){var e,i,r,o=this;function a(){return i.apply(this,arguments)||this}return W(r=o)&&/^class\s/.test(Function.prototype.toString.call(r))?(n(a,i=o),e=a):O(e=function(){(t.$constructor||o).apply(this,arguments)},this),k(e.prototype,t),e[ao]=!0,e.extend=this.extend,e.superCall=co,e.superApply=po,e.superClass=o,e}}function uo(t,e){t.extend=e.extend}var ho=Math.round(10*Math.random());function co(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function po(t,e,n){return this.superClass.prototype[e].apply(t,n)}function fo(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;return i&&(rt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),(n=so(t.prototype.type=i)).sub?n.sub!==oo&&((function(t){var n=e[t.main];return n&&n[oo]||(n=e[t.main]={___EC__COMPONENT__CONTAINER___:!0}),n}(n))[n.sub]=t):e[n.main]=t),t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[oo]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=so(t);var n=[];t=e[t.main];return t&&t[oo]?R(t,(function(t,e){e!==oo&&n.push(t)})):n.push(t),n},t.hasClass=function(t){return t=so(t),!!e[t.main]},t.getAllClassMainTypes=function(){var t=[];return R(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){return t=so(t),t=e[t.main],t&&t[oo]}}function go(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];i&&0<=A(i,s)||r&&A(r,s)<0||null!=(s=n.getShallow(s,e))&&(o[t[a][0]]=s)}return o}}var yo=go([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),mo=(vo.prototype.getAreaStyle=function(t,e){return yo(this,t,e)},vo);function vo(){}var _o=new kn(50);function xo(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=_o.get(t))?bo(e=i.image)||i.pending.push(n):((e=h.loadImage(t,wo,wo)).__zrImageSrc=t,_o.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function wo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function bo(t){return t&&t.width&&t.height}var So=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Mo(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=To(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=Co(o[a],r);return o.join("\n")}function To(t,e,n,i){for(var r=k({},i=i||{}),o=(r.font=e,n=tt(n,"..."),r.maxIterations=tt(i.maxIterations,2),r.minChar=tt(i.minChar,0)),a=(r.cnCharWidth=Yi("国",e),r.ascCharWidth=Yi("a",e)),s=(r.placeholder=tt(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;return i=Yi(n,e),s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function Co(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=Yi(t,i);if(!(o<=n)){for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<o?Math.floor(t.length*r/o):0;o=Yi(t=t.substr(0,s),i)}""===t&&(t=e.placeholder)}return t}var ko=function(){},Do=function(t){this.tokens=[],t&&(this.tokens=t)},Io=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function Ao(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=Ji(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=Lo(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new ko;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:Yi(g,h),p||c?u.push(new Do([y])):1===(f=(d=(u[u.length-1]||(u[0]=new Do)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var Oo=E(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function Lo(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=Yi(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||Oo[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var Po,Ro="__zr_style_"+Math.round(10*Math.random()),No={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Eo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},zo=(No[Ro]=!0,["z","z2","invisible"]),Bo=["invisible"];n(Fo,Po=Yn),Fo.prototype._init=function(t){for(var e=B(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):Po.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},Fo.prototype.beforeBrush=function(){},Fo.prototype.afterBrush=function(){},Fo.prototype.innerBeforeBrush=function(){},Fo.prototype.innerAfterBrush=function(){},Fo.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){return Vo.copy(t.getBoundingRect()),t.transform&&Vo.applyTransform(t.transform),Ho.width=e,Ho.height=n,!Vo.intersect(Ho)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},Fo.prototype.contain=function(t,e){return this.rectContain(t,e)},Fo.prototype.traverse=function(t,e){t.call(e,this)},Fo.prototype.rectContain=function(t,e){return t=this.transformCoordToLocal(t,e),this.getBoundingRect().contain(t[0],t[1])},Fo.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new De(0,0,0,0)),r?De.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},Fo.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new De(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},Fo.prototype.getPrevPaintRect=function(){return this._prevPaintRect},Fo.prototype.animateStyle=function(t){return this.animate("style",t)},Fo.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},Fo.prototype.attrKV=function(t,e){"style"!==t?Po.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},Fo.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:k(this.style,t),this.dirtyStyle(),this},Fo.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},Fo.prototype.dirty=function(){this.dirtyStyle()},Fo.prototype.styleChanged=function(){return!!(2&this.__dirty)},Fo.prototype.styleUpdated=function(){this.__dirty&=-3},Fo.prototype.createStyle=function(t){return yt(No,t)},Fo.prototype.useStyle=function(t){t[Ro]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},Fo.prototype.isStyleObject=function(t){return t[Ro]},Fo.prototype._innerSaveToNormal=function(t){Po.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,zo)},Fo.prototype._applyStateObj=function(t,e,n,i,r,o){Po.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=B(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);var c=B(a);for(h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);var d=this.__inHover?Bo:zo;for(h=0;h<d.length;h++)p=d[h],e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])},Fo.prototype._mergeStates=function(t){for(var e,n=Po.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},Fo.prototype._mergeStyle=function(t,e){return k(t,e),t},Fo.prototype.getAnimationStyleProps=function(){return Eo},Fo.initDefaultProps=((Yn=Fo.prototype).type="displayable",Yn.invisible=!1,Yn.z=0,Yn.z2=0,Yn.zlevel=0,Yn.culling=!1,Yn.cursor="pointer",Yn.rectHover=!1,Yn.incremental=!1,Yn._rect=null,Yn.dirtyRectTolerance=0,void(Yn.__dirty=3)),Yn=Fo;function Fo(t){return Po.call(this,t)||this}var Vo=new De(0,0,0,0),Ho=new De(0,0,0,0),Wo=Math.min,Go=Math.max,Uo=Math.sin,jo=Math.cos,Xo=2*Math.PI,Yo=bt(),qo=bt(),Zo=bt();function $o(t,e,n,i,r,o){r[0]=Wo(t,n),r[1]=Wo(e,i),o[0]=Go(t,n),o[1]=Go(e,i)}var Ko=[],Qo=[],Jo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},ta=[],ea=[],na=[],ia=[],ra=[],oa=[],aa=Math.min,sa=Math.max,la=Math.cos,ua=Math.sin,ha=Math.abs,ca=Math.PI,pa=2*ca,da="undefined"!=typeof Float32Array,fa=[];function ga(t){return Math.round(t/ca*1e8)/1e8%2*ca}function ya(t,e){var n=ga(t[0]),i=(n<0&&(n+=pa),n-t[0]),r=t[1];r+=i,!e&&pa<=r-n?r=n+pa:e&&pa<=n-r?r=n-pa:!e&&r<n?r=n+(pa-ga(n-r)):e&&n<r&&(r=n-(pa-ga(r-n))),t[0]=n,t[1]=r}va.prototype.increaseVersion=function(){this._version++},va.prototype.getVersion=function(){return this._version},va.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=ha(n/Ri/t)||0,this._uy=ha(n/Ri/e)||0)},va.prototype.setDPR=function(t){this.dpr=t},va.prototype.setContext=function(t){this._ctx=t},va.prototype.getContext=function(){return this._ctx},va.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},va.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},va.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Jo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},va.prototype.lineTo=function(t,e){var n=ha(t-this._xi),i=ha(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Jo.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},va.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Jo.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},va.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Jo.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},va.prototype.arc=function(t,e,n,i,r,o){return this._drawPendingPt(),fa[0]=i,fa[1]=r,ya(fa,o),this.addData(Jo.A,t,e,n,n,i=fa[0],(r=fa[1])-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=la(r)*n+t,this._yi=ua(r)*n+e,this},va.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},va.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Jo.R,t,e,n,i),this},va.prototype.closePath=function(){this._drawPendingPt(),this.addData(Jo.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},va.prototype.fill=function(t){t&&t.fill(),this.toStatic()},va.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},va.prototype.len=function(){return this._len},va.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!da||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},va.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();for(da&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n)),r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},va.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},va.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},va.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},va.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,da)&&11<this._len&&(this.data=new Float32Array(t))},va.prototype.getBoundingRect=function(){na[0]=na[1]=ra[0]=ra[1]=Number.MAX_VALUE,ia[0]=ia[1]=oa[0]=oa[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var s=e[a++],l=1===a;switch(l&&(r=n=e[a],o=i=e[a+1]),s){case Jo.M:n=r=e[a++],i=o=e[a++],ra[0]=r,ra[1]=o,oa[0]=r,oa[1]=o;break;case Jo.L:$o(n,i,e[a],e[a+1],ra,oa),n=e[a++],i=e[a++];break;case Jo.C:M=S=b=w=x=_=v=m=void 0;var u=n,h=i,c=e[a++],p=e[a++],d=e[a++],f=e[a++],g=e[a],y=e[a+1],m=ra,v=oa,_=pn,x=un,w=_(u,c,d,g,Ko);m[0]=1/0,m[1]=1/0,v[0]=-1/0,v[1]=-1/0;for(var b=0;b<w;b++){var S=x(u,c,d,g,Ko[b]);m[0]=Wo(S,m[0]),v[0]=Go(S,v[0])}for(w=_(h,p,f,y,Qo),b=0;b<w;b++){var M=x(h,p,f,y,Qo[b]);m[1]=Wo(M,m[1]),v[1]=Go(M,v[1])}m[0]=Wo(u,m[0]),v[0]=Go(u,v[0]),m[0]=Wo(g,m[0]),v[0]=Go(g,v[0]),m[1]=Wo(h,m[1]),v[1]=Go(h,v[1]),m[1]=Wo(y,m[1]),v[1]=Go(y,v[1]),n=e[a++],i=e[a++];break;case Jo.Q:_=n,F=i,O=e[a++],k=e[a++],A=e[a],T=e[a+1],I=ra,L=oa,t=D=t=C=void 0,C=gn,t=Go(Wo((D=mn)(_,O,A),1),0),D=Go(Wo(D(F,k,T),1),0),O=C(_,O,A,t),t=C(F,k,T,D),I[0]=Wo(_,A,O),I[1]=Wo(F,T,t),L[0]=Go(_,A,O),L[1]=Go(F,T,t),n=e[a++],i=e[a++];break;case Jo.A:var T,C=e[a++],k=e[a++],D=e[a++],I=e[a++],A=e[a++],O=e[a++]+A,L=(a+=1,!e[a++]),P=(l&&(r=la(A)*D+C,o=ua(A)*I+k),U=T=G=W=H=V=F=B=z=P=void 0,C),R=k,N=D,E=I,z=A,B=O,F=L,V=ra,H=oa,W=Et,G=zt;if((T=Math.abs(z-B))%Xo<1e-4&&1e-4<T)V[0]=P-N,V[1]=R-E,H[0]=P+N,H[1]=R+E;else{Yo[0]=jo(z)*N+P,Yo[1]=Uo(z)*E+R,qo[0]=jo(B)*N+P,qo[1]=Uo(B)*E+R,W(V,Yo,qo),G(H,Yo,qo),(z%=Xo)<0&&(z+=Xo),(B%=Xo)<0&&(B+=Xo),B<z&&!F?B+=Xo:z<B&&F&&(z+=Xo),F&&(T=B,B=z,z=T);for(var U=0;U<B;U+=Math.PI/2)z<U&&(Zo[0]=jo(U)*N+P,Zo[1]=Uo(U)*E+R,W(V,Zo,V),G(H,Zo,H))}n=la(O)*D+C,i=ua(O)*I+k;break;case Jo.R:$o(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],ra,oa);break;case Jo.Z:n=r,i=o}Et(na,na,ra),zt(ia,ia,oa)}return 0===a&&(na[0]=na[1]=ia[0]=ia[1]=0),new De(na[0],na[1],ia[0]-na[0],ia[1]-na[1])},va.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Jo.M:r=a=t[c++],o=s=t[c++];break;case Jo.L:var g=t[c++],y=(_=t[c++])-o;(ha(k=g-r)>n||ha(y)>i||c===e-1)&&(f=Math.sqrt(k*k+y*y),r=g,o=_);break;case Jo.C:var m=t[c++],v=t[c++],_=(g=t[c++],t[c++]),x=t[c++],w=t[c++];f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1;p<=10;p++){var d=.1*p,f=un(t,n,r,a,d),g=(d=un(e,i,o,s,d),f-u),y=d-h;c+=Math.sqrt(g*g+y*y),u=f,h=d}return c}(r,o,m,v,g,_,x,w),r=x,o=w;break;case Jo.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1;h<=10;h++){var c=.1*h,p=gn(t,n,r,c),d=(c=gn(e,i,o,c),p-s),f=c-l;u+=Math.sqrt(d*d+f*f),s=p,l=c}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++]),r=g,o=_;break;case Jo.A:x=t[c++],w=t[c++];var b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=la(M)*b+x,s=ua(M)*S+w),f=sa(b,S)*aa(pa,Math.abs(T)),r=la(C)*b+x,o=ua(C)*S+w;break;case Jo.R:a=r=t[c++],s=o=t[c++],f=2*t[c++]+2*t[c++];break;case Jo.Z:var k=a-r;y=s-o;f=Math.sqrt(k*k+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},va.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,c=this._ux,p=this._uy,d=this._len,f=e<1,g=0,y=0,m=0;if(!f||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var v=0;v<d;){var _=h[v++],x=1===v;switch(x&&(n=r=h[v],i=o=h[v+1]),_!==Jo.L&&0<m&&(t.lineTo(l,u),m=0),_){case Jo.M:n=r=h[v++],i=o=h[v++],t.moveTo(r,o);break;case Jo.L:var w=h[v++],b=h[v++],S=ha(w-r),M=ha(b-o);if(c<S||p<M){if(f){if(s<g+(W=a[y++])){var T=(s-g)/W;t.lineTo(r*(1-T)+w*T,o*(1-T)+b*T);break t}g+=W}t.lineTo(w,b),r=w,o=b,m=0}else S=S*S+M*M,m<S&&(l=w,u=b,m=S);break;case Jo.C:var C=h[v++],k=h[v++],D=h[v++],I=h[v++];M=h[v++],S=h[v++];if(f){if(s<g+(W=a[y++])){dn(r,C,D,M,T=(s-g)/W,ta),dn(o,k,I,S,T,ea),t.bezierCurveTo(ta[1],ea[1],ta[2],ea[2],ta[3],ea[3]);break t}g+=W}t.bezierCurveTo(C,k,D,I,M,S),r=M,o=S;break;case Jo.Q:if(C=h[v++],k=h[v++],D=h[v++],I=h[v++],f){if(s<g+(W=a[y++])){vn(r,C,D,T=(s-g)/W,ta),vn(o,k,I,T,ea),t.quadraticCurveTo(ta[1],ea[1],ta[2],ea[2]);break t}g+=W}t.quadraticCurveTo(C,k,D,I),r=D,o=I;break;case Jo.A:var A=h[v++],O=h[v++],L=h[v++],P=h[v++],R=h[v++],N=h[v++],E=h[v++],z=!h[v++],B=P<L?L:P,F=.001<ha(L-P),V=R+N,H=!1;if(f&&(s<g+(W=a[y++])&&(V=R+N*(s-g)/W,H=!0),g+=W),F&&t.ellipse?t.ellipse(A,O,L,P,E,R,V,z):t.arc(A,O,B,R,V,z),H)break t;x&&(n=la(R)*L+A,i=ua(R)*P+O),r=la(V)*L+A,o=ua(V)*P+O;break;case Jo.R:n=r=h[v],i=o=h[v+1],w=h[v++],b=h[v++];var W;N=h[v++],F=h[v++];if(f){if(s<g+(W=a[y++])){E=s-g,t.moveTo(w,b),t.lineTo(w+aa(E,N),b),0<(E-=N)&&t.lineTo(w+N,b+aa(E,F)),0<(E-=F)&&t.lineTo(w+sa(N-E,0),b+F),0<(E-=N)&&t.lineTo(w,b+sa(F-E,0));break t}g+=W}t.rect(w,b,N,F);break;case Jo.Z:if(f){if(s<g+(W=a[y++])){T=(s-g)/W,t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}g+=W}t.closePath(),r=n,o=i}}},va.prototype.clone=function(){var t=new va,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},va.CMD=Jo,va.initDefaultProps=((Tl=va.prototype)._saveData=!0,Tl._ux=0,Tl._uy=0,Tl._pendingPtDist=0,void(Tl._version=0));var ma=va;function va(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function _a(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var xa=2*Math.PI;function wa(t){return(t%=xa)<0&&(t+=xa),t}var ba=2*Math.PI;function Sa(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var Ma=ma.CMD,Ta=2*Math.PI,Ca=[-1,-1,-1],ka=[-1,-1];function Da(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=cn(e,i,o,s,u,Ca);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=Ca[y],v=0===m||1===m?.5:1;un(t,n,r,a,m)<l||(d<0&&(d=pn(e,i,o,s,ka),ka[1]<ka[0]&&1<d&&(void 0,c=ka[0],ka[0]=ka[1],ka[1]=c),f=un(e,i,o,s,ka[0]),1<d)&&(g=un(e,i,o,s,ka[1])),2===d?m<ka[0]?p+=f<e?v:-v:m<ka[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<ka[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function Ia(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=Ca,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,sn(h)?ln(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):sn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=en(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=mn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=gn(e,i,o,g),v=0;v<f;v++){var _=0===Ca[v]||1===Ca[v]?.5:1;gn(t,n,r,Ca[v])<a||(Ca[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===Ca[0]||1===Ca[0]?.5:1,gn(t,n,r,Ca[0])<a?0:o<e?_:-_}function Aa(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===Ma.M&&1<d&&(n||(l+=Sa(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case Ma.M:u=c=a[d++],h=p=a[d++];break;case Ma.L:if(n){if(_a(u,h,a[d],a[d+1],e,i,r))return!0}else l+=Sa(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ma.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&fn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=Da(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ma.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&_n(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=Ia(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case Ma.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=Sa(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%ba<1e-4||((r=o?(e=i,i=wa(r),wa(e)):(i=wa(i),wa(r)))<i&&(r+=ba),(t=Math.atan2(l,s))<0&&(t+=ba),i<=t&&t<=r)||i<=t+ba&&t+ba<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;e=Math.sqrt(n*n-s*s);if(Ca[0]=-e,Ca[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(Ta-1e-4<=n)return r=Ta,h=o?1:-1,a>=Ca[i=0]+t&&a<=Ca[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=Ta,r+=Ta);for(var l=0,u=0;u<2;u++){var h,c=Ca[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?Ta+c:c)&&c<=r||i<=c+Ta&&c+Ta<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case Ma.R:if(c=u=a[d++],p=h=a[d++],S=c+a[d++],M=p+a[d++],n){if(_a(c,p,S,p,e,i,r)||_a(S,p,S,M,e,i,r)||_a(S,M,c,M,e,i,r)||_a(c,M,c,p,e,i,r))return!0}else l=(l+=Sa(S,p,S,M,i,r))+Sa(c,M,c,p,i,r);break;case Ma.Z:if(n){if(_a(u,h,c,p,e,i,r))return!0}else l+=Sa(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<1e-4)||(l+=Sa(u,h,c,p,i,r)||0),0!==l}var Oa,La=D({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},No),Pa={style:D({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Eo.style)},Ra=ji.concat(["invisible","culling","z","z2","zlevel","parent"]),Na=(n(Ea,Oa=Yn),Ea.prototype.update=function(){var t=this,e=(Oa.prototype.update.call(this),this.style);if(e.decal){var n,i=this._decalEl=this._decalEl||new Ea,r=(i.buildPath===Ea.prototype.buildPath&&(i.buildPath=function(e){t.buildPath(e,t.shape)}),i.silent=!0,i.style);for(n in e)r[n]!==e[n]&&(r[n]=e[n]);r.fill=e.fill?e.decal:null,r.decal=null,r.shadowColor=null,e.strokeFirst&&(r.stroke=null);for(var o=0;o<Ra.length;++o)i[Ra[o]]=this[Ra[o]];i.__dirty|=1}else this._decalEl&&(this._decalEl=null)},Ea.prototype.getDecalElement=function(){return this._decalEl},Ea.prototype._init=function(t){var e=B(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?k(this.style,o):this.useStyle(o):"shape"===r?k(this.shape,o):Oa.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},Ea.prototype.getDefaultStyle=function(){return null},Ea.prototype.getDefaultShape=function(){return{}},Ea.prototype.canBeInsideText=function(){return this.hasFill()},Ea.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(G(e))return.5<(t=Zn(e,0))?Ni:.2<t?"#eee":Ei;if(e)return Ei}return Ni},Ea.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(G(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==Zn(t,0)<.4)return e}},Ea.prototype.buildPath=function(t,e,n){},Ea.prototype.pathUpdated=function(){this.__dirty&=-5},Ea.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},Ea.prototype.createPathProxy=function(){this.path=new ma(!1)},Ea.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},Ea.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},Ea.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||4&this.__dirty)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},Ea.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){if(n=this.path,this.hasStroke()&&(i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1,1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),Aa(n,i/r,!0,t,e))))return!0;if(this.hasFill())return Aa(n,0,!1,t,e)}return!1},Ea.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},Ea.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},Ea.prototype.animateShape=function(t){return this.animate("shape",t)},Ea.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},Ea.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):Oa.prototype.attrKV.call(this,t,e)},Ea.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:k(n,t),this.dirtyShape(),this},Ea.prototype.shapeChanged=function(){return!!(4&this.__dirty)},Ea.prototype.createStyle=function(t){return yt(La,t)},Ea.prototype._innerSaveToNormal=function(t){Oa.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=k({},this.shape))},Ea.prototype._applyStateObj=function(t,e,n,i,r,o){Oa.prototype._applyStateObj.call(this,t,e,n,i,r,o);var s,l=!(e&&i);if(e&&e.shape?r?i?s=e.shape:(s=k({},n.shape),k(s,e.shape)):(s=k({},(i?this:n).shape),k(s,e.shape)):l&&(s=n.shape),s)if(r){this.shape=k({},this.shape);for(var u={},h=B(s),c=0;c<h.length;c++){var p=h[c];"object"==a(s[p])?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(t,{shape:u},o)}else this.shape=s,this.dirtyShape()},Ea.prototype._mergeStates=function(t){for(var e,n=Oa.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},Ea.prototype.getAnimationStyleProps=function(){return Pa},Ea.prototype.isZeroArea=function(){return!1},Ea.extend=function(t){n(o,e=Ea),o.prototype.getDefaultStyle=function(){return T(t.style)},o.prototype.getDefaultShape=function(){return T(t.shape)};var e,i,r=o;function o(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}for(i in t)"function"==typeof t[i]&&(r.prototype[i]=t[i]);return r},Ea.initDefaultProps=((Tl=Ea.prototype).type="path",Tl.strokeContainThreshold=5,Tl.segmentIgnoreThreshold=0,Tl.subPixelOptimize=!1,Tl.autoBatch=!1,void(Tl.__dirty=7)),Ea);function Ea(t){return Oa.call(this,t)||this}var za,Ba=D({strokeFirst:!0,font:l,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},La),Fa=(n(Va,za=Yn),Va.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},Va.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},Va.prototype.createStyle=function(t){return yt(Ba,t)},Va.prototype.setBoundingRect=function(t){this._rect=t},Va.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Zi(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},Va.initDefaultProps=void(Va.prototype.dirtyRectTolerance=10),Va);function Va(){return null!==za&&za.apply(this,arguments)||this}Fa.prototype.type="tspan";var Ha=D({x:0,y:0},No),Wa={style:D({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Eo.style)};n(ja,Ga=Yn),ja.prototype.createStyle=function(t){return yt(Ha,t)},ja.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},ja.prototype.getWidth=function(){return this._getSize("width")},ja.prototype.getHeight=function(){return this._getSize("height")},ja.prototype.getAnimationStyleProps=function(){return Wa},ja.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new De(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var Ga,Ua=ja;function ja(){return null!==Ga&&Ga.apply(this,arguments)||this}Ua.prototype.type="image";var Xa=Math.round;function Ya(t,e,n){var i;return e?((i=Xa(2*t))+Xa(e))%2==0?i/2:(i+(n?1:-1))/2:t}var qa,Za=function(){this.x=0,this.y=0,this.width=0,this.height=0},$a={},Ka=(n(Qa,qa=Na),Qa.prototype.getDefaultShape=function(){return new Za},Qa.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=function(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Ya(i,n,!0),t.y=Ya(r,n,!0),t.width=Math.max(Ya(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Ya(r+e,n,!1)-t.y,0===e?0:1)),t}($a,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},Qa.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Qa);function Qa(t){return qa.call(this,t)||this}Ka.prototype.type="rect";var Ja,ts={fill:"#000"},es={style:D({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Eo.style)},ns=(n(is,Ja=Yn),is.prototype.childrenRef=function(){return this._children},is.prototype.update=function(){Ja.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},is.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ja.prototype.updateTransform.call(this)},is.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ja.prototype.getLocalTransform.call(this,t)},is.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ja.prototype.getComputedTransform.call(this)},is.prototype._updateSubTexts=function(){var t;this._childCursor=0,ls(t=this.style),R(t.rich,ls),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},is.prototype.addSelfToZr=function(t){Ja.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},is.prototype.removeSelfFromZr=function(t){Ja.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},is.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new De(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect();o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},is.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||ts},is.prototype.setTextContent=function(t){},is.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},k(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},is.prototype._mergeRich=function(t,e){for(var n=B(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},k(t[r],e[r])}},is.prototype.getAnimationStyleProps=function(){return es},is.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},is.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||l,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Qi(o),l=tt(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,p=(i=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?Lo(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,tt(e.height,i));if(p<i&&h&&(h=Math.floor(p/l),n=n.slice(0,h)),t&&a&&null!=c)for(var d=To(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),f=0;f<n.length;f++)n[f]=Co(n[f],d);h=p;var g=0;for(f=0;f<n.length;f++)g=Math.max(Yi(n[f],o),g);return null==c&&(c=g),t=g,r&&(h+=r[0]+r[2],t+=r[1]+r[3],c+=r[1]+r[3]),{lines:n,height:p,outerWidth:t=u?c:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:g,contentHeight:i,width:c}}(ps(e),e),o=ds(e),a=!!e.backgroundColor,s=r.outerHeight,u=r.outerWidth,h=r.contentWidth,c=r.lines,p=r.lineHeight,d=this._defaultStyle,f=e.x||0,g=e.y||0,y=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",v=f,_=Ki(g,r.contentHeight,m),x=((o||i)&&(t=$i(f,u,y),g=Ki(g,s,m),o)&&this._renderBackground(e,e,t,g,u,s),_+=p/2,i&&(v=cs(f,y,i),"top"===m?_+=i[0]:"bottom"===m&&(_-=i[2])),0),w=(o=!1,hs(("fill"in e?e:(o=!0,d)).fill)),b=(us("stroke"in e?e.stroke:a||d.autoStroke&&!o?null:(x=2,d.stroke))),S=0<e.textShadowBlur,M=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),T=r.calculatedLineHeight,C=0;C<c.length;C++){var k=this._getOrCreateChild(Fa),D=k.createStyle();k.useStyle(D),D.text=c[C],D.x=v,D.y=_,y&&(D.textAlign=y),D.textBaseline="middle",D.opacity=e.opacity,D.strokeFirst=!0,S&&(D.shadowBlur=e.textShadowBlur||0,D.shadowColor=e.textShadowColor||"transparent",D.shadowOffsetX=e.textShadowOffsetX||0,D.shadowOffsetY=e.textShadowOffsetY||0),D.stroke=b,D.fill=w,b&&(D.lineWidth=e.lineWidth||x,D.lineDash=e.lineDash,D.lineDashOffset=e.lineDashOffset||0),D.font=n,ss(D,e),_+=p,M&&k.setBoundingRect(new De($i(D.x,e.width,D.textAlign),Ki(D.y,T,D.textBaseline),h,T))}},is.prototype._updateRichTexts=function(){for(var t=this.style,e=function(t,e){var n=new Io;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=So.lastIndex=0;null!=(i=So.exec(t));){var u=i.index;l<u&&Ao(n,t.substring(l,u),e,s),Ao(n,i[2],e,s,i[1]),l=So.lastIndex}l<t.length&&Ao(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow;t:for(var m=0;m<n.lines.length;m++){for(var v=n.lines[m],_=0,x=0,w=0;w<v.tokens.length;w++){var b=(I=v.tokens[w]).styleName&&e.rich[I.styleName]||{},S=I.textPadding=b.padding,M=S?S[1]+S[3]:0,T=I.font=b.font||e.font,C=(I.contentHeight=Qi(T),tt(b.height,I.contentHeight));if(I.innerHeight=C,S&&(C+=S[0]+S[2]),I.height=C,I.lineHeight=et(b.lineHeight,e.lineHeight,C),I.align=b&&b.align||e.align,I.verticalAlign=b&&b.verticalAlign||"middle",y&&null!=o&&p+I.lineHeight>o){0<w?(v.tokens=v.tokens.slice(0,w),O(v,x,_),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m);break t}S=b.width;var k,D=null==S||"auto"===S;"string"==typeof S&&"%"===S.charAt(S.length-1)?(I.percentWidth=S,c.push(I),I.contentWidth=Yi(I.text,T)):(D&&(S=(S=b.backgroundColor)&&S.image)&&(k=void 0,bo(S="string"==typeof(h=S)?(k=_o.get(h))&&k.image:h))&&(I.width=Math.max(I.width,S.width*C/S.height)),null!=(k=g&&null!=r?r-x:null)&&k<I.width?!D||k<M?(I.text="",I.width=I.contentWidth=0):(I.text=Mo(I.text,k-M,T,e.ellipsis,{minChar:e.truncateMinChar}),I.width=I.contentWidth=Yi(I.text,T)):I.contentWidth=Yi(I.text,T)),I.width+=M,x+=I.width,b&&(_=Math.max(_,I.lineHeight))}O(v,x,_)}for(n.outerWidth=n.width=tt(r,d),n.outerHeight=n.height=tt(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]),m=0;m<c.length;m++){var I,A=(I=c[m]).percentWidth;I.width=parseInt(A,10)/100*n.width}}return n;function O(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}(ps(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,h=(l=t.verticalAlign||l.verticalAlign,a=$i(a,i,u),u=Ki(s,r,l),a),c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(ds(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},is.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},u=(s.text=t.text,t.verticalAlign),h=i+n/2;"top"===u?h=i+t.height/2:"bottom"===u&&(h=i+n-t.height/2),!t.isLineHolder&&ds(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,h-t.height/2,t.width,t.height);u=!!s.backgroundColor,i=t.textPadding,i&&(r=cs(r,o,i),h-=t.height/2-i[0]-t.innerHeight/2),n=this._getOrCreateChild(Fa),i=n.createStyle();var c=(n.useStyle(i),this._defaultStyle),p=!1,d=0,f=hs(("fill"in s?s:"fill"in e?e:(p=!0,c)).fill);u=us("stroke"in s?s.stroke:"stroke"in e?e.stroke:u||a||c.autoStroke&&!p?null:(d=2,c.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,i.text=t.text,i.x=r,i.y=h,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||l,i.opacity=et(s.opacity,e.opacity,1),ss(i,s),u&&(i.lineWidth=et(s.lineWidth,e.lineWidth,d),i.lineDash=tt(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=u),f&&(i.fill=f),p=t.contentWidth,c=t.contentHeight;n.setBoundingRect(new De($i(i.x,p,i.textAlign),Ki(i.y,c,i.textBaseline),p,c))},is.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this;(f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(Ka)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=tt(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(Ua)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=tt(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),g=(a||s).style;g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=et(t.opacity,e.opacity,1)},is.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&ot(n)||t.textFont||t.font},is);function is(t){var e=Ja.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=ts,e.attr(t),e}var rs={left:!0,right:1,center:1},os={top:1,bottom:1,middle:1},as=["fontStyle","fontWeight","fontSize","fontFamily"];function ss(t,e){for(var n=0;n<as.length;n++){var i=as[n],r=e[i];null!=r&&(t[i]=r)}}function ls(t){var e;t&&(t.font=ns.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||rs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||os[e]?e:"top",t.padding)&&(t.padding=it(t.padding))}function us(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function hs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function cs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function ps(t){return t=t.text,null!=t&&(t+=""),t}function ds(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var fs=Qr(),gs=1,ys={},ms=Qr(),vs=Qr(),_s=["emphasis","blur","select"],xs=["normal","emphasis","blur","select"],ws="highlight",bs="downplay",Ss="select",Ms="unselect",Ts="toggleSelect";function Cs(t){return null!=t&&"none"!==t}function ks(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function Ds(t){ks(t,"emphasis",2)}function Is(t){2===t.hoverState&&ks(t,"normal",0)}function As(t){ks(t,"blur",1)}function Os(t){1===t.hoverState&&ks(t,"normal",0)}function Ls(t){t.selected=!0}function Ps(t){t.selected=!1}function Rs(t,e,n){e(t,n)}function Ns(t,e,n){Rs(t,e,n),t.isGroup&&t.traverse((function(t){Rs(t,e,n)}))}function Es(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=A(e,"select"),a=!1,n instanceof Na&&(r=ms(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,Cs(o)||Cs(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=k({},i),(r=k({},r)).fill=o):!Cs(r.fill)&&Cs(o)?(a=!0,i=k({},i),(r=k({},r)).fill=Kn(o)):!Cs(r.stroke)&&Cs(e)&&(a||(i=k({},i),r=k({},r)),r.stroke=Kn(e)),i.style=r),i&&null==i.z2&&(a||(i=k({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return function(t,e,n){var i=0<=A(t.currentStates,e),r=t.style.opacity;return t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{},null==e.opacity&&(n=k({},n),e=k({opacity:i?r:.1*t.opacity},e),n.style=e),n}(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=k({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function zs(t){t.stateProxy=Es;var e=t.getTextContent();t=t.getTextGuideLine();e&&(e.stateProxy=Es),t&&(t.stateProxy=Es)}function Bs(t,e){js(t,e)||t.__highByOuter||Ns(t,Ds)}function Fs(t,e){js(t,e)||t.__highByOuter||Ns(t,Is)}function Vs(t,e){t.__highByOuter|=1<<(e||0),Ns(t,Ds)}function Hs(t,e){(t.__highByOuter&=~(1<<(e||0)))||Ns(t,Is)}function Ws(t){Ns(t,Os)}function Gs(t){Ns(t,Ls)}function Us(t){Ns(t,Ps)}function js(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Xs(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=vs(r),a=(e="series"===e,e?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r));e||i.push(a),o.isBlured&&(a.group.traverse((function(t){Os(t)})),e)&&n.push(r),o.isBlured=!1})),R(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function Ys(t,e,n,i){var r,o,a,s=i.getModel();function l(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Ws(i)}}n=n||"coordinateSystem",null!=t&&e&&"none"!==e&&(r=s.getSeriesByIndex(t),(o=r.coordinateSystem)&&o.master&&(o=o.master),a=[],s.eachSeries((function(t){var s=r===t,u=t.coordinateSystem;u=(u=u&&u.master?u.master:u)&&o?u===o:s;if(!("series"===n&&!s||"coordinateSystem"===n&&!u||"series"===e&&s)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){t.__highByOuter&&s&&"self"===e||As(t)})),P(e))l(t.getData(),e);else if(X(e))for(var h=B(e),c=0;c<h.length;c++)l(t.getData(h[c]),e[h[c]]);a.push(t),vs(t).isBlured=!0}})),s.eachComponent((function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(a,!0,s)})))}function qs(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(vs(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse((function(t){As(t)}))}function Zs(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;if(t=i.getModel().getComponent(t,e),!t)return r;if(e=i.getViewOfComponentModel(t),!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===fs(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function $s(t){R(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){(t.isSelected(n,i)?Gs:Us)(e)}))}))}function Ks(t,e,n){tl(t,!0),Ns(t,zs),t=fs(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}var Qs=["emphasis","blur","select"],Js={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function tl(t,e){e=!1===e;var n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function el(t){return!(!t||!t.__highDownDispatcher)}function nl(t){return t=t.type,t===Ss||t===Ms||t===Ts}function il(t){return t=t.type,t===ws||t===bs}var rl=ma.CMD,ol=[[],[],[]],al=Math.sqrt,sl=Math.atan2,ll=Math.sqrt,ul=Math.sin,hl=Math.cos,cl=Math.PI;function pl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function dl(t,e){return(t[0]*e[0]+t[1]*e[1])/(pl(t)*pl(e))}function fl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(dl(t,e))}function gl(t,e,n,i,r,o,a,s,l,u,h){l*=cl/180;var c=hl(l)*(t-n)/2+ul(l)*(e-i)/2,p=-1*ul(l)*(t-n)/2+hl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s);1<d&&(a*=ll(d),s*=ll(d)),d=(r===o?-1:1)*ll((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0,r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+hl(l)*r-ul(l)*d,n=(e+i)/2+ul(l)*r+hl(l)*d,e=fl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=fl(i,c);dl(i,c)<=-1&&(r=cl),(r=1<=dl(i,c)?0:r)<0&&(p=Math.round(r/cl*1e6)/1e6,r=2*cl+p%2*cl),h.addData(u,t,n,a,s,e,r,l,o)}var yl=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,ml=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;n(xl,vl=Na),xl.prototype.applyTransform=function(t){};var vl,_l=xl;function xl(){return null!==vl&&vl.apply(this,arguments)||this}function wl(t){return null!=t.setData}function bl(t,e){var n=function(t){var e=new ma;if(t){var n,i=0,r=0,o=i,a=r,s=ma.CMD,l=t.match(yl);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(ml)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,k=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),k=e.data,n===s.C&&(m+=i-k[C-4],v+=r-k[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),k=e.data,n===s.Q&&(m+=i-k[C-4],v+=r-k[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],gl(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],gl(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t);t=k({},e);return t.buildPath=function(t){var e;wl(t)?(t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e,1)):n.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=n,i=t;if(i){for(var r,o,a,s,l=e.data,u=e.len(),h=rl.M,c=rl.C,p=rl.L,d=rl.R,f=rl.A,g=rl.Q,y=0,m=0;y<u;){switch(r=l[y++],m=y,o=0,r){case h:case p:o=1;break;case c:o=3;break;case g:o=2;break;case f:var v=i[4],_=i[5],x=al(i[0]*i[0]+i[1]*i[1]),w=al(i[2]*i[2]+i[3]*i[3]),b=sl(-i[1]/w,i[0]/x);l[y]*=x,l[y++]+=v,l[y]*=w,l[y++]+=_,l[y++]*=x,l[y++]*=w,l[y++]+=b,l[y++]+=b,m=y+=2;break;case d:s[0]=l[y++],s[1]=l[y++],Nt(s,s,i),l[m++]=s[0],l[m++]=s[1],s[0]+=l[y++],s[1]+=l[y++],Nt(s,s,i),l[m++]=s[0],l[m++]=s[1]}for(a=0;a<o;a++){var S=ol[a];S[0]=l[y++],S[1]=l[y++],Nt(S,S,i),l[m++]=S[0],l[m++]=S[1]}}e.increaseVersion()}this.dirtyShape()},t}var Sl,Ml=function(){this.cx=0,this.cy=0,this.r=0},Tl=(n(Cl,Sl=Na),Cl.prototype.getDefaultShape=function(){return new Ml},Cl.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},Cl);function Cl(t){return Sl.call(this,t)||this}Tl.prototype.type="circle";var kl,Dl=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},Il=(n(Al,kl=Na),Al.prototype.getDefaultShape=function(){return new Dl},Al.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,o=(e=e.ry,.5522848*r),a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},Al);function Al(t){return kl.call(this,t)||this}Il.prototype.type="ellipse";var Ol=Math.PI,Ll=2*Ol,Pl=Math.sin,Rl=Math.cos,Nl=Math.acos,El=Math.atan2,zl=Math.abs,Bl=Math.sqrt,Fl=Math.max,Vl=Math.min,Hl=1e-4;function Wl(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a=(a?o:-o)/Bl(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,(t+n)/2),h=e-s,c=n-t,p=h*h+c*c,d=(o=r-o,s=s*n-e*t,n=(c<0?-1:1)*Bl(Fl(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,(s*c+h*n)/p);s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}var Gl,Ul=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},jl=(n(Xl,Gl=Na),Xl.prototype.getDefaultShape=function(){return new Ul},Xl.prototype.buildPath=function(t,e){(function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,k,D,I,A,O,L=Fl(e.r,0),P=Fl(e.r0||0,0),R=0<L;(R||0<P)&&(R||(L=P,P=0),L<P&&(R=L,L=P,P=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=zl(n-R),Hl<(a=Ll<m&&m%Ll)&&(m=a),Hl<L?Ll-Hl<m?(t.moveTo(i+L*Rl(R),r+L*Pl(R)),t.arc(i,r,L,R,n,!o),Hl<P&&(t.moveTo(i+P*Rl(n),r+P*Pl(n)),t.arc(i,r,P,n,R,o))):(S=b=w=x=_=v=c=h=k=C=T=M=u=l=s=a=void 0,p=L*Rl(R),d=L*Pl(R),f=P*Rl(n),g=P*Pl(n),(y=Hl<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(H(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=zl(L-P)/2,M=Vl(e,l),T=Vl(e,u),C=Vl(e,a),k=Vl(e,s),v=h=Fl(M,T),_=c=Fl(C,k),Hl<h||Hl<c)&&(x=L*Rl(n),w=L*Pl(n),b=P*Rl(R),S=P*Pl(R),m<Ol)&&(e=function(t,e,n,i,r,o,a,s){var l=(s-=o)*(n-=t)-(a-=r)*(i-=e);if(!(l*l<Hl))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],k=w-e[1],m=1/Pl(Nl((M*C+T*k)/(Bl(M*M+T*T)*Bl(C*C+k*k)))/2),M=Bl(e[0]*e[0]+e[1]*e[1]),v=Vl(h,(L-M)/(1+m)),_=Vl(c,(P-M)/(m-1))),y?Hl<v?(D=Vl(l,v),I=Vl(u,v),A=Wl(b,S,p,d,L,D,o),O=Wl(x,w,f,g,L,I,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&D===I?t.arc(i+A.cx,r+A.cy,v,El(A.y0,A.x0),El(O.y0,O.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,El(A.y0,A.x0),El(A.y1,A.x1),!o),t.arc(i,r,L,El(A.cy+A.y1,A.cx+A.x1),El(O.cy+O.y1,O.cx+O.x1),!o),0<I&&t.arc(i+O.cx,r+O.cy,I,El(O.y1,O.x1),El(O.y0,O.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),Hl<P&&y?Hl<_?(D=Vl(a,_),A=Wl(f,g,x,w,P,-(I=Vl(s,_)),o),O=Wl(p,d,b,S,P,-D,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&D===I?t.arc(i+A.cx,r+A.cy,_,El(A.y0,A.x0),El(O.y0,O.x0),!o):(0<I&&t.arc(i+A.cx,r+A.cy,I,El(A.y0,A.x0),El(A.y1,A.x1),!o),t.arc(i,r,P,El(A.cy+A.y1,A.cx+A.x1),El(O.cy+O.y1,O.cx+O.x1),o),0<D&&t.arc(i+O.cx,r+O.cy,D,El(O.y1,O.x1),El(O.y0,O.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,P,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))})(t,e)},Xl.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Xl);function Xl(t){return Gl.call(this,t)||this}jl.prototype.type="sector";var Yl,ql=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Zl=(n($l,Yl=Na),$l.prototype.getDefaultShape=function(){return new ql},$l.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},$l);function $l(t){return Yl.call(this,t)||this}function Kl(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)Et(h,h,t[p]),zt(c,c,t[p]);Et(h,h,i[0]),zt(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(St(t[p]));continue}r=t[p-1],o=t[p+1]}Tt(s,o,r),Dt(s,s,e);var g=At(f,r),y=At(f,o),m=g+y;m=(0!==m&&(g/=m,y/=m),Dt(l,s,-g),Dt(u,s,y),Mt([],f,l)),g=Mt([],f,u);i&&(zt(m,m,h),Et(m,m,c),zt(g,g,h),Et(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Zl.prototype.type="ring";var Ql,Jl=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},tu=(n(eu,Ql=Na),eu.prototype.getDefaultShape=function(){return new Jl},eu.prototype.buildPath=function(t,e){Kl(t,e,!0)},eu);function eu(t){return Ql.call(this,t)||this}tu.prototype.type="polygon";var nu,iu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},ru=(n(ou,nu=Na),ou.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ou.prototype.getDefaultShape=function(){return new iu},ou.prototype.buildPath=function(t,e){Kl(t,e,!1)},ou);function ou(t){return nu.call(this,t)||this}ru.prototype.type="polyline";var au,su={},lu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},uu=(n(hu,au=Na),hu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},hu.prototype.getDefaultShape=function(){return new lu},hu.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=function(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(Xa(2*i)===Xa(2*r)&&(t.x1=t.x2=Ya(i,n,!0)),Xa(2*o)===Xa(2*e))&&(t.y1=t.y2=Ya(o,n,!0)),t}(su,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o;e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},hu.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},hu);function hu(t){return au.call(this,t)||this}uu.prototype.type="line";var cu=[],pu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function du(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?hn:un)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?hn:un)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?yn:gn)(t.x1,t.cpx1,t.x2,e),(n?yn:gn)(t.y1,t.cpy1,t.y2,e)]}n(yu,fu=Na),yu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},yu.prototype.getDefaultShape=function(){return new pu},yu.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2;e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(vn(n,a,r,e,cu),a=cu[1],r=cu[2],vn(i,s,o,e,cu),s=cu[1],o=cu[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(dn(n,a,l,r,e,cu),a=cu[1],l=cu[2],r=cu[3],dn(i,s,u,o,e,cu),s=cu[1],u=cu[2],o=cu[3]),t.bezierCurveTo(a,s,l,u,r,o)))},yu.prototype.pointAt=function(t){return du(this.shape,t,!1)},yu.prototype.tangentAt=function(t){return t=du(this.shape,t,!0),It(t,t)};var fu,gu=yu;function yu(t){return fu.call(this,t)||this}gu.prototype.type="bezier-curve";var mu,vu=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},_u=(n(xu,mu=Na),xu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},xu.prototype.getDefaultShape=function(){return new vu},xu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=(e=e.clockwise,Math.cos(o)),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},xu);function xu(t){return mu.call(this,t)||this}_u.prototype.type="arc",n(Su,wu=Na),Su.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},Su.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},Su.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},Su.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},Su.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Na.prototype.getBoundingRect.call(this)};var wu,bu=Su;function Su(){var t=null!==wu&&wu.apply(this,arguments)||this;return t.type="compound",t}Tu.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var Mu=Tu;function Tu(t){this.colorStops=t||[]}n(Du,Cu=Mu);var Cu,ku=Du;function Du(t,e,n,i,r,o){return r=Cu.call(this,r)||this,r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}n(Au,Iu=Mu);var Iu;Mu=Au;function Au(t,e,n,i,r){return i=Iu.call(this,i)||this,i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var Ou=[0,0],Lu=[0,0],Pu=new ve,Ru=new ve,Nu=(Eu.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width;t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);for(ve.sub(i[0],n[1],n[0]),ve.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize(),s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},Eu.prototype.intersect=function(t,e){var n=!0,i=!e;return Pu.set(1/0,1/0),Ru.set(0,0),!this._intersectCheckOneSide(this,t,Pu,Ru,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,Pu,Ru,i,-1)&&(n=!1,i)||i||ve.copy(e,n?Pu:Ru),n},Eu.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,Ou),this._getProjMinMaxOnAxis(s,e._corners,Lu),Ou[1]<Lu[0]||Lu[1]<Ou[0]){if(a=!1,r)return a;var u=Math.abs(Lu[0]-Ou[1]),h=Math.abs(Ou[0]-Lu[1]);Math.min(u,h)>i.len()&&(u<h?ve.scale(i,l,-u*o):ve.scale(i,l,h*o))}else n&&(u=Math.abs(Lu[0]-Ou[1]),h=Math.abs(Ou[0]-Lu[1]),Math.min(u,h)<n.len())&&(u<h?ve.scale(n,l,u*o):ve.scale(n,l,-h*o))}return a},Eu.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Math.min(u,a),s=Math.max(u,s)}n[0]=a,n[1]=s},Eu);function Eu(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new ve;for(n=0;n<2;n++)this._axes[n]=new ve;t&&this.fromBoundingRect(t,e)}var zu,Bu=[];n(Fu,zu=Yn),Fu.prototype.traverse=function(t,e){t.call(e,this)},Fu.prototype.useStyle=function(){this.style={}},Fu.prototype.getCursor=function(){return this._cursor},Fu.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Fu.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Fu.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Fu.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},Fu.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Fu.prototype.getDisplayables=function(){return this._displayables},Fu.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Fu.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Fu.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;var e;for(t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Fu.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new De(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Bu)),t.union(i)}this._rect=t}return this._rect},Fu.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Yn=Fu;function Fu(){var t=null!==zu&&zu.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Vu=Qr();function Hu(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(W(r)?(a=o,o=r,r=null):X(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=tt(l.duration,200),h=tt(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),W(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=W(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function Wu(t,e,n,i,r,o){Hu("update",t,e,n,i,r,o)}function Gu(t,e,n,i,r,o){Hu("enter",t,e,n,i,r,o)}function Uu(t){if(!t.__zr)return 1;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return 1}function ju(t,e,n,i){var r;t.removeTextContent(),t.removeTextGuideLine(),r={style:{opacity:0}},e=e,n=n,i=i,Uu(t=t)||Hu("leave",t,r,e,n,i,void 0)}var Xu=Math.max,Yu=Math.min,qu={};function Zu(t,e){qu[t]=e}function $u(t,e,n,i){return t=new _l(bl(t,e)),n&&("center"===i&&(n=Qu(n,t.getBoundingRect())),Ju(t,n)),t}function Ku(t,e,n){var i=new Ua({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Qu(e,t)))}});return i}function Qu(t,e){e=e.width/e.height;var n=t.height*e;e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Ju(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function th(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}Zu("circle",Tl),Zu("ellipse",Il),Zu("sector",jl),Zu("ring",Zl),Zu("polygon",tu),Zu("polyline",ru),Zu("rect",Ka),Zu("line",uu),Zu("bezierCurve",gu),Zu("arc",_u);var eh={};function nh(t,e){for(var n=0;n<_s.length;n++){var i=_s[n],r=e[i];i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function ih(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?W(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<_s.length;u++){var h=_s[u],c=e[h];l[h]=tt(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function rh(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||eh;t=l.ecModel;var p,d=t&&t.option.textStyle,f=function(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||eh).rich;if(n){e=e||{};for(var i=B(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),uh(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),uh(s,l,d,u,h,c,!0,!1),e&&k(a,e),a}function oh(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=tt(t.getShallow("distance"),n?null:5),a=t.getShallow("offset");n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var ah=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],sh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],lh=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function uh(t,e,n,i,r,o,a,s){n=!r&&n||eh;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=tt(e.getShallow("opacity"),n.opacity);"inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),u=tt(e.getShallow("textBorderWidth"),n.textBorderWidth),null!=u&&(t.lineWidth=u),h=tt(e.getShallow("textBorderType"),n.textBorderType),null!=h&&(t.lineDash=h),u=tt(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<ah.length;p++){var d=ah[p];null!=(f=tt(e.getShallow(d),n[d]))&&(t[d]=f)}for(p=0;p<sh.length;p++)d=sh[p],null!=(f=e.getShallow(d))&&(t[d]=f);if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<lh.length;p++){var f;d=lh[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType"),null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var hh=Qr();var ch=["textStyle","color"],ph=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],dh=new ns,fh=(gh.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(ch):null)},gh.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),ot([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},gh.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<ph.length;n++)e[ph[n]]=this.getShallow(ph[n]);return dh.useStyle(e),dh.update(),dh.getBoundingRect()},gh);function gh(){}var yh=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],mh=go(yh),vh=(_h.prototype.getLineStyle=function(t){return mh(this,t)},_h);function _h(){}var xh=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],wh=go(xh),bh=(Sh.prototype.getItemStyle=function(t,e){return wh(this,t,e)},Sh);function Sh(){}Ch.prototype.init=function(t,e,n){},Ch.prototype.mergeOption=function(t,e){C(this.option,t,!0)},Ch.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Ch.prototype.getShallow=function(t,e){var n=this.option;n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Ch.prototype.getModel=function(t,e){var n=null!=t;t=n?this.parsePath(t):null;return new Ch(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Ch.prototype.isEmpty=function(){return null==this.option},Ch.prototype.restoreData=function(){},Ch.prototype.clone=function(){return new this.constructor(T(this.option))},Ch.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Ch.prototype.resolveParentPath=function(t){return t},Ch.prototype.isAnimationEnabled=function(){if(!r.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Ch.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==a(n)?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Mh,Th=Ch;function Ch(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}lo(Th),zh=Th,Mh=["__\0is_clz",ho++].join("_"),zh.prototype[Mh]=!0,zh.isInstance=function(t){return!(!t||!t[Mh])},L(Th,vh),L(Th,bh),L(Th,mo),L(Th,fh);var kh=Math.round(10*Math.random());function Dh(t){return[t||"",kh++].join("_")}var Ih="EN",Ah={},Oh={},Lh=r.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Ih).toUpperCase().indexOf("ZH")?"ZH":Ih;function Ph(t,e){t=t.toUpperCase(),Oh[t]=new Th(e),Ah[t]=e}Ph("EN",{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Ph("ZH",{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Rh=36e5,Nh=24*Rh,Eh=(ho=365*Nh,{year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"}),zh="{yyyy}-{MM}-{dd}",Bh={year:"{yyyy}",month:"{yyyy}-{MM}",day:zh,hour:zh+" "+Eh.hour,minute:zh+" "+Eh.minute,second:zh+" "+Eh.second,millisecond:Eh.none},Fh=["year","month","day","hour","minute","second","millisecond"],Vh=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Hh(t,e){return"0000".substr(0,e-(t+="").length)+t}function Wh(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Gh(t,e,n,i){t=Lr(t);var r=t[Xh(n)](),o=t[Yh(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[qh(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[Zh(n)](),h=(u-1)%12+1,c=t[$h(n)](),p=t[Kh(n)](),d=(t=t[Qh(n)](),n=(i instanceof Th?i:Oh[i||Lh]||Oh[Ih]).getModel("time"),i=n.get("month"),n.get("monthAbbr")),f=n.get("dayOfWeek");n=n.get("dayOfWeekAbbr");return(e||"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Hh(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,i[o-1]).replace(/{MMM}/g,d[o-1]).replace(/{MM}/g,Hh(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Hh(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,f[l]).replace(/{ee}/g,n[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Hh(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Hh(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Hh(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Hh(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Hh(t,3)).replace(/{S}/g,t+"")}function Uh(t,e){t=Lr(t);var n=t[Yh(e)]()+1,i=t[qh(e)](),r=t[Zh(e)](),o=t[$h(e)](),a=t[Kh(e)]();t=0===t[Qh(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function jh(t,e,n){var i=j(t)?Lr(t):t;switch(e=e||Uh(t,n)){case"year":return i[Xh(n)]();case"half-year":return 6<=i[Yh(n)]()?1:0;case"quarter":return Math.floor((i[Yh(n)]()+1)/4);case"month":return i[Yh(n)]();case"day":return i[qh(n)]();case"half-day":return i[Zh(n)]()/24;case"hour":return i[Zh(n)]();case"minute":return i[$h(n)]();case"second":return i[Kh(n)]();case"millisecond":return i[Qh(n)]()}}function Xh(t){return t?"getUTCFullYear":"getFullYear"}function Yh(t){return t?"getUTCMonth":"getMonth"}function qh(t){return t?"getUTCDate":"getDate"}function Zh(t){return t?"getUTCHours":"getHours"}function $h(t){return t?"getUTCMinutes":"getMinutes"}function Kh(t){return t?"getUTCSeconds":"getSeconds"}function Qh(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Jh(t){return t?"setUTCMonth":"setMonth"}function tc(t){return t?"setUTCDate":"setDate"}function ec(t){return t?"setUTCHours":"setHours"}function nc(t){return t?"setUTCMinutes":"setMinutes"}function ic(t){return t?"setUTCSeconds":"setSeconds"}function rc(t){return t?"setUTCMilliseconds":"setMilliseconds"}function oc(t){var e;return zr(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):G(t)?t:"-"}function ac(t,e){return"{"+t+(null==e?"":e)+"}"}var sc=it,lc=["a","b","c","d","e","f","g"];function uc(t,e,n){var i=(e=H(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=lc[o];t=t.replace(ac(a),ac(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(ac(lc[l],s),n?Kt(u):u)}return t}function hc(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location.href=t):window.open(t,e)}var cc=R,pc=["left","right","top","bottom","width","height"],dc=[["width","left","right"],["height","top","bottom"]];function fc(t,e,n,i,r){var o=0,a=0,s=(null==i&&(i=1/0),null==r&&(r=1/0),0);e.eachChild((function(l,u){var h,c,p,d=l.getBoundingRect();u=e.childAt(u+1),u=u&&u.getBoundingRect();s="horizontal"===t?(c=d.width+(u?-u.x+d.x:0),i<(h=o+c)||l.newline?(o=0,h=c,a+=s+n,d.height):Math.max(s,d.height)):(c=d.height+(u?-u.y+d.y:0),r<(p=a+c)||l.newline?(o+=s+n,a=0,p=c,d.width):Math.max(s,d.width)),l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=p+n)}))}function gc(t,e,n){n=sc(n||0);var i=e.width,r=e.height,o=Tr(t.left,i),a=Tr(t.top,r),s=(e=Tr(t.right,i),Tr(t.bottom,r)),l=Tr(t.width,i),u=Tr(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}return o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0)),p=new De(o+n[3],a+n[0],l,u),p.margin=n,p}function yc(t){return t=t.layoutMode||t.constructor.layoutMode,X(t)?t:t?{type:t}:null}function mc(t,e,n){var i=n&&n.ignoreSize,r=(n=(H(i)||(i=[i,i]),o(dc[0],0)),o(dc[1],1));function o(n,r){var o={},l=0,u={},h=0;if(cc(n,(function(e){u[e]=t[e]})),cc(n,(function(t){a(e,t)&&(o[t]=u[t]=e[t]),s(o,t)&&l++,s(u,t)&&h++})),i[r])s(e,n[1])?u[n[2]]=null:s(e,n[2])&&(u[n[1]]=null);else if(2!==h&&l){if(!(2<=l))for(var c=0;c<n.length;c++){var p=n[c];if(!a(o,p)&&a(t,p)){o[p]=t[p];break}}return o}return u}function a(t,e){return t.hasOwnProperty(e)}function s(t,e){return null!=t[e]&&"auto"!==t[e]}function l(t,e,n){cc(t,(function(t){e[t]=n[t]}))}l(dc[0],t,n),l(dc[1],t,r)}function vc(t){return e={},(n=t)&&e&&cc(pc,(function(t){n.hasOwnProperty(t)&&(e[t]=n[t])})),e;var e,n}V(fc,"vertical"),V(fc,"horizontal");var _c,xc,wc,bc,Sc=Qr(),Mc=(n(Tc,_c=Th),Tc.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Tc.prototype.mergeDefaultAndTheme=function(t,e){var n=yc(this),i=n?vc(t):{};C(t,e.getTheme().get(this.mainType)),C(t,this.getDefaultOption()),n&&mc(t,i,n)},Tc.prototype.mergeOption=function(t,e){C(this.option,t,!0);var n=yc(this);n&&mc(this.option,t,n)},Tc.prototype.optionUpdated=function(t,e){},Tc.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[ao])return t.defaultOption;var e=Sc(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=C(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Tc.prototype.getReferringComponents=function(t,e){var n=t+"Id";return io(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Tc.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Tc.prototype.getZLevelKey=function(){return""},Tc.prototype.setZLevel=function(t){this.option.zlevel=t},Tc.protoInitialize=((vh=Tc.prototype).type="component",vh.id="",vh.name="",vh.mainType="",vh.subType="",void(vh.componentIndex=0)),Tc);function Tc(t,e,n){return t=_c.call(this,t,e,n)||this,t.uid=Dh("ec_cpt_model"),t}function Cc(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}uo(Mc,Th),fo(Mc),wc={},(xc=Mc).registerSubTypeDefaulter=function(t,e){t=so(t),wc[t.main]=e},xc.determineSubType=function(t,e){var n,i=e.type;return i||(n=so(t).main,xc.hasSubTypes(t)&&wc[n]&&(i=wc[n](e))),i},bc=function(t){var e=[];return R(Mc.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=N(e,(function(t){return so(t).main})),"dataset"!==t&&A(e,"dataset")<=0&&e.unshift("dataset"),e},Mc.topologicalTravel=function(t,e,n,i){if(t.length){o={},a=[],R(r=e,(function(t){var e,n,i=Cc(o,t),s=i.originalDeps=bc(t);e=r,n=[],R(s,(function(t){0<=A(e,t)&&n.push(t)})),s=n;i.entryCount=s.length,0===i.entryCount&&a.push(t),R(s,(function(e){A(i.predecessor,e)<0&&i.predecessor.push(e);var n=Cc(o,e);A(n.successor,e)<0&&n.successor.push(t)}))}));e={graph:o,noEntryList:a};var r,o,a,s=e.graph,l=e.noEntryList,u={};for(R(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),R(c.successor,p?f:d)}R(u,(function(){throw new Error("")}))}function d(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};bh="","undefined"!=typeof navigator&&(bh=navigator.platform||""),mo="rgba(0, 0, 0, 0.2)";var kc={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:mo,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:mo,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:mo,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:mo,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:mo,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:mo,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:bh.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Dc=ft(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Ic="original",Ac="arrayRows",Oc="objectRows",Lc="keyedColumns",Pc="typedArray",Rc="unknown",Nc="column",Ec="row",zc={Must:1,Might:2,Not:3},Bc=Qr();function Fc(t,e,n){var i,r,o,a,s,l={},u=Hc(e);return u&&t&&(i=[],r=[],e=e.ecModel,e=Bc(e).datasetMap,u=u.uid+"_"+n.seriesLayoutBy,R(t=t.slice(),(function(e,n){e=X(e)?e:t[n]={name:e},"ordinal"===e.type&&null==o&&(o=n,a=c(e)),l[e.name]=[]})),s=e.get(u)||e.set(u,{categoryWayDim:a,valueWayDim:0}),R(t,(function(t,e){var n,a=t.name;t=c(t);null==o?(n=s.valueWayDim,h(l[a],n,t),h(r,n,t),s.valueWayDim+=t):o===e?(h(l[a],0,t),h(i,0,t)):(n=s.categoryWayDim,h(l[a],n,t),h(r,n,t),s.categoryWayDim+=t)})),i.length&&(l.itemName=i),r.length)&&(l.seriesName=r),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){return t=t.dimsDef,t?t.length:1}}function Vc(t,e,n){var i,r,o,a={};return Hc(t)&&(i=e.sourceFormat,r=e.dimensionsDefine,i!==Oc&&i!==Lc||R(r,(function(t,e){"name"===(X(t)?t.name:t)&&(o=e)})),t=function(){for(var t={},a={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=Gc(e.data,i,e.seriesLayoutBy,r,e.startIndex,l),c=(s.push(h),h===zc.Not);if(c&&null==t.v&&l!==o&&(t.v=l),null!=t.n&&t.n!==t.v&&(c||s[t.n]!==zc.Not)||(t.n=l),p(t)&&s[t.n]!==zc.Not)return t;c||(h===zc.Might&&null==a.v&&l!==o&&(a.v=l),null!=a.n&&a.n!==a.v)||(a.n=l)}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(a)?a:null}())&&(a.value=[t.v],t=null!=o?o:t.n,a.itemName=[t],a.seriesName=[t]),a}function Hc(t){if(!t.get("data",!0))return io(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},no).models[0]}function Wc(t,e){return Gc(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function Gc(t,e,n,i,r,o){var a,s,l;if(!q(t)){if(i&&(X(i=i[o])?(s=i.name,l=i.type):G(i)&&(s=i)),null!=l)return"ordinal"===l?zc.Must:zc.Not;if(e===Ac){var u=t;if(n===Ec){for(var h=u[o],c=0;c<(h||[]).length&&c<5;c++)if(null!=(a=m(h[r+c])))return a}else for(c=0;c<u.length&&c<5;c++){var p=u[r+c];if(p&&null!=(a=m(p[o])))return a}}else if(e===Oc){var d=t;if(!s)return zc.Not;for(c=0;c<d.length&&c<5;c++)if((g=d[c])&&null!=(a=m(g[s])))return a}else if(e===Lc){if(!s)return zc.Not;if(!(h=t[s])||q(h))return zc.Not;for(c=0;c<h.length&&c<5;c++)if(null!=(a=m(h[c])))return a}else if(e===Ic){var f=t;for(c=0;c<f.length&&c<5;c++){var g,y=jr(g=f[c]);if(!H(y))return zc.Not;if(null!=(a=m(y[o])))return a}}}return zc.Not;function m(t){var e=G(t);return null!=t&&isFinite(t)&&""!==t?e?zc.Might:zc.Not:e&&"-"!==t?zc.Must:void 0}}var Uc,jc,Xc,Yc=ft(),qc=Qr(),Zc=(Qr(),$c.prototype.getColorFromPalette=function(t,e,n){var i=Wr(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=qc;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=null!=n&&r?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(r,n):i)||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},$c.prototype.clearColorPalette=function(){var t;(t=qc)(this).paletteIdx=0,t(this).paletteNameMap={}},$c);function $c(){}var Kc,Qc="\0_ec_inner",Jc=(n(tp,Kc=Th),tp.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new Th(i),this._locale=new Th(r),this._optionManager=o},tp.prototype.setOption=function(t,e,n){e=ip(e),this._optionManager.setOption(t,n,e),this._resetOption(null,e)},tp.prototype.resetOption=function(t,e){return this._resetOption(t,ip(e))},tp.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):Xc(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&R(n,(function(t){i=!0,this._mergeOption(t,e)}),this),i},tp.prototype.mergeOption=function(t){this._mergeOption(t,null)},tp.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=ft(),s=e&&e.replaceMergeMainTypeMap;Bc(this).datasetMap=ft(),R(t,(function(t,e){null!=t&&(Mc.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?T(t):C(n[e],t,!0))})),s&&s.each((function(t,e){Mc.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),Mc.topologicalTravel(o,Mc.getAllClassMainTypes(),(function(e){var o,a=function(t,e,n){return(e=(e=Yc.get(e))&&e(t))?n.concat(e):n}(this,e,Wr(t[e])),l=i.get(e),u=l?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",h=(l=function(t,e,n){var i,r,o,a,s,l,u,h,c,p,d="normalMerge"===n,f="replaceMerge"===n,g="replaceAll"===n,y=(t=t||[],e=(e||[]).slice(),ft());return R(e,(function(t,n){X(t)||(e[n]=null)})),n=function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||$r(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,n),(d||f)&&(i=n,r=t,o=y,R(a=e,(function(t,e){var n,s,l;t&&null!=t.id&&(n=Yr(t.id),null!=(s=o.get(n)))&&(rt(!(l=i[s]).newOption,'Duplicated option on id "'+n+'".'),l.newOption=t,l.existing=r[s],a[e]=null)}))),d&&(s=n,R(l=e,(function(t,e){if(t&&null!=t.name)for(var n=0;n<s.length;n++){var i=s[n].existing;if(!s[n].newOption&&i&&(null==i.id||null==t.id)&&!$r(t)&&!$r(i)&&Xr("name",i,t))return s[n].newOption=t,void(l[e]=null)}}))),d||f?(h=n,c=f,R(e,(function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||$r(e.existing)||e.existing&&null!=t.id&&!Xr("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}}))):g&&(u=n,R(e,(function(t){u.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})}))),t=n,p=ft(),R(t,(function(t){var e=t.existing;e&&p.set(e.id,t)})),R(t,(function(t){var e=t.newOption;rt(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})})),R(t,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(X(i)){if(r.name=null!=i.name?Yr(i.name):n?n.name:Hr+e,n)r.id=Yr(n.id);else if(null!=i.id)r.id=Yr(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}})),n}(l,a,u),function(t,e,n){R(t,(function(t){var i,r,o=t.newOption;X(o)&&(t.keyInfo.mainType=e,t.keyInfo.subType=(i=e,o=o,t=t.existing,r=n,o.type||(t?t.subType:r.determineSubType(i,o))))}))}(l,e,Mc),n[e]=null,i.set(e,null),r.set(e,0),[]),c=[],p=0;R(l,(function(t,n){var i=t.existing,r=t.newOption;if(r){var a=Mc.getClass(e,t.keyInfo.subType,!("series"===e));if(!a)return;if("tooltip"===e){if(o)return;o=!0}i&&i.constructor===a?(i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1)):(n=k({componentIndex:n},t.keyInfo),k(i=new a(r,this,this,n),n),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0))}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(h.push(i.option),c.push(i),p++):(h.push(void 0),c.push(void 0))}),this),n[e]=h,i.set(e,c),r.set(e,p),"series"===e&&Uc(this)}),this),this._seriesIndices||Uc(this)},tp.prototype.getOption=function(){var t=T(this.option);return R(t,(function(e,n){if(Mc.hasClass(n)){for(var i=Wr(e),r=i.length,o=!1,a=r-1;0<=a;a--)i[a]&&!$r(i[a])?o=!0:(i[a]=null,o||r--);i.length=r,t[n]=i}})),delete t[Qc],t},tp.prototype.getTheme=function(){return this._theme},tp.prototype.getLocaleModel=function(){return this._locale},tp.prototype.setUpdatePayload=function(t){this._payload=t},tp.prototype.getUpdatePayload=function(){return this._payload},tp.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){if(t=n[e||0],t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},tp.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],R(Wr(e),(function(t){r[t]&&o.push(r[t])}))):o=null!=n?ep("id",n,r):null!=i?ep("name",i,r):z(r,(function(t){return!!t})),np(o,t)):[]},tp.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):z(this._componentsMap.get(i),(function(t){return!!t}));return n=np(o,t),t.filter?z(n,t.filter):n},tp.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(W(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=G(t)?i.get(t):X(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},tp.prototype.getSeriesByName=function(t){var e=qr(t,null);return z(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},tp.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},tp.prototype.getSeriesByType=function(t){return z(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},tp.prototype.getSeries=function(){return z(this._componentsMap.get("series"),(function(t){return!!t}))},tp.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},tp.prototype.eachSeries=function(t,e){jc(this),R(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},tp.prototype.eachRawSeries=function(t,e){R(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},tp.prototype.eachSeriesByType=function(t,e,n){jc(this),R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},tp.prototype.eachRawSeriesByType=function(t,e,n){return R(this.getSeriesByType(t),e,n)},tp.prototype.isSeriesFiltered=function(t){return jc(this),null==this._seriesIndicesMap.get(t.componentIndex)},tp.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},tp.prototype.filterSeries=function(t,e){jc(this);var n=[];R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=ft(n)},tp.prototype.restoreData=function(t){Uc(this);var e=this._componentsMap,n=[];e.each((function(t,e){Mc.hasClass(e)&&n.push(e)})),Mc.topologicalTravel(n,Mc.getAllClassMainTypes(),(function(n){R(e.get(n),(function(e){!e||"series"===n&&function(t,e){var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}(e,t)||e.restoreData()}))}))},tp.internalField=(Uc=function(t){var e=t._seriesIndices=[];R(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=ft(e)},jc=function(t){},void(Xc=function(t,e){t.option={},t.option[Qc]=1,t._componentsMap=ft({series:[]}),t._componentsCount=ft();var n,i,r=e.aria;X(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,R(r,(function(t,e){"colorLayer"===e&&i||Mc.hasClass(e)||("object"==a(t)?n[e]=n[e]?C(n[e],t,!1):T(t):null==n[e]&&(n[e]=t))})),C(e,kc,!1),t._mergeOption(e,null)})),tp);function tp(){return null!==Kc&&Kc.apply(this,arguments)||this}function ep(t,e,n){var i,r;return H(e)?(i=ft(),R(e,(function(t){null!=t&&null!=qr(t,null)&&i.set(t,!0)})),z(n,(function(e){return e&&i.get(e[t])}))):(r=qr(e,null),z(n,(function(e){return e&&null!=r&&e[t]===r})))}function np(t,e){return e.hasOwnProperty("subType")?z(t,(function(t){return t&&t.subType===e.subType})):t}function ip(t){var e=ft();return t&&R(Wr(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}function rp(t){R(op,(function(e){this[e]=F(t[e],t)}),this)}L(Jc,Zc);var op=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ap={},sp=(lp.prototype.create=function(t,e){var n=[];R(ap,(function(i,r){i=i.create(t,e),n=n.concat(i||[])})),this._coordinateSystems=n},lp.prototype.update=function(t,e){R(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},lp.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},lp.register=function(t,e){ap[t]=e},lp.get=function(t){return ap[t]},lp);function lp(){this._coordinateSystems=[]}var up=/^(min|max)?(.+)$/,hp=(cp.prototype.setOption=function(t,e,n){t&&(R(Wr(t.series),(function(t){t&&t.data&&q(t.data)&&st(t.data)})),R(Wr(t.dataset),(function(t){t&&t.source&&q(t.source)&&st(t.source)}))),t=T(t);var i=this._optionBackup;t=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);function p(t){R(e,(function(e){e(t,n)}))}return a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t),h&&H(u)&&R(u,(function(t){t&&t.option&&(t.query?o.push(t):i=i||t)})),p(r),R(l,p),R(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},cp.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],T(t?e.baseOption:this._newBaseOption)},cp.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return n.length&&(t=t.getComponent("timeline"))?T(n[t.getCurrentIndex()]):e},cp.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return R(t,(function(t,e){var n;e=e.match(up);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))})),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=N(o,(function(t){return T((-1===t?r:i[t]).option)}))),this._currentMediaIndices=o}return a},cp);function cp(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var pp=R,dp=X,fp=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function gp(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=fp.length;n<i;n++){var r=fp[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?C(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?C(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function yp(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,D(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function mp(t){yp(t,"itemStyle"),yp(t,"lineStyle"),yp(t,"areaStyle"),yp(t,"label"),yp(t,"labelLine"),yp(t,"upperLabel"),yp(t,"edgeLabel")}function vp(t,e){var n=dp(t)&&t[e],i=dp(n)&&n.textStyle;if(i)for(var r=0,o=Ur.length;r<o;r++){var a=Ur[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function _p(t){t&&(mp(t),vp(t,"label"),t.emphasis)&&vp(t.emphasis,"label")}function xp(t){return H(t)?t:t?[t]:[]}function wp(t){return(H(t)?t[0]:t)||{}}function bp(t){t&&R(Sp,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var Sp=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Mp=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Tp=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Cp(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Tp.length;n++){var i=Tp[n][1],r=Tp[n][0];null!=e[i]&&(e[r]=e[i])}}function kp(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Dp(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Ip(t,e){(function(t,e){pp(xp(t.series),(function(t){if(dp(t)&&dp(t)){gp(t),mp(t),vp(t,"label"),vp(t,"upperLabel"),vp(t,"edgeLabel"),t.emphasis&&(vp(t.emphasis,"label"),vp(t.emphasis,"upperLabel"),vp(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(gp(e),_p(e)),t.markLine),i=(n&&(gp(n),_p(n)),t.markArea),r=(i&&_p(i),t.data);if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!q(o))for(var a=0;a<o.length;a++)_p(o[a]);R(t.categories,(function(t){mp(t)}))}if(r&&!q(r))for(a=0;a<r.length;a++)_p(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)_p(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)H(l[a])?(_p(l[a][0]),_p(l[a][1])):_p(l[a])}"gauge"===t.type?(vp(t,"axisLabel"),vp(t,"title"),vp(t,"detail")):"treemap"===t.type?(yp(t.breadcrumb,"itemStyle"),R(t.levels,(function(t){mp(t)}))):"tree"===t.type&&mp(t.leaves)}}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),pp(n,(function(e){pp(xp(t[e]),(function(t){t&&(vp(t,"axisLabel"),vp(t.axisPointer,"label"))}))})),pp(xp(t.parallel),(function(t){t=t&&t.parallelAxisDefault,vp(t,"axisLabel"),vp(t&&t.axisPointer,"label")})),pp(xp(t.calendar),(function(t){yp(t,"itemStyle"),vp(t,"dayLabel"),vp(t,"monthLabel"),vp(t,"yearLabel")})),pp(xp(t.radar),(function(t){vp(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),pp(xp(t.geo),(function(t){dp(t)&&(_p(t),pp(xp(t.regions),(function(t){_p(t)})))})),pp(xp(t.timeline),(function(t){_p(t),yp(t,"label"),yp(t,"itemStyle"),yp(t,"controlStyle",!0),t=t.data,H(t)&&R(t,(function(t){X(t)&&(yp(t,"label"),yp(t,"itemStyle"))}))})),pp(xp(t.toolbox),(function(t){yp(t,"iconStyle"),pp(t.feature,(function(t){yp(t,"iconStyle")}))})),vp(wp(t.axisPointer),"label"),vp(wp(t.tooltip).axisPointer,"label")})(t,e),t.series=Wr(t.series),R(t.series,(function(t){if(X(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),kp(t.label),(e=t.data)&&!q(e))for(var i=0;i<e.length;i++)kp(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n="pointer.color".split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t);if(null!=r){for(var o,s=t,l="itemStyle.color",u=l.split(","),h=s,c=0;c<u.length-1;c++)null==h[o=u[c]]&&(h[o]={}),h=h[o];null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Cp(t),Cp(t.backgroundStyle),Cp(t.emphasis),(e=t.data)&&!q(e))for(i=0;i<e.length;i++)"object"==a(e[i])&&(Cp(e[i]),Cp(e[i]&&e[i].emphasis))}else"sunburst"===n?((l=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=l)),Dp(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Dp)):"graph"===n||"sankey"===n?(s=t)&&null!=s.focusNodeAdjacency&&(s.emphasis=s.emphasis||{},null==s.emphasis.focus)&&(s.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&D(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),bp(t)}})),t.dataRange&&(t.visualMap=t.dataRange),R(Mp,(function(e){e=t[e],e&&R(e=H(e)?e:[e],(function(t){bp(t)}))}))}function Ap(t){R(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,u,h){var c,p,d=a.get(e.stackedDimension,h);if(isNaN(d))return r;s?p=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var f,g,y,m=NaN,v=n-1;0<=v;v--){var _=t[v];if(0<=(p=s?p:_.data.rawIndexOf(_.stackedByDimension,c))&&(_=_.data.getByRawIndex(_.stackResultDimension,p),"all"===l||"positive"===l&&0<_||"negative"===l&&_<0||"samesign"===l&&0<=d&&0<_||"samesign"===l&&d<=0&&_<0)){f=d,g=_,void 0,y=Math.max(kr(f),kr(g)),f+=g,d=20<y?f:Cr(f,y),m=_;break}}return i[0]=d,i[1]=m,i}))}))}var Op,Lp,Pp=function(t){this.data=t.data||(t.sourceFormat===Lc?{}:[]),this.sourceFormat=t.sourceFormat||Rc,this.seriesLayoutBy=t.seriesLayoutBy||Nc,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Wc(this,n)===zc.Must&&(i.type="ordinal")}};function Rp(t){return t instanceof Pp}function Np(t,e,n){n=n||zp(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a,s;return t?(e===Ac?(s=t,"auto"===i||null==i?Fp((function(t){null!=t&&"-"!==t&&(G(t)?null==a&&(a=1):a=0)}),n,s,10):a=j(i)?i:i?1:0,r||1!==a||(r=[],Fp((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===Ec?s.length:s[0]?s[0].length:null):e===Oc?r=r||function(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e)return B(e)}(t):e===Lc?r||(r=[],R(t,(function(t,e){r.push(e)}))):e===Ic&&(i=jr(t[0]),o=H(i)&&i.length||1),{startIndex:a,dimensionsDefine:Bp(r),dimensionsDetectedCount:o}):{dimensionsDefine:Bp(r),startIndex:a,dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Pp({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:T(e)})}function Ep(t){return new Pp({data:t,sourceFormat:q(t)?Pc:Ic})}function zp(t){var e=Rc;if(q(t))e=Pc;else if(H(t)){0===t.length&&(e=Ac);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(H(r)||q(r)){e=Ac;break}if(X(r)){e=Oc;break}}}}else if(X(t))for(var o in t)if(vt(t,o)&&P(t[o])){e=Lc;break}return e}function Bp(t){var e;if(t)return e=ft(),N(t,(function(t,n){var i;t={name:(t=X(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(i=e.get(t.name))?t.name+="-"+i.count++:e.set(t.name,{count:1})),t}))}function Fp(t,e,n,i){if(e===Ec)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function Vp(t){return t=t.sourceFormat,t===Oc||t===Lc}jp.prototype.getSource=function(){return this._source},jp.prototype.count=function(){return 0},jp.prototype.getItem=function(t,e){},jp.prototype.appendData=function(t){},jp.prototype.clean=function(){},jp.protoInitialize=((fh=jp.prototype).pure=!1,void(fh.persistent=!0)),jp.internalField=(Lp=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex;n=n.dimensionsDefine;k(t,Op[nd(r,o)]),r===Pc?(t.getItem=Hp,t.count=Gp,t.fillStorage=Wp):(i=Zp(r,o),t.getItem=F(i,null,e,a,n),i=Qp(r,o),t.count=F(i,null,e,a,n))},Hp=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},Wp=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},Gp=function(){return this._data?this._data.length/this._dimSize:0},(fh={})[Ac+"_"+Nc]={pure:!0,appendData:Xp},fh[Ac+"_"+Ec]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},fh[Oc]={pure:!0,appendData:Xp},fh[Lc]={pure:!0,appendData:function(t){var e=this._data;R(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},fh[Ic]={appendData:Xp},fh[Pc]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Op=fh));var Hp,Wp,Gp,Up=jp;function jp(t,e){t=Rp(t)?t:Ep(t);var n=(this._source=t,this._data=t.data);t.sourceFormat===Pc&&(this._offset=0,this._dimSize=e,this._data=n),Lp(this,n,t)}function Xp(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Yp(t,e,n,i){return t[i]}(zh={})[Ac+"_"+Nc]=function(t,e,n,i){return t[i+e]},zh[Ac+"_"+Ec]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},zh[Oc]=Yp,zh[Lc]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},zh[Ic]=Yp;var qp=zh;function Zp(t,e){return qp[nd(t,e)]}function $p(t,e,n){return t.length}(vh={})[Ac+"_"+Nc]=function(t,e,n){return Math.max(0,t.length-e)},vh[Ac+"_"+Ec]=function(t,e,n){return t=t[0],t?Math.max(0,t.length-e):0},vh[Oc]=$p,vh[Lc]=function(t,e,n){return t=t[n[0].name],t?t.length:0},vh[Ic]=$p;var Kp=vh;function Qp(t,e){return Kp[nd(t,e)]}function Jp(t,e,n){return t[e]}(mo={})[Ac]=Jp,mo[Oc]=function(t,e,n){return t[n]},mo[Lc]=Jp,mo[Ic]=function(t,e,n){return t=jr(t),t instanceof Array?t[e]:t},mo[Pc]=Jp;var td=mo;function ed(t){return td[t]}function nd(t,e){return t===Ac?t+"_"+e:t}function id(t,e,n){if(t){var i,r;e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),ed(i)(e,t,n)):(r=e,i===Ic?jr(e):r)}}var rd=/\{@(.+?)\}/g;od.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=(t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,this.mainType),u="series"===l;n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},od.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n);n=this.getDataParams(t,n);return o&&(n.value=o.interpolatedValue),null!=i&&H(n.value)&&(n.value=n.value[i]),W(r=r||a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]))?(n.status=e,n.dimensionIndex=i,r(n)):G(r)?uc(r,n).replace(rd,(function(e,n){var i=n.length;"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),i=id(a,t,n);return null!=(i=o&&H(o.interpolatedValue)&&0<=(n=a.getDimensionIndex(n))?o.interpolatedValue[n]:i)?i+"":""})):void 0},od.prototype.getRawValue=function(t,e){return id(this.getData(e),t)},od.prototype.formatTooltip=function(t,e,n){},bh=od;function od(){}function ad(t){return new sd(t)}ld.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return 1<=t?t:1}if(o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l,o=t&&t.step,this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(H(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c,a=null!=this._settedOutputEnd?this._settedOutputEnd:c,this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},ld.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},ld.prototype._doProgress=function(t,e,n,i,r){gd.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:gd.next},this.context)},ld.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),H(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n;t=this._downstream;return t&&t.dirty(),n},ld.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},ld.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},ld.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},ld.prototype.getUpstream=function(){return this._upstream},ld.prototype.getDownstream=function(){return this._downstream},ld.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var sd=ld;function ld(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var ud,hd,cd,pd,dd,fd,gd=fd={reset:function(t,e,n,i){hd=t,ud=e,cd=n,pd=i,dd=Math.ceil(pd/cd),fd.next=1<cd&&0<pd?md:yd}};function yd(){return hd<ud?hd++:null}function md(){var t=hd%dd*cd+Math.ceil(hd/dd);t=ud<=hd?null:t<pd?t:hd;return hd++,t}function vd(t,e){return e=e&&e.type,"ordinal"===e?t:null==(t="time"!==e||j(t)||null==t||"-"===t?t:+Lr(t))||""===t?NaN:+t}var _d=ft({number:function(t){return parseFloat(t)},time:function(t){return+Lr(t)},trim:function(t){return G(t)?ot(t):t}});function xd(t){return _d.get(t)}var wd={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},bd=(Sd.prototype.evaluate=function(t){return j(t)?this._opFn(t,this._rvalFloat):this._opFn(Er(t),this._rvalFloat)},Sd);function Sd(t,e){j(e)||Fr(""),this._opFn=wd[t],this._rvalFloat=Er(e)}Td.prototype.evaluate=function(t,e){var n=j(t)?t:Er(t),i=j(e)?e:Er(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=G(t),o=G(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Md=Td;function Td(t,e){t="desc"===t,this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}kd.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=a(t))===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=Er(t)===this._rvalFloat),this._isEQ?n:!n};var Cd=kd;function kd(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=a(e),this._rvalFloat=Er(e)}Id.prototype.getRawData=function(){throw new Error("not supported")},Id.prototype.getRawDataItem=function(t){throw new Error("not supported")},Id.prototype.cloneRawData=function(){},Id.prototype.getDimensionInfo=function(t){},Id.prototype.cloneAllDimensionInfo=function(){},Id.prototype.count=function(){},Id.prototype.retrieveValue=function(t,e){},Id.prototype.retrieveValueFromItem=function(t,e){},Id.prototype.convertValue=vd;var Dd=Id;function Id(){}function Ad(t){return Nd(t.sourceFormat)||Fr(""),t.data}function Od(t){var e=t.sourceFormat,n=t.data;if(Nd(e)||Fr(""),e===Ac){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Oc){for(i=[],r=0,o=n.length;r<o;r++)i.push(k({},n[r]));return i}}function Ld(t,e,n){if(null!=n)return j(n)||!isNaN(n)&&!vt(e,n)?t[n]:vt(e,n)?e[n]:void 0}function Pd(t){return T(t)}var Rd=ft();function Nd(t){return t===Ac||t===Oc}fh="undefined";var Ed,zd=("undefined"===typeof Uint32Array?"undefined":a(Uint32Array))==fh?Array:Uint32Array,Bd=("undefined"===typeof Uint16Array?"undefined":a(Uint16Array))==fh?Array:Uint16Array,Fd=("undefined"===typeof Int32Array?"undefined":a(Int32Array))==fh?Array:Int32Array,Vd=(zh=("undefined"===typeof Float64Array?"undefined":a(Float64Array))==fh?Array:Float64Array,{float:zh,int:Fd,ordinal:Array,number:Array,time:zh});function Hd(t){return 65535<t?zd:Bd}function Wd(){return[1/0,-1/0]}function Gd(t,e,n,i,r){if(n=Vd[n||"float"],r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}jd.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Ed[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],Vp(i),this._dimensions=N(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},jd.prototype.getProvider=function(){return this._provider},jd.prototype.getSource=function(){return this._provider.getSource()},jd.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new Vd[e||"float"](this._rawCount),this._rawExtent[r]=Wd(),r},jd.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=Wd()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},jd.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},jd.prototype.getDimensionProperty=function(t){return t=this._dimensions[t],t&&t.property},jd.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t),t=e.count();return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},jd.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)Gd(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Ed.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},jd.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=N(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=Wd()),Gd(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d);f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},jd.prototype.count=function(){return this._count},jd.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},jd.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},jd.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},jd.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},jd.prototype.getMedian=function(t){var e=[],n=(t=(this.each([t],(function(t){isNaN(t)||e.push(t)})),e.sort((function(t,e){return t-e}))),this.count());return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},jd.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},jd.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},jd.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else for(i=new(e=Hd(this._rawCount))(this.count()),r=0;r<i.length;r++)i[r]=r;return i},jd.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(Hd(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},jd.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=B(t),r=i.length;if(!r)return this;var o=e.count(),a=new(Hd(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks;l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){d=c[i[0]];var g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1];for(f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,b=(x=e.getRawIndex(f),0);b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},jd.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},jd.prototype.modify=function(t,e){this._updateDims(this,t,e)},jd.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,s=t.count(),l=[],u=t._rawExtent,h=0;h<e.length;h++)u[e[h]]=Wd();for(var c=0;c<s;c++){for(var p=t.getRawIndex(c),d=0;d<o;d++)l[d]=i[e[d]][p];l[o]=c;var f=n&&n.apply(null,l);if(null!=f)for("object"!=a(f)&&(r[0]=f,f=r),h=0;h<f.length;h++){var g=e[h],y=f[h],m=u[g];g=i[g];g&&(g[p]=y),y<m[0]&&(m[0]=y),y>m[1]&&(m[1]=y)}}},jd.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(Hd(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;c=h;var m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0;for(g=c;g<m;g++){var M;y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},jd.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=Wd(),c=new(Hd(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},jd.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},jd.prototype.getDataExtent=function(t){var e=this._chunks[t],n=Wd();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=[o,a]},jd.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},jd.prototype.clone=function(t,e){var n,i,r=new jd,o=this._chunks,a=t&&E(t,(function(t,e){return t[e]=!0,t}),{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},jd.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=T(this._extent),t._rawExtent=T(this._rawExtent)},jd.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,i=(e=new t(n),0);i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},jd.prototype._getRawIdxIdentity=function(t){return t},jd.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},jd.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},jd.internalField=void(Ed={arrayRows:Xd,objectRows:function(t,e,n,i){return vd(t[e],this._dimensions[i])},keyedColumns:Xd,original:function(t,e,n,i){return t=t&&(null==t.value?t:t.value),vd(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var Ud=jd;function jd(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=ft()}function Xd(t,e,n,i){return vd(t[i],this._dimensions[i])}qd.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},qd.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},qd.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},qd.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},qd.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;$d(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=q(o=i.get("data",!0))?Pc:Ic,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=tt(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=tt(i.sourceHeader,r.sourceHeader),i=tt(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[Np(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[Np(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},qd.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&Kd(""),[]),a=[];return R(t,(function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||Kd(""),o.push(e),a.push(t._getVersionSign())})),i?e=function(t,e){var n=Wr(t);t=n.length,t||Fr("");for(var i=0,r=t;i<r;i++)e=function(t,e){e.length||Fr(""),X(t)||Fr("");var n=t.type,i=Rd.get(n);return i||Fr(""),n=N(e,(function(t){var e=t,n=(t=i,new Dd),r=e.data,o=n.sourceFormat=e.sourceFormat,a=e.startIndex,s=(e.seriesLayoutBy!==Nc&&Fr(""),[]),l={};if(c=e.dimensionsDefine)R(c,(function(t,e){var n=t.name;e={index:e,name:n,displayName:t.displayName};s.push(e),null!=n&&(vt(l,n)&&Fr(""),l[n]=e)}));else for(var u=0;u<e.dimensionsDetectedCount;u++)s.push({index:u});var h=Zp(o,Nc),c=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return h(r,a,s,t)},n.getRawData=F(Ad,null,e)),n.cloneRawData=F(Od,null,e),Qp(o,Nc)),p=(n.count=F(c,null,r,a,s),ed(o)),d=(n.retrieveValue=function(t,e){return t=h(r,a,s,t),d(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=s[e])?p(t,e,n.name):void 0});return n.getDimensionInfo=F(Ld,null,s,l),n.cloneAllDimensionInfo=F(Pd,null,s),n})),N(Wr(i.transform({upstream:n[0],upstreamList:n,config:T(t.config)})),(function(t,n){X(t)||Fr(""),t.data||Fr(""),Nd(zp(t.data))||Fr("");var i=e[0];n=i&&0===n&&!t.dimensions?((n=i.startIndex)&&(t.data=i.data.slice(0,n).concat(t.data)),{seriesLayoutBy:Nc,sourceHeader:n,dimensions:i.metaRawOption.dimensions}):{seriesLayoutBy:Nc,sourceHeader:0,dimensions:t.dimensions};return Np(t.data,n,null)}))}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}(i,o,n.componentIndex):null!=r&&(e=[new Pp({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:T(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},qd.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},qd.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},qd.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},qd.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0];r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],$d(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new Ud).initData(new Up(e,t.length),t),o[n]=r),r},qd.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return $d(e)?(t=Hc(e))?[t.getSourceManager()]:[]:N((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?io(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},no).models:[],(function(t){return t.getSourceManager()}))},qd.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return $d(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var Yd=qd;function qd(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function Zd(t){t.option.transform&&st(t.option.transform)}function $d(t){return"series"===t.mainType}function Kd(t){throw new Error(t)}function Qd(t,e){return e.type=t,e}function Jd(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,f=(t=t.multipleSeries,p.getData()),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=H(m),_=function(t,e){var n;return e=t.getData().getItemVisual(e,"style")[t.visualDrawType],n=n||"transparent",G(t=e)?t:X(t)&&t.colorStops&&(t.colorStops[0]||{}).color||n}(p,d);function x(t,e){e=s.getDimensionInfo(e),e&&!1!==e.otherDims.tooltip&&(l?c.push(Qd("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}return 1<y||v&&!y?(i=m,r=d,o=g,a=_,s=p.getData(),l=E(i,(function(t,e,n){return n=s.getDimensionInfo(n),t||n&&!1!==n.tooltip&&null!=n.displayName}),!1),u=[],h=[],c=[],o.length?R(o,(function(t){x(id(s,r,t),t)})):R(i,x),i=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,e=o.blocks,n=(o=o.inlineValues)[0]):y?(y=f.getDimensionInfo(g[0]),n=o=id(f,d,g[0]),i=y.type):n=o=v?m[0]:m,g=Zr(p),y=g&&p.name||"",v=f.getName(d),m=t?y:v,Qd("section",{header:y,noHeader:t||!g,sortParam:n,blocks:[Qd("nameValue",{markerType:"item",markerColor:_,name:m,noName:!ot(m),value:o,valueType:i,dataIndex:d})].concat(e||[])})}var tf=Qr();function ef(t,e){return t.getName(e)||t.getId(e)}n(of,nf=Mc),of.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=ad({count:sf,reset:lf}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(tf(this).sourceManager=new Yd(this)).prepareSource(),t=this.getInitialData(t,n),hf(t,this),this.dataTask.context.data=t,tf(this).dataBeforeProcessed=t,af(this),this._initSelectedMapFromData(t)},of.prototype.mergeDefaultAndTheme=function(t,e){var n=yc(this),i=n?vc(t):{},r=this.subType;Mc.hasClass(r),C(t,e.getTheme().get(this.subType)),C(t,this.getDefaultOption()),Gr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&mc(t,i,n)},of.prototype.mergeOption=function(t,e){t=C(this.option,t,!0),this.fillDataTextStyle(t.data);var n=yc(this);n&&mc(this.option,t,n),n=tf(this).sourceManager,n.dirty(),n.prepareSource(),n=this.getInitialData(t,e);hf(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,tf(this).dataBeforeProcessed=n,af(this),this._initSelectedMapFromData(n)},of.prototype.fillDataTextStyle=function(t){if(t&&!q(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&Gr(t[n],"label",e)},of.prototype.getInitialData=function(t,e){},of.prototype.appendData=function(t){this.getRawData().appendData(t.data)},of.prototype.getData=function(t){var e=pf(this);return e?(e=e.context.data,null==t?e:e.getLinkedData(t)):tf(this).data},of.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},of.prototype.setData=function(t){var e,n=pf(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),tf(this).data=t},of.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return ft(t)},of.prototype.getSourceManager=function(){return tf(this).sourceManager},of.prototype.getSource=function(){return this.getSourceManager().getSource()},of.prototype.getRawData=function(){return tf(this).dataBeforeProcessed},of.prototype.getColorBy=function(){return this.get("colorBy")||"series"},of.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},of.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},of.prototype.formatTooltip=function(t,e,n){return Jd({series:this,dataIndex:t,multipleSeries:e})},of.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!r.node||t&&t.ssr)&&!!(t=!((t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold"))&&t)},of.prototype.restoreData=function(){this.dataTask.dirty()},of.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return Zc.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},of.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},of.prototype.getProgressive=function(){return this.get("progressive")},of.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},of.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},of.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=ef(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},of.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},of.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=B(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},of.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[ef(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},of.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},of.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){X(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=ef(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=ef(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},of.prototype._initSelectedMapFromData=function(t){var e;this.option.selectedMap||(e=[],t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),0<e.length&&this._innerSelect(t,e))},of.registerClass=function(t){return Mc.registerClass(t)},of.protoInitialize=((vh=of.prototype).type="series.__base__",vh.seriesIndex=0,vh.ignoreStyleOnData=!1,vh.hasSymbolVisual=!1,vh.defaultSymbol="circle",vh.visualStyleAccessPath="itemStyle",void(vh.visualDrawType="fill"));var nf,rf=of;function of(){var t=null!==nf&&nf.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function af(t){var e,n,i=t.name;Zr(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],R(t,(function(t){t=e.getDimensionInfo(t),t.displayName&&n.push(t.displayName)})),n.join(" ")||i))}function sf(t){return t.model.getRawData().count()}function lf(t){return t=t.model,t.setData(t.getRawData().cloneShallow()),uf}function uf(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function hf(t,e){R(gt(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,V(cf,e))}))}function cf(t,e){return t=pf(t),t&&t.setOutputEnd((e||this).count()),e}function pf(t){var e,n=(t.ecModel||{}).scheduler;n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}L(rf,bh),L(rf,Zc),uo(rf,Mc),ff.prototype.init=function(t,e){},ff.prototype.render=function(t,e,n,i){},ff.prototype.dispose=function(t,e){},ff.prototype.updateView=function(t,e,n,i){},ff.prototype.updateLayout=function(t,e,n,i){},ff.prototype.updateVisual=function(t,e,n,i){},ff.prototype.toggleBlurSeries=function(t,e,n){},ff.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var df=ff;function ff(){this.group=new dr,this.uid=Dh("viewComponent")}lo(df),fo(df);var gf,yf=Qr(),mf=(gf=Qr(),function(t){var e=gf(t),n=(t=t.pipelineContext,!!e.large),i=!!e.progressiveRender,r=e.large=!(!t||!t.large);e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}),vf=(_f.prototype.init=function(t,e){},_f.prototype.render=function(t,e,n,i){},_f.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType),t&&wf(t,i,"emphasis")},_f.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType),t&&wf(t,i,"normal")},_f.prototype.remove=function(t,e){this.group.removeAll()},_f.prototype.dispose=function(t,e){},_f.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},_f.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},_f.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},_f.prototype.eachRendered=function(t){var e=this.group,n=t;if(e)if(H(e))for(var i=0;i<e.length;i++)th(e[i],n);else th(e,n)},_f.markUpdateMethod=function(t,e){yf(t).updateMethod=e},_f.protoInitialize=void(_f.prototype.type="chart"),_f);function _f(){this.group=new dr,this.uid=Dh("viewChart"),this.renderTask=ad({plan:bf,reset:Sf}),this.renderTask.context={view:this}}function xf(t,e,n){t&&el(t)&&("emphasis"===e?Vs:Hs)(t,n)}function wf(t,e,n){var i,r=Kr(t,e),o=e&&null!=e.highlightKey?(e=e.highlightKey,i=null==(i=ys[e])&&gs<=32?ys[e]=gs++:i):null;null!=r?R(Wr(r),(function(e){xf(t.getItemGraphicEl(e),n,o)})):t.eachItemGraphicEl((function(t){xf(t,n,o)}))}function bf(t){return mf(t.model)}function Sf(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=(t=t.view,r&&yf(r).updateMethod);o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),Mf[o]}lo(vf),fo(vf);var Mf={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function Tf(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}function p(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var d=s||e,f=s||n;s=null,r=i-(f?l:u)-d,clearTimeout(h),f?h=setTimeout(c,d):0<=r?c():h=setTimeout(c,-r),l=i}return e=e||0,p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}var Cf=Qr(),kf={itemStyle:go(xh,!0),lineStyle:go(yh,!0)},Df={lineStyle:"stroke",itemStyle:"fill"};function If(t,e){return t=t.visualStyleMapper||kf[e],t||(console.warn("Unknown style type '"+e+"'."),kf.itemStyle)}function Af(t,e){return t=t.visualDrawType||Df[e],t||(console.warn("Unknown style type '"+e+"'."),"fill")}mo={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=If(t,i)(r),a=(r=r.getShallow("decal"),r&&(n.setVisual("decal",r),r.dirty=!0),Af(t,i)),s=(r=o[a],W(r)?r:null);i="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!i||(r=t.getColorFromPalette(t.name,null,e.getSeriesCount()),o[a]||(o[a]=r,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||W(o.fill)?r:o.fill,o.stroke="auto"===o.stroke||W(o.stroke)?r:o.stroke),n.setVisual("style",o),n.setVisual("drawType",a),!e.isSeriesFiltered(t)&&s)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=k({},o);r[a]=s(i),e.setItemVisual(n,"style",r)}}}};var Of=new Th,Lf=(fh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n,i,r;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),n=t.visualStyleAccessPath||"itemStyle",i=If(t,n),r=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var o=t.getRawDataItem(e);o&&o[n]&&(Of.option=o[n],o=i(Of),k(t.ensureUniqueItemVisual(e,"style"),o),Of.option.decal&&(t.setItemVisual(e,"decal",Of.option.decal),Of.option.decal.dirty=!0),r in o)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},zh={performRawSeries:!0,overallReset:function(t){var e=ft();t.eachSeries((function(t){var n,i=t.getColorBy();t.isColorBySeries()||(i=t.type+"-"+i,(n=e.get(i))||e.set(i,n={}),Cf(t).scope=n)})),t.eachSeries((function(e){var n,i,r,o,a,s;e.isColorBySeries()||t.isSeriesFiltered(e)||(n=e.getRawData(),i={},r=e.getData(),o=Cf(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=Af(e,a),r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a,l=i[t];r.getItemVisual(l,"colorFromPalette")&&(l=r.ensureUniqueItemVisual(l,"style"),t=n.getName(t)||t+"",a=n.count(),l[s]=e.getColorFromPalette(t,o,a))})))}))}},Math.PI);Rf.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){t=t.overallTask,t&&t.dirty()}))},Rf.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},Rf.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},Rf.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=(e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,t.get("large")&&i>=t.get("largeThreshold"));i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},Rf.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=ft();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},Rf.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;R(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{});rt(!(i.reset&&i.overallReset),""),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},Rf.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},Rf.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},Rf.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},Rf.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}R(t,(function(t,s){var l,u,h,c,p;i.visualType&&i.visualType!==t.visualType||(l=(u=o._stageTaskMap.get(t.uid)).seriesTaskMap,(u=u.overallTask)?((c=u.agentStubMap).each((function(t){a(i,t)&&(t.dirty(),h=!0)})),h&&u.dirty(),o.updatePayload(u,n),p=o.getPerformArgs(u,i.block),c.each((function(t){t.perform(p)})),u.perform(p)&&(r=!0)):l&&l.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)})))})),this.unfinished=r||this.unfinished},Rf.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},Rf.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())}))},Rf.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},Rf.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=ft(),s=(e=t.seriesType,t.getTargetSeries);function l(e){var s=e.uid;s=a.set(s,o&&o.get(s)||ad({plan:Ff,reset:Vf,count:Gf}));s.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,s)}t.createOnAllSeries?n.eachRawSeries(l):e?n.eachRawSeriesByType(e,l):s&&s(n,i).each(l)},Rf.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||ad({reset:Nf}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=ft(),l=(e=t.seriesType,t.getTargetSeries),u=!0,h=!1;function c(t){var e=t.uid;e=s.set(e,a&&a.get(e)||(h=!0,ad({reset:Ef,onDirty:Bf})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}rt(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,R(n.getSeries(),c)),h&&o.dirty()},Rf.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t),t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},Rf.wrapStageHandler=function(t,e){return(t=W(t)?{overallReset:t,seriesType:function(t){Uf=null;try{t(jf,Xf)}catch(t){}return Uf}(t)}:t).uid=Dh("stageHandler"),e&&(t.visualType=e),t};var Pf=Rf;function Rf(t,e,n,i){this._stageTaskMap=ft(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function Nf(t){t.overallReset(t.ecModel,t.api,t.payload)}function Ef(t){return t.overallProgress&&zf}function zf(){this.agent.dirty(),this.getDownstream().dirty()}function Bf(){this.agent&&this.agent.dirty()}function Ff(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function Vf(t){return t.useClearVisual&&t.data.clearAllVisual(),t=t.resetDefines=Wr(t.reset(t.model,t.ecModel,t.api,t.payload)),1<t.length?N(t,(function(t,e){return Wf(e)})):Hf}var Hf=Wf(0);function Wf(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function Gf(t){return t.data.count()}var Uf,jf={},Xf={};function Yf(t,e){for(var n in e.prototype)t[n]=_t}function qf(){return{axisLine:{lineStyle:{color:Zf}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}Yf(jf,Jc),Yf(Xf,rp),jf.eachSeriesByType=jf.eachRawSeriesByType=function(t){Uf=t},jf.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Uf=t.subType)};vh=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],bh={color:vh,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],vh]};var Zf="#B9B8CE",$f=(xh="#100C2A",yh=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],vh={darkMode:!0,color:yh,backgroundColor:xh,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Zf}},textStyle:{color:Zf},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Zf}},dataZoom:{borderColor:"#71708A",textStyle:{color:Zf},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Zf}},timeline:{lineStyle:{color:Zf},label:{color:Zf},controlStyle:{color:Zf,borderColor:Zf}},calendar:{itemStyle:{color:xh},dayLabel:{color:Zf},monthLabel:{color:Zf},yearLabel:{color:Zf}},timeAxis:qf(),logAxis:qf(),valueAxis:qf(),categoryAxis:qf(),line:{symbol:"circle"},graph:{color:yh},gauge:{title:{color:Zf},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Zf},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},vh.categoryAxis.splitLine.show=!1,Kf.prototype.normalizeQuery=function(t){var e,n,i,r={},o={},a={};return G(t)?(e=so(t),r.mainType=e.main||null,r.subType=e.sub||null):(n=["Index","Name","Id"],i={name:1,dataIndex:1,dataType:1},R(t,(function(t,e){for(var s=!1,l=0;l<n.length;l++){var u=n[l],h=e.lastIndexOf(u);0<h&&h===e.length-u.length&&"data"!==(h=e.slice(0,h))&&(r.mainType=h,r[u.toLowerCase()]=t,s=!0)}i.hasOwnProperty(e)&&(o[e]=t,s=!0),s||(a[e]=t)}))),{cptQuery:r,dataQuery:o,otherQuery:a}},Kf.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},Kf.prototype.afterTrigger=function(){this.eventInfo=null},Kf);function Kf(){}var Qf=["symbol","symbolSize","symbolRotate","symbolOffset"],Jf=Qf.concat(["symbolKeepAspect"]);xh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i,r={},o={},a=!1,s=0;s<Qf.length;s++){var l=Qf[s],u=t.get(l);W(u)?(a=!0,o[l]=u):r[l]=u}if(r.symbol=r.symbol||t.defaultSymbol,n.setVisual(k({legendIcon:t.legendIcon||r.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},r)),!e.isSeriesFiltered(t))return i=B(o),{dataEach:a?function(e,n){for(var r=t.getRawValue(n),a=t.getDataParams(n),s=0;s<i.length;s++){var l=i[s];e.setItemVisual(n,l,o[l](r,a))}}:null}}}},yh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Jf.length;i++){var r=Jf[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function tg(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e,i,a=t.seriesIndex,s=t.option.selectedMap,l=r.selected,u=0;u<l.length;u++)l[u].seriesIndex===a&&(i=Kr(e=t.getData(),r.fromActionPayload),n.trigger(o,{type:o,seriesId:t.id,name:H(i)?e.getName(i[0]):e.getName(i),selected:G(s)?s:k({},s)}))}))}function eg(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var ng=Math.round(9*Math.random()),ig="function"==typeof Object.defineProperty,rg=(og.prototype.get=function(t){return this._guard(t)[this._id]},og.prototype.set=function(t,e){return t=this._guard(t),ig?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},og.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},og.prototype.has=function(t){return!!this._guard(t)[this._id]},og.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},og);function og(){this._id="__ec_inner_"+ng++}var ag=Na.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2;e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),sg=Na.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2;e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),lg=Na.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=(e=Math.max(r,e.height),r=r/2,r*r/(e-r)),a=(e=i-e+r+o,Math.asin(o/r)),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),ug=Na.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x;e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),hg={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i),r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},cg={},pg=(R({line:uu,rect:Ka,roundRect:Ka,square:Ka,circle:Tl,diamond:sg,pin:lg,arrow:ug,triangle:ag},(function(t,e){cg[e]=new t})),Na.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){t=tr(t,e,n);var i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=cg[r])||cg[r="rect"],hg[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function dg(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function fg(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Ku(t.slice(8),new De(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?$u(t.slice(7),{},new De(e,n,i,r),a?"center":"cover"):new pg({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=dg,o&&a.setColor(o),a}function gg(t){return isFinite(t)}function yg(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=gg(u)?u:.5,h=gg(h)?h:.5,c=0<=c&&gg(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=gg(o)?o:0,l=gg(l)?l:1,i=gg(i)?i:0,u=gg(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function mg(t){return parseInt(t,10)}function vg(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e];e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||mg(n[i])||mg(t.style[i]))-(mg(n[o])||0)-(mg(n[e])||0)|0)}function _g(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:j(r)?[r]:H(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=N(i,(function(t){return t/e})),r/=e),[i,r]}var xg=new ma(!0);function wg(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function bg(t){return"string"==typeof t&&"none"!==t}function Sg(t){return t=t.fill,null!=t&&"none"!==t}function Mg(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function Tg(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function Cg(t,e,n){n=xo(e.image,e.__image,n);if(bo(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*xt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var kg=["shadowBlur","shadowOffsetX","shadowOffsetY"],Dg=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Ig(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(Lg(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?No.opacity:o),!i&&e.blend===n.blend||(a||(Lg(t,r),a=!0),t.globalCompositeOperation=e.blend||No.blend);for(var s=0;s<kg.length;s++){var l=kg[s];!i&&e[l]===n[l]||(a||(Lg(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(Lg(t,r),a=!0),t.shadowColor=e.shadowColor||No.shadowColor),a}function Ag(t,e,n,i,r){var o=Pg(e,r.inHover),a=i?null:n&&Pg(n,r.inHover)||{};if(o!==a){var s=Ig(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(Lg(t,r),s=!0),bg(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(Lg(t,r),s=!0),bg(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(Lg(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(Lg(t,r),s=!0),t.lineWidth=n);for(var l=0;l<Dg.length;l++){var u=Dg[l],h=u[0];!i&&o[h]===a[h]||(s||(Lg(t,r),s=!0),t[h]=o[h]||u[1])}}}function Og(t,e){e=e.transform;var n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}function Lg(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function Pg(t,e){return e&&t.__hoverStyle||t.style}function Rg(t,e){Ng(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Ng(t,e,n,i){var r=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var o=e.__clipPaths,a=n.prevElClipPaths,s=!1,u=!1;if(!a||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(o,a)){if(a&&a.length&&(Lg(t,n),t.restore(),u=s=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),o&&o.length){Lg(t,n),t.save();for(var h=o,c=t,p=(a=n,!1),d=0;d<h.length;d++){var f=h[d];p=p||f.isZeroArea();Og(c,f),c.beginPath(),f.buildPath(c,f.shape),c.clip()}a.allClipped=p,s=!0}n.prevElClipPaths=o}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();a=n.prevEl;var g,y,m,v,_,x,w,b,S,M,T,C,k,D,I,A,O,L,P,R,N,E,z,B=(a||(u=s=!0),e instanceof Na&&e.autoBatch&&(o=e.style,B=Sg(o),g=wg(o),!(o.lineDash||!(+B^+g)||B&&"string"!=typeof o.fill||g&&"string"!=typeof o.stroke||o.strokePercent<1||o.strokeOpacity<1||o.fillOpacity<1)));s=(s||(g=r,o=a.transform,g&&o?g[0]!==o[0]||g[1]!==o[1]||g[2]!==o[2]||g[3]!==o[3]||g[4]!==o[4]||g[5]!==o[5]:g||o)?(Lg(t,n),Og(t,e)):B||Lg(t,n),Pg(e,n.inHover));if(e instanceof Na)1!==n.lastDrawType&&(u=!0,n.lastDrawType=1),Ag(t,e,a,u,n),B&&(n.batchFill||n.batchStroke)||t.beginPath(),r=t,o=e,G=B,I=wg(v=s),A=Sg(v),O=v.strokePercent,L=O<1,P=!o.path,o.silent&&!L||!P||o.createPathProxy(),R=o.path||xg,N=o.__dirty,G||(_=v.fill,z=v.stroke,x=A&&!!_.colorStops,w=I&&!!z.colorStops,b=A&&!!_.image,S=I&&!!z.image,E=k=C=T=M=void 0,(x||w)&&(E=o.getBoundingRect()),x&&(M=N?yg(r,_,E):o.__canvasFillGradient,o.__canvasFillGradient=M),w&&(T=N?yg(r,z,E):o.__canvasStrokeGradient,o.__canvasStrokeGradient=T),b&&(C=N||!o.__canvasFillPattern?Cg(r,_,o):o.__canvasFillPattern,o.__canvasFillPattern=C),S&&(k=N||!o.__canvasStrokePattern?Cg(r,z,o):o.__canvasStrokePattern,o.__canvasStrokePattern=C),x?r.fillStyle=M:b&&(C?r.fillStyle=C:A=!1),w?r.strokeStyle=T:S&&(k?r.strokeStyle=k:I=!1)),E=o.getGlobalScale(),R.setScale(E[0],E[1],o.segmentIgnoreThreshold),r.setLineDash&&v.lineDash&&(D=(_=_g(o))[0],W=_[1]),z=!0,(P||4&N)&&(R.setDPR(r.dpr),L?R.setContext(null):(R.setContext(r),z=!1),R.reset(),o.buildPath(R,o.shape,G),R.toStatic(),o.pathUpdated()),z&&R.rebuildPath(r,L?O:1),D&&(r.setLineDash(D),r.lineDashOffset=W),G||(v.strokeFirst?(I&&Tg(r,v),A&&Mg(r,v)):(A&&Mg(r,v),I&&Tg(r,v))),D&&r.setLineDash([]),B&&(n.batchFill=s.fill||"",n.batchStroke=s.stroke||"");else if(e instanceof Fa)3!==n.lastDrawType&&(u=!0,n.lastDrawType=3),Ag(t,e,a,u,n),x=t,M=e,null!=(C=(b=s).text)&&(C+=""),C&&(x.font=b.font||l,x.textAlign=b.textAlign,x.textBaseline=b.textBaseline,T=w=void 0,x.setLineDash&&b.lineDash&&(w=(M=_g(M))[0],T=M[1]),w&&(x.setLineDash(w),x.lineDashOffset=T),b.strokeFirst?(wg(b)&&x.strokeText(C,b.x,b.y),Sg(b)&&x.fillText(C,b.x,b.y)):(Sg(b)&&x.fillText(C,b.x,b.y),wg(b)&&x.strokeText(C,b.x,b.y)),w)&&x.setLineDash([]);else if(e instanceof Ua)2!==n.lastDrawType&&(u=!0,n.lastDrawType=2),S=a,k=u,Ig(t,Pg(e,(E=n).inHover),S&&Pg(S,E.inHover),k,E),_=t,P=s,(o=(N=e).__image=xo(P.image,N.__image,N,N.onload))&&bo(o)&&(z=P.x||0,R=P.y||0,L=N.getWidth(),N=N.getHeight(),O=o.width/o.height,null==L&&null!=N?L=N*O:null==N&&null!=L?N=L/O:null==L&&null==N&&(L=o.width,N=o.height),P.sWidth&&P.sHeight?(y=P.sx||0,m=P.sy||0,_.drawImage(o,y,m,P.sWidth,P.sHeight,z,R,L,N)):P.sx&&P.sy?(y=P.sx,m=P.sy,_.drawImage(o,y,m,L-y,N-m,z,R,L,N)):_.drawImage(o,z,R,L,N));else if(e.getTemporalDisplayables){4!==n.lastDrawType&&(u=!0,n.lastDrawType=4);var F,V,H=t,W=e,G=n,U=W.getDisplayables(),j=W.getTemporalDisplayables(),X=(H.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:G.viewWidth,viewHeight:G.viewHeight,inHover:G.inHover});for(F=W.getCursor(),V=U.length;F<V;F++)(Y=U[F]).beforeBrush&&Y.beforeBrush(),Y.innerBeforeBrush(),Ng(H,Y,X,F===V-1),Y.innerAfterBrush(),Y.afterBrush&&Y.afterBrush(),X.prevEl=Y;for(var Y,q=0,Z=j.length;q<Z;q++)(Y=j[q]).beforeBrush&&Y.beforeBrush(),Y.innerBeforeBrush(),Ng(H,Y,X,q===Z-1),Y.innerAfterBrush(),Y.afterBrush&&Y.afterBrush(),X.prevEl=Y;W.clearTemporalDisplayables(),W.notClear=!0,H.restore()}B&&i&&Lg(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=-2,e.__isRendered=!1}var Eg=new rg,zg=new kn(100),Bg=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Fg(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&Eg.delete(t),e=Eg.get(t);if(e)return e;for(var o,s=D(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),l=(e=("none"===s.backgroundColor&&(s.backgroundColor=null),{repeat:"repeat"}),e),u=[n],c=!0,p=0;p<Bg.length;++p){var d=s[Bg[p]];if(null!=d&&!H(d)&&!G(d)&&!j(d)&&"boolean"!=typeof d){c=!1;break}u.push(d)}c&&(o=u.join(",")+(r?"-svg":""),x=zg.get(o))&&(r?l.svgElement=x:l.image=x);var f,g=function t(e){if(!e||0===e.length)return[[0,0]];if(j(e))return[[o=Math.ceil(e),o]];for(var n=!0,i=0;i<e.length;++i)if(!j(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;j(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=N(e[i],(function(t){return Math.ceil(t)}))).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(s.dashArrayX),y=function(t){if(!t||"object"==a(t)&&0===t.length)return[0,0];if(j(t))return[e=Math.ceil(t),e];var e=N(t,(function(t){return Math.ceil(t)}));return t.length%2?e.concat(e):e}(s.dashArrayY),m=function t(e){if(!e||0===e.length)return[["rect"]];if(G(e))return[[e]];for(var n=!0,i=0;i<e.length;++i)if(!G(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)G(e[i])?r.push([e[i]]):r.push(e[i]);return r}(s.symbol),v=function(t){return N(t,Vg)}(g),_=Vg(y),x=!r&&h.createCanvas(),w=r&&{tag:"g",attrs:{},key:"dcl",children:[]},b=function(){for(var t=1,e=0,n=v.length;e<n;++e)t=Br(t,v[e]);var i=1;for(e=0,n=m.length;e<n;++e)i=Br(i,m[e].length);t*=i;var r=_*v.length*m.length;return{width:Math.max(1,Math.min(t,s.maxTileWidth)),height:Math.max(1,Math.min(r,s.maxTileHeight))}}();x&&(x.width=b.width*n,x.height=b.height*n,f=x.getContext("2d")),f&&(f.clearRect(0,0,x.width,x.height),s.backgroundColor)&&(f.fillStyle=s.backgroundColor,f.fillRect(0,0,x.width,x.height));for(var S=0,M=0;M<y.length;++M)S+=y[M];if(!(S<=0))for(var T=-_,C=0,k=0,I=0;T<b.height;){if(C%2==0){for(var A=k/2%m.length,O=0,L=0,P=0;O<2*b.width;){var R,E,z,B,F,V=0;for(M=0;M<g[I].length;++M)V+=g[I][M];if(V<=0)break;L%2==0&&(E=.5*(1-s.symbolSize),R=O+g[I][L]*E,E=T+y[C]*E,z=g[I][L]*s.symbolSize,B=y[C]*s.symbolSize,F=P/2%m[A].length,function(t,e,o,a,l){var u=r?1:n;l=fg(l,t*u,e*u,o*u,a*u,s.color,s.symbolKeepAspect);r?(t=i.painter.renderOneToVNode(l))&&w.children.push(t):Rg(f,l)}(R,E,z,B,m[A][F])),O+=g[I][L],++P,++L===g[I].length&&(L=0)}++I===g.length&&(I=0)}T+=y[C],++k,++C===y.length&&(C=0)}return c&&zg.put(o,x||w),l.image=x,l.svgElement=w,l.svgWidth=b.width,l.svgHeight=b.height,e.rotation=s.rotation,e.scaleX=e.scaleY=r?1:1/n,Eg.set(t,e),t.dirty=!1,e}function Vg(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Hg=new Wt,Wg={},Gg=(sg={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},"__flagInMainProcess"),Ug="__pendingUpdate",jg="__needsUpdateStatus",Xg=/^[a-zA-Z0-9_]+$/,Yg="__connectUpdateStatus";function qg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return $g(this,t,e);this.id}}function Zg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return $g(this,t,e)}}function $g(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),Wt.prototype[e].apply(t,n)}n(Jg,Kg=Wt);var Kg,Qg=Jg;function Jg(){return null!==Kg&&Kg.apply(this,arguments)||this}lg=Qg.prototype;var ty,ey,ny,iy,ry,oy,ay,sy,ly,uy,hy,cy,py,dy,fy,gy,yy,my,vy,_y=(lg.on=Zg("on"),lg.off=Zg("off"),n(xy,vy=Wt),xy.prototype._onframe=function(){if(!this._disposed){my(this);var t=this._scheduler;if(this[Ug]){var e=this[Ug].silent;this[Gg]=!0;try{ty(this),iy.update.call(this,null,this[Ug].updateParams)}catch(t){throw this[Gg]=!1,this[Ug]=null,t}this._zr.flush(),this[Gg]=!1,this[Ug]=null,sy.call(this,e),ly.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),oy(this,i),t.performVisualTasks(i),dy(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},xy.prototype.getDom=function(){return this._dom},xy.prototype.getId=function(){return this.id},xy.prototype.getZr=function(){return this._zr},xy.prototype.isSSR=function(){return this._ssr},xy.prototype.setOption=function(t,e,n){if(!this[Gg])if(this._disposed)this.id;else{X(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[Gg]=!0,this._model&&!e||(e=new hp(this._api),a=this._theme,(s=this._model=new Jc).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},Ay);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[Ug]={silent:i,updateParams:s},this[Gg]=!1,this.getZr().wakeUp();else{try{ty(this),iy.update.call(this,null,s)}catch(t){throw this[Ug]=null,this[Gg]=!1,t}this._ssr||this._zr.flush(),this[Ug]=null,this[Gg]=!1,sy.call(this,i),ly.call(this,i)}}},xy.prototype.setTheme=function(){},xy.prototype.getModel=function(){return this._model},xy.prototype.getOption=function(){return this._model&&this._model.getOption()},xy.prototype.getWidth=function(){return this._zr.getWidth()},xy.prototype.getHeight=function(){return this._zr.getHeight()},xy.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||r.hasGlobalWindow&&window.devicePixelRatio||1},xy.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},xy.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},xy.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},xy.prototype.getSvgDataURL=function(){var t;if(r.svgSupported)return R((t=this._zr).storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()},xy.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,R(r,(function(t){e.eachComponent({mainType:t},(function(t){t=i._componentsMap[t.__viewId],t.group.ignore||(n.push(t),t.group.ignore=!0)}))})),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),R(n,(function(t){t.group.ignore=!1})),r;this.id},xy.prototype.getConnectedDataURL=function(t){var e,n,i,r,o,a,s,l,u,c,p,d,f,g,y;if(!this._disposed)return e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,Ny[n]?(a=o=1/0,l=s=-1/0,u=[],c=t&&t.pixelRatio||this.getDevicePixelRatio(),R(Ry,(function(h,c){var p;h.group===n&&(p=e?h.getZr().painter.getSvgDom().innerHTML:h.renderToCanvas(T(t)),h=h.getDom().getBoundingClientRect(),o=i(h.left,o),a=i(h.top,a),s=r(h.right,s),l=r(h.bottom,l),u.push({dom:p,left:h.left,top:h.top}))})),p=(s*=c)-(o*=c),d=(l*=c)-(a*=c),f=h.createCanvas(),(g=xr(f,{renderer:e?"svg":"canvas"})).resize({width:p,height:d}),e?(y="",R(u,(function(t){var e=t.left-o,n=t.top-a;y+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),g.painter.getSvgRoot().innerHTML=y,t.connectedBackgroundColor&&g.painter.setBackgroundColor(t.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()):(t.connectedBackgroundColor&&g.add(new Ka({shape:{x:0,y:0,width:p,height:d},style:{fill:t.connectedBackgroundColor}})),R(u,(function(t){t=new Ua({style:{x:t.left*c-o,y:t.top*c-a,image:t.dom}}),g.add(t)})),g.refreshImmediately(),f.toDataURL("image/"+(t&&t.type||"png")))):this.getDataURL(t);this.id},xy.prototype.convertToPixel=function(t,e){return ry(this,"convertToPixel",t,e)},xy.prototype.convertFromPixel=function(t,e){return ry(this,"convertFromPixel",t,e)},xy.prototype.containPixel=function(t,e){var n;if(!this._disposed)return R(to(this._model,t),(function(t,i){0<=i.indexOf("Models")&&R(t,(function(t){var r=t.coordinateSystem;r&&r.containPoint?n=n||!!r.containPoint(e):"seriesModels"===i&&(r=this._chartsMap[t.__viewId])&&r.containPoint&&(n=n||r.containPoint(e,t))}),this)}),this),!!n;this.id},xy.prototype.getVisual=function(t,e){t=to(this._model,t,{defaultMainType:"series"});var n=t.seriesModel.getData();t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},xy.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},xy.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},xy.prototype._initEvents=function(){var t,e,n,i=this;R(Cy,(function(t){function e(e){var n,r,o,a=i.getModel(),s=e.target;"globalout"===t?n={}:s&&eg(s,(function(t){var e;t=fs(t);return t&&null!=t.dataIndex?(e=t.dataModel||a.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,s)||{},1):t.eventData&&(n=k({},t.eventData),1)}),!0),n&&(r=n.componentType,o=n.componentIndex,"markLine"!==r&&"markPoint"!==r&&"markArea"!==r||(r="series",o=n.seriesIndex),o=(r=r&&null!=o&&a.getComponent(r,o))&&i["series"===r.mainType?"_chartsMap":"_componentsMap"][r.__viewId],n.event=e,n.type=t,i._$eventProcessor.eventInfo={targetEl:s,packedEvent:n,model:r,view:o},i.trigger(t,n))}e.zrEventfulCallAtLast=!0,i._zr.on(t,e,i)})),R(Dy,(function(t,e){i._messageCenter.on(e,(function(t){this.trigger(e,t)}),i)})),R(["selectchanged"],(function(t){i._messageCenter.on(t,(function(e){this.trigger(t,e)}),i)})),t=this._messageCenter,n=(e=this)._api,t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(tg("map","selectchanged",e,i,t),tg("pie","selectchanged",e,i,t)):"select"===t.fromAction?(tg("map","selected",e,i,t),tg("pie","selected",e,i,t)):"unselect"===t.fromAction&&(tg("map","unselected",e,i,t),tg("pie","unselected",e,i,t))}))},xy.prototype.isDisposed=function(){return this._disposed},xy.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},xy.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&ro(this.getDom(),By,""),e=(t=this)._api,n=t._model,R(t._componentsViews,(function(t){t.dispose(n,e)})),R(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete Ry[t.id])},xy.prototype.resize=function(t){if(!this[Gg])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){e=e.resetOption("media");var n=t&&t.silent;this[Ug]&&(null==n&&(n=this[Ug].silent),e=!0,this[Ug]=null),this[Gg]=!0;try{e&&ty(this),iy.update.call(this,{type:"resize",animation:k({duration:0},t&&t.animation)})}catch(t){throw this[Gg]=!1,t}this[Gg]=!1,sy.call(this,n),ly.call(this,n)}}},xy.prototype.showLoading=function(t,e){this._disposed?this.id:(X(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Py[t]&&(t=Py[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},xy.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},xy.prototype.makeActionFromEvent=function(t){var e=k({},t);return e.type=Dy[t.type],e},xy.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(X(e)||(e={silent:!!e}),ky[t.type]&&this._model&&(this[Gg]?this._pendingActions.push(t):(n=e.silent,ay.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&r.browser.weChat&&this._throttledZrFlush(),sy.call(this,n),ly.call(this,n))))},xy.prototype.updateLabelLayout=function(){Hg.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},xy.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},xy.internalField=(ty=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),ey(t,!0),ey(t,!1),e.plan()},ey=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l,u=t.__requireNewView,h=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type);u=!u&&o[h];u||(l=so(t.type),(u=new(e?df.getClass(l.main,l.sub):vf.getClass(l.sub))).init(n,s),o[h]=u,r.push(u),a.add(u.group)),t.__viewId=u.__id=h,u.__alive=!0,u.__model=t,u.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},e||i.prepareView(u,t,n,s)}for(e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u),l=0;l<r.length;){var h=r[l];h.__alive?l++:(e||h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},ny=function(t,e,n,i,r){var o,a,s=t._model;function l(i){i&&i.__alive&&i[e]&&i[e](i.__model,s,t._api,n)}s.setUpdatePayload(n),i?((o={})[i+"Id"]=n[i+"Id"],o[i+"Index"]=n[i+"Index"],o[i+"Name"]=n[i+"Name"],o={mainType:i,query:o},r&&(o.subType=r),null!=(r=n.excludeSeriesId)&&(a=ft(),R(Wr(r),(function(t){t=qr(t,null),null!=t&&a.set(t,!0)}))),s&&s.eachComponent(o,(function(e){var i,r,o=a&&null!=a.get(e.id);if(!o)if(il(n))if(e instanceof rf){if(n.type===ws&&!n.notBlur&&!e.get(["emphasis","disabled"])){o=e;var s=n,l=t._api,u=o.seriesIndex,h=o.getData(s.dataType);if(h){s=(H(s=Kr(h,s))?s[0]:s)||0;var c=h.getItemGraphicEl(s);if(!c)for(var p=h.count(),d=0;!c&&d<p;)c=h.getItemGraphicEl(d++);c?Ys(u,(s=fs(c)).focus,s.blurScope,l):(s=o.get(["emphasis","focus"]),o=o.get(["emphasis","blurScope"]),null!=s&&Ys(u,s,o,l))}}}else u=Zs(e.mainType,e.componentIndex,n.name,t._api),s=u.focusSelf,o=u.dispatchers,n.type===ws&&s&&!n.notBlur&&qs(e.mainType,e.componentIndex,t._api),o&&R(o,(function(t){(n.type===ws?Vs:Hs)(t)}));else nl(n)&&e instanceof rf&&(l=e,o=n,t._api,nl(o)&&(i=o.dataType,H(r=Kr(l.getData(i),o))||(r=[r]),l[o.type===Ts?"toggleSelect":o.type===Ss?"select":"unselect"](r,i)),$s(e),yy(t))}),t),s&&s.eachComponent(o,(function(e){a&&null!=a.get(e.id)||l(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)):R([].concat(t._componentsViews).concat(t._chartsViews),l)},iy={prepareAndUpdate:function(t){ty(this),iy.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),oy(this,n),o.update(n,i),wy(n),a.performVisualTasks(n,t),cy(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),Hg.trigger("afterupdate",n,i))},updateTransform:function(t){var e,n,i=this,r=this._model,o=this._api;r&&(r.setUpdatePayload(t),e=[],r.eachComponent((function(n,a){"series"!==n&&(n=i.getViewOfComponentModel(a))&&n.__alive&&(!n.updateTransform||(a=n.updateTransform(a,r,o,t))&&a.update)&&e.push(n)})),n=ft(),r.eachSeries((function(e){var a=i._chartsMap[e.__viewId];(!a.updateTransform||(a=a.updateTransform(e,r,o,t))&&a.update)&&n.set(e.uid,1)})),wy(r),this._scheduler.performVisualTasks(r,t,{setDirty:!0,dirtyMap:n}),dy(this,r,o,t,{},n),Hg.trigger("afterupdate",r,o))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),vf.markUpdateMethod(t,"updateView"),wy(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),cy(this,e,this._api,t,{}),Hg.trigger("afterupdate",e,this._api))},updateVisual:function(t){var e=this,n=this._model;n&&(n.setUpdatePayload(t),n.eachSeries((function(t){t.getData().clearAllVisual()})),vf.markUpdateMethod(t,"updateVisual"),wy(n),this._scheduler.performVisualTasks(n,t,{visualType:"visual",setDirty:!0}),n.eachComponent((function(i,r){"series"!==i&&(i=e.getViewOfComponentModel(r))&&i.__alive&&i.updateVisual(r,n,e._api,t)})),n.eachSeries((function(i){e._chartsMap[i.__viewId].updateVisual(i,n,e._api,t)})),Hg.trigger("afterupdate",n,this._api))},updateLayout:function(t){iy.update.call(this,t)}},ry=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=to(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},oy=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},ay=function(t,e){var n,i,r=this,o=this.getModel(),a=t.type,s=t.escapeConnect,l=ky[a],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&so(h[0]),d=(h=(this[Gg]=!0,[t]),!1),f=(t.batch&&(d=!0,h=N(t.batch,(function(e){return(e=D(k({},e),t)).batch=null,e}))),[]),g=nl(t),y=il(t);if(y&&Xs(this._api),R(h,(function(e){var i,o;(n=(n=l.action(e,r._model,r._api))||k({},e)).type=u.event||n.type,f.push(n),y?(i=(o=eo(t)).queryOptionMap,o=o.mainTypeSpecified?i.keys()[0]:"series",ny(r,c,e,o),yy(r)):g?(ny(r,c,e,"series"),yy(r)):p&&ny(r,c,e,p.main,p.sub)})),"none"!==c&&!y&&!g&&!p)try{this[Ug]?(ty(this),iy.update.call(this,t),this[Ug]=null):iy[c].call(this,t)}catch(e){throw this[Gg]=!1,e}n=d?{type:u.event||a,escapeConnect:s,batch:f}:f[0],this[Gg]=!1,e||((h=this._messageCenter).trigger(n.type,n),g&&(d={type:"selectchanged",escapeConnect:s,selected:(i=[],o.eachSeries((function(t){R(t.getAllData(),(function(e){e.data;e=e.type;var n=t.getSelectedDataIndices();0<n.length&&(n={dataIndex:n,seriesIndex:t.seriesIndex},null!=e&&(n.dataType=e),i.push(n))}))})),i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t},h.trigger(d.type,d)))},sy=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();ay.call(this,n,t)}},ly=function(t){t||this.trigger("updated")},uy=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[Ug]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},hy=function(t,e){t.on("mouseover",(function(t){var n,i,r,o,a=eg(t.target,el);a&&(a=a,n=t,t=e._api,i=fs(a),r=(o=Zs(i.componentMainType,i.componentIndex,i.componentHighDownName,t)).dispatchers,o=o.focusSelf,r?(o&&qs(i.componentMainType,i.componentIndex,t),R(r,(function(t){return Bs(t,n)}))):(Ys(i.seriesIndex,i.focus,i.blurScope,t),"self"===i.focus&&qs(i.componentMainType,i.componentIndex,t),Bs(a,n)),yy(e))})).on("mouseout",(function(t){var n,i,r=eg(t.target,el);r&&(r=r,n=t,Xs(t=e._api),(i=Zs((i=fs(r)).componentMainType,i.componentIndex,i.componentHighDownName,t).dispatchers)?R(i,(function(t){return Fs(t,n)})):Fs(r,n),yy(e))})).on("click",(function(t){var n;t=eg(t.target,(function(t){return null!=fs(t).dataIndex}),!0);t&&(n=t.selected?"unselect":"select",t=fs(t),e._api.dispatchAction({type:n,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))}))},cy=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent((function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})})),c&&(Xe(a=u.concat(h),(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),R(a,(function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel;t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)}))),py(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive=!1})),dy(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},py=function(t,e,n,i,r,o){R(o||t._componentsViews,(function(t){var r=t.__model;My(0,t),t.render(r,e,n,i),Sy(r,t),Ty(r,t)}))},dy=function(t,e,n,i,o,a){var s,l,u,h,c=t._scheduler,p=(o=k(o||{},{updatedSeries:e.getSeries()}),Hg.trigger("series:beforeupdate",e,n,o),!1);e.eachSeries((function(e){var n,r=t._chartsMap[e.__viewId],o=(r.__alive=!0,r.renderTask);c.updatePayload(o,i),My(0,r),a&&a.get(e.uid)&&o.dirty(),o.perform(c.getPerformArgs(o))&&(p=!0),r.group.silent=!!e.get("silent"),o=r,n=e.get("blendMode")||null,o.eachRendered((function(t){t.isGroup||(t.style.blend=n)})),$s(e)})),c.unfinished=p||c.unfinished,Hg.trigger("series:layoutlabels",e,n,o),Hg.trigger("series:transition",e,n,o),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];Sy(e,n),Ty(e,n)})),l=e,u=(s=t)._zr.storage,h=0,u.traverse((function(t){t.isGroup||h++})),h>l.get("hoverLayerThreshold")&&!r.node&&!r.worker&&l.eachSeries((function(t){t.preventUsingHoverLayer||(t=s._chartsMap[t.__viewId]).__alive&&t.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))})),Hg.trigger("series:afterupdate",e,n,o)},yy=function(t){t[jg]=!0,t.getZr().wakeUp()},my=function(t){t[jg]&&(t.getZr().storage.traverse((function(t){Uu(t)||by(t)})),t[jg]=!1)},fy=function(t){return n(e,i=rp),e.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},e.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},e.prototype.enterEmphasis=function(e,n){Vs(e,n),yy(t)},e.prototype.leaveEmphasis=function(e,n){Hs(e,n),yy(t)},e.prototype.enterBlur=function(e){Ns(e,As),yy(t)},e.prototype.leaveBlur=function(e){Ws(e),yy(t)},e.prototype.enterSelect=function(e){Gs(e),yy(t)},e.prototype.leaveSelect=function(e){Us(e),yy(t)},e.prototype.getModel=function(){return t.getModel()},e.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},e.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},new e(t);function e(){return null!==i&&i.apply(this,arguments)||this}var i},void(gy=function(t){function e(t,e){for(var n=0;n<t.length;n++)t[n][Yg]=e}R(Dy,(function(n,i){t._messageCenter.on(i,(function(n){var i,r;!Ny[t.group]||0===t[Yg]||n&&n.escapeConnect||(i=t.makeActionFromEvent(n),r=[],R(Ry,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),R(r,(function(t){1!==t[Yg]&&t.dispatchAction(i)})),e(r,2))}))}))})),xy);function xy(t,e,n){var i=vy.call(this,new $f)||this;i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},G(e)&&(e=Ly[e]),i._dom=t,n.ssr&&br((function(t){t=fs(t);var e,n=t.dataIndex;if(null!=n)return(e=ft()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e})),t=i._zr=xr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:tt(n.useDirtyRect,!1),useCoarsePointer:tt(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize}),i._ssr=n.ssr,i._throttledZrFlush=Tf(F(t.flush,t),17),(e=T(e))&&Ip(e,!0),i._theme=e,i._locale=G(e=n.locale||Lh)?(n=Ah[e.toUpperCase()]||{},"ZH"===e||"EN"===e?T(n):C(T(n),T(Ah[Ih]),!1)):C(T(e),T(Ah[Ih]),!1),i._coordSysMgr=new sp,n=i._api=fy(i);function r(t,e){return t.__prio-e.__prio}return Xe(Oy,r),Xe(Iy,r),i._scheduler=new Pf(i,n,Iy,Oy),i._messageCenter=new Qg,i._initEvents(),i.resize=F(i.resize,i),t.animation.on("frame",i._onframe,i),uy(t,i),hy(t,i),st(i),i}function wy(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function by(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function Sy(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered((function(t){return function t(e,n,i,r){var o=e.getTextContent(),a=e.getTextGuideLine(),s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);return o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2),a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1)),r}(t,n,i,-1/0),!0})))}function My(t,e){e.eachRendered((function(t){var e,n;Uu(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))}))}function Ty(t,e){var n=t.getModel("stateAnimation"),i=t.isAnimationEnabled(),r=(t=n.get("duration"),0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null);e.eachRendered((function(t){var e,n,o;t.states&&t.states.emphasis&&(Uu(t)||(t instanceof Na&&((o=ms(n=t)).normalFill=n.style.fill,o.normalStroke=n.style.stroke,n=n.states.select||{},o.selectFill=n.style&&n.style.fill||null,o.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(o=t.prevStates)&&t.useStates(o),i&&(t.stateTransition=r,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=r),e)&&(e.stateTransition=r),t.__dirty&&by(t)))}))}ug=_y.prototype;var Cy=(ug.on=qg("on"),ug.off=qg("off"),ug.one=function(t,e,n){var i=this;this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]),ky={},Dy={},Iy=[],Ay=[],Oy=[],Ly={},Py={},Ry={},Ny={},Ey=+new Date,zy=+new Date,By="_echarts_instance_";function Fy(t){Ny[t]=!1}function Vy(t){return Ry[(e=By,(t=t).getAttribute?t.getAttribute(e):t[e])];var e}function Hy(t,e){Ly[t]=e}function Wy(t){A(Ay,t)<0&&Ay.push(t)}function Gy(t,e){Qy(Iy,t,e,2e3)}function Uy(t){Xy("afterinit",t)}function jy(t){Xy("afterupdate",t)}function Xy(t,e){Hg.on(t,e)}function Yy(t,e,n){W(e)&&(n=e,e="");var i=X(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Dy[e]||(rt(Xg.test(i)&&Xg.test(e)),ky[i]||(ky[i]={action:n,actionInfo:t}),Dy[e]=i)}function qy(t,e){sp.register(t,e)}function Zy(t,e){Qy(Oy,t,e,1e3,"layout")}function $y(t,e){Qy(Oy,t,e,3e3,"visual")}ag=Fy;var Ky=[];function Qy(t,e,n,i,r){(W(e)||X(e))&&(n=e,e=i),0<=A(Ky,n)||(Ky.push(n),(i=Pf.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function Jy(t,e){Py[t]=e}function tm(t,e,n){var i=Wg.registerMap;i&&i(t,e,n)}function em(t){var e=(t=T(t)).type,n=(e||Fr(""),e.split(":")),i=(2!==n.length&&Fr(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Rd.set(e,t)}function nm(t){return null==t?0:t.length||1}function im(t){return t}$y(2e3,mo),$y(4500,fh),$y(4500,zh),$y(2e3,xh),$y(4500,yh),$y(7e3,(function(t,e){t.eachRawSeries((function(n){var i;!t.isSeriesFiltered(n)&&((i=n.getData()).hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=Fg(n,e))})),n=i.getVisual("decal"))&&(i.getVisual("style").decal=Fg(n,e))}))})),Wy(Ip),Gy(900,(function(t){var e=ft();t.eachSeries((function(t){var n,i=t.get("stack");i&&(i=e.get(i)||e.set(i,[]),(t={stackResultDimension:(n=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:n.getCalculationInfo("stackedOverDimension"),stackedDimension:n.getCalculationInfo("stackedDimension"),stackedByDimension:n.getCalculationInfo("stackedByDimension"),isStackedByIndex:n.getCalculationInfo("isStackedByIndex"),data:n,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(i.length&&n.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(t))})),e.each(Ap)})),Jy("default",(function(t,e){D(e=e||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n,i=new dr,r=new Ka({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),o=(i.add(r),new ns({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001})),a=new Ka({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return i.add(a),e.showSpinner&&((n=new _u({shape:{startAngle:-Lf/2,endAngle:-Lf/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Lf/2}).start("circularInOut"),n.animateShape(!0).when(1e3,{startAngle:3*Lf/2}).delay(300).start("circularInOut"),i.add(n)),i.resize=function(){var i=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(i=(t.getWidth()-2*s-(e.showSpinner&&i?10:0)-i)/2-(e.showSpinner&&i?0:5+i/2)+(e.showSpinner?0:i/2)+(i?0:s),t.getHeight()/2);e.showSpinner&&n.setShape({cx:i,cy:l}),a.setShape({x:i-s,y:l-s,width:2*s,height:2*s}),r.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},i.resize(),i})),Yy({type:ws,event:ws,update:ws},_t),Yy({type:bs,event:bs,update:bs},_t),Yy({type:Ss,event:Ss,update:Ss},_t),Yy({type:Ms,event:Ms,update:Ms},_t),Yy({type:Ts,event:Ts,update:Ts},_t),Hy("light",bh),Hy("dark",vh),om.prototype.add=function(t){return this._add=t,this},om.prototype.update=function(t){return this._update=t,this},om.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},om.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},om.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},om.prototype.remove=function(t){return this._remove=t,this},om.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},om.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=nm(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},om.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=nm(l),c=nm(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},om.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=nm(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},om.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=nm(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var rm=om;function om(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||im,this._newKeyGetter=i||im,this.context=r,this._diffModeMultiple="multiple"===o}sm.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},sm.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var am=sm;function sm(t,e){this._encode=t,this._schema=e}function lm(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var um=function(t){this.otherDims={},null!=t&&k(this,t)},hm=Qr(),cm={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},pm=(dm.prototype.isDimensionOmitted=function(){return this._dimOmitted},dm.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=ym(this.source))},dm.prototype.getSourceDimensionIndex=function(t){return tt(this._dimNameMap.get(t),-1)},dm.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},dm.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Vp(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(cm[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},dm.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},dm.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},dm);function dm(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function fm(t){return t instanceof pm}function gm(t){for(var e=ft(),n=0;n<(t||[]).length;n++){var i=t[n];i=X(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function ym(t){var e=hm(t);return e.dimNameMap||(e.dimNameMap=gm(t.dimensionsDefine))}var mm,vm,_m,xm,wm,bm,Sm,Mm=X,Tm=N,Cm="undefined"==typeof Int32Array?Array:Int32Array,km=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Dm=["_approximateExtent"],Im=(Am.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},Am.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},Am.prototype._recognizeDimIndex=function(t){if(j(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},Am.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},Am.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},Am.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},Am.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},Am.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},Am.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},Am.prototype.getStore=function(){return this._store},Am.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof Ud?t:i)||(r=this.dimensions,t=Rp(t)||P(t)?new Up(t,r.length):t,i=new Ud,r=Tm(r,(function(t){return{type:o._dimInfos[t].type,property:t}})),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=ft(),o=[],a=[],s={},l=(R(t.dimensions,(function(e){var n,l,u=t.getDimensionInfo(e),h=u.coordDim;h&&(n=u.coordDimIndex,lm(i,h)[n]=e,u.isExtraCoord||(r.set(h,1),"ordinal"!==(l=u.type)&&"time"!==l&&(o[0]=e),lm(s,h)[n]=t.getDimensionIndex(u.name)),u.defaultTooltip)&&a.push(e),Dc.each((function(t,e){var n=lm(i,e);e=u.otherDims[e];null!=e&&!1!==e&&(n[e]=u.name)}))})),[]),u={},h=(r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=N(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u,i.label);return h&&h.length&&(o=h.slice()),h=i.tooltip,h&&h.length?a=h.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new am(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},Am.prototype.appendData=function(t){t=this._store.appendData(t),this._doInit(t[0],t[1])},Am.prototype.appendValues=function(t,e){t=this._store.appendValues(t,e.length);var n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&Sm(this,o)},Am.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},Am.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Pc&&!t.fillStorage},Am.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Ic&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!X(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=qr(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=qr(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)Sm(this,a);mm(this)}},Am.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},Am.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},Am.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},Am.prototype.setCalculationInfo=function(t,e){Mm(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},Am.prototype.getName=function(t){t=this.getRawIndex(t);var e=this._nameList[t];return null==(e=null==e&&null!=this._nameDimIdx?_m(this,this._nameDimIdx,t):e)?"":e},Am.prototype._getCategory=function(t,e){return e=this._store.get(t,e),t=this._store.getOrdinalMeta(t),t?t.categories[e]:e},Am.prototype.getId=function(t){return vm(this,this.getRawIndex(t))},Am.prototype.count=function(){return this._store.count()},Am.prototype.get=function(t,e){var n=this._store;t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},Am.prototype.getByRawIndex=function(t,e){var n=this._store;t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},Am.prototype.getIndices=function(){return this._store.getIndices()},Am.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},Am.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},Am.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},Am.prototype.getValues=function(t,e){var n=this,i=this._store;return H(t)?i.getValues(Tm(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},Am.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},Am.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},Am.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},Am.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},Am.prototype.rawIndexOf=function(t,e){return t=(t&&this._invertedIndicesMap[t])[e],null==t||isNaN(t)?-1:t},Am.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},Am.prototype.each=function(t,e,n){W(t)&&(n=e,e=t,t=[]),n=n||this,t=Tm(xm(t),this._getStoreDimIndex,this),this._store.each(t,n?F(e,n):e)},Am.prototype.filterSelf=function(t,e,n){return W(t)&&(n=e,e=t,t=[]),n=n||this,t=Tm(xm(t),this._getStoreDimIndex,this),this._store=this._store.filter(t,n?F(e,n):e),this},Am.prototype.selectRange=function(t){var e=this,n={};return R(B(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},Am.prototype.mapArray=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n=n||this),i},Am.prototype.map=function(t,e,n,i){return n=n||i||this,i=Tm(xm(t),this._getStoreDimIndex,this),t=bm(this),t._store=this._store.map(i,n?F(e,n):e),t},Am.prototype.modify=function(t,e,n,i){n=n||i||this,i=Tm(xm(t),this._getStoreDimIndex,this),this._store.modify(i,n?F(e,n):e)},Am.prototype.downSample=function(t,e,n,i){var r=bm(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},Am.prototype.lttbDownSample=function(t,e){var n=bm(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},Am.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},Am.prototype.getItemModel=function(t){var e=this.hostModel;t=this.getRawDataItem(t);return new Th(t,e,e&&e.ecModel)},Am.prototype.diff=function(t){var e=this;return new rm(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return vm(t,e)}),(function(t){return vm(e,t)}))},Am.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},Am.prototype.setVisual=function(t,e){this._visual=this._visual||{},Mm(t)?k(this._visual,t):this._visual[t]=e},Am.prototype.getItemVisual=function(t,e){return t=this._itemVisuals[t],t=t&&t[e],null==t?this.getVisual(e):t},Am.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},Am.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];n=(i=i||(n[t]={}))[e];return null==n&&(H(n=this.getVisual(e))?n=n.slice():Mm(n)&&(n=k({},n)),i[e]=n),n},Am.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Mm(e)?k(i,e):i[e]=n},Am.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},Am.prototype.setLayout=function(t,e){Mm(t)?k(this._layout,t):this._layout[t]=e},Am.prototype.getLayout=function(t){return this._layout[t]},Am.prototype.getItemLayout=function(t){return this._itemLayouts[t]},Am.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?k(this._itemLayouts[t]||{},e):e},Am.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},Am.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=fs(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse((function(t){t=fs(t),t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"})),this._graphicEls[t]=e},Am.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},Am.prototype.eachItemGraphicEl=function(t,e){R(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},Am.prototype.cloneShallow=function(t){return t=t||new Am(this._schema||Tm(this.dimensions,this._getDimInfo,this),this.hostModel),wm(t,this),t._store=this._store,t},Am.prototype.wrapMethod=function(t,e){var n=this[t];W(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(nt(arguments)))})},Am.internalField=(mm=function(t){var e=t._invertedIndicesMap;R(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new Cm(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},_m=function(t,e,n){return qr(t._getCategory(e,n),null)},vm=function(t,e){var n=t._idList[e];return null==(n=null==n&&null!=t._idDimIdx?_m(t,t._idDimIdx,e):n)?"e\0\0"+e:n},xm=function(t){return H(t)?t:null!=t?[t]:[]},bm=function(t){var e=new Am(t._schema||Tm(t.dimensions,t._getDimInfo,t),t.hostModel);return wm(e,t),e},wm=function(t,e){R(km.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,R(Dm,(function(n){t[n]=T(e[n])})),t._calculationInfo=k({},e._calculationInfo)},void(Sm=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=_m(t,r,e)),null==s&&null!=o&&(i[e]=s=_m(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),Am);function Am(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"]),o=(fm(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],p=(c=G(c)?new um({name:c}):c instanceof um?c:new um(c),c.name),d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=ft(),R(a,(function(t){i.set(o[t].storeDimIndex,t)})))}function Om(t,e){Rp(t)||(t=Ep(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=ft(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),R(n,(function(t){X(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))})),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?ym(t):gm(o),p=e.encodeDefine,d=ft(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new Fd(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=X(e=o[t])?e:{name:e},n=new um,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each((function(t,e){var n;t=Wr(t).slice();1===t.length&&!G(t[0])&&t[0]<0?d.set(e,!1):(n=d.set(e,[]),R(t,(function(t,i){t=G(t)?c.get(t):t,null!=t&&t<l&&v(y(n[i]=t),e,i)})))}));var m=0;function v(t,e,n){null!=Dc.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}R(r,(function(t){G(t)?(r=t,i={}):(r=(i=t).name,t=i.ordinalMeta,i.ordinalMeta=null,(i=k({},i)).ordinalMeta=t,e=i.dimsDef,n=i.otherDims,i.name=i.coordDim=i.coordDimIndex=i.dimsDef=i.otherDims=null);var e,n,i,r,o=d.get(r);if(!1!==o){if(!(o=Wr(o)).length)for(var a=0;a<(e&&e.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&o.push(m++)}R(o,(function(t,o){t=y(t),h&&null!=i.type&&(t.type=i.type),v(D(t,i),r,o),null==t.name&&e&&(X(o=e[o])||(o={name:o}),t.name=t.displayName=o.name,t.defaultTooltip=o.defaultTooltip),n&&D(t.otherDims,n)}))}}));var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)R(s,(function(t){S(t)})),s.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||Wc(t,M)!==zc.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=ft(),A=0;A<C.length;A++){var O=C[A],L=O.name,P=I.get(L)||0;0<P&&(O.name=L+(P-1)),P++,I.set(L,P)}return new pm({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var Lm=function(t){this.coordSysDims=[],this.axisMap=ft(),this.categoryAxisMap=ft(),this.coordSysName=t},Pm={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",no).models[0];t=t.getReferringComponents("yAxis",no).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),Rm(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Rm(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",no).models[0],e.coordSysDims=["single"],n.set("single",t),Rm(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){t=t.getReferringComponents("polar",no).models[0];var r=t.findAxisModel("radiusAxis");t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),Rm(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),Rm(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=(t=r.getComponent("parallel",t.get("parallelIndex")),e.coordSysDims=t.dimensions.slice());R(t.parallelAxisIndex,(function(t,a){t=r.getComponent("parallelAxis",t);var s=o[a];n.set(s,t),Rm(t)&&(i.set(s,t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=a)}))}};function Rm(t){return"category"===t.get("type")}function Nm(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(fm(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return R(i,(function(t,e){G(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))})),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,R(i,(function(t){t.coordDim===h&&c++})),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function Em(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}Bm.prototype.getSetting=function(t){return this._setting[t]},Bm.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Bm.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Bm.prototype.getExtent=function(){return this._extent.slice()},Bm.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Bm.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},Bm.prototype.isBlank=function(){return this._isBlank},Bm.prototype.setBlank=function(t){this._isBlank=t};var zm=Bm;function Bm(t){this._setting=t||{},this._extent=[1/0,-1/0]}fo(zm);var Fm=0,Vm=(Hm.createByAxisModel=function(t){t=t.option;var e=t.data;e=e&&N(e,Wm);return new Hm({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},Hm.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},Hm.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return G(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},Hm.prototype._getOrCreateMap=function(){return this._map||(this._map=ft(this.categories))},Hm);function Hm(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Fm}function Wm(t){return X(t)&&null!=t.value?t.value:t+""}function Gm(t){return kr(t)+2}function Um(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function jm(t,e){return t>=e[0]&&t<=e[1]}function Xm(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Ym(t,e){return t*(e[1]-e[0])+e[0]}n($m,qm=zm),$m.prototype.parse=function(t){return null==t?NaN:G(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},$m.prototype.contain=function(t){return jm(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},$m.prototype.normalize=function(t){return Xm(t=this._getTickNumber(this.parse(t)),this._extent)},$m.prototype.scale=function(t){return t=Math.round(Ym(t,this._extent)),this.getRawOrdinalNumber(t)},$m.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},$m.prototype.getMinorTicks=function(t){},$m.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},$m.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},$m.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},$m.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},$m.prototype.count=function(){return this._extent[1]-this._extent[0]+1},$m.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},$m.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},$m.prototype.getOrdinalMeta=function(){return this._ordinalMeta},$m.prototype.calcNiceTicks=function(){},$m.prototype.calcNiceExtent=function(){},$m.type="ordinal";var qm,Zm=$m;function $m(t){t=qm.call(this,t)||this;var e=(t.type="ordinal",t.getSetting("ordinalMeta"));return H(e=e||new Vm({}))&&(e=new Vm({categories:N(e,(function(t){return X(t)?t.value:t}))})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}zm.registerClass(Zm);var Km,Qm=Cr,Jm=(n(tv,Km=zm),tv.prototype.parse=function(t){return t},tv.prototype.contain=function(t){return jm(t,this._extent)},tv.prototype.normalize=function(t){return Xm(t,this._extent)},tv.prototype.scale=function(t){return Ym(t,this._extent)},tv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},tv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},tv.prototype.getInterval=function(){return this._interval},tv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Gm(t)},tv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:Qm(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Qm(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:Qm(s+e,r)}:{value:n[1]})}return o},tv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Qm(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},tv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=kr(t.value)||0:"auto"===e&&(e=this._intervalPrecision),oc(Qm(t.value,e,!0)))},tv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=function(t,e,n,i){var r={},o=t[1]-t[0];return o=r.interval=Nr(o/e,!0),null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),e=r.intervalPrecision=Gm(o),n=r.niceTickExtent=[Cr(Math.ceil(t[0]/o)*o,e),Cr(Math.floor(t[1]/o)*o,e)],i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),Um(i,0,o),Um(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},tv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]);isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),n=this._interval;t.fixMin||(e[0]=Qm(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Qm(Math.ceil(e[1]/n)*n))},tv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},tv.type="interval",tv);function tv(){var t=null!==Km&&Km.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}function ev(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function nv(t){return t.dim+t.index}zm.registerClass(Jm),n(ov,iv=Jm),ov.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Gh(t.value,Bh[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Wh(this._minLevelUnit))]||Bh.second,e,this.getSetting("locale"))},ov.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(G(n))o=n;else if(W(n))o=n(t.value,e,{level:t.level});else{var a=k({},Eh);if(0<t.level)for(var s=0;s<Fh.length;++s)a[Fh[s]]="{primary|"+a[Fh[s]]+"}";var l=n?!1===n.inherit?n:D(n,a):a,u=Uh(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=Vh.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}H(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return Gh(new Date(t.value),o,i,r)},ov.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,e,n,i){var r=Vh,o=0;function a(t,r,o){var a=[],s=!r.length;if(!function(t,e,n,i){function r(t){return jh(c,t,i)===jh(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=Lr(e),p=Lr(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(Wh(t),i[0],i[1],n)){s&&(r=[{value:function(t,e,n){var i=new Date(t);switch(Wh(e)){case"year":case"month":i[Jh(n)](0);case"day":i[tc(n)](1);case"hour":i[ec(n)](0);case"minute":i[nc(n)](0);case"second":i[ic(n)](0),i[rc(n)](0)}return i.getTime()}(new Date(i[0]),t,n)},{value:i[1]}]);for(var l,u,h=0;h<r.length-1;h++){var c=r[h].value,p=r[h+1].value;if(c!==p){var d=void 0,f=void 0,g=void 0;switch(t){case"year":d=Math.max(1,Math.round(e/Nh/365)),f=Xh(n),g=n?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":u=e,d=6<(u/=30*Nh)?6:3<u?3:2<u?2:1,f=Yh(n),g=Jh(n);break;case"week":case"half-week":case"day":u=e,d=16<(u/=Nh)?16:7.5<u?7:3.5<u?4:1.5<u?2:1,f=qh(n),g=tc(n);break;case"half-day":case"quarter-day":case"hour":l=e,d=12<(l/=Rh)?12:6<l?6:3.5<l?4:2<l?2:1,f=Zh(n),g=ec(n);break;case"minute":d=sv(e,!0),f=$h(n),g=nc(n);break;case"second":d=sv(e,!1),f=Kh(n),g=ic(n);break;case"millisecond":d=Nr(e,!0),f=Qh(n),g=rc(n)}M=S=b=w=void 0;for(var y=d,m=c,v=p,_=f,x=g,w=a,b=new Date(m),S=m,M=b[_]();S<v&&S<=i[1];)w.push({value:S}),b[x](M+=y),S=b.getTime();w.push({value:S,notAdd:!0}),"year"===t&&1<o.length&&0===h&&o.unshift({value:o[0].value-d})}}for(h=0;h<a.length;h++)o.push(a[h])}}for(var s=[],l=[],u=0,h=0,c=0;c<r.length&&o++<1e4;++c){var p=Wh(r[c]);if(function(t){return t===Wh(t)}(r[c])){a(r[c],s[s.length-1]||[],l);var d=r[c+1]?Wh(r[c+1]):null;if(p!==d){if(l.length){h=u,l.sort((function(t,e){return t.value-e.value}));for(var f=[],g=0;g<l.length;++g){var y=l[g].value;0!==g&&l[g-1].value===y||(f.push(l[g]),y>=i[0]&&y<=i[1]&&u++)}if(p=(i[1]-i[0])/e,1.5*p<u&&p/1.5<h)break;if(s.push(f),p<u||t===r[c])break}l=[]}}}var m=z(N(s,(function(t){return z(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return 0<t.length})),v=[],_=m.length-1;for(c=0;c<m.length;++c)for(var x=m[c],w=0;w<x.length;++w)v.push({value:x[w].value,level:_-c});v.sort((function(t,e){return t.value-e.value}));var b=[];for(c=0;c<v.length;++c)0!==c&&v[c].value===v[c-1].value||b.push(v[c]);return b}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},ov.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Nh,n[1]+=Nh),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Nh),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},ov.prototype.calcNiceTicks=function(t,e,n){var i=this._extent;i=i[1]-i[0],this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),i=av.length,t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(av,this._approxInterval,0,i),i-1);this._interval=av[t][1],this._minLevelUnit=av[Math.max(t-1,0)][0]},ov.prototype.parse=function(t){return j(t)?t:+Lr(t)},ov.prototype.contain=function(t){return jm(this.parse(t),this._extent)},ov.prototype.normalize=function(t){return Xm(this.parse(t),this._extent)},ov.prototype.scale=function(t){return Ym(t,this._extent)},ov.type="time";var iv,rv=ov;function ov(t){return t=iv.call(this,t)||this,t.type="time",t}var av=[["second",1e3],["minute",6e4],["hour",Rh],["quarter-day",6*Rh],["half-day",12*Rh],["day",1.2*Nh],["half-week",3.5*Nh],["week",7*Nh],["month",31*Nh],["quarter",95*Nh],["half-year",ho/2],["year",ho]];function sv(t,e){return 30<(t/=e?6e4:1e3)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}zm.registerClass(rv);var lv,uv=zm.prototype,hv=Jm.prototype,cv=Cr,pv=Math.floor,dv=Math.ceil,fv=Math.pow,gv=Math.log,yv=(n(mv,lv=zm),mv.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return N(hv.getTicks.call(this,t),(function(t){t=t.value;var e=Cr(fv(this.base,t));e=t===n[0]&&this._fixMin?vv(e,i[0]):e;return{value:t===n[1]&&this._fixMax?vv(e,i[1]):e}}),this)},mv.prototype.setExtent=function(t,e){var n=gv(this.base);t=gv(Math.max(0,t))/n,e=gv(Math.max(0,e))/n,hv.setExtent.call(this,t,e)},mv.prototype.getExtent=function(){var t=this.base,e=uv.getExtent.call(this);return e[0]=fv(t,e[0]),e[1]=fv(t,e[1]),t=this._originalScale.getExtent(),this._fixMin&&(e[0]=vv(e[0],t[0])),this._fixMax&&(e[1]=vv(e[1],t[1])),e},mv.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=gv(t[0])/gv(e),t[1]=gv(t[1])/gv(e),uv.unionExtent.call(this,t)},mv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},mv.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=Pr(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[Cr(dv(e[0]/i)*i),Cr(pv(e[1]/i)*i)],this._interval=i,this._niceExtent=t}},mv.prototype.calcNiceExtent=function(t){hv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},mv.prototype.parse=function(t){return t},mv.prototype.contain=function(t){return jm(t=gv(t)/gv(this.base),this._extent)},mv.prototype.normalize=function(t){return Xm(t=gv(t)/gv(this.base),this._extent)},mv.prototype.scale=function(t){return t=Ym(t,this._extent),fv(this.base,t)},mv.type="log",mv);function mv(){var t=null!==lv&&lv.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new Jm,t._interval=0,t}function vv(t,e){return cv(t,kr(e))}rg=yv.prototype,rg.getMinorTicks=hv.getMinorTicks,rg.getLabel=hv.getLabel,zm.registerClass(yv),xv.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),this._modelMinRaw=e.get("min",!0));W(r)?this._modelMinNum=Sv(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=Sv(t,r)),r=this._modelMaxRaw=e.get("max",!0);W(r)?this._modelMaxNum=Sv(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=Sv(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=H(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Ji(t[0],1),Ji(t[1],1)]},xv.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),e=J(a)||J(s)||t&&!i,this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),n=this._determinedMin,r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},xv.prototype.modifyDataMinMax=function(t,e){this[bv[t]]=e},xv.prototype.setDeterminedMinMax=function(t,e){this[wv[t]]=e},xv.prototype.freeze=function(){this.frozen=!0};var _v=xv;function xv(t,e,n){this._prepareParams(t,e,n)}var wv={min:"_determinedMin",max:"_determinedMax"},bv={min:"_dataMin",max:"_dataMax"};function Sv(t,e){return null==e?null:J(e)?NaN:t.parse(e)}function Mv(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new _v(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=function(t,e){var n=[];return e.eachSeriesByType(t,(function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)})),n}("bar",c),n=!1,R(t,(function(t){n=n||t.getBaseAxis()===e.axis})),n)&&(s=function(t){var e,n,i=function(t){var e,n={},i=(R(t,(function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type){t=t.getData();for(var i=e.dim+"_"+e.index,r=t.getDimensionIndex(t.mapDimension(e.dim)),o=t.getStore(),a=0,s=o.count();a<s;++a){var l=o.get(r,a);n[i]?n[i].push(l):n[i]=[l]}}})),{});for(e in n)if(n.hasOwnProperty(e)){var r=n[e];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];0<s&&(o=null===o?s:Math.min(o,s))}i[e]=o}}return i}(t),r=[];return R(t,(function(t){var e,n,o=t.coordinateSystem.getBaseAxis(),a=o.getExtent(),s=(e="category"===o.type?o.getBandWidth():"value"===o.type||"time"===o.type?(e=o.dim+"_"+o.index,e=i[e],s=Math.abs(a[1]-a[0]),n=o.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?s/n*e:s):(n=t.getData(),Math.abs(a[1]-a[0])/n.count()),Tr(t.get("barWidth"),e)),l=(a=Tr(t.get("barMaxWidth"),e),Tr(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e)),u=t.get("barGap"),h=t.get("barCategoryGap");r.push({bandWidth:e,barWidth:s,barMaxWidth:a,barMinWidth:l,barGap:u,barCategoryGap:h,axisKey:nv(o),stackId:ev(t)})})),e={},R(r,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=(r=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r.stacks),a=(i=(e[i]=r,t.stackId),o[i]||r.autoWidthCount++,o[i]=o[i]||{width:0,maxWidth:0},t.barWidth);a&&!o[i].width&&(o[i].width=a,a=Math.min(r.remainedWidth,a),r.remainedWidth-=a),a=t.barMaxWidth,a&&(o[i].maxWidth=a),a=t.barMinWidth,a&&(o[i].minWidth=a),o=t.barGap,null!=o&&(r.gap=o),i=t.barCategoryGap;null!=i&&(r.categoryGap=i)})),n={},R(e,(function(t,e){n[e]={};var i,r=t.stacks,o=t.bandWidth,a=t.categoryGap,s=(null==a&&(s=B(r).length,a=Math.max(35-4*s,15)+"%"),Tr(a,o)),l=Tr(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l),p=(c=Math.max(c,0),R(r,(function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,u-=e+l*e,h--):(e=c,n&&n<e&&(e=Math.min(n,u)),(e=i&&e<i?i:e)!==c&&(t.width=e,u-=e+l*e,h--))})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0),0),d=(R(r,(function(t,e){t.width||(t.width=c),p+=(i=t).width*(1+l)})),i&&(p-=i.width*l),-p/2);R(r,(function(t,i){n[e][i]=n[e][i]||{bandWidth:o,offset:d,width:t.width},d+=t.width*(1+l)}))})),n}(t),c=u,t=h,s=s,a=(a=(i=e).axis.getExtent())[1]-a[0],void 0!==(s=function(t,e,n){if(t&&e)return t=t[nv(e)],t}(s,i.axis))&&(r=1/0,R(s,(function(t){r=Math.min(t.offset,r)})),o=-1/0,R(s,(function(t){o=Math.max(t.offset+t.width,o)})),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function Tv(t){var e,n,i,r=t.getLabelModel().get("formatter"),o="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?(i=r,function(e,n){return t.scale.getFormattedLabel(e,n,i)}):G(r)?(n=r,function(e){return e=t.scale.getLabel(e),n.replace("{value}",null!=e?e:"")}):W(r)?(e=r,function(n,i){return null!=o&&(i=n.value-o),e((a=n,"category"===(r=t).type?r.scale.getLabel(a):a.value),i,null!=n.level?{level:n.level}:null);var r,a}):function(e){return t.scale.getLabel(e)}}function Cv(t){return t=t.get("interval"),null==t?"auto":t}Dv.prototype.getNeedCrossZero=function(){return!this.option.scale},Dv.prototype.getCoordSysModel=function(){};var kv=Dv;function Dv(){}lg=Object.freeze({__proto__:null,createDimensions:function(t,e){return Om(t,e).dimensions},createList:function(t){return function(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,c=(t=(t?(h=!0,i=Ep(t)):h=(i=u.getSource()).sourceFormat===Ic,function(t){var e=t.get("coordinateSystem"),n=new Lm(e);if(e=Pm[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),r=t,c=(c=e).get("coordinateSystem"),c=sp.get(c),p=(p=r&&r.coordSysDims?N(r.coordSysDims,(function(t){var e={name:t};t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e})):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,d=(p=W(p)?p:p?V(Fc,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=Om(i,c),c=p.dimensions,o=n.createInvertedIndices,(a=t)&&R(c,(function(t,e){var n=t.coordDim;n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)})),l||null==s||(c[s].otherDims.itemName=0),s);return n=h?null:u.getSharedDataStore(p),t=Nm(e,{schema:p,store:n}),c=new Im(p,e),c.setCalculationInfo(t),p=null==d||(u=i).sourceFormat!==Ic||H(jr(function(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)},c.hasItemOption=!1,c.initData(h?i:n,null,p),c}(null,t)},createScale:function(t,e){var n,i,r,o,a,s=e;return(e=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Zm({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new rv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(zm.getClass(e)||Jm)}}(s=e instanceof Th?s:new Th(e))).setExtent(t[0],t[1]),n=Mv(t=e,s=s),i=n.extent,r=s.get("splitNumber"),t instanceof yv&&(t.base=s.get("logBase")),o=t.type,a=s.get("interval"),o="interval"===o||"time"===o,t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?s.get("minInterval"):null,maxInterval:o?s.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a),e},createSymbol:fg,createTextStyle:function(t,e){return rh(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:Em,enableDataStack:Nm,getStackedDimension:function(t,e){return Em(t,e)?t.getCalculationInfo("stackResultDimension"):e}},enableHoverEmphasis:Ks,getECData:fs,getLayoutRect:gc,mixinAxisModelCommonMethods:function(t){L(t,kv)}});var Iv=[],Av={registerPreprocessor:Wy,registerProcessor:Gy,registerPostInit:Uy,registerPostUpdate:jy,registerUpdateLifecycle:Xy,registerAction:Yy,registerCoordinateSystem:qy,registerLayout:Zy,registerVisual:$y,registerTransform:em,registerLoading:Jy,registerMap:tm,registerImpl:function(t,e){Wg[t]=e},PRIORITY:sg,ComponentModel:Mc,ComponentView:df,SeriesModel:rf,ChartView:vf,registerComponentModel:function(t){Mc.registerClass(t)},registerComponentView:function(t){df.registerClass(t)},registerSeriesModel:function(t){rf.registerClass(t)},registerChartView:function(t){vf.registerClass(t)},registerSubTypeDefaulter:function(t,e){Mc.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){wr(t,e)}};function Ov(t){H(t)?R(t,(function(t){Ov(t)})):0<=A(Iv,t)||(Iv.push(t),(t=W(t)?{install:t}:t).install(Av))}function Lv(t,e){return Math.abs(t-e)<1e-8}function Pv(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=Sa(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return Lv(r[0],s[0])&&Lv(r[1],s[1])||(i+=Sa(r[0],r[1],s[0],s[1],e,n)),0!==i}}var Rv=[];function Nv(t,e){for(var n=0;n<t.length;n++)Nt(t[n],t[n],e)}function Ev(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(Et(e,e,o),zt(n,n,o))}}function zv(t){this.name=t}zv.prototype.setCenter=function(t){this._center=t},zv.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())},ug=zv;var Bv,Fv,Vv=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},Hv=function(t){this.type="linestring",this.points=t},Wv=(n(Gv,Bv=ug),Gv.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior;o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},Gv.prototype.getBoundingRect=function(t){var e,n,i=this._rect;return i&&!t||(e=[1/0,1/0],n=[-1/0,-1/0],R(this.geometries,(function(i){"polygon"===i.type?Ev(i.exterior,e,n,t):R(i.points,(function(i){Ev(i,e,n,t)}))})),isFinite(e[0])&&isFinite(e[1])&&isFinite(n[0])&&isFinite(n[1])||(e[0]=e[1]=n[0]=n[1]=0),i=new De(e[0],e[1],n[0]-e[0],n[1]-e[1]),t)||(this._rect=i),i},Gv.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(Pv(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(Pv(s[l],t[0],t[1]))continue t;return!0}}}return!1},Gv.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,a=(o=(n?i=i||n/o:n=o*i,new De(t,e,n,i)),r.calculateTransform(o)),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(Nv(u.exterior,a),R(u.interiors,(function(t){Nv(t,a)}))):R(u.points,(function(t){Nv(t,a)}))}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},Gv.prototype.cloneShallow=function(t){return t=new Gv(t=null==t?this.name:t,this.geometries,this._center),t._rect=this._rect,t.transformTo=null,t},Gv);function Gv(t,e,n){return t=Bv.call(this,t)||this,t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function Uv(t,e){return t=Fv.call(this,t)||this,t.type="geoSVG",t._elOnlyForCalculate=e,t}function jv(t,e,n){for(var i=0;i<t.length;i++)t[i]=Xv(t[i],e[i],n)}function Xv(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function Yv(t,e){var n,i,r;return N(z((t=(n=t).UTF8Encoding?(null==(r=(i=n).UTF8Scale)&&(r=1024),R(i.features,(function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=Xv(i,n,r);break;case"Polygon":case"MultiLineString":jv(i,n,r);break;case"MultiPolygon":R(i,(function(t,e){return jv(t,n[e],r)}))}})),i.UTF8Encoding=!1,i):n).features,(function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length})),(function(t){var n=t.properties,i=t.geometry,r=[];switch(i.type){case"Polygon":var o=i.coordinates;r.push(new Vv(o[0],o.slice(1)));break;case"MultiPolygon":R(i.coordinates,(function(t){t[0]&&r.push(new Vv(t[0],t.slice(1)))}));break;case"LineString":r.push(new Hv([i.coordinates]));break;case"MultiLineString":r.push(new Hv(i.coordinates))}return t=new Wv(n[e||"name"],r,n.cp),t.properties=n,t}))}n(Uv,Fv=ug),Uv.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=(e=[e.x+e.width/2,e.y+e.height/2],he(Rv)),i=t;i&&!i.isGeoSVGGraphicRoot;)pe(n,i.getLocalTransform(),n),i=i.parent;return ye(n,n),Nt(e,e,n),e};mo=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort((function(t,e){return t-e})),t},getPercentWithPrecision:function(t,e,n){return t[e]&&Ar(t,n)[e]||0},getPixelPrecision:Ir,getPrecision:kr,getPrecisionSafe:Dr,isNumeric:zr,isRadianAroundZero:function(t){return-1e-4<t&&t<1e-4},linearMap:Mr,nice:Nr,numericToNumber:Er,parseDate:Lr,quantile:function(t,e){e=(t.length-1)*e+1;var n=Math.floor(e),i=+t[n-1];return(e-=n)?i+e*(t[n]-i):i},quantity:Pr,quantityExponent:Rr,reformIntervals:function(t){t.sort((function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:function(t){var e=2*Math.PI;return(t%e+e)%e},round:Cr}),fh=Object.freeze({__proto__:null,format:Gh,parse:Lr}),zh=Object.freeze({__proto__:null,Arc:_u,BezierCurve:gu,BoundingRect:De,Circle:Tl,CompoundPath:bu,Ellipse:Il,Group:dr,Image:Ua,IncrementalDisplayable:Yn,Line:uu,LinearGradient:ku,Polygon:tu,Polyline:ru,RadialGradient:Mu,Rect:Ka,Ring:Zl,Sector:jl,Text:ns,clipPointsByRect:function(t,e){return N(t,(function(t){var n=t[0];n=Xu(n,e.x),n=Yu(n,e.x+e.width),t=t[1],t=Xu(t,e.y);return[n,Yu(t,e.y+e.height)]}))},clipRectByRect:function(t,e){var n=Xu(t.x,e.x),i=Yu(t.x+t.width,e.x+e.width),r=Xu(t.y,e.y);t=Yu(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}},createIcon:function(t,e,n){var i=(e=k({rectHover:!0},e)).style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),D(i,n),new Ua(e)):$u(t.replace("path://",""),e,n,"center")},extendPath:function(t,e){return function(t,e){var i,r=bl(t,e);function o(t){return t=i.call(this,t)||this,t.applyTransform=r.applyTransform,t.buildPath=r.buildPath,t}return n(o,i=_l),o}(t,e)},extendShape:function(t){return Na.extend(t)},getShapeClass:function(t){if(qu.hasOwnProperty(t))return qu[t]},getTransform:function(t,e){for(var n=he([]);t&&t!==e;)pe(n,t.getLocalTransform(),n),t=t.parent;return n},initProps:Gu,makeImage:Ku,makePath:$u,mergePath:function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new Na(e)).createPathProxy(),e.buildPath=function(t){var e;wl(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e},registerShape:Zu,resizePath:Ju,updateProps:Wu}),xh=Object.freeze({__proto__:null,addCommas:oc,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:Kt,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=Lr(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"]();e=e[n+"Milliseconds"]();return t.replace("MM",Hh(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Hh(i%100+"",2)).replace("dd",Hh(o,2)).replace("d",o).replace("hh",Hh(a,2)).replace("h",a).replace("mm",Hh(s,2)).replace("m",s).replace("ss",Hh(l,2)).replace("s",l).replace("SSS",Hh(e,3))},formatTpl:uc,getTextRect:function(t,e,n,i,r,o,a,s){return new ns({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:function(t,e){var n=(t=G(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Kt(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Kt(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""},normalizeCssArray:sc,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e?t&&t.charAt(0).toUpperCase()+t.slice(1):t},truncateText:Mo}),yh=Object.freeze({__proto__:null,bind:F,clone:T,curry:V,defaults:D,each:R,extend:k,filter:z,indexOf:A,inherits:O,isArray:H,isFunction:W,isObject:X,isString:G,map:N,merge:C,reduce:E});var qv=Qr();function Zv(t,e){var n,i=$v(t,"labels"),r=(e=Cv(e),Kv(i,e));return r||Qv(i,e,{labels:W(e)?t_(t,e):Jv(t,n="auto"===e?null!=(i=qv(r=t).autoInterval)?i:qv(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function $v(t,e){return qv(t)[e]||(qv(t)[e]=[])}function Kv(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Qv(t,e,n){return t.push({key:e,value:n}),n}function Jv(t,e,n){for(var i=Tv(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=(e=o[0],r.count()),h=(t=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),"category"===(u=t).type&&0===Cv(u.getLabelModel())),u=a.get("showMinLabel")||t,a=a.get("showMaxLabel")||t,u&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function t_(t,e,n){var i=t.scale,r=Tv(t),o=[];return R(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s})})),o}var e_=[0,1];n_.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]);e=Math.max(e[0],e[1]);return n<=t&&t<=e},n_.prototype.containData=function(t){return this.scale.contain(t)},n_.prototype.getExtent=function(){return this._extent.slice()},n_.prototype.getPixelPrecision=function(t){return Ir(t||this.scale.getExtent(),this._extent)},n_.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},n_.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&i_(n=n.slice(),i.count()),Mr(t,e_,n,e)},n_.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&i_(n=n.slice(),i.count()),i=Mr(t,n,e_,e);return this.scale.scale(i)},n_.prototype.pointToData=function(t,e){},n_.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=N(function(t,e){var n,i,r,o,a,s;return"category"===t.type?(e=e,o=$v(n=t,"ticks"),a=Cv(e),(s=Kv(o,a))||(e.get("show")&&!n.scale.isBlank()||(i=[]),i=W(a)?t_(n,a,!0):"auto"===a?(s=Zv(n,n.getLabelModel()),r=s.labelCategoryInterval,N(s.labels,(function(t){return t.tickValue}))):Jv(n,r=a,!0),Qv(o,a,{ticks:i,tickCategoryInterval:r}))):{ticks:N(t.scale.getTicks(),(function(t){return t.value}))}}(this,l).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);l=l.get("alignWithLabel");function h(t,e){return t=Cr(t),e=Cr(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1]}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,R(n,(function(t){t.coord-=r/2})),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},n_.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),N(this.scale.getMinorTicks(t=0<t&&t<100?t:5),(function(t){return N(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this))},n_.prototype.getViewLabels=function(){return function(t){return"category"===t.type?(r=(i=t).getLabelModel(),o=Zv(i,r),!r.get("show")||i.scale.isBlank()?{labels:[],labelCategoryInterval:o.labelCategoryInterval}:o):(r=(e=t).scale.getTicks(),n=Tv(e),{labels:N(r,(function(t,i){return{level:t.level,formattedLabel:n(t,i),rawLabel:e.scale.getLabel(t),tickValue:t.value}}))});var e,n,i,r,o}(this).labels},n_.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},n_.prototype.getTickModel=function(){return this.model.getModel("axisTick")},n_.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent();e=e[1]-e[0]+(this.onBand?1:0),0===e&&(e=1),t=Math.abs(t[1]-t[0]);return Math.abs(t)/e},n_.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=Tv(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),u=(s=Math.abs(s*Math.sin(n)),0),h=0;a<=i[1];a+=o){var c=1.3*(p=Zi(e({value:a}),t.font,"center","top")).width,p=1.3*p.height;u=Math.max(u,c,7),h=Math.max(h,p,7)}n=u/l,l=h/s,isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),s=Math.max(0,Math.floor(Math.min(n,l))),n=qv(d.model),l=d.getExtent();var d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},bh=n_;function n_(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function i_(t,e){e=(t[1]-t[0])/e/2,t[0]+=e,t[1]-=e}var r_=2*Math.PI,o_=ma.CMD,a_=["top","right","bottom","left"];function s_(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=(n=n-t,i=i-e,Math.sqrt(n*n+i*i));l=(l*(n/=h)+u*(i/=h))/h,s&&(l=Math.min(Math.max(l,0),1)),u=a[0]=t+(l*=h)*n,s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function l_(t,e,n,i,r,o,a){return n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i),Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var u_=[];function h_(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case o_.M:p=f=m[x++],d=g=m[x++];break;case o_.L:b=s_(p,d,m[x],m[x+1],v,_,u_,!0),p=m[x++],d=m[x++];break;case o_.C:b=fn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,u_),p=m[x++],d=m[x++];break;case o_.Q:b=_n(p,d,m[x++],m[x++],m[x],m[x+1],v,_,u_),p=m[x++],d=m[x++];break;case o_.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],k=m[x++],D=m[x++],I=(x+=1,!!(1-m[x++])),A=Math.cos(k)*T+S,O=Math.sin(k)*C+M;x<=1&&(f=A,g=O),O=(A=k)+D,I=I,a=(v-S)*(o=C)/T+S,s=_,l=u_,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-O)%r_<1e-4||((O=I?(I=A,A=wa(O),wa(I)):(A=wa(A),wa(O)))<A&&(O+=r_),(I=Math.atan2(s,a))<0&&(I+=r_),A<=I&&I<=O)||A<=I+r_&&I+r_<=O?(l[0]=h,l[1]=c,u-o):(c=((I=o*Math.cos(A)+i)-a)*(I-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(O)+i)-a)*(u-a)+((A=o*Math.sin(O)+r)-s)*(A-s))?(l[0]=I,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(k+D)*T+S,d=Math.sin(k+D)*C+M;break;case o_.R:b=l_(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,u_);break;case o_.Z:b=s_(p,d,f,g,v,_,u_,!0),p=f,d=g}b<y&&(y=b,n.set(u_[0],u_[1]))}return y}var c_=new ve,p_=new ve,d_=new ve,f_=new ve,g_=new ve;function y_(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||a_,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&ye([],h),p=e.get("length2")||0;u&&d_.copy(u);for(var d,f=0;f<a.length;f++){var g=a[f],y=(_=v=y=void 0,g),m=s,v=c_,_=f_,x=m.width,w=m.height;switch(y){case"top":v.set(m.x+x/2,m.y-0),_.set(0,-1);break;case"bottom":v.set(m.x+x/2,m.y+w+0),_.set(0,1);break;case"left":v.set(m.x-0,m.y+w/2),_.set(-1,0);break;case"right":v.set(m.x+x+0,m.y+w/2),_.set(1,0)}ve.scaleAndAdd(p_,c_,f_,p),p_.transform(c),g=t.getBoundingRect(),g=u?u.distance(p_):t instanceof Na?h_(p_,t.path,d_):(y=d_,d=l_((d=g).x,g.y,g.width,g.height,p_.x,p_.y,u_),y.set(u_[0],u_[1]),d),g<l&&(l=g,p_.transform(h),d_.transform(h),d_.toArray(o[0]),p_.toArray(o[1]),c_.toArray(o[2]))}__(o,e.get("minTurnAngle")),n.setShape({points:o})}}}var m_=[],v_=new ve;function __(t,e){var n,i;e<=180&&0<e&&(e=e/180*Math.PI,c_.fromArray(t[0]),p_.fromArray(t[1]),d_.fromArray(t[2]),ve.sub(f_,c_,p_),ve.sub(g_,d_,p_),i=f_.len(),n=g_.len(),i<.001||n<.001||(f_.scale(1/i),g_.scale(1/n),i=f_.dot(g_),Math.cos(e)<i&&(n=s_(p_.x,p_.y,d_.x,d_.y,c_.x,c_.y,m_,!1),v_.fromArray(m_),v_.scaleAndAdd(g_,n/Math.tan(Math.PI-e)),i=d_.x!==p_.x?(v_.x-p_.x)/(d_.x-p_.x):(v_.y-p_.y)/(d_.y-p_.y),isNaN(i)||(i<0?ve.copy(v_,p_):1<i&&ve.copy(v_,d_),v_.toArray(t[1])))))}function x_(t,e,n,i){var r="normal"===n;n=r?t:t.ensureState(n),n.ignore=e,e=i.get("smooth"),e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),e=i.getModel("lineStyle").getLineStyle();r?t.useStyle(e):n.style=e}function w_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){e=Ot(i[0],i[1]);var r=Ot(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=Rt([],i[1],i[0],n/e),n=Rt([],i[1],i[2],n/r),r=Rt([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function b_(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<xs.length;l++){var u,h=xs[l],c=e[h],p="normal"===h;c&&(u=c.get("show"),(p?s:tt(r.states[h]&&r.states[h].ignore,s))||!tt(u,a)?((u=p?i:i&&i.states[h])&&(u.ignore=!0),i&&x_(i,!0,h,c)):(i||(i=new ru,t.setTextGuideLine(i),p||!s&&a||x_(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),x_(i,!1,h,c)))}i&&(D(i.style,n),i.style.fill=null,n=o.get("showAbove"),(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=n||!1,i.buildPath=w_)}else i&&t.removeTextGuideLine()}function S_(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<_s.length;i++){var r=_s[i];n[r]=t.getModel([r,e])}return n}function M_(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s=0,l=!1,u=0,h=0;h<a;h++){var c,p=t[h],d=p.rect;(c=d[e]-s)<0&&(d[e]-=c,p.label[e]-=c,l=!0),u+=Math.max(-c,0),s=d[e]+d[n]}0<u&&o&&x(-u/a,0,a);var f,g,y=t[0],m=t[a-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),l}function v(){f=y.rect[e]-i,g=r-m.rect[e]-m.rect[n]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,a),(e+=t)<0&&w(-e*n,1)):w(-t*n,1))}function x(n,i,r){0!==n&&(l=!0);for(var o=i;o<r;o++){var a=t[o];a.rect[e]+=n,a.label[e]+=n}}function w(i,r){for(var o=[],s=0,l=1;l<a;l++){var u=t[l-1].rect;u=Math.max(t[l].rect[e]-u[e]-u[n],0);o.push(u),s+=u}if(s){var h=Math.min(Math.abs(i)/s,r);if(0<i)for(l=0;l<a-1;l++)x(o[l]*h,0,l+1);else for(l=a-1;0<l;l--)x(-o[l-1]*h,l,a)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(a-1))),i=0;i<a-1;i++)if(0<e?x(n,0,i+1):x(-n,a-i-1,a),(t-=n)<=0)return}}function T_(t,e,n,i){return M_(t,"y","height",e,n,i)}function C_(t,e){var n=t.label;e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var k_=["align","verticalAlign","width","height","fontSize"],D_=new Gi,I_=Qr(),A_=Qr();function O_(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var L_=["x","y","rotation"],P_=(R_.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},R_.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),h=(l=(De.applyTransform(u,u,l),l?D_.setLocalTransform(l):(D_.x=D_.y=D_.rotation=D_.originX=D_.originY=0,D_.scaleX=D_.scaleY=1),D_.rotation=wa(D_.rotation),i.__hostTarget),l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),De.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:D_.x,y:D_.y,scaleX:D_.scaleX,scaleY:D_.scaleY,rotation:D_.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},R_.prototype.addLabelsOfSeries=function(t){var e=this,n=(this._chartViewList.push(t),t.__model),i=n.get("labelLayout");(W(i)||B(i).length)&&t.group.traverse((function(t){if(t.ignore)return!0;var r=t.getTextContent();t=fs(t);r&&!r.disableLabelLayout&&e._addLabel(t.dataIndex,t.dataType,n,r,i)}))},R_.prototype.updateLayoutConfig=function(t){for(var e=t.getWidth(),n=t.getHeight(),i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,u=(l=W(r.layoutOption)?r.layoutOption(C_(r,a)):r.layoutOption,r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=Tr(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=Tr(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),I_(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<k_.length;p++){var d=k_[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){y_(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},R_.prototype.layout=function(t){var e=t.getWidth(),n=(t=t.getHeight(),function(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new Nu(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}(this._labelList)),i=z(n,(function(t){return"shiftX"===t.layoutOption.moveOverlap})),r=z(n,(function(t){return"shiftY"===t.layoutOption.moveOverlap}));M_(i,"x","width",0,e,void 0),T_(r,0,t);var o=z(n,(function(t){return t.layoutOption.hideOverlap})),a=[],s=(o.sort((function(t,e){return e.priority-t.priority})),new De(0,0,0,0));function l(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var u=0;u<o.length;u++){for(var h=o[u],c=h.axisAligned,p=h.localRect,d=h.transform,f=h.label,g=h.labelLine,y=(s.copy(h.rect),s.width-=.1,s.height-=.1,s.x+=.05,s.y+=.05,h.obb),m=!1,v=0;v<a.length;v++){var _=a[v];if(s.intersect(_.rect)){if(c&&_.axisAligned){m=!0;break}if(_.obb||(_.obb=new Nu(_.localRect,_.transform)),(y=y||new Nu(p,d)).intersect(_.obb)){m=!0;break}}}m?(l(f),g&&l(g)):(f.attr("ignore",h.defaultAttr.ignore),g&&g.attr("ignore",h.defaultAttr.labelGuideIgnore),a.push(h))}},R_.prototype.processLabelsOverall=function(){var t=this;R(this._chartViewList,(function(e){var n=e.__model,i=e.ignoreLabelLineUpdate,r=n.isAnimationEnabled();e.group.traverse((function(e){if(e.ignore&&!e.forceLabelAnimation)return!0;var o=!i,a=e.getTextContent();(o=!o&&a?I_(a).needsUpdateLabelLine:o)&&t._updateLabelLine(e,n),r&&t._animateLabels(e,n)}))}))},R_.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=fs(t),r=i.dataIndex;n&&null!=r&&(e=(n=e.getData(i.dataType)).getItemModel(r),i={},(r=n.getItemVisual(r,"style"))&&(n=n.getVisual("drawType"),i.stroke=r[n]),r=e.getModel("labelLine"),b_(t,S_(e),i),y_(t,r))},R_.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Uu(t))||(o=(r=I_(s)).oldLayout,n=(i=fs(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=A(t,"select")&&s.attr(r.oldLayoutSelect),0<=A(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),Wu(s,a,e,n)):(s.attr(a),hh(s).valueAnimation||(t=tt(s.style.opacity,1),s.style.opacity=0,Gu(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(O_(t=r.oldLayoutSelect={},a,L_),O_(t,s.states.select,L_)),s.states.emphasis&&(O_(t=r.oldLayoutEmphasis={},a,L_),O_(t,s.states.emphasis,L_)),function(t,e,n,i,r){var o,a,s,l=hh(t);l.valueAnimation&&l.prevValue!==l.value&&(o=l.defaultInterpolatedText,a=tt(l.interpolatedValue,l.prevValue),s=l.value,t.percent=0,(null==l.prevValue?Gu:Wu)(t,{percent:1},i,e,null,(function(i){var u=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(j(i))return Cr(p=Vr(n||0,i,r),o?Math.max(kr(n||0),kr(i)):e);if(G(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=Vr(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=Cr(p,o?Math.max(kr(d),kr(c)):e))}return a}(n,l.precision,a,s,i);l.interpolatedValue=1===i?null:u,i=ih({labelDataIndex:e,labelFetcher:r,defaultText:o?o(u):u+""},l.statesModels,u);nh(t,i)})))}(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=A_(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),Wu(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Gu(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},R_);function R_(){this._labelList=[],this._chartViewList=[]}var N_=Qr();function E_(t){t.registerUpdateLifecycle("series:beforeupdate",(function(t,e,n){(N_(e).labelManager||(N_(e).labelManager=new P_)).clearLabels()})),t.registerUpdateLifecycle("series:layoutlabels",(function(t,e,n){var i=N_(e).labelManager;n.updatedSeries.forEach((function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))})),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()}))}function z_(t,e,n){var i=h.createCanvas(),r=e.getWidth(),o=(e=e.getHeight(),i.style);return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}Ov(E_),n(V_,B_=Wt),V_.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},V_.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},V_.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},V_.prototype.setUnpainted=function(){this.__firstTimePaint=!0},V_.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=z_("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},V_.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r=[],o=this.maxRepaintRectCount,a=!1,s=new De(0,0,0,0);function l(t){if(t.isFinite()&&!t.isZero())if(0===r.length)(e=new De(0,0,0,0)).copy(t),r.push(e);else{for(var e,n=!1,i=1/0,l=0,u=0;u<r.length;++u){var h=r[u];if(h.intersect(t)){var c=new De(0,0,0,0);c.copy(h),c.union(t),r[u]=c,n=!0;break}a&&(s.copy(t),s.union(h),c=t.width*t.height,h=h.width*h.height,(h=s.width*s.height-c-h)<i)&&(i=h,l=u)}a&&(r[l].union(t),n=!0),n||((e=new De(0,0,0,0)).copy(t),r.push(e)),a=a||r.length>=o}}for(var u,h=this.__startIndex;h<this.__endIndex;++h)(c=t[h])&&(d=c.shouldBePainted(n,i,!0,!0),(p=c.__isRendered&&(1&c.__dirty||!d)?c.getPrevPaintRect():null)&&l(p),u=d&&(1&c.__dirty||!c.__isRendered)?c.getPaintRect():null)&&l(u);for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c,p,d=(c=e[h])&&c.shouldBePainted(n,i,!0,!0);!c||d&&c.__zr||!c.__isRendered||(p=c.getPrevPaintRect())&&l(p)}do{var f=!1;for(h=0;h<r.length;)if(r[h].isZero())r.splice(h,1);else{for(var g=h+1;g<r.length;)r[h].intersect(r[g])?(f=!0,r[h].union(r[g]),r.splice(g,1)):g++;h++}}while(f);return this._paintRects=r},V_.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},V_.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},V_.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height,s=(e=e||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u)),this.domBack);function p(t,n,i,o){var a;r.clearRect(t,n,i,o),e&&"transparent"!==e&&(a=void 0,$(e)?(a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||yg(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o):K(e)&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,a=Cg(r,e,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()),s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&R(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))};var B_,F_=V_;function V_(t,e,n){var i,r=B_.call(this)||this;r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||Ri,"string"==typeof t?i=z_(t,e,n):X(t)&&(t=(i=t).id),r.id=t,t=(r.dom=i).style;return t&&(mt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var H_=314159;G_.prototype.getType=function(){return"canvas"},G_.prototype.isSingleCanvas=function(){return this._singleCanvas},G_.prototype.getViewportRoot=function(){return this._domRoot},G_.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},G_.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r];a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},G_.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},G_.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),Ng(i,a,r,o===e-1))}i&&i.restore()}},G_.prototype.getHoverLayer=function(){return this.getLayer(1e5)},G_.prototype.paintOne=function(t,e){Rg(t,e)},G_.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer((function(t){t.afterBrush&&t.afterBrush()})):(a=this,Qe((function(){a._paintList(t,e,n,i)}))))},G_.prototype._compositeManually=function(){var t=this.getLayer(H_).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},G_.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s];l=this._layers[l];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||n)&&o.push(l)}for(var u=!0,h=!1,c=function(r){function s(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(c=y;c<d.__endIndex;c++){var r=t[c];if(r.__inHover&&(h=!0),i._doPaintEl(r,d,a,e,n,c===d.__endIndex-1),m&&15<Date.now()-v)break}n.prevElClipPaths&&f.restore()}var l,c,d=o[r],f=d.ctx,g=a&&d.createRepaintRects(t,e,p._width,p._height),y=n?d.__startIndex:d.__drawIndex,m=!n&&d.incremental&&Date.now,v=m&&Date.now();r=d.zlevel===p._zlevelList[0]?p._backgroundColor:null;if(d.__startIndex!==d.__endIndex&&(y!==d.__startIndex||(l=t[y]).incremental&&l.notClear&&!n)||d.clear(!1,r,g),-1===y&&(console.error("For some unknown reason. drawIndex is -1"),y=d.__startIndex),g)if(0===g.length)c=d.__endIndex;else for(var _=p.dpr,x=0;x<g.length;++x){var w=g[x];f.save(),f.beginPath(),f.rect(w.x*_,w.y*_,w.width*_,w.height*_),f.clip(),s(w),f.restore()}else f.save(),s(),f.restore();d.__drawIndex=c,d.__drawIndex<d.__endIndex&&(u=!1)},p=this,d=0;d<o.length;d++)c(d);return r.wxa&&R(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:u,needsRefreshHover:h}},G_.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx,n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(Ng(e,t,r,o),t.setPrevPaintRect(n))):Ng(e,t,r,o)},G_.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=H_);var n=this._layers[t];return n||((n=new F_("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?C(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&C(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},G_.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},G_.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},G_.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},G_.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},G_.prototype.getLayers=function(){return this._layers},G_.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||M("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer((function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},G_.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},G_.prototype._clearLayer=function(t){t.clear()},G_.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,(function(t){t.setUnpainted()}))},G_.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?C(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||C(this._layers[r],n[t],!0)}}},G_.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(A(n,t),1))},G_.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=vg(r,0,i),e=vg(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(H_).resize(t,e)}return this},G_.prototype.clearLayer=function(t){t=this._layers[t],t&&t.clear()},G_.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},G_.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[H_].dom;var e=new F_("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];Ng(n,u,o,s===l-1)}return e.dom},G_.prototype.getWidth=function(){return this._width},G_.prototype.getHeight=function(){return this._height};var W_=G_;function G_(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=k({},n||{}),this.dpr=n.devicePixelRatio||Ri,this._singleCanvas=r,(this.root=t).style&&(mt(t),t.innerHTML=""),this.storage=e;e=this._zlevelList;var o,a,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new F_(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[H_]=o).zlevel=H_,e.push(H_),this._domRoot=t):(this._width=vg(t,0,n),this._height=vg(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}n(X_,U_=Mc),X_.prototype.init=function(t,e,n){U_.prototype.init.call(this,t,e,n),this._sourceManager=new Yd(this),Zd(this)},X_.prototype.mergeOption=function(t,e){U_.prototype.mergeOption.call(this,t,e),Zd(this)},X_.prototype.optionUpdated=function(){this._sourceManager.dirty()},X_.prototype.getSourceManager=function(){return this._sourceManager},X_.type="dataset",X_.defaultOption={seriesLayoutBy:Nc};var U_,j_=X_;function X_(){var t=null!==U_&&U_.apply(this,arguments)||this;return t.type="dataset",t}n(Z_,Y_=df),Z_.type="dataset";var Y_,q_=Z_;function Z_(){var t=null!==Y_&&Y_.apply(this,arguments)||this;return t.type="dataset",t}function $_(t){t.registerComponentModel(j_),t.registerComponentView(q_)}Ov([function(t){t.registerPainter("canvas",W_)},$_]),Ov(E_);var K_=2*Math.PI,Q_=Math.PI/180;function J_(t,e){return gc(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function tx(t,e){var n,i=J_(t,e),r=t.get("center"),o=t.get("radius"),a=(H(o)||(o=[0,o]),Tr(i.width,e.getWidth())),s=(e=Tr(i.height,e.getHeight()),Math.min(a,e)),l=Tr(o[0],s/2);o=Tr(o[1],s/2),s=t.coordinateSystem;return s=s?(n=(t=s.dataToPoint(r))[0]||0,t[1]||0):(n=Tr((r=H(r)?r:[r,r])[0],a)+i.x,Tr(r[1],e)+i.y),{cx:n,cy:s,r0:l,r:o}}function ex(t,e,n){e.eachSeriesByType(t,(function(t){var e,i=t.getData(),r=i.mapDimension("value"),o=J_(t,n),a=tx(t,n),s=a.cx,l=a.cy,u=a.r,h=a.r0,c=-t.get("startAngle")*Q_,p=(a=t.get("endAngle"),t.get("padAngle")*Q_),d=(a="auto"===a?c-K_:-a*Q_,t.get("minAngle")*Q_+p),f=0,g=(i.each(r,(function(t){isNaN(t)||f++})),i.getSum(r)),y=Math.PI/(g||f)*2,m=t.get("clockwise"),v=t.get("roseType"),_=t.get("stillShowZeroSum"),x=i.getDataExtent(r),w=(x[0]=0,m?1:-1),b=(t=[c,a],w*p/2),S=(ya(t,!m),c=t[0],Math.abs(t[1]-c)),M=S,T=0,C=c;i.setLayout({viewRect:o,r:u}),i.each(r,(function(t,e){var n,r,o,a;isNaN(t)?i.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:m,cx:s,cy:l,r0:h,r:v?NaN:u}):((n="area"!==v?0===g&&_?y:t*y:S/f)<d?M-=n=d:T+=t,r=C+w*n,o=0,a=n<p?o=C+w*n/2:(o=C+b,r-b),i.setItemLayout(e,{angle:n,startAngle:o,endAngle:a,clockwise:m,cx:s,cy:l,r0:h,r:v?Mr(t,x,[h,u]):u}),C=r)})),M<K_&&f&&(M<=.001?(e=S/f,i.each(r,(function(t,n){var r,o;isNaN(t)||(t=0,o=((r=i.getItemLayout(n)).angle=e)<p?t=c+w*(n+.5)*e:(t=c+w*n*e+b,c+w*(n+1)*e-b),r.startAngle=t,r.endAngle=o)}))):(y=M/T,C=c,i.each(r,(function(t,e){var n,r;isNaN(t)||(n=0,r=(t=(e=i.getItemLayout(e)).angle===d?d:t*y)<p?n=C+w*t/2:(n=C+b,C+w*t-b),e.startAngle=n,e.endAngle=r,C+=w*t)}))))}))}var nx=Math.PI/180;function ix(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h,c=t.length,p=0;p<c;p++)"outer"===t[p].position&&"labelLine"===t[p].labelAlignTo&&(h=t[p].label.x-u,t[p].linePoints[1][0]+=h,t[p].label.x=u);if(T_(t,l,l+a)){for(var d,f,g,y,m,v=t,_={list:[],maxY:0},x={list:[],maxY:0},w=0;w<v.length;w++)"none"===v[w].labelAlignTo&&(f=(d=v[w]).label.y>n?x:_,(g=Math.abs(d.label.y-n))>=f.maxY&&(m=d.label.x-e-d.len2*r,y=i+d.len,m=Math.abs(m)<y?Math.sqrt(g*g/(1-m*m/y/y)):y,f.rB=m,f.maxY=g),f.list.push(d));b(_),b(x)}}function b(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len;h=h*h,u=Math.sqrt((1-Math.abs(u*u/a))*h),h=e+(u+l.len2)*r,u=h-l.label.x;rx(l,l.targetTextWidth-u*r,!0),l.label.x=h}}}function rx(t,e,n){var i,r,o,a,s,l,u;void 0===n&&(n=!1),null==t.labelStyleWidth&&(s=(i=t.label).style,r=t.rect,l=s.backgroundColor,u=(u=s.padding)?u[1]+u[3]:0,s=s.overflow,e<(o=r.width+(l?0:u))||n)&&(a=r.height,s&&s.match("break")?(i.setStyle("backgroundColor",null),i.setStyle("width",e-u),s=i.getBoundingRect(),i.setStyle("width",Math.ceil(s.width)),i.setStyle("backgroundColor",l)):(s=e-u,l=e<o||n&&!(s>t.unconstrainedWidth)?s:null,i.setStyle("width",l)),u=i.getBoundingRect(),r.width=u.width,e=(i.style.margin||0)+2.1,r.height=u.height+e,r.y-=(r.height-a)/2)}function ox(t){return"center"===t.position}function ax(t,e,n){if(t=t.get("borderRadius"),null==t)return n?{cornerRadius:0}:null;H(t)||(t=[t,t,t,t]);var i=Math.abs(e.r||0-e.r0||0);return{cornerRadius:N(t,(function(t){return Ji(t,i)}))}}n(ux,sx=jl),ux.prototype.updateData=function(t,e,n,i){var r=this,o=t.hostModel,a=t.getItemModel(e),s=a.getModel("emphasis"),l=t.getItemLayout(e),u=k(ax(a.getModel("itemStyle"),l,!0),l);if(isNaN(u.startAngle))r.setShape(u);else{i?(r.setShape(u),i=o.getShallow("animationType"),o.ecModel.ssr?(Gu(r,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),r.originX=u.cx,r.originY=u.cy):"scale"===i?(r.shape.r=l.r0,Gu(r,{shape:{r:l.r}},o,e)):null!=n?(r.setShape({startAngle:n,endAngle:n}),Gu(r,{shape:{startAngle:l.startAngle,endAngle:l.endAngle}},o,e)):(r.shape.endAngle=l.startAngle,Wu(r,{shape:{endAngle:l.endAngle}},o,e))):(Vu(i=r).oldStyle=i.style,Wu(r,{shape:u},o,e)),r.useStyle(t.getItemVisual(e,"style"));var h=r,c=a,p=void 0;p=p||"itemStyle";for(var d=0;d<Qs.length;d++){var f=Qs[d],g=c.getModel([f,p]);h.ensureState(f).style=g[Js[p]]()}n=(l.startAngle+l.endAngle)/2,i=o.get("selectedOffset"),u=Math.cos(n)*i,n=Math.sin(n)*i,i=a.getShallow("cursor"),i&&r.attr("cursor",i),this._updateLabel(o,t,e),r.ensureState("emphasis").shape=k({r:l.r+(s.get("scale")&&s.get("scaleSize")||0)},ax(s.getModel("itemStyle"),l)),k(r.ensureState("select"),{x:u,y:n,shape:ax(a.getModel(["select","itemStyle"]),l)}),k(r.ensureState("blur"),{shape:ax(a.getModel(["blur","itemStyle"]),l)}),i=r.getTextGuideLine(),o=r.getTextContent(),i&&k(i.ensureState("select"),{x:u,y:n}),k(o.ensureState("select"),{x:u,y:n}),function(t,e,n,i){i?tl(t,!1):Ks(t,e,n)}(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))}},ux.prototype._updateLabel=function(t,e,n){var i=e.getItemModel(n),r=i.getModel("labelLine"),o=e.getItemVisual(n,"style"),a=o&&o.fill,s=(o=o&&o.opacity,this),l=function(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<_s.length;i++){var r=_s[i];n[r]=t.getModel([r,e])}return n}(i),u={labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:a,defaultOpacity:o,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)},h=void 0;u=u||eh;for(var c=s instanceof ns,p=!1,d=0;d<xs.length;d++)if((m=l[xs[d]])&&m.getShallow("show")){p=!0;break}var f=c?s:s.getTextContent();if(p){c||(f||(f=new ns,s.setTextContent(f)),s.stateProxy&&(f.stateProxy=s.stateProxy));var g=ih(u,l),y=(e=l.normal,!!e.getShallow("show"));n=rh(e,h,u,!1,!c);for(n.text=g.normal,c||s.setTextConfig(oh(e,u,!1)),d=0;d<_s.length;d++){var m,v,_,x=_s[d];(m=l[x])&&(v=f.ensureState(x),(_=!!tt(m.getShallow("show"),y))!=y&&(v.ignore=!_),v.style=rh(m,h,u,!0,!c),v.style.text=g[x],c||(s.ensureState(x).textConfig=oh(m,u,!0)))}f.silent=!!e.getShallow("silent"),null!=f.style.x&&(n.x=f.style.x),null!=f.style.y&&(n.y=f.style.y),f.ignore=!y,f.useStyle(n),f.dirty(),u.enableTextSetter&&(hh(f).setLabelText=function(t){t=ih(u,l,t),nh(f,t)})}else f&&(f.ignore=!0);s.dirty(),e=this.getTextContent(),this.setTextConfig({position:null,rotation:null}),e.attr({z2:10}),n=t.get(["label","position"]),"outside"!==n&&"outer"!==n?this.removeTextGuideLine():(this.getTextGuideLine()||(e=new ru,this.setTextGuideLine(e)),b_(this,S_(i),{stroke:a,opacity:et(r.get(["lineStyle","opacity"]),o,1)}))};var sx,lx=ux;function ux(t,e,n){var i=sx.call(this)||this,r=(i.z2=2,new ns);return i.setTextContent(r),i.updateData(t,e,n,!0),i}n(px,hx=vf),px.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&0<o.count()){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")&&((n=new jl({shape:tx(t,n)})).useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=n,s.add(n)),o.diff(a).add((function(t){var e=new lx(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){e=a.getItemGraphicEl(e),e.updateData(o,t,r),e.off("click"),s.add(e),o.setItemGraphicEl(t,e)})).remove((function(e){var n,i,r,o=a.getItemGraphicEl(e);function s(){n.parent&&n.parent.remove(n)}i=t,r=e,(n=o).isGroup?n.traverse((function(t){t.isGroup||ju(t,i,r,s)})):ju(n,i,r,s)})).execute(),function(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*nx,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y;function p(t){t.ignore=!0}if(s=s.height,i.each((function(t){var s,c,d,f,g,y,m,v,_,x,w=i.getItemGraphicEl(t),b=w.shape,S=w.getTextContent(),M=w.getTextGuideLine(),T=(t=i.getItemModel(t),t.getModel("label")),C=T.get("position")||t.get(["emphasis","label","position"]),k=T.get("distanceToLabelLine"),D=T.get("alignTo"),I=Tr(T.get("edgeDistance"),u),A=T.get("bleedMargin"),O=(t=t.getModel("labelLine"),Tr(t.get("length"),u)),L=Tr(t.get("length2"),u);Math.abs(b.endAngle-b.startAngle)<a?(R(S.states,p),S.ignore=!0,M&&(R(M.states,p),M.ignore=!0)):function(t){if(!t.ignore)return 1;for(var e in t.states)if(!1===t.states[e].ignore)return 1}(S)&&(x=(b.startAngle+b.endAngle)/2,s=Math.cos(x),c=Math.sin(x),e=b.cx,n=b.cy,d="inside"===C||"inner"===C,m="center"===C?(f=b.cx,g=b.cy,"center"):(f=(m=(d?(b.r+b.r0)/2*s:b.r*s)+e)+3*s,g=(v=(d?(b.r+b.r0)/2*c:b.r*c)+n)+3*c,d||(y=m+s*(O+l-b.r),b=v+c*(O+l-b.r),_=y+(s<0?-1:1)*L,f="edge"===D?s<0?h+I:h+u-I:_+(s<0?-k:k),y=[[m,v],[y,g=b],[_,b]]),d?"center":"edge"===D?0<s?"right":"left":0<s?"left":"right"),v=Math.PI,_=0,j(b=T.get("rotate"))?_=b*(v/180):"center"===C?_=0:"radial"===b||!0===b?_=s<0?-x+v:-x:"tangential"===b&&"outside"!==C&&"outer"!==C&&((T=Math.atan2(s,c))<0&&(T=2*v+T),_=(T=0<c?v+T:T)-v),o=!!_,S.x=f,S.y=g,S.rotation=_,S.setStyle({verticalAlign:"middle"}),d?(S.setStyle({align:m}),(x=S.states.select)&&(x.x+=S.x,x.y+=S.y)):((b=S.getBoundingRect().clone()).applyTransform(S.getComputedTransform()),T=(S.style.margin||0)+2.1,b.y-=T/2,b.height+=T,r.push({label:S,labelLine:M,position:C,len:O,len2:L,minTurnAngle:t.get("minTurnAngle"),maxSurfaceAngle:t.get("maxSurfaceAngle"),surfaceNormal:new ve(s,c),linePoints:y,textAlign:m,labelDistance:k,labelAlignTo:D,edgeDistance:I,bleedMargin:A,rect:b,unconstrainedWidth:b.width,labelStyleWidth:S.style.width})),w.setTextConfig({inside:d}))})),!o&&t.get("avoidLabelOverlap")){for(var d=r,f=e,g=(t=n,l),y=u,m=h,v=[],_=[],x=Number.MAX_VALUE,w=-Number.MAX_VALUE,b=0;b<d.length;b++){var S=d[b].label;ox(d[b])||(S.x<f?(x=Math.min(x,S.x),v):(w=Math.max(w,S.x),_)).push(d[b])}var M;for(b=0;b<d.length;b++)!ox(T=d[b])&&T.linePoints&&null==T.labelStyleWidth&&(S=T.label,C=T.linePoints,void 0,M="edge"===T.labelAlignTo?S.x<f?C[2][0]-T.labelDistance-m-T.edgeDistance:m+y-T.edgeDistance-C[2][0]-T.labelDistance:"labelLine"===T.labelAlignTo?S.x<f?x-m-T.bleedMargin:m+y-w-T.bleedMargin:S.x<f?S.x-m-T.bleedMargin:m+y-S.x-T.bleedMargin,T.targetTextWidth=M,rx(T,M));ix(_,f,t,g,1,0,s,0,c,w),ix(v,f,t,g,-1,0,s,0,c,x);var T,C,k,D,I;for(b=0;b<d.length;b++)!ox(T=d[b])&&T.linePoints&&(S=T.label,C=T.linePoints,k="edge"===T.labelAlignTo,D=(D=S.style.padding)?D[1]+D[3]:0,D=S.style.backgroundColor?0:D,D=T.rect.width+D,I=C[1][0]-C[2][0],k?S.x<f?C[2][0]=m+T.edgeDistance+D+T.labelDistance:C[2][0]=m+y-T.edgeDistance-D-T.labelDistance:(S.x<f?C[2][0]=S.x+T.labelDistance:C[2][0]=S.x-T.labelDistance,C[1][0]=C[2][0]+I),C[1][1]=C[2][1]=S.y)}for(var A=0;A<r.length;A++){var O,L=r[A],P=L.label,N=L.labelLine,E=isNaN(P.x)||isNaN(P.y);P&&(P.setStyle({align:L.textAlign}),E&&(R(P.states,p),P.ignore=!0),O=P.states.select)&&(O.x+=P.x,O.y+=P.y),N&&(O=L.linePoints,E||!O?(R(N.states,p),N.ignore=!0):(__(O,L.minTurnAngle),function(t,e,n){if(n<=180&&0<n){n=n/180*Math.PI,c_.fromArray(t[0]),p_.fromArray(t[1]),d_.fromArray(t[2]),ve.sub(f_,p_,c_),ve.sub(g_,d_,p_);var i=f_.len(),r=g_.len();if(!(i<.001||r<.001)&&(f_.scale(1/i),g_.scale(1/r),f_.dot(e)<Math.cos(n))){if(i=s_(p_.x,p_.y,d_.x,d_.y,c_.x,c_.y,m_,!1),v_.fromArray(m_),r=Math.PI/2,e=r+Math.acos(g_.dot(e))-n,r<=e)ve.copy(v_,d_);else{if(v_.scaleAndAdd(g_,i/Math.tan(Math.PI/2-e)),n=d_.x!==p_.x?(v_.x-p_.x)/(d_.x-p_.x):(v_.y-p_.y)/(d_.y-p_.y),isNaN(n))return;n<0?ve.copy(v_,p_):1<n&&ve.copy(v_,d_)}v_.toArray(t[1])}}}(O,L.surfaceNormal,L.maxSurfaceAngle),N.setShape({points:O}),P.__hostTarget.textGuideLineConfig={anchor:new ve(O[0][0],O[0][1])}))}}(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},px.prototype.dispose=function(){},px.prototype.containPoint=function(t,e){var n;e=e.getData().getItemLayout(0);if(e)return n=t[0]-e.cx,t=t[1]-e.cy,(n=Math.sqrt(n*n+t*t))<=e.r&&n>=e.r0},px.type="pie";var hx,cx=px;function px(){var t=null!==hx&&hx.apply(this,arguments)||this;return t.ignoreLabelLineUpdate=!0,t}fx.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},fx.prototype.containName=function(t){return 0<=this._getRawData().indexOfName(t)},fx.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},fx.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)};var dx=fx;function fx(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}var gx,yx=Qr(),mx=(n(vx,gx=rf),vx.prototype.init=function(t){gx.prototype.init.apply(this,arguments),this.legendVisualProvider=new dx(F(this.getData,this),F(this.getRawData,this)),this._defaultLabelLine(t)},vx.prototype.mergeOption=function(){gx.prototype.mergeOption.apply(this,arguments)},vx.prototype.getInitialData=function(){return e=H(e={coordDimensions:["value"],encodeDefaulter:V(Vc,t=this)})?{coordDimensions:e}:k({encodeDefine:t.getEncode()},e),n=t.getSource(),e=Om(n,e).dimensions,(e=new Im(e,t)).initData(n,void 0),e;var t,e,n},vx.prototype.getDataParams=function(t){var e,n=this.getData(),i=yx(n),r=i.seats;r||(e=[],n.each(n.mapDimension("value"),(function(t){e.push(t)})),r=i.seats=Ar(e,n.hostModel.get("percentPrecision"))),i=gx.prototype.getDataParams.call(this,t);return i.percent=r[t]||0,i.$vars.push("percent"),i},vx.prototype._defaultLabelLine=function(t){Gr(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},vx.type="series.pie",vx.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},vx);function vx(){return null!==gx&&gx.apply(this,arguments)||this}Ov((function(t){t.registerChartView(cx),t.registerSeriesModel(mx),function(t,e){R([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(n){e(n[0],(function(e,i,r){var o;e=k({},e),r.dispatchAction(k(e,{type:n[1],seriesIndex:(r=e,o=[],i.eachComponent({mainType:"series",subType:t,query:r},(function(t){o.push(t.seriesIndex)})),o)}))}))}))}("pie",t.registerAction),t.registerLayout(V(ex,"pie")),t.registerProcessor({seriesType:"pie",reset:function(t,e){var n,i=e.findComponents({mainType:"legend"});i&&i.length&&(n=t.getData()).filterSelf((function(t){for(var e=n.getName(t),r=0;r<i.length;r++)if(!i[r].isSelected(e))return!1;return!0}))}}),t.registerProcessor({seriesType:"pie",reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value");e=n.get(e,t);return!(j(e)&&!isNaN(e)&&e<0)}))}})})),n(bx,_x=Mc),bx.type="title",bx.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}};var _x,xx=bx;function bx(){var t=null!==_x&&_x.apply(this,arguments)||this;return t.type=bx.type,t.layoutMode={type:"box",ignoreSize:!0},t}n(Tx,Sx=df),Tx.prototype.render=function(t,e,n){var i,r,o,a,s,l,u,h,c;this.group.removeAll(),t.get("show")&&(i=this.group,u=t.getModel("textStyle"),r=t.getModel("subtextStyle"),h=t.get("textAlign"),c=tt(t.get("textBaseline"),t.get("textVerticalAlign")),s=(u=new ns({style:rh(u,{text:t.get("text"),fill:u.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),r=new ns({style:rh(r,{text:l,fill:r.getTextColor(),y:s.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),o=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),u.silent=!o&&!s,r.silent=!a&&!s,o&&u.on("click",(function(){hc(o,"_"+t.get("target"))})),a&&r.on("click",(function(){hc(a,"_"+t.get("subtarget"))})),fs(u).eventData=fs(r).eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(u),l&&i.add(r),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=gc(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),h||("right"===(h="middle"===(h=t.get("left")||t.get("right"))?"center":h)?l.x+=l.width:"center"===h&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.x=l.x,i.y=l.y,i.markRedraw(),u.setStyle(n={align:h,verticalAlign:c}),r.setStyle(n),s=i.getBoundingRect(),u=l.margin,(h=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new Ka({shape:{x:s.x-u[3],y:s.y-u[0],width:s.width+u[1]+u[3],height:s.height+u[0]+u[2],r:t.get("borderRadius")},style:h,subPixelOptimize:!0,silent:!0}),i.add(c))},Tx.type="title";var Sx,Mx=Tx;function Tx(){var t=null!==Sx&&Sx.apply(this,arguments)||this;return t.type=Tx.type,t}Ov((function(t){t.registerComponentModel(xx),t.registerComponentView(Mx)})),Ov($_);var Cx={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},kx=(Dx.prototype.evaluate=function(t){var e=a(t);return G(e)?this._condVal.test(t):!!j(e)&&this._condVal.test(t+"")},Dx);function Dx(t){null==(this._condVal=G(t)?new RegExp(t):Q(t)?t:null)&&Fr("")}Ax.prototype.evaluate=function(){return this.value};var Ix=Ax;function Ax(){}Lx.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var Ox=Lx;function Lx(){}Rx.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var Px=Rx;function Rx(){}Ex.prototype.evaluate=function(){return!this.child.evaluate()};var Nx=Ex;function Ex(){}Bx.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var zx=Bx;function Bx(){}function Fx(t,e){if(!0===t||!1===t)return(n=new Ix).value=t,n;var n;if(Hx(t)||Fr(""),t.and)return Vx("and",t,e);if(t.or)return Vx("or",t,e);if(t.not)return n=e,Hx(o=(o=t).not)||Fr(""),(l=new Nx).child=Fx(o,n),l.child||Fr(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=B(i),l=i.parser,u=l?xd(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=vt(Cx,p)?Cx[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Cd("eq"===t,e):vt(wd,t)?new bd(t,e):null}(c,p)||"reg"===c&&new kx(p))||Fr(""),a.push(c))}return a.length||Fr(""),(l=new zx).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function Vx(t,e,n){return e=e[t],H(e)||Fr(""),e.length||Fr(""),t=new("and"===t?Ox:Px),t.children=N(e,(function(t){return Fx(t,n)})),t.children.length||Fr(""),t}function Hx(t){return X(t)&&!P(t)}Gx.prototype.evaluate=function(){return this._cond.evaluate()};var Wx=Gx;function Gx(t,e){this._cond=Fx(t,e)}var Ux={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:ft({dimension:!0}),prepareGetValue:function(t){var e=t.dimension;vt(t,"dimension")||Fr(""),t=i.getDimensionInfo(e);return t||Fr(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new Wx(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},jx={type:"echarts:sort",transform:function(t){for(var e=t.upstream,n=(t=t.config,t=Wr(t),t.length||Fr(""),[]),i=(t=(R(t,(function(t){var i=t.dimension,r=t.order,o=t.parser,a=(t=t.incomparable,i=(null==i&&Fr(""),"asc"!==r&&"desc"!==r&&Fr(""),t&&"min"!==t&&"max"!==t&&Fr(""),"asc"!==r&&"desc"!==r&&Fr(""),e.getDimensionInfo(i)),i||Fr(""),o?xd(o):null);o&&!a&&Fr(""),n.push({dimIdx:i.index,parser:a,comparator:new Md(r,t)})})),e.sourceFormat),t!==Ac&&t!==Oc&&Fr(""),[]),r=0,o=e.count();r<o;r++)i.push(e.getRawDataItem(r));return i.sort((function(t,i){for(var r=0;r<n.length;r++){var o=n[r],a=e.retrieveValueFromItem(t,o.dimIdx),s=e.retrieveValueFromItem(i,o.dimIdx);o=(o.parser&&(a=o.parser(a),s=o.parser(s)),o.comparator.evaluate(a,s));if(0!==o)return o}return 0})),{data:i}}};Ov((function(t){t.registerTransform(Ux),t.registerTransform(jx)})),t.Axis=bh,t.ChartView=vf,t.ComponentModel=Mc,t.ComponentView=df,t.List=Im,t.Model=Th,t.PRIORITY=sg,t.SeriesModel=rf,t.color=jn,t.connect=function(t){var e;return H(t)&&(e=t,t=null,R(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+zy++,R(e,(function(e){e.group=t}))),Ny[t]=!0,t},t.dataTool={},t.dependencies={zrender:"5.5.0"},t.disConnect=ag,t.disconnect=Fy,t.dispose=function(t){G(t)?t=Ry[t]:t instanceof _y||(t=Vy(t)),t instanceof _y&&!t.isDisposed()&&t.dispose()},t.env=r,t.extendChartView=function(t){return t=vf.extend(t),vf.registerClass(t),t},t.extendComponentModel=function(t){return t=Mc.extend(t),Mc.registerClass(t),t},t.extendComponentView=function(t){return t=df.extend(t),df.registerClass(t),t},t.extendSeriesModel=function(t){return t=rf.extend(t),rf.registerClass(t),t},t.format=xh,t.getCoordinateSystemDimensions=function(t){if(t=sp.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=Vy,t.getInstanceById=function(t){return Ry[t]},t.getMap=function(t){var e=Wg.getMap;return e&&e(t)},t.graphic=zh,t.helper=lg,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=Vy(t);if(r)return r}return(r=new _y(t,e,n)).id="ec_"+Ey++,Ry[r.id]=r,i&&ro(t,By,r.id),gy(r),Hg.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=Rg,t.matrix=me,t.number=mo,t.parseGeoJSON=Yv,t.parseGeoJson=Yv,t.registerAction=Yy,t.registerCoordinateSystem=qy,t.registerLayout=Zy,t.registerLoading=Jy,t.registerLocale=Ph,t.registerMap=tm,t.registerPostInit=Uy,t.registerPostUpdate=jy,t.registerPreprocessor=Wy,t.registerProcessor=Gy,t.registerTheme=Hy,t.registerTransform=em,t.registerUpdateLifecycle=Xy,t.registerVisual=$y,t.setCanvasCreator=function(t){c({createCanvas:t})},t.setPlatformAPI=c,t.throttle=Tf,t.time=fh,t.use=Ov,t.util=yh,t.vector=Bt,t.version="5.5.0",t.zrUtil=wt,t.zrender=Sr}))},4085:function(t,e,n){"use strict";var i=n("8bdb"),r=n("85c1");i({global:!0,forced:r.globalThis!==r},{globalThis:r})},"43ce":function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={props:{mode:{type:String,default:"back"},title:{type:String,default:""}},data:function(){return{headTop:0,barHeight:0}},created:function(){var t=this.$wanlshop.wanlsys();this.headTop=t.top,this.barHeight=t.barHeight}};e.default=i},4733:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("8d0b"))},"4d84":function(t,e,n){var i=n("144f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("967d").default;r("7ff6cc0a",i,!0,{sourceMap:!1,shadowMode:!1})},"4f9b":function(t,e,n){"use strict";var i=n("6a50");i("Float32",(function(t){return function(e,n,i){return t(this,e,n,i)}}))},5075:function(t,e,n){"use strict";var i=n("ae5c"),r=n("71e9"),o=n("e7e3"),a=n("52df"),s=n("81a7"),l=n("1fc1"),u=n("1297"),h=n("d67c"),c=n("5112"),p=n("7e91"),d=TypeError,f=function(t,e){this.stopped=t,this.result=e},g=f.prototype;t.exports=function(t,e,n){var y,m,v,_,x,w,b,S=n&&n.that,M=!(!n||!n.AS_ENTRIES),T=!(!n||!n.IS_RECORD),C=!(!n||!n.IS_ITERATOR),k=!(!n||!n.INTERRUPTED),D=i(e,S),I=function(t){return y&&p(y,"normal",t),new f(!0,t)},A=function(t){return M?(o(t),k?D(t[0],t[1],I):D(t[0],t[1])):k?D(t,I):D(t)};if(T)y=t.iterator;else if(C)y=t;else{if(m=c(t),!m)throw new d(a(t)+" is not iterable");if(s(m)){for(v=0,_=l(t);_>v;v++)if(x=A(t[v]),x&&u(g,x))return x;return new f(!1)}y=h(t,m)}w=T?t.next:y.next;while(!(b=r(w,y)).done){try{x=A(b.value)}catch(O){p(y,"throw",O)}if("object"==typeof x&&x&&u(g,x))return x}return new f(!1)}},"57e7":function(t,e,n){"use strict";var i=n("e37c"),r=n("e4ca"),o=n("a74c"),a=n("ae5c"),s=n("b720"),l=n("1eb8"),u=n("5075"),h=n("0cc2"),c=n("97ed"),p=n("437f"),d=n("ab4a"),f=n("d0b1").fastKey,g=n("235c"),y=g.set,m=g.getterFor;t.exports={getConstructor:function(t,e,n,h){var c=t((function(t,r){s(t,p),y(t,{type:e,index:i(null),first:void 0,last:void 0,size:0}),d||(t.size=0),l(r)||u(r,t[h],{that:t,AS_ENTRIES:n})})),p=c.prototype,g=m(e),v=function(t,e,n){var i,r,o=g(t),a=_(t,e);return a?a.value=n:(o.last=a={index:r=f(e,!0),key:e,value:n,previous:i=o.last,next:void 0,removed:!1},o.first||(o.first=a),i&&(i.next=a),d?o.size++:t.size++,"F"!==r&&(o.index[r]=a)),t},_=function(t,e){var n,i=g(t),r=f(e);if("F"!==r)return i.index[r];for(n=i.first;n;n=n.next)if(n.key===e)return n};return o(p,{clear:function(){var t=g(this),e=t.first;while(e)e.removed=!0,e.previous&&(e.previous=e.previous.next=void 0),e=e.next;t.first=t.last=void 0,t.index=i(null),d?t.size=0:this.size=0},delete:function(t){var e=g(this),n=_(this,t);if(n){var i=n.next,r=n.previous;delete e.index[n.index],n.removed=!0,r&&(r.next=i),i&&(i.previous=r),e.first===n&&(e.first=i),e.last===n&&(e.last=r),d?e.size--:this.size--}return!!n},forEach:function(t){var e,n=g(this),i=a(t,arguments.length>1?arguments[1]:void 0);while(e=e?e.next:n.first){i(e.value,e.key,this);while(e&&e.removed)e=e.previous}},has:function(t){return!!_(this,t)}}),o(p,n?{get:function(t){var e=_(this,t);return e&&e.value},set:function(t,e){return v(this,0===t?0:t,e)}}:{add:function(t){return v(this,t=0===t?0:t,t)}}),d&&r(p,"size",{configurable:!0,get:function(){return g(this).size}}),c},setStrong:function(t,e,n){var i=e+" Iterator",r=m(e),o=m(i);h(t,e,(function(t,e){y(this,{type:i,target:t,state:r(t),kind:e,last:void 0})}),(function(){var t=o(this),e=t.kind,n=t.last;while(n&&n.removed)n=n.previous;return t.target&&(t.last=n=n?n.next:t.state.first)?c("keys"===e?n.key:"values"===e?n.value:[n.key,n.value],!1):(t.target=void 0,c(void 0,!0))}),n?"entries":"values",!n,!0),p(e)}}},"5d6e":function(t,e,n){"use strict";var i=n("af9e");t.exports=i((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}}))},"66eed":function(t,e,n){"use strict";n.r(e);var i=n("7383"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},"6d5f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"wanl-headbar flex align-center justify-between",style:{top:t.headTop+"px",height:t.barHeight+"px"}},["back"==t.mode?n("v-uni-view",{staticClass:"action",style:{height:t.barHeight+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$wanlshop.back(1)}}},[n("v-uni-image",{staticClass:"white-back",attrs:{src:t.$wanlshop.imgstc("/default/white_back.png"),mode:""}})],1):t._e(),"logo"==t.mode?n("v-uni-image",{staticClass:"logo",attrs:{src:t.$wanlshop.imgstc("/old/logo.jpg?t=2"),mode:""}}):t._e(),t.title?n("v-uni-view",{staticClass:"flex align-center justify-center text-center text-white text-xl title",style:{height:t.barHeight+"px",lineHeight:t.barHeight+"px"}},[t._v(t._s(t.title))]):n("v-uni-image",{staticClass:"logoText",attrs:{src:t.$wanlshop.imgstc("/home/<USER>"),mode:""}})],1)},r=[]},7383:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("2634")),o=i(n("fcf3")),a=i(n("2fdc"));n("64aa"),n("c223"),n("5c47"),n("bf0f"),n("2797"),n("08eb"),n("18f7"),n("4626");var s=n("82bb"),l=n("f190"),u={name:"lime-echart",props:{customStyle:String,isDisableScroll:Boolean,isClickable:{type:Boolean,default:!0},enableHover:Boolean,beforeDelay:{type:Number,default:30}},data:function(){return{use2dCanvas:!1,ariaLabel:"图表",width:null,height:null,nodeWidth:null,nodeHeight:null,config:{},inited:!1,finished:!1,file:"",platform:"",isPC:!1,isDown:!1,isOffscreenCanvas:!1,offscreenWidth:0,offscreenHeight:0}},computed:{canvasId:function(){return"lime-echart".concat(this._&&this._.uid||this._uid)},offscreenCanvasId:function(){return"".concat(this.canvasId,"_offscreen")},offscreenStyle:function(){return"width:".concat(this.offscreenWidth,"px;height: ").concat(this.offscreenHeight,"px; position: fixed; left: 99999px; background: red")},canvasStyle:function(){return this.width&&this.height?"width:"+this.width+"px;height:"+this.height+"px":""}},beforeDestroy:function(){this.clear(),this.dispose(),this.isPC&&document.removeEventListener("mousewheel",this.mousewheel)},created:function(){"ontouchstart"in window||(this.isPC=!0,document.addEventListener("mousewheel",this.mousewheel)),this.use2dCanvas="2d"===this.type&&(0,l.canIUseCanvas2d)()},mounted:function(){var t=this;this.$nextTick((function(){t.$emit("finished")}))},methods:{setChart:function(t){this.chart?"function"===typeof t&&this.chart&&t(this.chart):console.warn("组件还未初始化，请先使用 init")},setOption:function(){var t;this.chart&&this.chart.setOption?(t=this.chart).setOption.apply(t,arguments):console.warn("组件还未初始化，请先使用 init")},showLoading:function(){var t;this.chart&&(t=this.chart).showLoading.apply(t,arguments)},hideLoading:function(){this.chart&&this.chart.hideLoading()},clear:function(){this.chart&&this.chart.clear()},dispose:function(){this.chart&&this.chart.dispose()},resize:function(t){var e=this;t&&t.width&&t.height?(this.height=t.height,this.width=t.width,this.chart&&this.chart.resize(t)):this.$nextTick((function(){uni.createSelectorQuery().in(e).select(".lime-echart").boundingClientRect().exec((function(t){if(t){var n=t[0],i=n.width,r=n.height;e.width=i=i||300,e.height=r=r||300,e.chart.resize({width:i,height:r})}}))}))},canvasToTempFilePath:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.use2dCanvas,i=this.canvasId;return new Promise((function(r,o){var a=Object.assign({canvasId:i,success:r,fail:o},e);n&&(delete a.canvasId,a.canvas=t.canvasNode),uni.canvasToTempFilePath(a,t)}))},init:function(t){var e=arguments,n=this;return(0,a.default)((0,r.default)().mark((function i(){var a,u,h,c,p,d,f;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:for(a=e.length,u=new Array(a>1?a-1:0),h=1;h<a;h++)u[h-1]=e[h];if(!u||0!=u.length||t){i.next=4;break}return console.error("缺少参数：init(echarts, theme?:string, opts?: object, callback?: function)"),i.abrupt("return");case 4:if(c=null,p={},Array.from(e).forEach((function(t){"function"===typeof t&&(d=t),["string"].includes((0,o.default)(t))&&(c=t),"object"===(0,o.default)(t)&&(p=t)})),!n.beforeDelay){i.next=9;break}return i.next=9,(0,l.sleep)(n.beforeDelay);case 9:return i.next=11,n.getContext();case 11:if(f=i.sent,(0,s.setCanvasCreator)(t,f),n.chart=t.init(f.canvas,c,Object.assign({},f,p)),"function"!==typeof d){i.next=18;break}d(n.chart),i.next=19;break;case 18:return i.abrupt("return",n.chart);case 19:case"end":return i.stop()}}),i)})))()},getContext:function(){var t=this;return(0,l.getRect)("#".concat(this.canvasId),{context:this,type:this.use2dCanvas?"fields":"boundingClientRect"}).then((function(e){if(e){var n,i=l.devicePixelRatio,r=e.width,o=e.height,a=e.node;if(t.width=r=r||300,t.height=o=o||300,a){var u=a.getContext("2d");n=new s.Canvas(u,t,!0,a),t.canvasNode=a}else{i=t.isPC?l.devicePixelRatio:1,t.rect=e,t.nodeWidth=r*i,t.nodeHeight=o*i;var h=uni.createCanvasContext(t.canvasId,t);n=new s.Canvas(h,t,!1)}return{canvas:n,width:r,height:o,devicePixelRatio:i,node:a}}return{}}))},getRelative:function(t,e){var n=t.clientX,i=t.clientY;return n&&i||!e||!e[0]||(n=e[0].clientX,i=e[0].clientY),{x:n-this.rect.left,y:i-this.rect.top,wheelDelta:t.wheelDelta||0}},getTouch:function(t,e){var n=e&&e[0]||{},i=n.x;return i?e[0]:this.getRelative(t,e)},touchStart:function(t){var e=this;this.isDown=!0;var n=function(){var n=(0,l.convertTouchesToArray)(t.touches);if(e.chart){var i=e.getTouch(t,n);e.startX=i.x,e.startY=i.y,e.startT=new Date;var r=e.chart.getZr().handler;s.dispatch.call(r,"mousedown",i),s.dispatch.call(r,"mousemove",i),r.processGesture((0,l.wrapTouch)(t),"start"),clearTimeout(e.endTimer)}};this.isPC?(0,l.getRect)("#".concat(this.canvasId),{context:this}).then((function(t){e.rect=t,n()})):n()},touchMove:function(t){this.isPC&&this.enableHover&&!this.isDown&&(this.isDown=!0);var e=(0,l.convertTouchesToArray)(t.touches);if(this.chart&&this.isDown){var n=this.chart.getZr().handler;s.dispatch.call(n,"mousemove",this.getTouch(t,e)),n.processGesture((0,l.wrapTouch)(t),"change")}},touchEnd:function(t){if(this.isDown=!1,this.chart){var e=(0,l.convertTouchesToArray)(t.changedTouches),n=e&&e[0]||{},i=n.x,r=(i?e[0]:this.getRelative(t,e))||{},o=this.chart.getZr().handler,a=Math.abs(r.x-this.startX)<10&&new Date-this.startT<200;s.dispatch.call(o,"mouseup",r),o.processGesture((0,l.wrapTouch)(t),"end"),a?s.dispatch.call(o,"click",r):this.endTimer=setTimeout((function(){s.dispatch.call(o,"mousemove",{x:999999999,y:999999999}),s.dispatch.call(o,"mouseup",{x:999999999,y:999999999})}),50)}},mousewheel:function(t){this.chart&&s.dispatch.call(this.chart.getZr().handler,"mousewheel",this.getTouch(t))}}};e.default=u},7658:function(t,e,n){"use strict";var i=n("8bdb"),r=n("85c1"),o=n("bb80"),a=n("8466"),s=n("81a9"),l=n("d0b1"),u=n("5075"),h=n("b720"),c=n("474f"),p=n("1eb8"),d=n("1c06"),f=n("af9e"),g=n("29ba"),y=n("181d"),m=n("dcda");t.exports=function(t,e,n){var v=-1!==t.indexOf("Map"),_=-1!==t.indexOf("Weak"),x=v?"set":"add",w=r[t],b=w&&w.prototype,S=w,M={},T=function(t){var e=o(b[t]);s(b,t,"add"===t?function(t){return e(this,0===t?0:t),this}:"delete"===t?function(t){return!(_&&!d(t))&&e(this,0===t?0:t)}:"get"===t?function(t){return _&&!d(t)?void 0:e(this,0===t?0:t)}:"has"===t?function(t){return!(_&&!d(t))&&e(this,0===t?0:t)}:function(t,n){return e(this,0===t?0:t,n),this})},C=a(t,!c(w)||!(_||b.forEach&&!f((function(){(new w).entries().next()}))));if(C)S=n.getConstructor(e,t,v,x),l.enable();else if(a(t,!0)){var k=new S,D=k[x](_?{}:-0,1)!==k,I=f((function(){k.has(1)})),A=g((function(t){new w(t)})),O=!_&&f((function(){var t=new w,e=5;while(e--)t[x](e,e);return!t.has(-0)}));A||(S=e((function(t,e){h(t,b);var n=m(new w,t,S);return p(e)||u(e,n[x],{that:n,AS_ENTRIES:v}),n})),S.prototype=b,b.constructor=S),(I||O)&&(T("delete"),T("has"),v&&T("get")),(O||D)&&T(x),_&&b.clear&&delete b.clear}return M[t]=S,i({global:!0,constructor:!0,forced:S!==w},M),y(S,t),_||n.setStrong(S,t,v),S}},"7d56":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 首页头部标题图片  120*30 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.wanl-headbar[data-v-54a522ac]{padding-right:%?25?%;position:fixed;left:0;width:100%;z-index:99999}.wanl-headbar .action[data-v-54a522ac]{position:relative;z-index:9}.wanl-headbar .title[data-v-54a522ac]{height:100%;font-family:Microsoft YaHei;display:inline-block;height:%?50?%;margin:auto;position:absolute;left:0;right:0;bottom:0;top:0}.wanl-headbar .logo[data-v-54a522ac]{width:%?50?%;height:%?50?%;position:absolute;left:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);border-radius:%?20?%}.wanl-headbar .logoText[data-v-54a522ac]{font-family:Microsoft YaHei;display:inline-block;width:%?200?%;height:%?50?%;margin:auto;position:absolute;left:0;right:0;bottom:0;top:0}',""]),t.exports=e},"7f48":function(t,e,n){"use strict";var i=n("8bdb"),r=n("af9e"),o=n("8449").f,a=r((function(){return!Object.getOwnPropertyNames(1)}));i({target:"Object",stat:!0,forced:a},{getOwnPropertyNames:o})},"825c":function(t,e,n){"use strict";var i=n("6a50");i("Float64",(function(t){return function(e,n,i){return t(this,e,n,i)}}))},"82bb":function(t,e,n){"use strict";var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.Canvas=void 0,e.dispatch=function(t,e){var n=e.x,i=e.y,r=e.wheelDelta;this.dispatch(t,{zrX:n,zrY:i,zrDelta:r,preventDefault:function(){},stopPropagation:function(){}})},e.setCanvasCreator=function(t,e){var n=e.canvas,i=e.node;if(t&&!t.registerPreprocessor)return console.warn("echarts 版本不对或未传入echarts，vue3请使用esm格式");t.registerPreprocessor((function(t){t&&t.series&&(t.series.length>0?t.series.forEach((function(t){t.progressive=0})):"object"===(0,o.default)(t.series)&&(t.series.progressive=0))})),t.setPlatformAPI&&t.setPlatformAPI({loadImage:n.setChart?function(t,e,n){var r=null;return i&&i.createImage?(r=i.createImage(),r.onload=e.bind(r),r.onerror=n.bind(r),r.src=t,r):(r=new h,r.onload=e.bind(r),r.onerror=n.bind(r),r.src=t,r)}:null,createCanvas:function(){var t="createOffscreenCanvas";return uni.canIUse(t)&&uni[t]?uni[t]({type:"2d"}):n}})},n("aa9c"),n("bf0f"),n("2797"),n("dd2b"),n("6a54"),n("f7a5"),n("e966"),n("5c47"),n("2c10");var r=i(n("b7c7")),o=i(n("fcf3")),a=i(n("80b1")),s=i(n("efe5")),l={},u=function(){function t(){(0,a.default)(this,t),this.__events={}}return(0,s.default)(t,[{key:"on",value:function(t,e){if(t&&e){var n=this.__events[t]||[];n.push(e),this.__events[t]=n}}},{key:"emit",value:function(t,e){var n=this;if(t.constructor===Object&&(e=t,t=e&&e.type),t){var i=this.__events[t];i&&i.length&&i.forEach((function(t){t.call(n,e)}))}}},{key:"off",value:function(t,e){var n=this.__events,i=n[t];if(i&&i.length)if(e)for(var r=0,o=i.length;r<o;r++)i[r]===e&&(i.splice(r,1),r--);else delete n[t]}}]),t}(),h=function(){function t(){(0,a.default)(this,t),this.currentSrc=null,this.naturalHeight=0,this.naturalWidth=0,this.width=0,this.height=0,this.tagName="IMG"}return(0,s.default)(t,[{key:"src",get:function(){return this.currentSrc},set:function(t){var e=this;this.currentSrc=t,uni.getImageInfo({src:t,success:function(t){e.naturalWidth=e.width=t.width,e.naturalHeight=e.height=t.height,e.onload()},fail:function(){e.onerror()}})}}]),t}(),c=function(){function t(e,n,i){(0,a.default)(this,t),this.tagName="canvas",this.com=n,this.canvasId=i,this.ctx=e}return(0,s.default)(t,[{key:"width",get:function(){return this.com.offscreenWidth||0},set:function(t){this.com.offscreenWidth=t}},{key:"height",get:function(){return this.com.offscreenHeight||0},set:function(t){this.com.offscreenHeight=t}},{key:"getContext",value:function(t){return this.ctx}},{key:"getImageData",value:function(){var t=this;return new Promise((function(e,n){t.com.$nextTick((function(){uni.canvasGetImageData({x:0,y:0,width:t.com.offscreenWidth,height:t.com.offscreenHeight,canvasId:t.canvasId,success:function(t){e(t)},fail:function(t){n(t)}},t.com)}))}))}}]),t}(),p=function(){function t(e,n,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};(0,a.default)(this,t),l[n.canvasId]={ctx:e},this.canvasId=n.canvasId,this.chart=null,this.isNew=i,this.tagName="canvas",this.canvasNode=r,this.com=n,i||this._initStyle(e),this._initEvent(),this._ee=new u}return(0,s.default)(t,[{key:"getContext",value:function(t){if("2d"===t)return this.ctx}},{key:"setAttribute",value:function(t,e){"aria-label"===t&&(this.com["ariaLabel"]=e)}},{key:"setChart",value:function(t){this.chart=t}},{key:"createOffscreenCanvas",value:function(t){if(!this.children){this.com.isOffscreenCanvas=!0,this.com.offscreenWidth=t.width||300,this.com.offscreenHeight=t.height||300;var e=this.com,n=this.com.offscreenCanvasId,i=uni.createCanvasContext(n,this.com);this._initStyle(i),this.children=new c(i,e,n)}return this.children}},{key:"appendChild",value:function(t){console.log("child",t)}},{key:"dispatchEvent",value:function(t,e){return"object"==(0,o.default)(t)?this._ee.emit(t.type,t):this._ee.emit(t,e),!0}},{key:"attachEvent",value:function(){}},{key:"detachEvent",value:function(){}},{key:"addEventListener",value:function(t,e){this._ee.on(t,e)}},{key:"removeEventListener",value:function(t,e){this._ee.off(t,e)}},{key:"_initCanvas",value:function(t,e){}},{key:"_initStyle",value:function(t,e){if(["fillStyle","strokeStyle","fontSize","globalAlpha","opacity","textAlign","textBaseline","shadow","lineWidth","lineCap","lineJoin","lineDash","miterLimit"].forEach((function(e){Object.defineProperty(t,e,{set:function(n){if("opacity"!==e){if("fillStyle"!==e&&"strokeStyle"!==e||"none"!==n&&null!==n){if("object"==(0,o.default)(n))return void((n.hasOwnProperty("colorStop")||n.hasOwnProperty("colors"))&&t["set"+e.charAt(0).toUpperCase()+e.slice(1)](n));t["set"+e.charAt(0).toUpperCase()+e.slice(1)](n)}}else t.setGlobalAlpha(n)}})})),this.isNew||e||(t.uniDrawImage=t.drawImage,t.drawImage=function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];n[0]=n[0].src,t.uniDrawImage.apply(t,n)}),t.createRadialGradient||(t.createRadialGradient=function(){return t.createCircularGradient.apply(t,(0,r.default)(Array.prototype.slice.call(arguments).slice(-3)))}),t.strokeText||(t.strokeText=function(){t.fillText.apply(t,arguments)}),!t.measureText){var n=function(t){for(var e=0,n=0;n<t.length;n++)t.charCodeAt(n)>0&&t.charCodeAt(n)<128?e++:e+=2;return e};t.measureText=function(e,i){var r,o=(null===t||void 0===t||null===(r=t.state)||void 0===r?void 0:r.fontSize)||12;i&&(o=parseInt(i.match(/([\d\.]+)px/)[1])),o/=2;var a=o>=16,s=a?1.3:1;return{width:n(e)*o*s}}}}},{key:"_initEvent",value:function(t){var e=this;this.event={};[{wxName:"touchStart",ecName:"mousedown"},{wxName:"touchMove",ecName:"mousemove"},{wxName:"touchEnd",ecName:"mouseup"},{wxName:"touchEnd",ecName:"click"}].forEach((function(t){e.event[t.wxName]=function(n){var i=n.touches[0];e.chart.getZr().handler.dispatch(t.ecName,{zrX:"tap"===t.wxName?i.clientX:i.x,zrY:"tap"===t.wxName?i.clientY:i.y})}}))}},{key:"width",get:function(){return this.canvasNode.width||0},set:function(t){this.canvasNode.width=t}},{key:"height",get:function(){return this.canvasNode.height||0},set:function(t){this.canvasNode.height=t}},{key:"ctx",get:function(){return l[this.canvasId]["ctx"]||null}},{key:"chart",get:function(){return l[this.canvasId]["chart"]||null},set:function(t){l[this.canvasId]["chart"]=t}}]),t}();e.Canvas=p},"84b1":function(t,e,n){"use strict";var i=n("4d84"),r=n.n(i);r.a},"91a4":function(t,e,n){"use strict";var i=n("1197"),r=n.n(i);r.a},a578:function(t,e,n){"use strict";var i=n("8bdb"),r=n("f298");i({target:"String",proto:!0},{repeat:r})},af2c:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default,r=n("3639").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("39d8")),a=i(n("2634")),s=i(n("2fdc"));n("64aa"),n("aa9c"),n("fd3c"),n("bf0f"),n("2797");var l,u=r(n("3f75")),h=i(n("0abb")),c={components:{lEchart:h.default},data:function(){return{headTop:0,type:1,uid:"",dataList:[],color:["#FFC0CB","#91cc75","#7B68EE","#FF6347","#4169E1","#FFD700","#FF69B4","#9400D3","#F0F8FF","#808000","#FFA500","#FA8072","#C0C0C0"]}},onShow:function(){var t=this.$wanlshop.wanlsys();this.headTop=t.top},onLoad:function(t){t.uid&&(this.uid=Number(t.uid),this.initData())},methods:(l={tabClick:function(t){this.type=t,this.initData()},initData:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$api.post({url:"/wanlshop/user/countUserLevelDist",data:{type:t.type,uid:t.uid},success:function(e){for(var n in e){var i=t.objArrtransArr(e[n],"vlNum","lvname"),r=[];"vipLevel"==n?(r.push(i),t.init1(r,"#chart1","会员等级")):"svipLevel"==n&&(r.push(i),t.init2(r,"chartRef2","批发商等级"))}}});case 1:case"end":return e.stop()}}),e)})))()},init1:function(t,e,n){var i=this;return(0,s.default)((0,a.default)().mark((function e(){var r,o,s;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,i.$refs.chartRef1.init(u);case 2:o=e.sent,s={title:{text:n,left:"center",textStyle:{color:"#333",fontWeight:"bold",fontSize:16}},color:["#F0E68C","#87CEEB","#FF6347","#7B68EE","#90EE90"],series:null===(r=t||[])||void 0===r?void 0:r.map((function(t,e){var n=33.3*e;return{type:"pie",radius:[20,60],top:n+"%",height:"100%",left:"center",width:550,itemStyle:{borderColor:"#fff",borderWidth:1},label:{alignTo:"edge",formatter:"{lvname|{b}}\n{lvname|{c} 人}",minMargin:5,edgeDistance:140,lineHeight:15,rich:{time:{fontSize:10,color:"#999"}}},labelLine:{length:5,length2:0,maxSurfaceAngle:80},data:t}}))},o.setOption(s);case 5:case"end":return e.stop()}}),e)})))()},init2:function(t,e,n){var i=this;return(0,s.default)((0,a.default)().mark((function e(){var r,o;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,i.$refs.chartRef2.init(u);case 2:r=e.sent,o={title:{text:n,left:"center",textStyle:{color:"#333",fontWeight:"bold",fontSize:16}},color:i.color,series:(t||[]).map((function(t,e){var n=33.3*e;return{type:"pie",radius:[20,60],top:n+"%",height:"100%",left:"center",width:550,itemStyle:{borderColor:"#fff",borderWidth:1},label:{alignTo:"edge",formatter:"{lvname|{b}}\n{lvname|{c} 人}",minMargin:5,edgeDistance:140,lineHeight:15,rich:{time:{fontSize:10,color:"#999"}}},labelLine:{length:5,length2:0,maxSurfaceAngle:80},data:t}}))},r.setOption(o);case 5:case"end":return e.stop()}}),e)})))()},objArrtransArr:function(t,e,n){var i=[];return t.forEach((function(t){var r={};r.value=t[e],r.name=t[n],i.push(r)})),i},gotoViews:function(t){uni.navigateTo({url:t})}},(0,o.default)(l,"gotoViews",(function(t){uni.navigateBack()})),(0,o.default)(l,"more",(function(){console.log("1234"),this.showMore=!this.showMore})),(0,o.default)(l,"moreClose",(function(){console.log("1234"),this.showMore=!this.showMore})),(0,o.default)(l,"gotoV",(function(t){uni.reLaunch({url:t})})),l)};e.default=c},b3e2:function(t,e,n){"use strict";var i,r=n("c238"),o=n("85c1"),a=n("bb80"),s=n("a74c"),l=n("d0b1"),u=n("7658"),h=n("d871c"),c=n("1c06"),p=n("235c").enforce,d=n("af9e"),f=n("a20b"),g=Object,y=Array.isArray,m=g.isExtensible,v=g.isFrozen,_=g.isSealed,x=g.freeze,w=g.seal,b=!o.ActiveXObject&&"ActiveXObject"in o,S=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},M=u("WeakMap",S,h),T=M.prototype,C=a(T.set);if(f)if(b){i=h.getConstructor(S,"WeakMap",!0),l.enable();var k=a(T["delete"]),D=a(T.has),I=a(T.get);s(T,{delete:function(t){if(c(t)&&!m(t)){var e=p(this);return e.frozen||(e.frozen=new i),k(this,t)||e.frozen["delete"](t)}return k(this,t)},has:function(t){if(c(t)&&!m(t)){var e=p(this);return e.frozen||(e.frozen=new i),D(this,t)||e.frozen.has(t)}return D(this,t)},get:function(t){if(c(t)&&!m(t)){var e=p(this);return e.frozen||(e.frozen=new i),D(this,t)?I(this,t):e.frozen.get(t)}return I(this,t)},set:function(t,e){if(c(t)&&!m(t)){var n=p(this);n.frozen||(n.frozen=new i),D(this,t)?C(this,t,e):n.frozen.set(t,e)}else C(this,t,e);return this}})}else(function(){return r&&d((function(){var t=x([]);return C(new M,t,1),!v(t)}))})()&&s(T,{set:function(t,e){var n;return y(t)&&(v(t)?n=x:_(t)&&(n=w)),C(this,t,e),n&&n(t),this}})},b7c7:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,r.default)(t)||(0,o.default)(t)||(0,a.default)()};var i=s(n("4733")),r=s(n("d14d")),o=s(n("5d6b")),a=s(n("30f7"));function s(t){return t&&t.__esModule?t:{default:t}}},c02e:function(t,e,n){"use strict";var i=n("6a50");i("Int32",(function(t){return function(e,n,i){return t(this,e,n,i)}}))},c1a3:function(t,e,n){"use strict";n("15ab")},c238:function(t,e,n){"use strict";var i=n("af9e");t.exports=!i((function(){return Object.isExtensible(Object.preventExtensions({}))}))},c24b:function(t,e,n){"use strict";var i=n("8bdb"),r=n("c238"),o=n("af9e"),a=n("1c06"),s=n("d0b1").onFreeze,l=Object.freeze,u=o((function(){l(1)}));i({target:"Object",stat:!0,forced:u,sham:!r},{freeze:function(t){return l&&a(t)?l(s(t)):t}})},d0af:function(t,e,n){"use strict";n("b3e2")},d0b1:function(t,e,n){"use strict";var i=n("8bdb"),r=n("bb80"),o=n("11bf"),a=n("1c06"),s=n("338c"),l=n("d6b1").f,u=n("80bb"),h=n("8449"),c=n("1ea2"),p=n("d7b4"),d=n("c238"),f=!1,g=p("meta"),y=0,m=function(t){l(t,g,{value:{objectID:"O"+y++,weakData:{}}})},v=t.exports={enable:function(){v.enable=function(){},f=!0;var t=u.f,e=r([].splice),n={};n[g]=1,t(n).length&&(u.f=function(n){for(var i=t(n),r=0,o=i.length;r<o;r++)if(i[r]===g){e(i,r,1);break}return i},i({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:h.f}))},fastKey:function(t,e){if(!a(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,g)){if(!c(t))return"F";if(!e)return"E";m(t)}return t[g].objectID},getWeakData:function(t,e){if(!s(t,g)){if(!c(t))return!0;if(!e)return!1;m(t)}return t[g].weakData},onFreeze:function(t){return d&&f&&c(t)&&!s(t,g)&&m(t),t}};o[g]=!0},d14d:function(t,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("01a2"),n("e39c"),n("bf0f"),n("844d"),n("18f7"),n("de6c"),n("08eb")},d181:function(t,e,n){"use strict";n.r(e);var i=n("f9ba"),r=n("dfd6");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("91a4");var a=n("828b"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"a69d2974",null,!1,i["a"],void 0);e["default"]=s.exports},d871c:function(t,e,n){"use strict";var i=n("bb80"),r=n("a74c"),o=n("d0b1").getWeakData,a=n("b720"),s=n("e7e3"),l=n("1eb8"),u=n("1c06"),h=n("5075"),c=n("4d16"),p=n("338c"),d=n("235c"),f=d.set,g=d.getterFor,y=c.find,m=c.findIndex,v=i([].splice),_=0,x=function(t){return t.frozen||(t.frozen=new w)},w=function(){this.entries=[]},b=function(t,e){return y(t.entries,(function(t){return t[0]===e}))};w.prototype={get:function(t){var e=b(this,t);if(e)return e[1]},has:function(t){return!!b(this,t)},set:function(t,e){var n=b(this,t);n?n[1]=e:this.entries.push([t,e])},delete:function(t){var e=m(this.entries,(function(e){return e[0]===t}));return~e&&v(this.entries,e,1),!!~e}},t.exports={getConstructor:function(t,e,n,i){var c=t((function(t,r){a(t,d),f(t,{type:e,id:_++,frozen:void 0}),l(r)||h(r,t[i],{that:t,AS_ENTRIES:n})})),d=c.prototype,y=g(e),m=function(t,e,n){var i=y(t),r=o(s(e),!0);return!0===r?x(i).set(e,n):r[i.id]=n,t};return r(d,{delete:function(t){var e=y(this);if(!u(t))return!1;var n=o(t);return!0===n?x(e)["delete"](t):n&&p(n,e.id)&&delete n[e.id]},has:function(t){var e=y(this);if(!u(t))return!1;var n=o(t);return!0===n?x(e).has(t):n&&p(n,e.id)}}),r(d,n?{get:function(t){var e=y(this);if(u(t)){var n=o(t);return!0===n?x(e).get(t):n?n[e.id]:void 0}},set:function(t,e){return m(this,t,e)}}:{add:function(t){return m(this,t,!0)}}),c}}},db08:function(t,e,n){"use strict";var i=n("1043"),r=n.n(i);r.a},dfd6:function(t,e,n){"use strict";n.r(e);var i=n("af2c"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},e7d2:function(t,e,n){"use strict";n.r(e);var i=n("6d5f"),r=n("f2f1");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("db08");var a=n("828b"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"54a522ac",null,!1,i["a"],void 0);e["default"]=s.exports},f190:function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.canIUseCanvas2d=function(){return!1},e.compareVersion=o,e.convertTouchesToArray=function(t){if(Array.isArray(t))return t;if("object"===(0,r.default)(t)&&null!==t)return Object.values(t);return t},e.devicePixelRatio=void 0,e.getRect=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="boundingClientRect",i=e.context,r=e.type,o=void 0===r?n:r;return new Promise((function(e,r){var a=uni.createSelectorQuery().in(i).select(t),s=function(t){t?e(t):r()};o==n?a[o](s).exec():a[o]({node:!0,size:!0,rect:!0},s).exec()}))},e.sleep=function(t){return new Promise((function(e){setTimeout((function(){e(!0)}),t)}))},e.wrapTouch=function(t){for(var e=0;e<t.touches.length;++e){var n=t.touches[e];n.offsetX=n.x,n.offsetY=n.y}return t};var r=i(n("fcf3"));function o(t,e){t=t.split("."),e=e.split(".");var n=Math.max(t.length,e.length);while(t.length<n)t.push("0");while(e.length<n)e.push("0");for(var i=0;i<n;i++){var r=parseInt(t[i],10),o=parseInt(e[i],10);if(r>o)return 1;if(r<o)return-1}return 0}n("aa9c"),n("e966"),n("22b6"),n("bf0f"),n("5c47");uni.getSystemInfoSync();var a=uni.getSystemInfoSync().pixelRatio;e.devicePixelRatio=a},f2f1:function(t,e,n){"use strict";n.r(e);var i=n("43ce"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},f9ba:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={wanlHeadbar:n("e7d2").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"page"},[n("wanl-headbar",{attrs:{mode:"back",title:"团队分布"}}),n("v-uni-view",{staticClass:"bg",style:{backgroundImage:"url("+t.$wanlshop.imgstc("/default/team_bg.png?t=1")+")"}},[n("v-uni-view",{staticClass:" radius-native bg-white w-100 teamListInfo"},[n("v-uni-view",{staticClass:"screen flex align-center "},[n("v-uni-view",{staticClass:"text-df flex align-center flex justify-center align-center",class:{active:1===t.type},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabClick(1)}}},[n("v-uni-text",{staticClass:"margin-right-xs"},[t._v("直推分布")])],1),n("v-uni-view",{staticClass:"text-df flex align-center flex justify-center align-center",class:{active:2===t.type},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.tabClick(2)}}},[n("v-uni-text",{staticClass:"margin-right-xs"},[t._v("团队分布")])],1)],1),n("v-uni-scroll-view",{staticClass:"type padding-lr",attrs:{"scroll-y":"true"}},[n("v-uni-view",{staticClass:"Box"},[n("v-uni-view",{staticStyle:{width:"640rpx",height:"500rpx"}},[n("l-echart",{ref:"chartRef1",on:{finished:function(e){arguments[0]=e=t.$handleEvent(e),t.init1.apply(void 0,arguments)}}})],1),n("v-uni-view",{staticStyle:{width:"640rpx",height:"500rpx"}},[n("l-echart",{ref:"chartRef2",on:{finished:function(e){arguments[0]=e=t.$handleEvent(e),t.init2.apply(void 0,arguments)}}})],1)],1)],1)],1)],1)],1)},o=[]}}]);