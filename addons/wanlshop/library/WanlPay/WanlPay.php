<?php

namespace addons\wanlshop\library\WanlPay;

use app\admin\model\SystemFranchisee;
use app\admin\model\wanlshop\Ninestar;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\Order;
use app\api\model\wanlshop\OrderGoods;
use app\api\service\PriceSrv;
use app\common\library\Auth;
use app\common\library\SassConfigLoader;
use app\common\model\CurrencyFlqLog;
use app\common\model\CurrencyRmbLog;
use app\common\model\OfflineActivityOrder;
use app\common\model\User;
use Symfony\Component\HttpFoundation\Request;
use think\Db;
use think\Exception;
use think\Hook;
use fast\Http;
use fast\Random;
use WanlPay\Supports\Str;
use WanlPay\Yansongda\Gateways\Wechat\Support;
use WanlPay\Yansongda\Pay;
use WanlPay\Yansongda\Log;
use EasyWeChat\Factory;
use addons\wanlshop\library\WeixinSdk\Mp;
use app\common\model\CurrencyCnyLog;
use GuzzleHttp\Client;
use think\Config;

/**
 *
 * WanlPay 多终端支付
 * <AUTHOR> <<EMAIL>>
 * @link http://www.wanlshop.com
 *
 * @careful 未经版权所有权人书面许可，不得自行复制到第三方系统使用、二次开发、分支、复制和商业使用！
 * @creationtime  2020年8月7日23:46:12 - 2020年8月26日05:10:09
 * @lasttime -
 * @version V0.0.1 https://pay.yanda.net.cn/docs/2.x/overview
 **/
class WanlPay
{
    private $type;
    private $method;
    private $code;
    private $jssdkdata;
    private $wanlpay;
    private $order_id;
    protected $rsa_public_key;        //对方公钥，加密用
    protected $rsa_private_key;        //己方私钥，解密用

    public function __construct($type = '', $method = '', $code = '')
    {
        $auth = Auth::instance(); // 方式
        $this->type = $type;  // 类型
        $this->method = $method; // 方式
        $this->user_id = $auth->isLogin() ? $auth->id : 0; // 用户ID
        $this->request = \think\Request::instance();
        $this->code = $code; // 小程序code
        //这里是中福统详的
        // 		$this->rsa_public_key = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFbqPEIXdJZ36TuPaday4QumqddHNIGiw37GsyXiF2mtdq+lOrx0QwLxycPS1fcJUfbLJTCLxpdD0JEtzoD5UmRk42ij4B2EDETyopg/I7Py3y7lvjOwxnI3TwpqVQlzxs1uLhL0C++niW4/3w8QDMqBt5f0vLrioWI6OQafQdFQIDAQAB';
        // 		$this->rsa_private_key = 'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC10i4jylYL0davWEZMCIOTWzopyBELE9KeFzz8OXiqqJ2JkMJ8YFkWQqeKpVVv2IvFA44z7ueAiSopaM/v1m6vNZYiKDbWFDvRxtQuJnUfJZOKZrHRpfUig7O99/A/I6LvOsG0Ox99kS1WUtNAthj+g+n3iuUymZyL7gGdFgeZcclfxg9RbGukbfdYi51J5ojtns4TrIrbofCUi8Y1hnEoiFpQ7RpNn/eEqLYtKvxZZZxrG/8mwLM9B7StvDzV7zet6CTX+5bDwAw8TztgwLckib/OqfFjSMZOOH9MmU05OAtW8xJn4K9TZunL4BWozld64WGUwjXG7PuyIHomJX6XAgMBAAECggEABNajA7DOuWHMJL5koIFZfwJeed4pFfJhGHPl25Br9Z6nEpIXmte1hzIe40GjFGbe0U9W6WriHjmINbHkVq2OJHt4k1AepXPEY8C2xMOhOkL1uikhckLIUzLOBbAjZIq+Oz+FCBbKr7y9SB7MIFy4LY21HDFGCCKuV8rtoBCGAz9IVeO13H0+bWDaYZNVLFpKfG8BnJD68903/G8XeblxCsEF11ap9GEkEHxO97Xc+MgAORcX9wRAelKyx/+eNx8UsmSOsYj4X7JXU9NhvgJ1bpQLj8cge0IQBhWZMBG+Bl3uvdwC1PKLPUCNsP9OZZmGHf0bV0VI0qli/C6xJ0U7gQKBgQD3Ian3yZ0eEZAlTZ6btjODSJ0xWRa7JCD/3eSoqFNmXHA4DzLH9XklbwlZq+gu6VLDRvTgxlgBjrR3tiSN+odzEdQlNDSwdzQO9Vm2KHBlMom/kogI5oGdfIdLhpwZ6ej4Gpfb6MlqbpI6D3ULBZD8fbs5gb/U4m1WZGTHE0V3kwKBgQC8WIZnWrrpX01qWMQWwsAcJMmIPbpJIldGMhdGiF4V7KwCf04YWzhew12uHejeE79THAhugdhT8fgRHyq79aMY8Z5obvt2pDjBqAd9+EjE4Csi5y/2OqSfZy3JQNyZdXry+QQ2WxKLYezqOVX+LKVS1xkLP4qVvpim1bULcjs3bQKBgQDqbHWFhitTNYfNyuZUgK42r6/k+sRUOv5OmFbqIdoS9EbC9q/N/C2x87Uj6azyqwOGF438lkkvGQZZtF7Siz7nSZc6v13nsvuf4N77qzqK1DjUj5QZFUcIc/MHEqaK8la62scIpLZDDqYJC1aUYPBago4SC+FDeJIM7GioasBvnwKBgFuML/Am0rlCoI9zjASALv6/BtJZJqYLsh4llNisygagHk+sb1o5ZKHzFiLe2OSw9otAEf0c0fijoW85Ff18fQVP1C5w23ukgQ3oTZP2TfCcAXuaod5uTLxKW5/6Df/tcZCOZ3TAObhUJeMHcXv1WXp1oO+tt//nP63ALJresFfVAoGBAJ5T1D/+EBhUXnscUdpakaaWrJ9/ZM+EeprGCofEWibTjLzx4RXaBbkBudun5O/AG4pt5w+vuq0SFtecWOCd7oz6VYfrJ1CcQvpuHK8G8GzQfvTDwV+SW8cp5RHMjwwAmcrmxaumJm9hJDcAlqYEF1AloIdZy+HuFNn8mVFN7EYQ';
        //下面的是福荟大健康的
        $this->rsa_public_key = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpbroVD0NHFdKc7I9iZwBB0WD7zIXqCz5JMiJvMNrmHiCCkk7dZ1Zh/6Rk+a5p/vuTTbzvPKAH0OVN3j6g9wrNPRLcOqFcSjEKlRcZTyhKHND956AdsF0GcR7PJPvPhqbjKmLhMX/Kr/dMhtPhbn3vZWyzKqAVDv/t9AvjqvWAcQIDAQAB';
        $this->rsa_private_key = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCm76/dwAxFvTrz9EvxSz+YrUUHG3tGZqfmKlwaWqloBFvvXWqIs+5Z5gRxZ4l01kYMRGr+En299nRe7D4kbPvSlHFLIS/vw0UwWygjYHhqzEsxp5TvKPSQ7gMKEzN5AOQmhPpuZX2QMsvy7KGyiSqb7gGojt7ASYdDsUNW6fKXw2vjKhiNBg3bsYc2i/GXbS9v830Po8+7T4DdwALcWnvIgjdM8IeEtUHszGM1jOq1ZTdTrWMiAY7ya3bmWFw63YP9y/tMsTETjd4pBu0lXbruKtb9iNnkiuUGLjLhqXqGK3k8KWTBSpvRnPc0VYtGWE6N1oe1V2qwtnhSozSCmS2RAgMBAAECggEAAmZDkt5lzsLPjB4g0VERkvI6ocWFWhezph1JwZ83/CTD5YbkNx7O+D/UJIUmdqV10EtTdl9xqz9VoN+obQGcBQTZNAlAFSQrZGyLAScr0+0nz4jnHSDrkwG4zXZFMSIQJXWX2+pdiasRP2ajVFugXylmoLOdBAqKhTCRFTi7oe7JL14ATE548HlPz8O734V9VZOjI3dPw818XBEgR1+tbKGu90xriec+ls0SIwG9ZWv+iwqOziQ+6memsGP3JVYuIDklrzbh4vSmTQ8khWFn5lT4srjAQAFBHwnFfygVvn2Cx0sDuirQ2D0+b8/hVp5YNDCb/ATLHSc4Aa5/P2dkAQKBgQDkPhXjl05+jw3lSUfaMXiB5ujdoXjxyHaeP7H2poymXVQDW2pRZMH52WEIgeml+wxiFsZ7AdvBBkxQxCEs3B3I24S7t1P9eCWlEhph3LGzQlu6vNtvu1CQuNpzDq3vYQFUUqSFvGOfUvf7cmoOe7pAXbGs5YKjN+fZK9tlt/U3IQKBgQC7PPGDJMtU4pVsXnFNaNi5vyFBpPDrO+IUvkM4GY17HBGBZoyBljnOUGLyUuyI5XN5pPWfpZGtrXZb7mckJVGKpo1jW5iwqWSezslUPuiOiVSJlZ3uk1mMNKA9q2tDyYLv+MtAiEmh+6GJ37pB9I4R2PWFTs3y9+bD3c+1RdjYcQKBgDxoFiD7DaI/OD1DzMl5qFwxAdaOgtTy6jntv6r432a0cqTEz/mkwhXfK2g2AeGU9O0M/BDYKBIJGa+SiPk3dIaRWqrX1VnTL5tllCfsYfeAi4h7rVP/7k2vLtmeu/gL428uxC5E8tTa/5jD6j/VDTE12u1YfN+3zxvSGKnuKu8hAoGAaIFsf3ky8C8DnZfc/4rMpGgUhNIuEe27kz5awF4qxJL3BlWZiN6zZv8hKwWztfjzierTmYbNF7YgwNEAyoD10UCC2/kM8VWqrqTWZGo0XRIzmBNlMfsWWRYqdiFOXmiD57LipyMqjfmnwQ+ZoyR/g2CoMP66HqnVKG+OdiwFC1ECgYEArE02g0JjXRHKQrim+ZB5A0GrDbOVjpAJwt6UdV5rJ6VVpT++6Lh5zxV+2pJ8HjgXBOkr80FWq1dgo0nh2sfyIbkxKniYTdXRvriPbVehNm8bV/rn7w7FmRNNpE5i7GylXwMTfq+yJJ2uNa/0LnFfdDjJWOw+/2JnYd9Pvj4LjJo=';
    }

    //加密
    private function publicEncryptRsa($plainData = '')
    {
        if (!is_string($plainData)) {
            return null;
        }
        $encrypted = '';
        $partLen = $this->getPublicKenLen() / 8 - 11;
        $plainData = str_split($plainData, $partLen);
        $publicPEMKey = $this->getPublicKey();
        foreach ($plainData as $chunk) {
            $partialEncrypted = '';
            $encryptionOk = openssl_public_encrypt($chunk, $partialEncrypted, $publicPEMKey, OPENSSL_PKCS1_PADDING);
            if ($encryptionOk === false) {
                return false;
            }
            $encrypted .= $partialEncrypted;
        }
        return base64_encode($encrypted);
    }

    private function getPublicKenLen()
    {
        $pub_id = openssl_get_publickey($this->getPublicKey());
        return openssl_pkey_get_details($pub_id)['bits'];
    }

    private function getPublicKey()
    {
        $public_key = $this->rsa_public_key;
        //这里的public_key，是一行的情况，才是这样处理；
        $pubic_pem = chunk_split($public_key, 64, "\n");
        $pubic_pem = "-----BEGIN PUBLIC KEY-----\n" . $pubic_pem . "-----END PUBLIC KEY-----\n";
        return $pubic_pem;
    }

    private function privateDecryptRsa($data = '')
    {
        if (!is_string($data)) {
            return null;
        }
        $decrypted = '';

        $partLen = $this->getPrivateKenLen() / 8;
        $data = str_split(base64_decode($data), $partLen);

        $privatePEMKey = $this->getPrivateKey();

        foreach ($data as $chunk) {
            $partial = '';
            $decryptionOK = openssl_private_decrypt($chunk, $partial, $privatePEMKey, OPENSSL_PKCS1_PADDING);
            if ($decryptionOK === false) {
                return false;
            }
            $decrypted .= $partial;
        }
        return $decrypted;
    }

    private function getPrivateKenLen()
    {
        $pub_id = openssl_get_privatekey($this->getPrivateKey());
        return openssl_pkey_get_details($pub_id)['bits'];
    }

    private function getPrivateKey()
    {
        //这里的private_key，是一行的情况，才是这样处理；
        $private_key = $this->rsa_private_key;
        $private_pem = chunk_split($private_key, 64, "\n");
        $private_pem = "-----BEGIN PRIVATE KEY-----\n" . $private_pem . "-----END PRIVATE KEY-----\n";
        return $private_pem;
    }


    /**
     * 支付
     */
    public function pay($order_id, $order_type)
    {
        $this->order_id = $order_id;
        if ($this->user_id == 0) {
            return ['code' => 10001, 'msg' => '用户ID不存在'];
        }
        // 获取支付信息
        $pay = model('app\api\model\wanlshop\Pay')
            ->where('order_id', 'in', $order_id)
            ->where(['user_id' => $this->user_id, 'type' => $order_type == 'groups' ? 'groups' : 'goods'])
            ->select();
        // 1.0.8 升级
        $price = 0; // 付款金额
        $points = 0; // 付款金额
        $filall = 0; // 付款金额
        $filin = 0; // 付款金额
        $order_no = []; // 订单号
        $pay_id = []; // 交易号
        foreach ($pay as $row) {
            $price = bcadd($price, $row['price'], 2); // 总价格
            $points = bcadd($points, $row['points'], 2); // 总价格
            $filall = bcadd($filall, $row['fil'], 2); // 总价格
            $filin = bcadd($filin, $row['order_fil'], 2); // 总价格
            $order_no[] = $row['order_no']; // 订单集
            $pay_id[] = $row['id']; // 交易集
        }
        $price = $price + $points;
        $balancermb = 'balancermb';
//        //提货券付款，判断是否首次
//        if($this->type == $balancermb){
//            //付款数量>退款数量 即非首次购买
//            $refund_rmb = CurrencyRmbLog::where(['user_id'=>$this->user_id,'type'=>'refund','status'=>'1'])->count();
//            $pay_rmb = CurrencyRmbLog::where(['user_id'=>$this->user_id,'type'=>'pay','status'=>'1'])->count();
//            if($pay_rmb > $refund_rmb){
//                $balancermb = ''; //非首次付款，需要支付20邮费
//            }
//        }
        $thirdPay = in_array($this->type, ['balance', 'balancebd',
            // 'balancecny',
            'balancens', 'balancexnb',
            $balancermb, //变微信支付20快递费+提货券支付
            'balanceusdt', 'points', 'mutibl', 'filusdt', 'mutibdp', 'mutiblp']);
        // 第三方支付订单 1.0.8
        if (!$thirdPay) {
//             != 'balance' && $this->type != 'balancebd' && $this->type != 'balancecny' && $this->type != 'balancexnb' && $this->type != 'balancermb' && $this->type != 'balanceusdt' && $this->type != 'points' && $this->type != 'mutibl'
//            && $this->type != 'filusdt' && $this->type != 'mutibdp' && $this->type != 'mutiblp'){
            $payOutTrade = model('app\api\model\wanlshop\PayOutTrade');
            $payOutTrade->out_trade_no = date('YmdHis') . rand(10000000, 99999999) . substr(implode(NULL, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
            $payOutTrade->pay_id = $pay_id;
            $payOutTrade->order_id = $order_id;
            $payOutTrade->price = $price;
            $payOutTrade->save();
        }
        // 1.0.8升级 拼团订单
        if ($order_type == 'groups') {
            $title = '拼团订单-' . implode("_", $order_no);
            $model_order = model('app\api\model\wanlshop\groups\Order');
            $model_order_goods = model('app\api\model\wanlshop\groups\OrderGoods');
            $model_goods = model('app\api\model\wanlshop\groups\Goods');
        } else {
            $title = '商城订单-' . implode("_", $order_no);
            $model_order = model('app\api\model\wanlshop\Order');
            $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
            $model_goods = model('app\api\model\wanlshop\Goods');
        }
        $user = model('app\common\model\User')->get($this->user_id);
        foreach ($pay as $row) {
            if ($order_type != 'groups') {
                foreach ($model_order_goods->where('order_id', $row['order_id'])->select() as $goods) {
                    $saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
                    if ($goods['shop_id'] == $saas_info[$user['saas_id']]['shop_id_vip']) {
//                        if ($this->type != 'balancebd'
//                            && $this->type != 'balancecny'
////                                    && $this->type != 'balance'
//                        ) {
//                            return ['code' => 500, 'msg' => '有产品无法使用该支付方式'];
//                        }
                        $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                        if ($goodsData['category_id'] == 108 && $user['is_certified'] == 0) {
                            //return ['code' => 500 ,'msg' => '必须实名认证才能升级为店长'];
                        }
                    }
                }
            }
        }
        // 支付方式
        if ($thirdPay) {
            $payment = ['name' => '余额'];
            if ($this->type == 'balancebd') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_bd';
                $payment['name'] = '现金';
            } else if ($this->type == 'balancecny') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_cny';
                $payment['name'] = '达人券';
            } else if ($this->type == 'balancermb') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_rmb';
                $payment['name'] = '提货券';
            } else if ($this->type == 'balancexnb') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_xnb';
                $payment['name'] = '积分';
            } else if ($this->type == 'balanceflq') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_flq';
                $payment['name'] = '福利券';
            } else if ($this->type == 'balanceusdt') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_usdt';
                $payment['name'] = 'USDt';
            } else if ($this->type == 'balance') {
                $payment['fuc'] = 'money';
                $payment['type'] = 'money';
                $payment['name'] = '金币';
            } else if ($this->type == 'points') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_points';
                $payment['name'] = '积分';
            } else if ($this->type == 'filusdt') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_usdt';
                $payment['name'] = 'FILUSDT';
                $payment['type2'] = 'currency_fil';
            } else if ($this->type == 'mutibdp') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_bd';
                $payment['name'] = '现金积分';
                $payment['fuc2'] = 'currency';
                $payment['type2'] = 'currency_points';
            } else if ($this->type == 'mutiblp') {
                $payment['fuc'] = 'money';
                $payment['type'] = 'money';
                $payment['name'] = '金币积分';
                $payment['fuc2'] = 'currency';
                $payment['type2'] = 'currency_points';
            } else if ($this->type == 'balancens') {
                $payment['fuc'] = 'currency';
                $payment['type'] = 'currency_ns';
                $payment['name'] = '绿色通用积分';
            }
            // 支付列表
            $pay_list = [];
            // 订单列表
            $order_list = [];
            // 拼团列表
            $groups_list = [];
            // 拼团列表
            $groups_team_list = [];
            // 判断金额
            if ($this->type == 'balancexnb') {
                if (!$user) {
                    return ['code' => 500, 'msg' => '登陆异常!'];
                }
                if (!$user || $user[$payment['type']] * PriceSrv::getSdrcny() < $price) {
                    return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
                }
            } else if ($this->type == 'balanceusdt') {
                if (!$user) {
                    return ['code' => 500, 'msg' => '登陆异常!'];
                }
                if (!$user || $user[$payment['type']] * PriceSrv::getUsdtcny() < $price) {
                    return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
                }
            } else if ($this->type == 'mutibdp' || $this->type == 'mutiblp') {
                if (!$user) {
                    return ['code' => 500, 'msg' => '登陆异常!'];
                }
                if ($this->type == 'mutiblp') {
                    $priceft = $price - $points;
                    $ftrate = 0.6;
                    if ($user['vip_level'] >= 3 && $user['invite_nums'] >= 5) $ftrate = 0.5;
                    if ($user['vip_level'] == 4 && $user['invite_nums'] >= 10) $ftrate = 0.3;
                    $priceft = $priceft / (1 - $ftrate) * $ftrate;
                } else {
                    $priceft = 0;
                }
                if ($user[$payment['type2']] < $points) {
                    $points = $user[$payment['type2']];
                    if ($points < 0) $points = 0;
                }
                if ($user[$payment['type']] < ($price - $points + $priceft)) {
                    return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
                }
                $price = $price - $points;
            } else {
                if (!$user || $user[$payment['type']] < $price) {
                    return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
                }
                if (array_key_exists('type2', $payment)) {
                    if ($user[$payment['type2']] < $filall) {
                        return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
                    }
                }
            }
            $result = false;
            $balance_no = date('YmdHis') . rand(10000000, 99999999);
            Db::startTrans();
            try {
                foreach ($pay as $row) {
                    $isAlone = false; // 拼团订单一般是单订单，暂可以这样操作
                    $groups_state = 'start'; // 拼团状态
                    foreach ($model_order_goods->where('order_id', $row['order_id'])->select() as $goods) {
                        if ($order_type == 'groups' && !empty($goods['group_type'])) {
                            if ($goods['group_type'] == 'alone') {
                                $isAlone = true;
                            } else {
                                // 查询团ID
                                $groups = model('app\api\model\wanlshop\groups\Groups')
                                    ->where(['group_no' => $goods['group_no']])
                                    ->find();
                                // 判断是否超团
                                $groups_team = model('app\api\model\wanlshop\groups\Team')
                                    ->where(['group_no' => $goods['group_no']])
                                    ->select();
                                // 已拼团总数量
                                $groups_team_count = count($groups_team);
                                if ($groups_team_count >= $groups['people_num'] || $groups['join_num'] >= $groups['people_num']) {
                                    $this->error(__('参与拼单失败，拼团已完成'));
                                }
                                // 判断是否具备成团条件
                                if (($groups['people_num'] - $groups_team_count) <= 1 || ($groups['people_num'] - $groups['join_num']) <= 1) {
                                    $groups_state = 'success';
                                    //调整其他拼团订单
                                    // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
                                    foreach ($groups_team as $team) {
                                        $order_list[] = ['id' => $team['order_id'], 'state' => 3, 'groupstime' => time()];
                                    }
                                }
                                // 拼团状态: ready=准备中,start=拼团中,success=已成团,fail=拼团失败,auto=自动成团
                                $groups_list[] = [
                                    'id' => $groups['id'],
                                    'join_num' => $groups['join_num'] + 1,
                                    'state' => $groups_state,
                                    'grouptime' => $groups_state === 'success' ? time() : NULL
                                ];
                                $groups_team_list[] = [
                                    'user_id' => $user['id'],
                                    'shop_id' => $goods['shop_id'],
                                    'group_no' => $goods['group_no'],
                                    'order_id' => $goods['order_id'],
                                    'order_goods_id' => $goods['id']
                                ];
                            }
                        } else {
                            $saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
                            if ($goods['shop_id'] == $saas_info[$user['saas_id']]['shop_id_vip']) {
                                if ($this->type != 'balancebd'
                                    && $this->type != 'balancecny'
                                    && $this->type != 'balancens'
                                ) {
                                    return ['code' => 500, 'msg' => '有产品无法使用' . $payment['name'] . '支付'];
                                }
                                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                                if ($goodsData['category_id'] == 108 && $user['is_certified'] == 0) {
                                    //return ['code' => 500 ,'msg' => '必须实名认证才能升级为店长'];
                                }
                            }
//                            if($goods['shop_id'] != 2 && $this->type == 'balancebd')
//                                return ['code' => 500 ,'msg' => '有产品无法使用'.$payment['name'].'支付'];
//                            if($goods['shop_id'] < 4 && $this->type == 'points')
//                                return ['code' => 500 ,'msg' => '有产品无法使用'.$payment['name'].'支付'];
//                            if($goods['number']>1) return ['code' => 500 ,'msg' => '每次只允许购买1单'];
//                            if($user['vip_status'] == 1 && $user['invite_nums'] < 3) return ['code' => 500 ,'msg' => '您当前只允许购买1次'];
//                            if($user['invite_nums'] >=3 && ($user['self_amt']+600)>6600) return ['code' => 500 ,'msg' => '超过购买限额'];
//                            if(($user['self_amt']+$goods['number']*612)>7956) return ['code' => 500 ,'msg' => '超过购买限额'];
                        }
                        // 新增付款人数、新增销量
                        $model_goods->where('id', $goods['goods_id'])->inc('payment')->inc('sales', $goods['number'])->update();
                    }
                    // 订单列表
                    if ($groups_state === 'success') {
                        $order_list[] = ['id' => $row['order_id'], 'state' => 3, 'paymenttime' => time(), 'groupstime' => time()];
                    } else {
                        $myOrder = $model_order->field('id,self_pickup,flq')->find($row['order_id']);
                        //自提订单直接去待收货
                        if ($myOrder && $myOrder->self_pickup) {
                            $order_list[] = ['id' => $row['order_id'], 'state' => 2, 'paymenttime' => time()];
                        } else {
                            $order_list[] = ['id' => $row['order_id'], 'state' => $isAlone ? 3 : 2, 'paymenttime' => time()];
                        }
                        if ($myOrder->flq == 1 && $this->type != 'wechat') {
                            return ['code' => 500, 'msg' => '福利券支付订单，仅支持微信支付'];
                        }
                        $goods = OrderGoods::field('id,goods_id')->where('order_id', $row['order_id'])->find();
                        if ($goods) {
                            $sdata = [
                                'good_id' => $goods->goods_id,
                                // 'shop_id' => $row['shop_id'],
                                'user_id' => $row['user_id'],
                                'state' => 0
                            ];
                            $share = (new GoodsShare)->where($sdata)->order('id ASC')->find();
                            if ($share) {
                                $share->state = 1;
                                $share->order_id = $row['order_id'];
                                $share->save();
                            }
                        }
                    }
                    // 支付列表
                    $pay_list[] = [
                        'id' => $row['id'],
                        'trade_no' => $balance_no, // 第三方交易号
                        'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                        'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                        'total_amount' => $price, // 总金额
                        'actual_payment' => $row['price'], // 实际支付
                        'notice' => json_encode([
                            'type' => $this->type,
                            'user_id' => $user['id'],
                            'trade_no' => $balance_no,
                            'out_trade_no' => $row['pay_no'],
                            'amount' => $row['price'],
                            'total_amount' => $price,
                            'order_id' => $row['order_id'],
                            'trade_type' => 'BALANCE',
                            'version' => '1.0.0'
                        ])
                    ];
                }
                // 更新支付列表
                $result = model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                // 更新订单列表
                $result = $model_order->saveAll($order_list);
                // 修改用户金额
                //Yooye 增加付款汇率 Start
                $prate = 1;
//                if($this->type == 'balancexnb') $prate = PriceSrv::getSdrcny();
//                if($this->type == 'balanceusdt') $prate = PriceSrv::getUsdtcny();
                //Yooye 增加付款汇率 End
                if (count($order_no) == 1) {
                    $result = self::{$payment['fuc']}(-$price / $prate, $user['id'], $payment['name'] . '支付' . ($order_type == 'groups' ? '拼团' : '商品') . '订单', $order_type == 'groups' ? 'groups' : 'pay', implode(",", $order_no), $payment['type']);
                    if (array_key_exists('type2', $payment)) {
                        if ($points > 0) $result = self::{$payment['fuc2']}(-$points, $user['id'], '余额支付' . ($order_type == 'groups' ? '拼团' : '积分支付'), $order_type == 'groups' ? 'groups' : 'pay', implode(",", $order_no), $payment['type2']);
//                        if($filall >0)$result = self::{$payment['fuc']}(-$filall, $user['id'], '余额支付'.$order_type == 'groups' ? '拼团':'积分支付', $order_type == 'groups' ? 'groups':'pay', implode(",",$order_no), $payment['type2']);
                        if ($this->type == 'filusdt')
                            $result = self::{$payment['fuc']}($filin, $user['id'], '余额支付' . ($order_type == 'groups' ? '拼团' : '矿机质押'), $order_type == 'groups' ? 'groups' : 'pay', implode(",", $order_no), 'currency_lmt');
                    }
                } else {
                    $result = self::{$payment['fuc']}(-$price / $prate, $user['id'], '余额合并支付' . ($order_type == 'groups' ? '拼团' : '商品') . '订单', $order_type == 'groups' ? 'groups' : 'pay', implode(",", $order_no), $payment['type']);
                    if (array_key_exists('type2', $payment)) {
                        if ($points > 0) $result = self::{$payment['fuc2']}(-$points, $user['id'], '余额合并支付' . ($order_type == 'groups' ? '拼团' : '积分合并支付'), $order_type == 'groups' ? 'groups' : 'pay', implode(",", $order_no), $payment['type2']);
                    }
                }
//                if($this->type == 'mutiblp' && $priceft >0){
//                    self::{$payment['fuc']}(-$priceft, $user['id'], '余额购物复投', 'pay', implode(",", $order_no), $payment['type']);
//                    $arr = array();
//                    $arr['action'] = 'investment';
//                    $arr['user_id'] = $user['id'];
//                    $arr['amount'] = $priceft;
//                    $arr['order_no'] = $order_no;
//                    Hook::listen("com_auto_settlement", $arr);
//                }
                if ($order_type == 'groups') {
                    model('app\api\model\wanlshop\groups\Groups')->isUpdate()->saveAll($groups_list);
                    model('app\api\model\wanlshop\groups\Team')->saveAll($groups_team_list);
                }
                // ----- 维品尚-确认下单调用
                $params = ['action' => 'payCallBack', 'order_id' => $row['order_id']];
                Hook::listen('weipinshang', $params);

                // --- 盲盒商品赠送抵扣券
                $this->addCouponNum($row['order_id']);
                $this->changeOrderOwnerToFriend($row['order_id']);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                return ['code' => 10002, 'msg' => $e->getMessage()];
            }
            // 返回结果
            if ($result !== false) {
                if ($order_type != 'groups') {
                    $arr = array();
                    $arr['action'] = 'orderPay';
                    $arr['order_id'] = $row['order_id'];
                    Hook::listen("com_auto_settlement", $arr);
                }
                return ['code' => 200, 'msg' => '成功-0', 'data' => []];
            } else {
                return ['code' => 10003, 'msg' => '支付异常'];
            }
            // 支付宝支付、更新数据均在回调中执行
        } else if ($this->type == 'alipay') {
            $data = [
                'out_trade_no' => $payOutTrade->out_trade_no,
                'total_amount' => $price,
                'subject' => $title
            ];
            try {
                $payConfig = $this->getConfig($this->type);
                \app\api\model\wanlshop\Pay::update(['merchant_id' => $payConfig['app_id']], ['freeze' => '1', 'id' => ['in', $pay_id]]); // 微信更新商户号
                $alipay = Pay::alipay($payConfig)->{$this->method}($data);
                if ($this->method == 'app' || $this->method == 'wap') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $alipay->getContent()];
                } else {
                    return ['code' => 200, 'msg' => '成功', 'data' => $alipay];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];

            }
            // 微信支付
        } else if ($this->type == 'FT-alipay') {
            $hookParams = ['action' => 'payment', 'order_id' => $order_id[0], 'type' => 'futie', 'platform' => $this->method];
            $mulConf = Hook::listen('multipayment', $hookParams)[0];
            if (!$mulConf) {
                return ['code' => 10006, 'msg' => $this->type . '：暂无有效支付商户-无法支付'];
            }
            try {
                $data['action'] = 'payment';
                $data['data'] = [
                    'type' => $this->type,
                    'mch_id' => $mulConf['merchant_id'],
                    'out_trade_no' => $payOutTrade->out_trade_no,
                    'total_amount' => round($price, 2),
                    'product_name' => $title
                ];
                $rsp = Hook::listen('ft', $data)[0];
                if ($rsp['code']) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $rsp['data']];
                }
                throw new \Exception('FT支付异常' . json_encode($rsp, JSON_UNESCAPED_UNICODE));
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 微信支付
        } else if ($this->type == 'wechat' || $this->type == 'balancermb' || $this->type == 'balanceflq' || $this->type == 'balancecny' || $this->type == 'balancedkq') {
            if ($this->type == 'wechat') {
                // 晚上不可支付
                $todayMidnight = strtotime("today midnight");
                $timestamp = strtotime('today 6:00');
                $nowTime = time();
                \think\Log::info("商铺已打洋！请明天再来。1=$todayMidnight 2=$nowTime 3=$timestamp price=$price");
                if ($todayMidnight < $nowTime && $nowTime < $timestamp && $price >= 999) {
                    \think\Log::info("商铺已打洋！请明天再来。price=$price");
                    return ['code' => 500, 'msg' => '商铺已打洋！请明天再来。'];
                }
            }
            $data = [
                'out_trade_no' => $payOutTrade->out_trade_no, // 订单号
                'body' => $title, // 标题
                'total_fee' => $price * 100 //付款金额 单位分
            ];
            if ($this->type == 'balancermb') {

                $freight_price = 20; // 快递费 20
                $data['total_fee'] = $freight_price * 100; //付款金额 单位分 快递费

                $ids = implode(",", $order_no);
                $hasRow = CurrencyRmbLog::where(['service_ids' => $ids, 'type' => 'pay'])->find();
                if (!$hasRow) {

                    // 修改支付信息
                    // order_price  订单金额
                    // price        支付金额
                    // freight_price 快递金额
                    // actual_payment 实际支付
                    // total_amount 总金额
                    \app\api\model\wanlshop\Pay::update(
                        ['order_price' => $price, 'freight_price' => $freight_price, 'price' => $freight_price, 'discount_price' => $price, 'total_amount' => $price + $freight_price],
                        ['id' => $pay[0]['id']]
                    );

                    // 冻结提货券
                    $payment = array();
                    $payment['fuc'] = 'currency';
                    $payment['type'] = 'currency_rmb';
                    $payment['name'] = '提货券';
                    self::currency(-$price, $user['id'], '提货券支付商品订单', 'pay', implode(",", $order_no), $payment['type']);
                }
                // 重置支付方式
                $this->type = 'wechat';
                $this->method = 'miniapp';
            } else if ($this->type == 'balanceflq') {

                // 只有shopgoods可以使用
                $ids = implode(",", $order_no);
                $hasRow = CurrencyFlqLog::where(['service_ids' => $ids, 'type' => 'pay'])->find();
                if (!$hasRow) {
                    $max_flq = 0;
                    \think\Log::info("爱心商品 start");
                    // 爱心商品 start
                    $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
                    $order_goods = $model_order_goods->where('order_id', 'in', $order_id)->find();
                    $goods_id = $order_goods['goods_id'];
                    $love = model('app\admin\model\wanlshop\Ninestar')->where(['type' => 'love', 'range' => $goods_id])->find();
                    if ($love) {
                        $max_flq = $love['integral'];
                    }
                    \think\Log::info("爱心商品 max_flq=$max_flq, goods_id=$goods_id");
                    // 爱心商品 end

                    //订单支付金额-福利券 = 微信支付金额
                    //最多支付福利券 80%--——799  50%---499
                    if ($max_flq <= 0) {
                        if ($price < 999) {
                            return ['code' => 10006, 'msg' => '非云店产品，无法使用福利券支付'];
                        }
//                        $max_flq = floor($price / 999) * 499;
                        $max_flq = $price * 0.5;
                        \think\Log::info("爱心商品 max_flq=$max_flq, goods_id=$goods_id");
                    }

                    //使用福利券
                    $use_flq = $user['currency_flq'];
                    if ($user['currency_flq'] > $max_flq) {
                        $use_flq = $max_flq;
                    }
                    // 冻结提货券
                    $payment = array();
                    $payment['fuc'] = 'currency';
                    $payment['type'] = 'currency_flq';
                    $payment['name'] = '福利券';
                    self::currency(-$use_flq, $user['id'], '福利券支付商品订单', 'pay', implode(",", $order_no), $payment['type']);

                    //付款金额【微信】
                    $pay_price = round($price - $use_flq, 2);

                    // ---- add pointers start 扣绿色积分支付
                    if (true) {
                        $ns = $user['currency_ns'];
                        \think\Log::info("FLQ-Pay pay_price=$pay_price, ns=$ns");
                        if ($ns >= $pay_price) {
                            self::currency(-$pay_price, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $balance_no = date('YmdHis') . rand(10000000, 99999999);
                            $pay_list[] = [
                                'id' => $row['id'],
                                'trade_no' => $balance_no, // 第三方交易号
                                'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                                'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                                'total_amount' => $price, // 总金额
                                'actual_payment' => $row['price'], // 实际支付
                                'notice' => json_encode([
                                    'type' => $this->type,
                                    'user_id' => $user['id'],
                                    'trade_no' => $balance_no,
                                    'out_trade_no' => $row['pay_no'],
                                    'amount' => $row['price'],
                                    'total_amount' => $price,
                                    'order_id' => $row['order_id'],
                                    'trade_type' => 'BALANCE',
                                    'version' => '1.0.0'
                                ])
                            ];
                            // 更新支付列表
                            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                            Order::update(['state' => '2', 'paymenttime' => time()], ['id' => $row['order_id']]);
                            $arr = array();
                            $arr['action'] = 'orderPay';
                            $arr['order_id'] = $row['order_id'];
                            $use_flq && model('app\api\model\wanlshop\Order')->update(['flq' => 1], ['id' => ['in', $order_id]]);
                            Hook::listen("com_auto_settlement", $arr);
                            return ['code' => 200, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
                        } else {
                            self::currency(-$ns, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $pay_price = $pay_price - $ns;
                            $use_flq += $ns;
                        }
                        \think\Log::info("FLQ-Pay weixin pay_price=$pay_price, ns=$ns");
                    }
                    // ---- add pointers end

                    $pay_discounts_price = 0;
                    //200元支付，修改为 80%--——200  50%---500
                    if ($pay_price % 500 == 0) {
                        //随机减5块以内
                        $pay_discounts_price = round(rand(1, 500) / 100, 2);
                        $pay_price -= $pay_discounts_price;
                    }
                    // 修改支付信息
                    // order_price  订单金额
                    // price        支付金额
                    // freight_price 快递金额
                    // actual_payment 实际支付
                    // total_amount 总金额
                    \app\api\model\wanlshop\Pay::update(
                        ['price' => $pay_price, 'total_amount' => $price, 'discount_price' => $use_flq, 'pay_discounts_price' => $pay_discounts_price],
                        ['id' => $pay[0]['id']]
                    );
                }

                //变更订单类型
                $use_flq && model('app\api\model\wanlshop\Order')->update(['flq' => 1], ['id' => ['in', $order_id]]);
                //福利券组合支付，先扣福利券，后调用真实对应支付方式支付
                return ['code' => 200, 'msg' => '成功-0', 'data' => ['type' => 'flq']];
            } else if ($this->type == 'balancecny') {

                // 只有shopgoods可以使用
                $ids = implode(",", $order_no);
                $hasRow = CurrencyCnyLog::where(['service_ids' => $ids, 'type' => 'pay'])->find();
                if (!$hasRow) {
                    $cng_rate = Config::get("cny_rate") ?? 0.1;
                    $max_cny = round($price * $cng_rate, 2);

                    //使用福利券
                    $use_cny = $user['currency_cny'];
                    if ($user['currency_cny'] > $max_cny) {
                        $use_cny = $max_cny;
                    }
                    // 冻结提货券
                    $payment = array();
                    $payment['fuc'] = 'currency';
                    $payment['type'] = 'currency_cny';
                    $payment['name'] = '达人券';
                    self::currency(-$use_cny, $user['id'], '达人券支付商品订单', 'pay', implode(",", $order_no), $payment['type']);

                    //付款金额【微信】
                    $pay_price = round($price - $use_cny, 2);

                    // ---- add pointers start 扣绿色积分支付
                    if (true) {
                        $ns = $user['currency_ns'];
                        \think\Log::info("CNY-Pay pay_price=$pay_price, ns=$ns");
                        if ($ns >= $pay_price) {
                            self::currency(-$pay_price, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $balance_no = date('YmdHis') . rand(10000000, 99999999);
                            $pay_list[] = [
                                'id' => $row['id'],
                                'trade_no' => $balance_no, // 第三方交易号
                                'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                                'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                                'total_amount' => $price, // 总金额
                                'actual_payment' => $row['price'], // 实际支付
                                'notice' => json_encode([
                                    'type' => $this->type,
                                    'user_id' => $user['id'],
                                    'trade_no' => $balance_no,
                                    'out_trade_no' => $row['pay_no'],
                                    'amount' => $row['price'],
                                    'total_amount' => $price,
                                    'order_id' => $row['order_id'],
                                    'trade_type' => 'BALANCE',
                                    'version' => '1.0.0'
                                ])
                            ];
                            // 更新支付列表
                            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                            Order::update(['state' => '2', 'paymenttime' => time()], ['id' => $row['order_id']]);
                            $arr = array();
                            $arr['action'] = 'orderPay';
                            $arr['order_id'] = $row['order_id'];
                            model('app\api\model\wanlshop\Order')->update(['flq' => 3, 'cny_rate' => $cng_rate], ['id' => ['in', $order_id]]);
                            Hook::listen("com_auto_settlement", $arr);
                            return ['code' => 200, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
                        } else {
                            self::currency(-$ns, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $pay_price = $pay_price - $ns;
                            $use_cny += $ns;
                        }
                        \think\Log::info("CNY-Pay weixin pay_price=$pay_price, ns=$ns");
                    }
                    // ---- add pointers end

                    $pay_discounts_price = 0;
                    //200元支付，修改为 80%--——200  50%---500
                    // if($pay_price % 500 == 0){
                    //     //随机减5块以内
                    //     $pay_discounts_price = round(rand(1, 500)/100,2);
                    //     $pay_price -= $pay_discounts_price;
                    // }
                    // 修改支付信息
                    // order_price  订单金额
                    // price        支付金额
                    // freight_price 快递金额
                    // actual_payment 实际支付
                    // total_amount 总金额
                    \app\api\model\wanlshop\Pay::update(
                        ['price' => $pay_price, 'total_amount' => $price, 'discount_price' => $use_cny, 'pay_discounts_price' => $pay_discounts_price],
                        ['id' => $pay[0]['id']]
                    );
                }

                //变更订单类型
                model('app\api\model\wanlshop\Order')->update(['flq' => 3], ['id' => ['in', $order_id]]);
                //达人券组合支付，先扣达人券，后调用真实对应支付方式支付
                return ['code' => 200, 'msg' => '成功-0', 'data' => ['type' => 'cny']];
            } else if ($this->type == 'balancedkq') {
                ///商品抵扣券
                $ids = implode(",", $order_no);
                $hasRow = CurrencyCnyLog::where(['service_ids' => $ids, 'type' => 'pay'])->find();
                if (!$hasRow) {

                    //商品抵扣券
                    $currency_dkq = $user['currency_dkq'];
                    $pay_dkq_price = 0;
                    if ($currency_dkq > 0) {
                        if ($currency_dkq >= $price) {

                            /// 订单直接支付成功
                            self::currency(-$price, $user['id'], '商品抵扣券支付商品订单', 'pay', implode(",", $order_no), 'currency_dkq');
                            $balance_no = date('YmdHis') . rand(10000000, 99999999);
                            $pay_list[] = [
                                'id' => $row['id'],
                                'trade_no' => $balance_no, // 第三方交易号
                                'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                                'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                                'total_amount' => $price, // 总金额
                                'actual_payment' => $row['price'], // 实际支付
                                'notice' => json_encode([
                                    'type' => $this->type,
                                    'user_id' => $user['id'],
                                    'trade_no' => $balance_no,
                                    'out_trade_no' => $row['pay_no'],
                                    'amount' => $row['price'],
                                    'total_amount' => $price,
                                    'order_id' => $row['order_id'],
                                    'trade_type' => 'BALANCE',
                                    'version' => '1.0.0'
                                ])
                            ];
                            // 更新支付列表
                            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                            Order::update(['state' => '2', 'paymenttime' => time()], ['id' => $row['order_id']]);
                            $arr = array();
                            $arr['action'] = 'orderPay';
                            $arr['order_id'] = $row['order_id'];
                            model('app\api\model\wanlshop\Order')->update(['flq' => 4], ['id' => ['in', $order_id]]);
                            Hook::listen("com_auto_settlement", $arr);

                            // ----- 维品尚-确认下单调用
                            $params = ['action' => 'payCallBack', 'order_id' => $row['order_id']];
                            Hook::listen('weipinshang', $params);

                            return ['code' => 200, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
                        } else {
                            $pay_dkq_price = $currency_dkq;
                            // 冻结商品抵扣券
                            $payment = array();
                            $payment['fuc'] = 'currency';
                            $payment['type'] = 'currency_dkq';
                            $payment['name'] = '商品抵扣券';
                            self::currency(-$pay_dkq_price, $user['id'], '商品抵扣券支付商品订单', 'pay', implode(",", $order_no), $payment['type']);
                        }
                    }

                    //付款金额【微信】
                    $pay_price = round($price - $pay_dkq_price, 2);

                    // ---- add pointers start 扣绿色积分支付
                    if (true) {
                        $ns = $user['currency_ns'];
                        \think\Log::info("DKQ-Pay pay_price=$pay_price, ns=$ns");
                        if ($ns >= $pay_price) {
                            self::currency(-$pay_price, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $balance_no = date('YmdHis') . rand(10000000, 99999999);
                            $pay_list[] = [
                                'id' => $row['id'],
                                'trade_no' => $balance_no, // 第三方交易号
                                'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                                'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                                'total_amount' => $price, // 总金额
                                'actual_payment' => $row['price'], // 实际支付
                                'notice' => json_encode([
                                    'type' => $this->type,
                                    'user_id' => $user['id'],
                                    'trade_no' => $balance_no,
                                    'out_trade_no' => $row['pay_no'],
                                    'amount' => $row['price'],
                                    'total_amount' => $price,
                                    'order_id' => $row['order_id'],
                                    'trade_type' => 'BALANCE',
                                    'version' => '1.0.0'
                                ])
                            ];
                            // 更新支付列表
                            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                            Order::update(['state' => '2', 'paymenttime' => time()], ['id' => $row['order_id']]);
                            $arr = array();
                            $arr['action'] = 'orderPay';
                            $arr['order_id'] = $row['order_id'];
                            model('app\api\model\wanlshop\Order')->update(['flq' => 4], ['id' => ['in', $order_id]]);
                            Hook::listen("com_auto_settlement", $arr);
                            return ['code' => 200, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
                        } else {
                            self::currency(-$ns, $user['id'], '绿色积分支付商品订单', 'pay', implode(",", $order_no), 'currency_ns');
                            $pay_price = $pay_price - $ns;
                            $pay_dkq_price += $ns;
                        }
                        \think\Log::info("DKQ-Pay weixin pay_price=$pay_price, ns=$ns");
                    }
                    // ---- add pointers end

                    $pay_discounts_price = 0;
                    //200元支付，修改为 80%--——200  50%---500
                    // if($pay_price % 500 == 0){
                    //     //随机减5块以内
                    //     $pay_discounts_price = round(rand(1, 500)/100,2);
                    //     $pay_price -= $pay_discounts_price;
                    // }
                    // 修改支付信息
                    // order_price  订单金额
                    // price        支付金额
                    // freight_price 快递金额
                    // actual_payment 实际支付
                    // total_amount 总金额
                    \app\api\model\wanlshop\Pay::update(
                        ['price' => $pay_price, 'total_amount' => $price, 'discount_price' => $pay_dkq_price, 'pay_discounts_price' => $pay_discounts_price],
                        ['id' => $pay[0]['id']]
                    );
                }

                //变更订单类型
                model('app\api\model\wanlshop\Order')->update(['flq' => 4], ['id' => ['in', $order_id]]);
                //达人券组合支付，先扣达人券，后调用真实对应支付方式支付
                return ['code' => 200, 'msg' => '成功-0', 'data' => ['type' => 'dkq']];
            } else {
                // 随机减优惠 10块钱之内
                // if($price == 99 || $price == 999 || $price == 4995) {
                //     $pp = 0;
                //     if($price == 99) {
                //         $pp = rand(1, 100);// 1块
                //     } else if($price == 999) {
                //         $pp = rand(1, 1000); // 10
                //     } else if($price == 4995) {
                //         $pp = rand(1, 5000);// 50块
                //     }
                //     $data['total_fee'] = $data['total_fee'] - $pp;

                //     \app\api\model\wanlshop\Pay::update(
                //         [ 'price'=>$data['total_fee']/100,'pay_discounts_price'=>$pp/100],
                //         ['id'=>$pay[0]['id']]
                //     );

                //     Log::info("随机减优惠 disPrice=$pp, order_type=$order_type");
                // }
            }
            if ($this->method == 'native_wechat' || $this->method == 'native') {
                $data = input();
                //以下是富友的支付的
                $updatetime = $row['updatetime'];
                if ($updatetime < time()) {
                    $update['pay_code_wechat'] = '';
                    $update['merchant_id'] = $row['merchant_id'] * 1 - 1;
                    db('wanlshop_pay')->where('pay_no', $row['pay_no'])->update($update);
                    unset($condition, $update);
                    $row['pay_code_wechat'] = '';
                }
                //如果时间超过5分钟则，自动失效
                if (!empty($row['pay_code_wechat'])) {
                    $return_data['pay_code'] = $row['pay_code_wechat'];
                    $return_data['now_time'] = date("Y-m-d H:i:s", $row['createtime']);
                    $return_data['end_time'] = date("Y-m-d H:i:s", $row['updatetime']);
                    $return_data['mark'] = '未过期调用原有订单';
                    return ['code' => 200, 'msg' => '返回参数', 'data' => $return_data];
                }
                //富友支付二维码
                // 'trade_type' => 'NATIVE',
                // 'body' => '福荟星球：'.$row['order_no'],
                // 'out_trade_no' => $payOutTrade->out_trade_no,
                // 'total_fee' => rand(1,2),
                // 'notify_url' => $config['notify_url'],
                // 'detail' => time(),
                // 'attach' => 'wechat_native_pay',
                // 'profit_sharing' => 'N',
                // 'time_start' => date('YmdHis'),
                // 'time_expire' => $numericString
                // 报文体，array
                // 富友商编：0003320F7588593
                // 商户名称：浙江中福统祥商业管理有限公司
                // 富友商编：0003320F7630196
                // 商户名称：福荟大健康管理（浙江）有限公司
                if ($price == 99 || $price == 999 || $price == 4995 || $price == 1300 || $price == 6500 || $price == 13000) {
                    $pp = 0;
                    if ($price == 99) {
                        $pp = rand(1, 100);// 1块
                    } else if ($price == 999) {
                        $pp = rand(1, 1000); // 10
                    } else if ($price == 4995) {
                        $pp = rand(1, 3000);// 50块
                    } else if ($price == 1300) {
                        $pp = rand(500, 1000);// 50块
                    } else if ($price == 6500) {
                        $pp = rand(1, 3000);// 50块
                    } else if ($price == 13000) {
                        $pp = rand(1, 3000);// 50块
                    }
                    $price = $price * 100 - $pp;
                    Log::info("随机减优惠 disPrice=$pp, order_type=$order_type");
                } else {
                    $price = $price * 100;
                }
                //         $config = get_addon_config('wanlshop');
                // 		$message_body = [
                // 			'mchnt_cd' 			=> '0003320F7630196',
                // 			'order_date' 		=> date('Ymd'),
                // 			'order_id' 			=> $payOutTrade->out_trade_no,
                // 			'order_amt' 		=> $price,
                // 			'order_pay_type' 	=> 'WECHAT',
                // 			'back_notify_url' 	=> 'https://shoa.wellmeee.com/api/wanlshop/callback/notify/type/wechat/saas/4/type/native',
                // 			'goods_name' 		=> $title,
                // 			'goods_detail' 		=> '福荟星球爆款单品'.$title,
                // 			'appid' 			=> '',
                // 			'openid' 			=> '',
                // 			'ver' 				=> '1.0.0',
                // 			'order_timeout'     => '5'
                //  		];
                //  		\think\Log::info("支付回调：".$config['ini']['appurl']. $config['sdk_qq']['notify_url'].'/saas/'.SAAS.'/type/native');
                // 		//生成message
                // 		$message = $this->publicEncryptRsa(json_encode($message_body));
                // 		//发送请求，这里用的Guzzle
                // 		$client = new Client(['verify' => false]);
                // 		$url = 'https://hlwnets.fuioupay.com/aggpos/order.fuiou';		//接口地址
                // 		$res = $client->request('POST', $url, [
                //             'json' => [
                //                 'mchnt_cd' => $message_body['mchnt_cd'],				//这里的mchnt_cd要取报文体里面的mchnt_cd
                //                 'message'   => $message
                //             ]
                //         ]);
                //         $result = $res->getBody()->getContents();
                // 		//得到响应，解密数据
                //         $decrypted = "";
                //         if ($result) {
                //             $result = json_decode($result, true);
                //             if ($result['resp_code'] == '0000') {
                // 				//只有resp_code为0000的时候，才有message。
                //                 $decrypted = $this->privateDecryptRsa($result['message'], $this->rsa_private_key);
                //                 if ($decrypted) {
                //                     $decrypted = json_decode($decrypted, true);
                //                 }
                //             }else{
                //                 return ['code' => 10005 ,'msg' => $result,'data'=>$result];
                //             }
                //         }else{
                //             return ['code' => 10005 ,'msg' => '请求失败2','data'=>$result];
                //         }
                // 		// 生成二维码
                // 		require "../vendor/phpqrcode/phpqrcode.php";
                // 		$qRcode = new \QRcode();
                // 		$data = $decrypted['order_info'];//网址或者是文本内容
                // 		// 纠错级别：L、M、Q、H
                // 		$level = 'L';
                // 		// 点的大小：1到10,用于手机端4就可以了
                // 		$size = 4;
                // 		// 生成的文件名
                // 		$qRcode->png($data, false, $level, $size);
                // 		$img =ob_get_contents();
                // 		ob_end_clean();
                // 		$imginfo = 'data:png;base64,' . chunk_split(base64_encode($img));//转base64
                // 		// 当前时间
                //         $currentTime = date('Y-m-d H:i:s');
                //         // 当前时间加上5分钟
                //         $futureTime = strtotime($currentTime . ' +5 minutes');
                //         // 格式化为标准的日期时间格式
                //         $formattedFutureTime = date('Y-m-d H:i:s', $futureTime);
                //         //把upatetime转为时间戳
                //         $numericString = preg_replace('/[^0-9]/', '', $formattedFutureTime);
                //         //更新这个订单把二维码写到数据库里面去
                //         // $hookParams = ['action' => 'payment', 'order_id' => $row['id']];
                //         // $mulConf = Hook::listen('multipayment', $hookParams)[0];
                // 		$update['pay_code_wechat'] = $imginfo;
                // 		$update['merchant_id'] = '99999999999999';
                // 		$update['updatetime'] = strtotime($formattedFutureTime);
                // 		db('wanlshop_pay')->where('pay_no',$row['pay_no'])->update($update);
                // 		unset($condition,$update);
                //         // 返回Base64编码的字符串
                //         $return_data['pay_code'] = $imginfo;
                //         $return_data['now_time'] = $currentTime;
                //         $return_data['end_time'] = $formattedFutureTime;
                // 		return ['code' => 200 ,'msg' => '返回参数','data'=>$return_data];
                //以下是微信的二维码支付的


                //
                //订单5分钟是否自动过期，如果自动过期了，则生成新的二维码


                $config = get_addon_config('wanlshop');
                // $params = [
                //     'appid'      => $config['sdk_qq']['appid'],
                //     'secret'     => $config['sdk_qq']['appsecret']
                // ];
                // return ['code' => 200 ,'msg' => '返回参数','data'=>$config];
                $app_id = 'wxbd5f10aa35e26482';
                $secret = '21f3b2687e2af2e5009bbd31e60f300d';
                $merchant_id = '1690602164';
                $key = 'SKSxxxDEDS4324g71289012HAsanxs22';
                $notify_url = $config['sdk_qq']['notify_url'];
                $config['app_id'] = $app_id;
                $config['secret'] = $secret;
                $config['mch_id'] = $merchant_id;
                $config['key'] = $key;
                $config['notify_url'] = 'https://shoa.wellmeee.com/api/wanlshop/callback/notify/type/wechat/saas/4/type/native_wechat';
                // $franchiseeMerchantId = $this->franchiseeMerchantId($order['franchisee_id']);
                $app = Factory::payment($config);
                unset($condition);
                $hookParams = ['action' => 'wechatpayment', 'order_id' => $row['id']];
                $mulConf = Hook::listen('multipayment', $hookParams)[0];
                //查询当前有额度且未被下降的的商户ID，如果有的话，则把商户ID加入到里面，且减去额度，
                $res = $app->setSubMerchant($mulConf['merchant_id']);
                // 当前时间
                $currentTime = date('Y-m-d H:i:s');
                // 当前时间加上5分钟
                $futureTime = strtotime($currentTime . ' +5 minutes');
                $updatetime = $futureTime;
                // 格式化为标准的日期时间格式
                $formattedFutureTime = date('Y-m-d H:i:s', $futureTime);
                //把upatetime转为时间戳

                $numericString = preg_replace('/[^0-9]/', '', $formattedFutureTime);
                $result = $app->order->unify([
                    'trade_type' => 'NATIVE',
                    'body' => '福荟星球：' . $row['order_no'],
                    'out_trade_no' => $payOutTrade->out_trade_no,
                    'total_fee' => $price,
                    'notify_url' => $config['notify_url'],
                    'detail' => time(),
                    'attach' => 'wechat_native_pay',
                    'profit_sharing' => 'N',
                    'time_start' => date('YmdHis'),
                    'time_expire' => $numericString
                ]);
                if ($result['return_code'] == 'FAIL') {
                    return ['code' => 10005, 'msg' => $result['return_msg']];
                }
                // 生成二维码
                require "../vendor/phpqrcode/phpqrcode.php";
                $qRcode = new \QRcode();
                if (empty($result['code_url'])) {
                    return ['code' => 10005, 'msg' => $result];
                }
                $data = $result['code_url'];//网址或者是文本内容
                // 纠错级别：L、M、Q、H
                $level = 'L';
                // 点的大小：1到10,用于手机端4就可以了
                $size = 4;
                // 生成的文件名
                $qRcode->png($data, false, $level, $size);
                $img = ob_get_contents();
                ob_end_clean();
                $imginfo = 'data:png;base64,' . chunk_split(base64_encode($img));//转base64
                //更新这个订单把二维码写到数据库里面去
                $update['pay_code_wechat'] = $imginfo;
                $update['merchant_id'] = $mulConf['merchant_id'];
                $update['updatetime'] = strtotime($formattedFutureTime);
                db('wanlshop_pay')->where('pay_no', $row['pay_no'])->update($update);
                unset($condition, $update);
                // 		$app->order->close($row['pay_no']);
                $return_data['pay_code'] = $imginfo;
                $return_data['now_time'] = $currentTime;
                $return_data['end_time'] = $formattedFutureTime;
                return ['code' => 200, 'msg' => '返回参数', 'data' => $return_data];

            }
            if ($this->method == 'miniapp' || $this->method == 'mp') {
                // 获取微信openid，前期版本仅可安全获取，后续版本优化登录逻辑
                $config = get_addon_config('wanlshop');
                $params = [
                    'appid' => $config['mp_weixin']['appid'],
                    'secret' => $config['mp_weixin']['appsecret'],
                    'js_code' => $this->code,
                    'grant_type' => 'authorization_code'
                ];
                $time = time();
                $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
                if ($result['ret']) {
                    $json = (array)json_decode($result['msg'], true);
                    $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                    if (!$third) {
                        $third = model('app\api\model\wanlshop\Third');
                        // array_key_exists("unionid",$json)
                        if (isset($json['unionid'])) {
                            $third->unionid = $json['unionid'];
                            $third->openid = $json['openid'];
                        } else {
                            $third->openid = $json['openid'];
                        }
                        $third->access_token = $json['session_key'];
                        $third->expires_in = 7776000;
                        $third->logintime = $time;
                        $third->expiretime = $time + 7776000;
                        $third->user_id = $this->user_id;
                        $third->save();
                    }
                    $data['openid'] = $json['openid'];
                } else {
                    return ['code' => 10005, 'msg' => '获取微信openid失败，无法支付'];
                }
            }
            // 开始支付
            try {
                $payConfig = $this->getConfig('wechat'); // 里面-切换获取mch_id TODO-
                \app\api\model\wanlshop\Pay::update(['merchant_id' => $payConfig['mch_id']], ['freeze' => '1', 'id' => ['in', $pay_id]]); // 微信更新商户号
                $wechat = Pay::wechat($payConfig)->{$this->method}($data);
                if ($this->method == 'app') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getContent()];
                } else if ($this->method == 'wap') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getTargetUrl()];
                } else {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'allpointers') {
            Db::startTrans();
            try {
                // 所有可用积分组合支付后再使用支付宝支付 扣除顺序：福利券，商品抵扣券，余额，绿色积分。
                // 判断商品是否可以使用【福利券】条件：love shopgoods
                $order_goods = model('app\api\model\wanlshop\OrderGoods')->where('order_id', 'in', $order_id)->find();
                $goods_id = $order_goods['goods_id'];
                $goodsInfo = model('app\api\model\wanlshop\Goods')->where('id', $goods_id)->find();
                $activity_type = $goodsInfo['activity_type'];
                $canFlqPay = in_array($activity_type, ['shopgoods', 'love']);

                $pay_price = $price;

                $flq = 5;
                /// 福利券
                if ($canFlqPay) {
                    $ids = implode(",", $order_no);
                    $hasRow = CurrencyFlqLog::where(['service_ids' => $ids, 'type' => 'pay'])->find();
                    if (!$hasRow) {
                        $max_flq = 0;
                        $love = model('app\admin\model\wanlshop\Ninestar')->where(['type' => 'love', 'range' => $goods_id])->find();
                        if ($love) {
                            $max_flq = $love['integral'];
                        }
                        \think\Log::info("allpointers 爱心商品 max_flq=$max_flq, goods_id=$goods_id");
                        // 爱心商品 end

                        //订单支付金额-福利券 = 微信支付金额
                        //最多支付福利券 80%--——799  50%---499
                        if ($max_flq <= 0) {
                            if ($price < 999) {
                                return ['code' => 10006, 'msg' => '非云店产品，无法使用福利券支付'];
                            }
//                        $max_flq = floor($price / 999) * 499;
                            $max_flq = $price * 0.5;
                            \think\Log::info("allpointers 爱心商品 max_flq=$max_flq, goods_id=$goods_id");
                        }

                        //使用福利券
                        $use_flq = $user['currency_flq'];
                        if ($user['currency_flq'] > $max_flq) {
                            $use_flq = $max_flq;
                        }

                        if($use_flq > 0) {
                            $flq = 1;
                        }
                        // 冻结提货券
                        self::currency(-$use_flq, $user['id'], '福利券支付商品订单', 'pay', implode(",", $order_no), 'currency_flq');

                        //剩余付款金额
                        $pay_price = round($pay_price - $use_flq, 2);
                    } else {
                        $flq = 1;
                    }
                }

                /// ****** 绿色积分 ******
                $retOk = self::pointerPayByType($user, $pay_price, $row, $price, $order_id, $order_no, 'currency_ns', '绿色积分支付商品订单', $flq);
                if ($retOk['code'] != 200) {
                    /// ****** 商品抵扣券 ******
                    $retOk = self::pointerPayByType($user, $pay_price, $row, $price, $order_id, $order_no, 'currency_dkq', '商品抵扣券分支付商品订单', $flq);
                    if ($retOk['code'] != 200) {
                        /// ****** 可用余额 ******
                        $retOk = self::pointerPayByType($user, $pay_price, $row, $price, $order_id, $order_no, 'currency_bd', '余额分支付商品订单', $flq);
                    }
                }
                Db::commit();
                if ($retOk['code'] == 200) {
                    return $retOk;
                } else {
                    return ['code' => 200, 'msg' => '成功-0', 'data' => ['type' => 'pointers']];
                }
            } catch (Exception $e) {
                Db::rollback();
                return ['code' => 10002, 'msg' => $e->getMessage()];
            }
        } else if ($this->type == 'jssdk') {
            // 微信公众账号支付
            try {
                $app = Factory::payment(Mp::pay());
                $third = model('app\api\model\wanlshop\Third')
                    ->where(['platform' => 'weixin_h5', 'user_id' => $this->user_id])
                    ->find();
                if (!$third) {
                    return ['code' => 10006, 'msg' => '未查询到OPENID'];
                }
                $result = $app->order->unify([
                    'body' => $title,
                    'out_trade_no' => $payOutTrade->out_trade_no,
                    'total_fee' => $price * 100, //付款金额 单位分
                    'trade_type' => 'JSAPI', // 请对应换成你的支付方式对应的值类型
                    'openid' => $third['openid']
                ]);

                if (isset($result['return_code']) && $result['return_code'] === 'SUCCESS') {
                    if (isset($result['result_code']) && $result['result_code'] === 'FAIL') {
                        return ['code' => 10004, 'msg' => $result['err_code_des']];
                    } else {
                        $jssdk = $app->jssdk;
                        $config = $jssdk->sdkConfig($result['prepay_id']); // 返回数组
                        return ['code' => 200, 'msg' => '成功', 'data' => $config];
                    }
                } else if (isset($result['return_code']) && $result['return_code'] === 'FAIL') {
                    return ['code' => 10002, 'msg' => $result['return_msg']];
                } else {
                    return ['code' => 10003, 'msg' => 'JSSDK接口生成订单失败'];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 百度支付
        } else if ($this->type == 'baidu') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // QQ支付
        } else if ($this->type == 'qq') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 苹果支付
        } else if ($this->type == 'apple') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'sandpay') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $payOutTrade->out_trade_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/sandpay',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/sandpay',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'UNIONPAY',
                'payWay' => 'H5_PAY',
                'payScene' => 'ONLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/html-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取杉德支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['redirectUrl']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeepay') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $payOutTrade->out_trade_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeepay',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeepay',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'UNIONPAY',
                'payWay' => 'H5_PAY',
                'payScene' => 'ONLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/trade-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['redirectUrl']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeewechat') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $payOutTrade->out_trade_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeewechat',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeewechat',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'WECHAT',
                'payWay' => 'MINI_PROGRAM',
                'payScene' => 'OFFLINE',
                'appId' => 'wx0d830b0fa95aed2f',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&appId=' . $data['appId'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/aggregation-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝微信支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['redirectUrl']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeeali') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $payOutTrade->out_trade_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeeali',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeeali',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'ALIPAY',
                'payWay' => 'USER_SCAN',
                'payScene' => 'OFFLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/aggregation-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝微信支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['prePayTn']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeecash') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $payOutTrade->out_trade_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeecash',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeecash',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'FRONTCASHIER',
                'payWay' => 'FRONTCASHIER',
                'payScene' => 'OFFLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/frontcashier-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝转账支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        }
    }

    private function pointerPayByType($user, &$pay_price, &$row, $price, $order_id, $order_no, $currencyType = 'currency_ns', $memo = '绿色积分支付商品订单', $flq = 1)
    {
        // ---- add pointers start 扣积分支付
        $ns = $user["$currencyType"];
        \think\Log::info("allpointers PointersPay $currencyType pay_price=$pay_price, val=$ns");
        if ($ns >= $pay_price) {
            self::currency(-$pay_price, $user['id'], $memo, 'pay', implode(",", $order_no), $currencyType);
            $balance_no = date('YmdHis') . rand(10000000, 99999999);
            $pay_list[] = [
                'id' => $row['id'],
                'trade_no' => $balance_no, // 第三方交易号
                'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                'total_amount' => $price, // 总金额
                'actual_payment' => $row['price'], // 实际支付
                'notice' => json_encode([
                    'type' => $this->type,
                    'user_id' => $user['id'],
                    'trade_no' => $balance_no,
                    'out_trade_no' => $row['pay_no'],
                    'amount' => $row['price'],
                    'total_amount' => $price,
                    'order_id' => $row['order_id'],
                    'trade_type' => 'BALANCE',
                    'version' => '1.0.0'
                ])
            ];
            // 更新成已支付状态
            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
            Order::update(['state' => '2', 'paymenttime' => time()], ['id' => $row['order_id']]);

            $this->changeOrderOwnerToFriend($row['order_id']);

            $arr = array();
            $arr['action'] = 'orderPay';
            $arr['order_id'] = $row['order_id'];

            ///
            model('app\api\model\wanlshop\Order')->update(['flq' => $flq], ['id' => ['in', $order_id]]);

            Hook::listen("com_auto_settlement", $arr);

            // --- 盲盒商品赠送抵扣券
            $this->addCouponNum($row['order_id']);

            return ['code' => 200, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
        } else {
            self::currency(-$ns, $user['id'], $memo, 'pay', implode(",", $order_no), $currencyType);
            $pay_price = $pay_price - $ns;
        }
        \think\Log::info("allpointers PointersPay $currencyType pay_price=$pay_price, val=$ns");
        \app\api\model\wanlshop\Pay::update(
            ['price' => $pay_price, 'total_amount' => $price, 'discount_price' => $price - $pay_price],
            ['id' => $row['id']]
        );
        model('app\api\model\wanlshop\Order')->update(['flq' => 4], ['id' => ['in', $order_id]]);
        return ['code' => 208, 'msg' => '成功-0', 'data' => ['payOk' => '1']];
        // ---- add pointers end
    }

    /**
     * app支付
     */
    public function payapp($order_id, $order_type)
    {
        if ($this->user_id == 0) {
            return ['code' => 10001, 'msg' => '用户ID不存在'];
        }
        // 获取支付信息
        $pay = model('app\api\model\wanlshop\Pay')
            ->where('order_id', 'in', $order_id)
            ->where(['user_id' => $this->user_id, 'type' => $order_type])
            ->select();
        // 1.0.8 升级
        $price = 0; // 付款金额
        $points = 0; // 付款金额
        $filall = 0; // 付款金额
        $filin = 0; // 付款金额
        $order_no = []; // 订单号
        $pay_id = []; // 交易号
        foreach ($pay as $row) {
            $price = bcadd($price, $row['price'], 2); // 总价格
            $points = bcadd($points, $row['points'], 2); // 总价格
            $filall = bcadd($filall, $row['fil'], 2); // 总价格
            $filin = bcadd($filin, $row['order_fil'], 2); // 总价格
            $order_no[] = $row['order_no']; // 订单集
            $pay_id[] = $row['id']; // 交易集
        }
        $price = $price + $points;

        $title = '爱心订单-' . implode("_", $order_no);
        $model_order = model('app\api\model\wanlshop\Order');
        $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
        $model_goods = model('app\api\model\wanlshop\Goods');

        $user = model('app\common\model\User')->get($this->user_id);

        $payment['fuc'] = 'currency';
        $payment['type'] = 'currency_bd';
        $payment['name'] = '现金';

        if (!$user || $user['currency_bd'] < $price) {
            \think\Log::info("payEnroll 不足本次支付 price=$price user=" . $user['currency_bd']);
            return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
        }

        $currencytype = 'currency_' . $pay[0]['integraltype'];
        if (!$user || $user[$currencytype] < $pay[0]['integral']) {
            \think\Log::info("payEnroll 不足本次支付 price=$price user=" . $user[$currencytype]);
            return ['code' => 500, 'msg' => '积分不足本次支付'];
        }


        $pay_list = [];
        $order_list = [];
        // 支付方式
        if ($this->type == 'balancebd') {//余额支付
            if ($user) {
                $result = false;
                $balance_no = date('YmdHis') . rand(10000000, 99999999);
                Db::startTrans();
                try {
                    foreach ($pay as $row) {

                        foreach ($model_order_goods->where('order_id', $row['order_id'])->select() as $goods) {

                            $saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
                            if ($goods['shop_id'] == $saas_info[$user['saas_id']]['shop_id_vip']) {
                                if ($this->type != 'balancebd') {
                                    return ['code' => 500, 'msg' => '有产品无法使用' . $payment['name'] . '支付'];
                                }
                            }

                            // 新增付款人数、新增销量
                            $model_goods->where('id', $goods['goods_id'])->inc('payment')->inc('sales', $goods['number'])->update();
                        }
                        // 订单列表

                        $myOrder = $model_order->field('id,self_pickup')->find($row['order_id']);

                        $order_list[] = ['id' => $row['order_id'], 'state' => 2, 'paymenttime' => time()];

                        $goods = OrderGoods::field('id,goods_id')->where('order_id', $row['order_id'])->find();
                        $integral = 0;
                        $integraltype = 'ns';

                        if ($goods) {
                            $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                            $activity_data = model('app\index\model\wanlshop\Ninestar')->where(['id' => $goodsData['activity_id']])->field("integral,integraltype")->find();

                            $integral = $activity_data['integral'];
                            $integraltype = $activity_data['integraltype'];
                            $sdata = [
                                'good_id' => $goods->goods_id,
                                // 'shop_id' => $row['shop_id'],
                                'user_id' => $row['user_id'],
                                'state' => 0
                            ];
                            $share = (new GoodsShare)->where($sdata)->order('id ASC')->find();
                            if ($share) {
                                $share->state = 1;
                                $share->order_id = $row['order_id'];
                                $share->save();
                            }
                        }

                        // 支付列表
                        $pay_list[] = [
                            'id' => $row['id'],
                            'trade_no' => $balance_no, // 第三方交易号
                            'pay_type' => 0, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                            'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                            'total_amount' => $price, // 总金额
                            'actual_payment' => $row['price'], // 实际支付
                            'integral' => $integral,
                            'integraltype' => $integraltype,
                            'notice' => json_encode([
                                'type' => $this->type,
                                'user_id' => $user['id'],
                                'trade_no' => $balance_no,
                                'out_trade_no' => $row['pay_no'],
                                'amount' => $row['price'],
                                'total_amount' => $price,
                                'order_id' => $row['order_id'],
                                'trade_type' => 'BALANCE',
                                'version' => '1.0.0'
                            ])
                        ];

                    }

                    // 更新支付列表
                    $result = model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
                    // 更新订单列表
                    $result = $model_order->saveAll($order_list);
                    // 修改用户金额
                    //Yooye 增加付款汇率 Start
                    $prate = 1;

                    if (count($order_no) == 1) {
                        $result = self::{$payment['fuc']}(-$price / $prate, $user['id'], '余额支付' . ('商品') . '订单', 'lovepay', implode(",", $order_no), $payment['type']);

                        self::currency(-$integral, $this->user_id, '爱心购物', 'lovepay', $balance_no, 'currency_ns');

                    } else {
                        $result = self::{$payment['fuc']}(-$price / $prate, $user['id'], '余额合并支付' . ('商品') . '订单', 'lovepay', implode(",", $order_no), $payment['type']);

                        self::currency(-$integral, $this->user_id, '爱心购物', 'lovepay', $balance_no, 'currency_ns');
                    }
                    //购买爱心商品赠送 消费贡献积分
                    self::currency($integral, $this->user_id, '爱心赠送', 'pay', $balance_no, 'currency_love');

                    Db::commit();
                    return ['code' => 200, 'msg' => '成功', 'data' => []];
                } catch (Exception $e) {
                    Db::rollback();
                    return ['code' => 10002, 'msg' => $e->getMessage()];
                }
            }

        }
    }


    /**
     * 转账给用户
     * @return void
     */
    public function transfer($transfer_id)
    {
        // 设置商家侧唯一订单号
        $model['out_biz_no'] = 'T' . rand(10, 99) . date('ymdHis') . rand(1000, 9999);

        // 设置订单总金额
        $model['trans_amount'] = "0.1";

        // 设置描述特定的业务场景
        $model['biz_scene'] = "DIRECT_TRANSFER";

        // 设置业务产品码
        $model['product_code'] = "TRANS_ACCOUNT_NO_PWD";

        // 设置转账业务的标题
        $model['order_title'] = "商家转账";

        // 设置收款方信息
        $payeeInfo = array();
        $payeeInfo['identity'] = "136****7958";
        $payeeInfo['name'] = "***";
        $payeeInfo['identity_type'] = "ALIPAY_LOGON_ID";
        $model['payee_info'] = $payeeInfo;

        // 设置业务备注
        $model['remark'] = "测试";

        return Pay::alipay($this->payConfig(SAAS, '****************'))->pay('transfer', $model);
    }


    /**
     * 用户充值
     */
    public function recharge($price, $remitimage = '', $ct = '') // bd ns
    {
        if ($this->user_id == 0) {
            return ['code' => 10001, 'msg' => '用户ID不存在'];
        }
        if ($price <= 0) {
            return ['code' => 10002, 'msg' => '充值金额不合法'];
        }
        // 充值订单号
        $pay_no = date("Ymdhis") . sprintf("%08d", $this->user_id) . mt_rand(1000, 9999);
        // 支付标题
        $title = '充值-' . $pay_no;
        // 获取配置
        if ($this->type == 'remit') {
            if ($remitimage == '') {
                return ['code' => 10002, 'msg' => '请上传打款凭证!'];
            }
            // 生成一个订单
            $order = \app\api\model\wanlshop\RechargeOrder::create([
                'orderid' => $pay_no,
                'user_id' => $this->user_id,
                'amount' => $price,
                'payamount' => 0,
                'paytype' => $this->type,
                'ip' => $this->request->ip(),
                'useragent' => substr($this->request->server('HTTP_USER_AGENT'), 0, 255),
                'status' => 'created',
                'ctype' => $ct ? $ct : 'currency_bd',
                'remitimage' => $remitimage
            ]);
        } else {
            // 生成一个订单
            $order = \app\api\model\wanlshop\RechargeOrder::create([
                'orderid' => $pay_no,
                'user_id' => $this->user_id,
                'amount' => $price,
                'payamount' => 0,
                'paytype' => $this->type,
                'ip' => $this->request->ip(),
                'useragent' => substr($this->request->server('HTTP_USER_AGENT'), 0, 255),
                'status' => 'pending',
                'ctype' => $ct ? $ct : 'currency_bd',
                'remitimage' => '[]'
            ]);
        }
        if ($this->type == 'alipay') {
            // 获取配置
            $payConfig = $this->getConfig($this->type);
            $payConfig['notify_url'] = str_replace("notify", "notify_recharge", $payConfig['notify_url']);
            $data = [
                'out_trade_no' => $pay_no,
                'total_amount' => $price,
                'subject' => $title
            ];
            try {
                $alipay = Pay::alipay($payConfig)->{$this->method}($data);
                if ($this->method == 'app' || $this->method == 'wap') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $alipay->getContent()];
                } else {
                    return ['code' => 200, 'msg' => '成功', 'data' => $alipay];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }

            // 微信支付
        } else if ($this->type == 'wechat') {
            // 获取配置
            $payConfig = $this->getConfig($this->type);
            $payConfig['notify_url'] = str_replace("notify", "notify_recharge", $payConfig['notify_url']);
            $data = [
                'out_trade_no' => $pay_no, // 订单号
                'body' => $title, // 标题
                'total_fee' => $price * 100 //付款金额 单位分
            ];
            if ($this->method == 'miniapp' || $this->method == 'mp') {
                // 获取微信openid，前期版本仅可安全获取，后续版本优化登录逻辑
                $config = get_addon_config('wanlshop');
                $params = [
                    'appid' => $config['mp_weixin']['appid'],
                    'secret' => $config['mp_weixin']['appsecret'],
                    'js_code' => $this->code,
                    'grant_type' => 'authorization_code'
                ];
                $time = time();
                $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
                if ($result['ret']) {
                    $json = (array)json_decode($result['msg'], true);
                    $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                    if (!$third) {
                        $third = model('app\api\model\wanlshop\Third');
                        if (isset($json['unionid'])) {
                            $third->unionid = $json['unionid'];
                            $third->openid = $json['openid'];
                        } else {
                            $third->openid = $json['openid'];
                        }
                        $third->access_token = $json['session_key'];
                        $third->expires_in = 7776000;
                        $third->logintime = $time;
                        $third->expiretime = $time + 7776000;
                        $third->user_id = $this->user_id;
                        $third->save();
                    }
                    $data['openid'] = $json['openid'];
                } else {
                    return ['code' => 10003, 'msg' => '获取微信openid失败，无法支付'];
                }
            }
            // 开始支付
            try {
                $wechat = Pay::wechat($payConfig)->{$this->method}($data);
                if ($this->method == 'app') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getContent()];
                } else if ($this->method == 'wap') {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getTargetUrl()];
                } else {
                    return ['code' => 200, 'msg' => '成功', 'data' => $wechat];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 微信公众账号充值
        } else if ($this->type == 'jssdk') {
            try {
                $payConfig = Mp::pay();
                $payConfig['notify_url'] = str_replace("notify", "notify_recharge", $payConfig['notify_url']);
                $app = Factory::payment($payConfig);
                $third = model('app\api\model\wanlshop\Third')
                    ->where(['platform' => 'weixin_h5', 'user_id' => $this->user_id])
                    ->find();
                if (!$third) {
                    return ['code' => 10006, 'msg' => '未查询到OPENID'];
                }
                $result = $app->order->unify([
                    'body' => $title,
                    'out_trade_no' => $pay_no,
                    'total_fee' => $price * 100, //付款金额 单位分
                    'trade_type' => 'JSAPI', // 请对应换成你的支付方式对应的值类型
                    'openid' => $third['openid']
                ]);

                if (isset($result['return_code']) && $result['return_code'] === 'SUCCESS') {
                    if (isset($result['result_code']) && $result['result_code'] === 'FAIL') {
                        return ['code' => 10004, 'msg' => $result['err_code_des']];
                    } else {
                        $jssdk = $app->jssdk;
                        $config = $jssdk->sdkConfig($result['prepay_id']); // 返回数组
                        return ['code' => 200, 'msg' => '成功', 'data' => $config];
                    }
                } else if (isset($result['return_code']) && $result['return_code'] === 'FAIL') {
                    return ['code' => 10002, 'msg' => $result['return_msg']];
                } else {
                    return ['code' => 10003, 'msg' => 'JSSDK接口生成订单失败'];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 百度支付
        } else if ($this->type == 'baidu') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // QQ支付
        } else if ($this->type == 'qq') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
            // 苹果支付
        } else if ($this->type == 'apple') {
            try {

            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'remit') {
            try {
                return ['code' => 200, 'msg' => '提交成功', 'data' => 1];
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'sandpay') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $pay_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify_recharge/type/sandpay',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/sandpay',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'UNIONPAY',
                'payWay' => 'H5_PAY',
                'payScene' => 'ONLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/html-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取杉德支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['redirectUrl']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeepay') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $pay_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify_recharge/type/yeepay',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeepay',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'UNIONPAY',
                'payWay' => 'H5_PAY',
                'payScene' => 'ONLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/trade-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['redirectUrl']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeeali') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $pay_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify_recharge/type/yeeali',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeeali',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'ALIPAY',
                'payWay' => 'USER_SCAN',
                'payScene' => 'OFFLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/aggregation-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝支付宝支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']['prePayTn']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        } else if ($this->type == 'yeecash') {
            $data = [
                'merchantId' => '6996282976456978432',
                'orderId' => $pay_no, // 订单号
                'goodsName' => $title, // 标题
                'remark' => $title, // 标题
                'amount' => $price, //付款金额 单位分
                'notifyUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/notify_recharge/type/yeecash',
                'redirectUrl' => 'https://bx.wininlife.online/api/wanlshop/callback/return/type/yeecash',
                'expiredTime' => date('YmdHis', time() + 6000),
                'payChannel' => 'FRONTCASHIER',
                'payWay' => 'FRONTCASHIER',
                'payScene' => 'OFFLINE',
                'userId' => $this->user_id,
                'userIp' => $this->request->ip()
            ];
//            var_dump($data);
            $key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
            $signbf = 'amount=' . $data['amount'] . '&expiredTime=' . $data['expiredTime'] . '&goodsName=' . $data['goodsName'] . '&merchantId=' . $data['merchantId'] . '&notifyUrl=' . $data['notifyUrl'] . '&orderId=' . $data['orderId'] . '&payChannel=' . $data['payChannel'] . '&payScene=' . $data['payScene'] . '&payWay=' . $data['payWay'] . '&redirectUrl=' . $data['redirectUrl'] . '&remark=' . $data['remark'] . '&userId=' . $data['userId'] . '&userIp=' . $data['userIp'] . '&key=' . $key;
            $sign = strtoupper(md5($signbf));
//            var_dump($signbf);
//            var_dump($sign);
            $params = array();
            $result = Http::sendRequest("https://pay.s100mi.com/api/v1/in-order/frontcashier-pay", $data, 'POST', $option = [], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
            } else {
                return ['code' => 10005, 'msg' => '获取易宝转账支付失败，无法支付'];
            }
            try {
                if ($result['msg']['code'] == 200) {
                    return ['code' => 200, 'msg' => '成功', 'data' => $result['msg']['data']];
                } else {
                    return ['code' => 10005, 'msg' => $result['msg']['message']];
                }
            } catch (\Exception $e) {
                return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
            }
        }
    }

    /**
     * 参加活动付款
     * @param $order_no
     * @return void
     */
    public function payEnroll($order_no)
    {
        //查询订单信息
        $order = OfflineActivityOrder::get(['order_no' => $order_no]);
        if (!$order) {
            return ['code' => 10005, 'msg' => '未知订单'];
        }
        if ($order['status'] != 1) {
            return ['code' => 10005, 'msg' => '订单状态异常，或已支付'];
        }
        $data = [
            'out_trade_no' => $order->order_no, // 订单号
            'body' => '活动经费', // 标题
            'total_fee' => $order['payment_amount'] * 100 //付款金额 单位分
        ];

        // 绿色积分支付
        if ($this->type == 'balancens') {
            $payment = ['name' => '余额'];
            $payment['fuc'] = 'currency';
            $payment['type'] = 'currency_ns';
            $payment['name'] = '绿色通用积分';

            $price = $order['payment_amount'];
            $user = model('app\common\model\User')->get($this->user_id);
            if (!$user || $user['currency_ns'] < $price) {
                \think\Log::info("payEnroll 不足本次支付 price=$price user=" . $user['currency_ns']);
                return ['code' => 500, 'msg' => $payment['name'] . '不足本次支付'];
            }

            $result = self::{$payment['fuc']}(-$price, $user['id'], $payment['name'] . '支付' . ('参加活动') . '订单', 'pay', $order_no, $payment['type']);
            if ($result !== false) {
                return ['code' => 200, 'msg' => '成功', 'data' => ['nsok' => 'ok']];
            } else {
                return ['code' => 500, 'msg' => '服务器繁忙！001'];
            }
        }

        // 获取微信openid，前期版本仅可安全获取，后续版本优化登录逻辑
        $config = get_addon_config('wanlshop');
        if ($this->method == 'miniapp' || $this->method == 'mp') {
            $params = [
                'appid' => $config['mp_weixin']['appid'],
                'secret' => $config['mp_weixin']['appsecret'],
                'js_code' => $this->code,
                'grant_type' => 'authorization_code'
            ];
            $time = time();
            $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
            $json = json_decode($result['msg'], true);
            if ($result['ret'] && array_get($json, 'openid')) {
                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                if (!$third) {
                    $third = model('app\api\model\wanlshop\Third');
                    if (isset($json['unionid'])) {
                        $third->unionid = $json['unionid'];
                        $third->openid = $json['openid'];
                    } else {
                        $third->openid = $json['openid'];
                    }
                    $third->access_token = $json['session_key'];
                    $third->expires_in = 7776000;
                    $third->logintime = $time;
                    $third->expiretime = $time + 7776000;
                    $third->user_id = $this->user_id;
                    $third->save();
                }
                $data['openid'] = $json['openid'];
            } else {
                return ['code' => 10005, 'msg' => '未获取到支付者openid，无法支付'];
            }
        }
        // 开始支付
        try {
            $payConfig = $this->getConfig($this->type);
            $payConfig['notify_url'] = $config['ini']['appurl'] . '/wanlshop/enroll/notify/type/' . $this->type;
            $wechat = Pay::wechat($payConfig)->{$this->method}($data);
            if ($this->method == 'app') {
                return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getContent()];
            } else if ($this->method == 'wap') {
                return ['code' => 200, 'msg' => '成功', 'data' => $wechat->getTargetUrl()];
            } else {
                return ['code' => 200, 'msg' => '成功', 'data' => $wechat];
            }
        } catch (\Exception $e) {
            return ['code' => 10006, 'msg' => $this->type . '：' . $e->getMessage()];
        }
    }

    /**
     * 参加活动支付回调
     */
    public function notifyEnroll()
    {
        try {
            $result = self::verify();
            Log::info('=====参加活动支付回调======');
            Log::info(json_encode($result, JSON_UNESCAPED_UNICODE));
            // 查询支付集
            $order = model('app\common\model\OfflineActivityOrder')->get(['order_no' => $result['out_trade_no']]);
            // 查询订单是否存在
            if (!$order) {
                return ['code' => 10001, 'msg' => '网络异常'];
            }
            // 总价格
            $price = $order['payment_amount'];

            // -----------------------------判断订单是否合法-----------------------------
            $config = get_addon_config('wanlshop');
            if ($this->type == 'wechat') {
                // 判断状态
                if ($result['result_code'] == 'SUCCESS') {
                    // 判断金额
                    if ($price != ($result['total_fee'] / 100)) {
                        return ['code' => 10004, 'msg' => '支付金额有误'];
                    }
                    // 判断商家ID
                    if ($config['sdk_qq']['mch_id'] != $result['mch_id']) {
                        return ['code' => 10004, 'msg' => '商户不合法'];
                    }
                    // H5微信支付
                    if ($result['trade_type'] == 'MWEB') {
                        if ($config['sdk_qq']['gz_appid'] != $result['appid']) {
                            return ['code' => 10005, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                    // 小程序支付
                    if ($result['trade_type'] == 'JSAPI') {
                        if ($config['mp_weixin']['appid'] != $result['appid']) {
                            return ['code' => 10006, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                    // App支付
                    if ($result['trade_type'] == 'APP') {
                        if ($config['sdk_qq']['wx_appid'] != $result['appid']) {
                            return ['code' => 10007, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
            } else {
                return ['code' => 500, 'msg' => '未知方式支付'];
            }
            // -----------------------------支付成功，修改订单-----------------------------

            // 更新订单列表
            $order->trade_no = $result['transaction_id'];//支付交易号
            $order->status = 2;//已支付
            $order->payment_time = time();//支付时间
            $order->payment_notice = json_encode($result, JSON_UNESCAPED_UNICODE);//支付通知
            $order->save();
        } catch (\Exception $e) {
            return ['code' => 10008, 'msg' => $e->getMessage()];
        }
        // 返回给支付接口
        if ($this->type == 'jssdk') {
            return ['code' => 200, 'msg' => 'true', 'data' => $order];
        } else {
            return ['code' => 200, 'msg' => $this->wanlpay->success()->send(), 'data' => $order];
        }
    }


    /**
     * 验证回调
     */
    private function verify()
    {
        try {
            if (strstr($this->type, 'FT-')) {
                $param['data'] = $_GET;
                $param['action'] = 'payCallBack';
                return Hook::listen('ft', $param)[0];
            } else
                if ($this->type == 'jssdk') {
                    $this->wanlpay = Factory::payment(Mp::pay());
                    $this->wanlpay->handlePaidNotify(function ($result, $fail) {
                        if ($result['return_code'] === 'SUCCESS') { // return_code 表示通信状态，不代表支付状态
                            // 用户是否支付成功
                            if (isset($result['return_code']) && $result['return_code'] === 'SUCCESS') {
                                if (isset($result['result_code']) && $result['result_code'] === 'FAIL') {
                                    $this->jssdkdata = ['code' => 10004, 'msg' => $result['err_code_des']];
                                } else {
                                    $this->jssdkdata = $result;
                                }
                            } else if (isset($result['return_code']) && $result['return_code'] === 'FAIL') {
                                $this->jssdkdata = ['code' => 10002, 'msg' => $result['return_msg']];
                            } else {
                                $this->jssdkdata = ['code' => 10003, 'msg' => 'JSSDK接口生成订单失败'];
                            }
                        } else {
                            $this->jssdkdata = ['code' => 10001, 'msg' => '通信失败'];
                        }
                    });
                    return $this->jssdkdata;
                } else if ($this->type == 'native_wechat') {
                    $config = [
                        // 必要配置
                        'app_id' => 'wxbd5f10aa35e26482',
                        'mch_id' => '1690602164',
                        'key' => 'SKSxxxDEDS4324g71289012HAsanxs22'
                    ];
                    $this->wanlpay = Factory::payment($config);
                    $this->wanlpay->handlePaidNotify(function ($result, $fail) {
                        if ($result['return_code'] === 'SUCCESS') { // return_code 表示通信状态，不代表支付状态
                            // 用户是否支付成功
                            if (isset($result['return_code']) && $result['return_code'] === 'SUCCESS') {
                                if (isset($result['result_code']) && $result['result_code'] === 'FAIL') {
                                    $this->jssdkdata = ['code' => 10004, 'msg' => $result['err_code_des']];
                                } else {
                                    $this->jssdkdata = $result;
                                    \think\Log::info("微信扫码支付回调成功." . json_encode($result));
                                }
                            } else if (isset($result['return_code']) && $result['return_code'] === 'FAIL') {
                                $this->jssdkdata = ['code' => 10002, 'msg' => $result['return_msg']];
                            } else {
                                $this->jssdkdata = ['code' => 10003, 'msg' => 'JSSDK接口生成订单失败'];
                            }
                        } else {
                            $this->jssdkdata = ['code' => 10001, 'msg' => '通信失败'];
                        }
                    });
                    return $this->jssdkdata;
                } else if ($this->type == 'sandpay') {
                    $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
                    return $this->wanlpay->verify($_POST);
                } else if ($this->type == 'yeepay') {
                    $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
                    return $this->wanlpay->verify($_POST);
                } else if ($this->type == 'yeeali') {
                    $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
                    return $this->wanlpay->verify($_POST);
                } else if ($this->type == 'yeecash') {
                    $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
                    return $this->wanlpay->verify($_POST);
                } else if ($this->type == 'alipay') {
                    $this->wanlpay = Pay::{$this->type}($this->payConfig(null, $_POST['app_id'] ?? ''));
                    return $this->wanlpay->verify($_POST);
                } else {
                    $data = null;
                    $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
                    return $this->wanlpay->verify($data);
                }
        } catch (\Exception $e) {
            return ['code' => 10008, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 支付回调
     */
    public function notify()
    {
        try {
            Log::info("我收到了富友回调:" . $this->type == 'native');
            if ($this->type == 'native') {
                $result = file_get_contents("php://input");
                \think\Log::info("富友回调：" . json_encode($result));
                //只有resp_code为0000的时候，才有message。
                if ($result) {
                    $result = json_decode($result, true);
                    if ($result['resp_code'] == '0000') {
                        //只有resp_code为0000的时候，才有message。
                        $decrypted = $this->privateDecryptRsa($result['message'], $this->rsa_private_key);
                        if ($decrypted) {
                            $decrypted = json_decode($decrypted, true);
                        }
                    }
                }
                //得到解密过后的数据
                \think\Log::info("回调解密支付的ID是：" . $decrypted['order_id']);
                \think\Log::info("回调解密：" . json_encode($decrypted));
                $result['out_trade_no'] = $decrypted['order_id'];
                // 查询支付集
                $payOutTrade = model('app\api\model\wanlshop\PayOutTrade')
                    ->where('out_trade_no', '=', $result['out_trade_no'])
                    ->find();

            } else {
                $result = self::verify();
                if (!$result) {
                    return ['code' => 10001, 'msg' => '无数据'];
                }
                // 查询支付集
                $payOutTrade = model('app\api\model\wanlshop\PayOutTrade')
                    ->where(['out_trade_no' => isset($result['out_trade_no']) ? $result['out_trade_no'] : (isset($result['orderId']) ? $result['orderId'] : $result['data']['mer_order_no'])])
                    ->find();
                if (!$payOutTrade) {
                    return ['code' => 10001, 'msg' => '无支付记录'];
                }
            }
            // 查询订单是否存在
            $pay = model('app\api\model\wanlshop\Pay')
                ->where('id', 'in', $payOutTrade['pay_id'])
                ->where('pay_state', 'neq', '1')
                ->select();
            if (!$pay) {
                return ['code' => 10001, 'msg' => '网络异常'];
            }
            // 1.0.8升级 拼团订单
            $order_type = $pay[0]['type'];
            if ($order_type == 'groups') {
                $model_order = model('app\api\model\wanlshop\groups\Order');
                $model_order_goods = model('app\api\model\wanlshop\groups\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\groups\Goods');
            } else {
                $model_order = model('app\api\model\wanlshop\Order');
                $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\Goods');
            }
            $trade_no = '';
            // 支付类型
            $pay_type = 8;
            $user_id = 0;
            $order_no = [];
            $shop_id = [];
            // 总价格
            $price = 0;
            // 主要用于日志
            foreach ($pay as $row) {
                // $price += $row['price']; 1.0.8 升级
                $price = bcadd($price, $row['price'], 2); // 总价格
                // 订单集
                $order_no[] = $row['order_no'];
                $user_id = $row['user_id'];
                $shop_id[] = $row['shop_id'];
            }
            // -----------------------------判断订单是否合法-----------------------------
            $config = get_addon_config('wanlshop');
            if (strstr($this->type, 'FT-')) {
                // 判断金额
                if ($price != $result['data']['total_fee']) {
                    return ['code' => 10002, 'msg' => '支付金额不合法'];
                }
                // 回调支付
                $pay_type = 10; // 支付类型
                $pay_name = '福铁支付宝';
                $trade_no = $result['data']['trade_no'];
            } else
                // 支付宝
                if ($this->type == 'alipay') {
                    // 判断状态
                    if (in_array($result['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                        // 判断金额
                        if ($price != $result['total_amount']) {
                            return ['code' => 10002, 'msg' => '支付金额不合法'];
                        }
                        // 判断appid
                        if ($pay[0]['merchant_id'] != $result['app_id']) {
                            return ['code' => 10003, 'msg' => 'APPID不合法'];
                        }
                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    // 回调支付
                    $pay_type = 2; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                    $pay_name = '支付宝';
                    $trade_no = $result['trade_no'];
                } else if ($this->type == 'wechat') {
                    // 判断状态
                    if ($result['result_code'] == 'SUCCESS') {
                        // 判断金额
                        if ($price != ($result['total_fee'] / 100)) {
                            $rPrice = $result['total_fee'] / 100;
                            // 修改添加优惠金额
                            \app\api\model\wanlshop\Pay::update(
                                ['actual_payment' => $rPrice],
                                ['id' => $pay[0]['id']]);

                            // 修改 fa_wanlshop_order_goods - actual_payment
                            \app\admin\model\wanlshop\OrderGoods::update(['actual_payment' => $rPrice], ['order_id' => $pay[0]['order_id']]);

//                        return ['code' => 10002 ,'msg' => '支付金额不合法'];
                        }
                        // 判断商家ID
                        if ($config['sdk_qq']['mch_id'] != $result['mch_id'] && $result['mch_id'] != $pay[0]['merchant_id']) {
                            return ['code' => 10004, 'msg' => '商户不合法'];
                        }
                        // H5微信支付
                        if ($result['trade_type'] == 'MWEB') {
                            if ($config['sdk_qq']['gz_appid'] != $result['appid']) {
                                return ['code' => 10005, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                            }
                        }
                        // 小程序支付
                        if ($result['trade_type'] == 'JSAPI') {
                            if ($config['mp_weixin']['appid'] != $result['appid']) {
                                return ['code' => 10006, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                            }
                        }
                        // App支付
                        if ($result['trade_type'] == 'APP') {
                            if ($config['sdk_qq']['wx_appid'] != $result['appid']) {
                                return ['code' => 10007, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                            }
                        }
                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    // 回调支付
                    $pay_type = 1; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                    $pay_name = '微信';
                    $trade_no = $result['transaction_id'];
                } else if ($this->type == 'jssdk') {
                    $pay_type = 1; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付
                    $pay_name = '微信JSSDK';
                    $trade_no = $result['transaction_id'];
                } else if ($this->type == 'sandpay') {
                    // 判断状态
                    if ($result['orderStatus'] == 'SUCCESS') {

                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                    $pay_name = '杉德支付';
                    $trade_no = $result['orderId'];
                } else if ($this->type == 'yeepay') {
                    // 判断状态
                    if ($result['orderStatus'] == 'SUCCESS') {

                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    $pay_type = 4; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付
                    $pay_name = '易宝支付';
                    $trade_no = $result['orderId'];
                } else if ($this->type == 'yeeali') {
                    // 判断状态
                    if ($result['orderStatus'] == 'SUCCESS') {

                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    $pay_type = 5; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝
                    $pay_name = '易宝支付宝';
                    $trade_no = $result['orderId'];
                } else if ($this->type == 'yeewechat') {
                    // 判断状态
                    if ($result['orderStatus'] == 'SUCCESS') {

                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    $pay_type = 6; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝
                    $pay_name = '易宝微信支付';
                    $trade_no = $result['orderId'];
                } else if ($this->type == 'yeecash') {
                    // 判断状态
                    if ($result['orderStatus'] == 'SUCCESS') {

                    } else {
                        return ['code' => 500, 'msg' => '支付回调失败'];
                    }
                    $pay_type = 7; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝
                    $pay_name = '易宝转账支付';
                    $trade_no = $result['orderId'];
                }
            // -----------------------------支付成功，修改订单-----------------------------
            $order_list = [];
            $pay_list = [];
            // 拼团列表
            $groups_list = [];
            // 拼团列表
            $groups_team_list = [];
            foreach ($pay as $row) {
                $isAlone = false; // 拼团订单一般是单订单，暂可以这样操作
                $groups_state = 'start'; // 拼团状态
                foreach ($model_order_goods->where('order_id', $row['order_id'])->select() as $goods) {
                    if ($order_type == 'groups' && !empty($goods['group_type'])) {
                        if ($goods['group_type'] == 'alone') {
                            $isAlone = true;
                        } else {
                            // 查询团ID
                            $groups = model('app\api\model\wanlshop\groups\Groups')
                                ->where(['group_no' => $goods['group_no']])
                                ->find();
                            // 判断是否超团
                            $groups_team = model('app\api\model\wanlshop\groups\Team')
                                ->where(['group_no' => $goods['group_no']])
                                ->select();
                            // 已拼团总数量
                            $groups_team_count = count($groups_team);
                            if ($groups_team_count >= $groups['people_num'] || $groups['join_num'] >= $groups['people_num']) {
                                $this->error(__('参与拼单失败，拼团已完成'));
                            }
                            // 判断是否具备成团条件
                            if (($groups['people_num'] - $groups_team_count) <= 1 || ($groups['people_num'] - $groups['join_num']) <= 1) {
                                $groups_state = 'success';
                                //调整其他拼团订单
                                // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
                                foreach ($groups_team as $team) {
                                    $order_list[] = ['id' => $team['order_id'], 'state' => 3, 'groupstime' => time()];
                                }
                            }
                            // 拼团状态: ready=准备中,start=拼团中,success=已成团,fail=拼团失败,auto=自动成团
                            $groups_list[] = [
                                'id' => $groups['id'],
                                'join_num' => $groups['join_num'] + 1,
                                'state' => $groups_state
                            ];
                            $groups_team_list[] = [
                                'user_id' => $user_id, // 1.0.9升级
                                'shop_id' => $goods['shop_id'],
                                'group_no' => $goods['group_no'],
                                'order_id' => $goods['order_id'],
                                'order_goods_id' => $goods['id']
                            ];
                        }
                    }
                    // 新增付款人数、新增销量
                    $model_goods->where('id', $goods['goods_id'])->inc('payment')->inc('sales', $goods['number'])->update();
                }
                // 订单列表
                if ($groups_state === 'success') {
                    $order_list[] = ['id' => $row['order_id'], 'state' => 3, 'paymenttime' => time(), 'groupstime' => time()];
                } else {
                    $order_list[] = ['id' => $row['order_id'], 'state' => $isAlone ? 3 : 2, 'paymenttime' => time()];
                }
                //如果是富友支付的话，回调要重新写入
                if ($this->type == 'native') {
                    //实际支付金额
                    $actual_payment = $decrypted['order_amt'] / 100;
                    //优惠金额 = 原价 - 实际支付金额
                    $pay_discounts_price = $row['price'] - $actual_payment;
                    $pay_list[] = [
                        'id' => $row['id'],
                        'trade_no' => $trade_no, // 第三方交易号
                        'pay_type' => 9, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝
                        'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                        'total_amount' => $row['price'], // 总金额
                        'actual_payment' => $actual_payment, // 实际支付
                        'pay_discounts_price' => $pay_discounts_price, // 付款优惠金额
                        'notice' => json_encode($decrypted)
                    ];
                } else {
                    if ($this->type == 'native_wechat') {
                        $pay_type = 1;
                    }
                    // 支付列表 1.0.9升级
                    $pay_list[] = [
                        'id' => $row['id'],
                        'trade_no' => $trade_no, // 第三方交易号
                        'pay_type' => $pay_type, // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝
                        'pay_state' => 1, // 支付状态 (支付回调):0=未支付,1=已支付,2=已退款
                        'total_amount' => $price, // 总金额
                        'actual_payment' => $row['price'], // 实际支付
                        'notice' => json_encode($result)
                    ];
                }
            }
            // 更新支付列表
            model('app\api\model\wanlshop\Pay')->saveAll($pay_list);
            // 更新订单列表
            $model_order->saveAll($order_list);
            // 支付日志
            // model('app\common\model\MoneyLog')->create([
            //     'user_id' => $user_id,
            //     'money' => -$price, // 操作金额
            //     'memo' => $pay_name.'支付' . $order_type == 'groups' ? '拼团' : '商城'  . '订单', // 备注
            //     'type' => $order_type == 'groups' ? 'groups' : 'pay', // 类型
            //     'service_ids' => implode(",",$order_no) // 业务ID
            // ]);
            if ($order_type == 'groups') {
                model('app\api\model\wanlshop\groups\Groups')->isUpdate()->saveAll($groups_list);
                model('app\api\model\wanlshop\groups\Team')->saveAll($groups_team_list);
            } else {
                $this->changeOrderOwnerToFriend($row['order_id']);

                $arr = array();
                $arr['action'] = 'orderPay';
                $arr['order_id'] = $row['order_id'];
                Hook::listen("com_auto_settlement", $arr);
                Hook::listen("warehouse", $arr, null, true); // 第三方下单
                // ----- 维品尚-确认下单调用
                $params = ['action' => 'payCallBack', 'order_id' => $row['order_id']];
                Hook::listen('weipinshang', $params);
                // ----- 多商户多类型集合支付-回调
                $params = ['action' => 'payCallBack', 'order_id' => $row['order_id']];
                Hook::listen('multipayment', $params);

                // --- 盲盒商品赠送抵扣券
                $this->addCouponNum($row['order_id']);


            }
            // if($this->type !== 'jssdk'){
            //     Log::debug('Alipay notify', $result->all());
            // }
        } catch (\Exception $e) {
            return ['code' => 10008, 'msg' => $e->getMessage(), 'file' => $e->getFile(), 'line' => $e->getLine(), 'data' => json_encode($result)];
        }
        //这里更新一下，返回回调支付成功
        if (strstr($this->type, 'FT-')) {
            //异步分账
            $params = [
                'action' => 'splitBill',
                'data' => [
                    'mch_id' => $result['attach'],
                    'out_trade_no' => $trade_no,
                    'total_amount' => $price,
                    'shop_id'=>$shop_id,
                ],
            ];
            Hook::listen('ft', $params);
            return ['code' => 200, 'msg' => $result['return']];
        } else
            if ($this->type == 'native') {
                return true;
            } else {
                // 返回给支付接口
                if ($this->type == 'jssdk') {
                    return ['code' => 200, 'msg' => 'true'];
                } else {
                    return ['code' => 200, 'msg' => $this->wanlpay->success()->send()];
                }
            }

    }

    /**
     * 充值支付回调
     */
    public function notify_recharge()
    {
        try {
            $result = self::verify();
            // 查询订单是否存在
            $order = model('app\api\model\wanlshop\RechargeOrder')
                ->where(['orderid' => $result['out_trade_no'] ? $result['out_trade_no'] : $result['orderId']])
                ->find();
            if (!$order) {
                return ['code' => 10001, 'msg' => '支付订单不存在'];
            } else {
                if ($order['status'] == 'paid') {
                    return ['code' => 10007, 'msg' => '订单已经支付过'];
                }
            }
            $memo = '';
            $trade_no = '';
            // -----------------------------判断订单是否合法-----------------------------
            $config = get_addon_config('wanlshop');
            // 支付宝
            if ($this->type == 'alipay') {
                // 判断状态
                if (in_array($result['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
                    // 判断金额
                    if ($order['amount'] != $result['total_amount']) {
                        return ['code' => 10002, 'msg' => '支付金额不合法'];
                    }
                    // 判断appid
                    if ($config['sdk_alipay']['app_id'] != $result['app_id']) {
                        return ['code' => 10003, 'msg' => 'APPID不合法'];
                    }
                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
                $memo = '支付宝充值';
                $trade_no = $result['trade_no'];
            } else if ($this->type == 'wechat') {
                // 判断状态
                if ($result['result_code'] == 'SUCCESS') {
                    // 判断金额
                    if ($order['amount'] != ($result['total_fee'] / 100)) {
                        return ['code' => 10002, 'msg' => '支付金额不合法'];
                    }
                    // 判断商家ID
                    if ($config['sdk_qq']['mch_id'] != $result['mch_id']) {
                        return ['code' => 10004, 'msg' => '商户不合法'];
                    }
                    // H5微信支付
                    if ($result['trade_type'] == 'MWEB') {
                        if ($config['sdk_qq']['gz_appid'] != $result['appid']) {
                            return ['code' => 10005, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                    // 小程序支付
                    if ($result['trade_type'] == 'JSAPI') {
                        if ($config['mp_weixin']['appid'] != $result['appid']) {
                            return ['code' => 10006, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                    // App支付
                    if ($result['trade_type'] == 'APP') {
                        if ($config['sdk_qq']['wx_appid'] != $result['appid']) {
                            return ['code' => 10007, 'msg' => '支付类型 ' . $result['trade_type'] . ' 不合法'];
                        }
                    }
                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
                $memo = '微信充值';
                $trade_no = $result['transaction_id'];
            } else if ($this->type == 'jssdk') {
                $memo = '微信公众号充值';
                $trade_no = $result['transaction_id'];
            } else if ($this->type == 'sandpay') {
                // 判断状态
                if ($result['orderStatus'] == 'SUCCESS') {

                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
//                $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                $memo = '杉德支付';
                $trade_no = $result['orderId'];
            } else if ($this->type == 'yeepay') {
                // 判断状态
                if ($result['orderStatus'] == 'SUCCESS') {

                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
//                $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                $memo = '易宝支付';
                $trade_no = $result['orderId'];
            } else if ($this->type == 'yeeali') {
                // 判断状态
                if ($result['orderStatus'] == 'SUCCESS') {

                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
//                $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                $memo = '易宝支付宝';
                $trade_no = $result['orderId'];
            } else if ($this->type == 'yeewechat') {
                // 判断状态
                if ($result['orderStatus'] == 'SUCCESS') {

                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
//                $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                $memo = '易宝微信';
                $trade_no = $result['orderId'];
            } else if ($this->type == 'yeecash') {
                // 判断状态
                if ($result['orderStatus'] == 'SUCCESS') {

                } else {
                    return ['code' => 500, 'msg' => '支付回调失败'];
                }
//                $pay_type = 3; // 支付类型:0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付
                $memo = '易宝转账支付';
                $trade_no = $result['orderId'];
            }
            // -----------------------------支付成功，修改订单-----------------------------
//			if ($order['status'] == 'created') {
//				$order->memo = $trade_no;
//			    $order->payamount = $order['amount']; // 上面已经判断过金额，可以直接使用
//                $order->paytime = time();
//                $order->status = 'paid';
//                $order->save();
//                // 更新用户金额
//                self::money(+$order['amount'], $order['user_id'], $memo, 'recharge', $order['id']);
//            }
            if ($order['status'] == 'pending') {
                $order->memo = $trade_no;
                $order->payamount = $order['amount']; // 上面已经判断过金额，可以直接使用
                $order->paytime = time();
                $order->status = 'created';
                $order->save();
                // 更新用户金额
//                self::money(+$order['amount'], $order['user_id'], $memo, 'recharge', $order['id']);
                self::currency($order['amount'], $order['user_id'], $memo, 'recharge', $order['id'], $order->ctype);
            }
            if ($this->type !== 'jssdk') {
                Log::debug('Alipay notify', $result->all());
            }
        } catch (\Exception $e) {
            return ['code' => 10008, 'msg' => $e->getMessage()];
        }
        // 返回给支付接口
        if ($this->type == 'jssdk') {
            return ['code' => 200, 'msg' => 'true'];
        } else {
            return ['code' => 200, 'msg' => $this->wanlpay->success()->send()];
        }
    }

    /**
     * 支付成功
     */
    public function return()
    {
        $this->wanlpay = Pay::{$this->type}($this->getConfig($this->type));
        try {
            return $this->wanlpay->verify();
        } catch (\Exception $e) {
            return __($e->getMessage());
        }
    }

    /**
     * 获取配置
     * @param string $type 支付类型
     * @return array|mixed
     */
    public function getConfig()
    {
        $config = get_addon_config('wanlshop');

        $pay_config = [];
        if ($this->type == 'alipay') {
            $pay_config = [
                'app_id' => $config['sdk_alipay']['app_id'],
                'notify_url' => $config['ini']['appurl'] . $config['sdk_alipay']['notify_url'] . '/saas/' . SAAS,
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'private_key' => $config['sdk_alipay']['private_key'],
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0
                ],
                // 'mode' => 'dev', // optional,设置此参数，将进入沙箱模式
            ];
            if (isset($config['sdk_alipay']['app_cert_public_key'])) {
                $pay_config['app_cert_public_key'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['app_cert_public_key']);
            }
            if (isset($config['sdk_alipay']['alipay_root_cert'])) {
                $pay_config['alipay_root_cert'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['alipay_root_cert']);
            }
            if (isset($config['sdk_alipay']['ali_public_key'])) {
                $pay_config['ali_public_key'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['ali_public_key']);
            }
            $order_id = $this->order_id[0];
            // 多商户选择切换支付： mch_id key
            $multiMerConfig = get_addon_config('multimerchantpayment');
            $openMultiPay = $multiMerConfig['openMultiPay'];
            \think\Log::info("MultiPay payment openMultiPay=$openMultiPay");
            if ($openMultiPay == 1) { // 1:是, 0:否; 不开启走原来 epay，原配置
                if ($this->order_id) { // 发起支付时进入
                    $hookParams = ['action' => 'payment', 'order_id' => $order_id, 'type' => $this->type, 'platform' => $this->method];
                    $mulConf = Hook::listen('multipayment', $hookParams)[0];
                    if ($mulConf) {
                        $pay_config['app_id'] = $mulConf['merchant_id'];
                        $pay_config['private_key'] = $mulConf['key'];
                        unset($mulConf['key']);
                        \think\Log::info("MultiPay payment {$this->type}" . json_encode($mulConf, JSON_UNESCAPED_UNICODE));
                        $pre = ADDON_PATH . 'multimerchantpayment/certs/alipay/' . $mulConf['merchant_id'] . '/';
                        $pay_config['app_cert_public_key'] = $pre . 'appCertPublicKey_' . $pay_config['app_id'] . '.crt';
                        $pay_config['alipay_root_cert'] = $pre . 'alipayRootCert.crt';
                        $pay_config['ali_public_key'] = $pre . 'alipayCertPublicKey_RSA2.crt';
                    } else {
                        throw new \Exception('暂无有效商户-无法支付');
                    }

                } else { // 回调时-无order_id
                    // 回调拿商户号换key - 用于签名验证使用
                    try {
                        $content = Request::createFromGlobals()->getContent();
                        parse_str($content, $data);
                        \think\Log::error('支付宝异步通知内容' . json_encode($data));
                        $pay_config['app_id'] = $data['app_id'];
                        $franObj = SystemFranchisee::get(['merchant_id' => $data['app_id']]);
                        if ($franObj) {
                            $pay_config['private_key'] = $franObj['key'];
                            $pre = ADDON_PATH . 'multimerchantpayment/certs/alipay/' . $pay_config['app_id'] . '/';
                            $pay_config['app_cert_public_key'] = $pre . 'appCertPublicKey_' . $pay_config['app_id'] . '.crt';
                            $pay_config['alipay_root_cert'] = $pre . 'alipayRootCert.crt';
                            $pay_config['ali_public_key'] = $pre . 'alipayCertPublicKey_RSA2.crt';
                        }
                    } catch (Exception $e) {
                        \think\Log::error("MultiPay payment {$this->type} 使用原配置 err=" . $e->getMessage());
                    }
                }
            }
            \think\Log::info("MultiPay payment config=" . json_encode($pay_config, JSON_UNESCAPED_UNICODE));
        } else if ($this->type == 'wechat') {
            $pay_config = [
                'appid' => $config['sdk_qq']['wx_appid'], // APP APPID
                'app_id' => $config['sdk_qq']['gz_appid'], // 公众号 APPID
                'miniapp_id' => $config['mp_weixin']['appid'], // 小程序 APPID
                'mch_id' => $config['sdk_qq']['mch_id'],
                'key' => $config['sdk_qq']['key'],
                'notify_url' => $config['ini']['appurl'] . $config['sdk_qq']['notify_url'],
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
            if ($config['sdk_qq']['pay_cert'] == 1) {
                $pay_config['cert_client'] = ADDON_PATH . 'wanlshop' . DS . 'certs' . DS . $this->type . DS . 'apiclient_cert.pem'; // optional, 退款，红包等情况时需要用到
                $pay_config['cert_key'] = ADDON_PATH . 'wanlshop' . DS . 'certs' . DS . $this->type . DS . 'apiclient_key.pem';// optional, 退款，红包等情况时需要用到
                if (defined('SAAS')) {
                    if (SAAS > 1) {
                        $pay_config['cert_client'] = ADDON_PATH . 'wanlshop' . DS . 'certs' . DS . $this->type . DS . 'apiclient_cert' . SAAS . '.pem'; // optional, 退款，红包等情况时需要用到
                        $pay_config['cert_key'] = ADDON_PATH . 'wanlshop' . DS . 'certs' . DS . $this->type . DS . 'apiclient_key' . SAAS . '.pem';// optional, 退款，红包等情况时需要用到
                    }
                }
            }
            // 多商户选择切换支付： mch_id key
            $multiMerConfig = get_addon_config('multimerchantpayment');
            $order_id = $this->order_id[0];
            \think\Log::info("MultiPay get config=" . json_encode($multiMerConfig, JSON_UNESCAPED_UNICODE) . '-orderId=' . $order_id);
            if ($multiMerConfig['openMultiPay'] == 1) { // 1:是, 0:否; 不开启走原来 epay，原配置
                if ($this->order_id) { // 发起支付时进入
                    $hookParams = ['action' => 'payment', 'order_id' => $order_id, 'type' => $this->type, 'platform' => $this->method];
                    $mulConf = Hook::listen('multipayment', $hookParams)[0];
                    if ($mulConf) {
                        $pay_config['mch_id'] = $mulConf['merchant_id'];
                        $pay_config['key'] = $mulConf['key'];
                        unset($mulConf['key']);
                        \think\Log::info("MultiPay " . json_encode($mulConf, JSON_UNESCAPED_UNICODE));
                    }
                } else { // 回调时-无order_id
                    // 回调拿商户号换key - 用于签名验证使用
                    try {
                        $content = Request::createFromGlobals()->getContent();
                        $data = Support::fromXml($content);
                        $mch_id = $data['mch_id'];
                        $franObj = SystemFranchisee::get(['merchant_id' => $mch_id]);
                        if ($franObj) {
                            $pay_config['key'] = $franObj['key'];
                        }
                    } catch (Exception $e) {
                        \think\Log::error("MultiPay payment 使用原配置 err=" . $e->getMessage());
                    }
                }
            }
        } else if ($this->type == 'sandpay') {
            $pay_config = [
                'merchantId' => '6996282976456978432', // APP APPID
                'notify_url' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/sandpay',
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
        } else if ($this->type == 'yeepay') {
            $pay_config = [
                'merchantId' => '6996282976456978432', // APP APPID
                'notify_url' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeepay',
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
        } else if ($this->type == 'yeewechat') {
            $pay_config = [
                'merchantId' => '6996282976456978432', // APP APPID
                'notify_url' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeewechat',
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
        } else if ($this->type == 'yeeali') {
            $pay_config = [
                'merchantId' => '6996282976456978432', // APP APPID
                'notify_url' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeeali',
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
        } else if ($this->type == 'yeecash') {
            $pay_config = [
                'merchantId' => '6996282976456978432', // APP APPID
                'notify_url' => 'https://bx.wininlife.online/api/wanlshop/callback/notify/type/yeecash',
                // 1.0.8升级 回调 https://pay.weixin.qq.com/wiki/doc/api/H5.php?chapter=15_4
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0,
                    // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
                ],
                // 'mode' => 'dev',
            ];
        }
        return $pay_config;
    }

    /**
     * 变更会员余额
     * @param int $money 余额
     * @param int $user_id 会员ID
     * @param string $memo 备注
     * @param string $type 类型
     * @param string $ids 业务ID
     */
    public function money($money, $user_id, $memo, $type = '', $ids = '', $node = '')
    {
        $user = model('app\common\model\User')->get($user_id);
        if ($user && $money != 0) {
            //退款操作
            // if($type == 'refund'){
            //     //微信支付原路返回
            //     //查询支付id
            //     $order_pay_id = model('\app\api\model\wanlshop\Refund')->where('id',$ids)->value('order_pay_id');
            //     $pay_log = model('\app\api\model\wanlshop\Pay')->where('id',$order_pay_id)->find('pay_type');
            //     if($pay_log['pay_type'] == 1){//0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝,
            //         $out_refund_no = rand(10,99).date('ymdHis').rand(10000,99999);
            //         $amount = $money*100;
            //         // 参数分别为：商户订单号、商户退款单号、订单金额、退款金额、其他参数
            //         $this->type = 'wechat';
            //         $result = Factory::payment($this->getConfig())->refund->byOutTradeNumber($out_refund_no, $out_refund_no, $amount, $amount, [
            //             'refund_desc' => $memo,
            //             'notify_url'    => '/wanlshop/refund/notify',
            //         ]);
            //         print_r($result);
            //         if ($result['return_code'] != 'SUCCESS'){
            //             throw new \Exception('退款异常');
            //         }
            //         $before = $user->money;
            //         $after = $before;
            //         $memo = $memo.'，微信支付，原路退回！';
            //     }else{
            //         $before = $user->money;
            //         $after = function_exists('bcadd') ? bcadd($user->money, $money, 2) : $user->money + $money;
            //         //更新会员信息
            //         $user->save(['money' => $after]);
            //     }
            // }else{
            $before = $user->money;
            $after = function_exists('bcadd') ? bcadd($user->money, $money, 2) : $user->money + $money;
            //更新会员信息
            $user->save(['money' => $after]);
            // }
            //写入日志
            $row = model('app\common\model\MoneyLog')->create([
                'user_id' => $user_id,
                'money' => $money, // 操作金额
                'before' => $before, // 原金额
                'after' => $after, // 增加后金额
                'memo' => $memo, // 备注
                'type' => $type, // 类型
                'service_ids' => $ids // 业务ID
            ]);
            return $row;
        } else {
            return ['code' => 500, 'msg' => '变更金额失败'];
        }
    }


    /**
     * 变更会员余额
     * @param int $mtype 类型
     * @param int $money 余额
     * @param int $user_id 会员ID
     * @param string $memo 备注
     * @param string $type 类型
     * @param string $ids 业务ID
     */
    public function currency($money, $user_id, $memo, $type = '', $ids = '', string $mtype = 'currency_points', $status = '1', $goods_id = 0)
    {
        $mtypeex = explode("_", $mtype);
        $mtypelog = ucfirst($mtypeex[1]);
        $user = model('app\common\model\User')->get($user_id);
        if ($user && $money != 0) {
            if ($type == 'refund') {
                //微信支付原路返回
                //查询支付id
                $pay_log = model('\app\api\model\wanlshop\Pay')->where('order_no', $ids)->where('user_id', $user_id)->where('pay_state', 1)->find();
                if (in_array($pay_log['pay_type'], [1, 2, 9, 10])) {//0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝,
                    $pay_log['notice'] = json_decode($pay_log['notice'], true);
                    $total_fee = $pay_log['price'] * 100;
                    if (isset($pay_log['notice']['total_fee'])) {
                        $total_fee = $pay_log['notice']['total_fee'];
                    }
                    $out_refund_no = rand(10, 99) . date('ymdHis') . rand(10000, 99999);
                    if ($pay_log['pay_type'] == 1) {
                        $amount = $money * 100;
                        $merchant_id = $pay_log['notice']['mch_id'];
                        \think\Log::info("MultiPay 退款 refund mch_id=$merchant_id, total_fee=$total_fee, out_refund_no=$out_refund_no, amount=$amount");
                        $this->type = 'wechat';
                        $result = Factory::payment($this->payConfig(SAAS, $merchant_id))->refund->byTransactionId($pay_log['trade_no'], $out_refund_no, $total_fee, $amount, [
                            'refund_desc' => $memo,
                            'notify_url' => 'http://' . $_SERVER['HTTP_HOST'] . '/index/wanlshop/refund/notify',
                        ]);
                        \think\Log::info("MultiPay multipayment refund 退款 payment=" . json_encode($result, JSON_UNESCAPED_UNICODE));
                        if ($result['return_code'] != 'SUCCESS' || $result['result_code'] != 'SUCCESS') {
                            if ($result['err_code_des'] != '订单已全额退款') {//兼容已退款订单状态变更
                                throw new \Exception($result['err_code_des']);
                            }
                        }
                    } elseif ($pay_log['pay_type'] == 10) {
                        if ($pay_log['notice']) {
                            if (isset($pay_log['notice']['data']['total_fee'])) {
                                $total_fee = $pay_log['notice']['data']['total_fee'];
                            }
                        }
                        //查询一下是否已经退款
                        $param2 = [
                            'data' => [
                                'mch_id' => $pay_log['notice']['attach'],
                                'trade_no' => $pay_log['notice']['data']['trade_no'],
                            ],
                            'action' => 'queryRefund',
                        ];
                        $rsp2 = Hook::listen('ft', $param2)[0];
                        if ($rsp2['result'] != 100) {
                            if ($rsp2['result'] == 111) {
                                throw new \Exception('请稍后重试，发起查询；' . json_encode($rsp2, JSON_UNESCAPED_UNICODE));
                            }
                            $param = [
                                'data' => [
                                    'mch_id' => $pay_log['notice']['attach'],
                                    'trade_no' => $pay_log['notice']['data']['trade_no'],
                                    'total_fee' => $total_fee,
                                    'shop_id' => [$pay_log['shop_id']],
                                ],
                                'action' => 'refund',
                            ];
                            $rsp = Hook::listen('ft', $param)[0];
                            if ($rsp && $rsp['result'] != 100) {
                                throw new \Exception('请稍后重试；' . json_encode($rsp2, JSON_UNESCAPED_UNICODE) . "\r\n" . json_encode($rsp, JSON_UNESCAPED_UNICODE));
                            } else {
                                throw new \Exception('退款异常，请稍后重试或联系管理员' . json_encode($rsp, JSON_UNESCAPED_UNICODE));
                            }
                        }

                        // throw new \Exception(json_encode($rsp,JSON_UNESCAPED_UNICODE));
                        model('\app\api\model\wanlshop\Pay')->update(['pay_state' => 2, 'refund_price' => $total_fee / 100], ['id' => $pay_log['id']]);
                        //后续不执行
                        return true;
                    } else if ($pay_log['pay_type'] == 9) {
                        //富友支付退款
                        // $merchant_id = $pay_log['notice']['mch_id'];
                        //报文体，array
                        $message_body = [
                            'mchnt_cd' => '0003320F7630196',
                            'refund_order_date' => date('Ymd'),
                            'refund_order_id' => $out_refund_no,
                            'pay_order_date' => $pay_log['notice']['order_date'],
                            'pay_order_id' => $pay_log['notice']['order_id'],
                            'refund_amt' => $pay_log['notice']['order_amt'],
                            'ver' => '1.0.0'
                        ];
                        //生成message
                        $message = $this->publicEncryptRsa(json_encode($message_body));
                        //发送请求，这里用的Guzzle
                        $client = new Client(['verify' => false]);
                        $url = 'https://refund-transfer.fuioupay.com/refund_transfer/aggposRefund.fuiou';        //接口地址
                        $res = $client->request('POST', $url, [
                            'json' => [
                                'mchnt_cd' => $message_body['mchnt_cd'],                //这里的mchnt_cd要取报文体里面的mchnt_cd
                                'message' => $message
                            ]
                        ]);
                        $result = $res->getBody()->getContents();
                        //得到响应，解密数据
                        $decrypted = "";
                        if ($result) {
                            $result = json_decode($result, true);
                            if ($result['resp_code'] == '0000') {
                                //只有resp_code为0000的时候，才有message。
                                $decrypted = $this->privateDecryptRsa($result['message'], $this->rsa_private_key);
                                if ($decrypted) {
                                    $decrypted = json_decode($decrypted, true);
                                    \think\Log::info("富友退款 refund" . json_encode($decrypted));
                                    model('\app\api\model\wanlshop\Pay')->update(['pay_state' => 2, 'refund_price' => $pay_log['notice']['order_amt'] / 100], ['id' => $pay_log['id']]);
                                } else {
                                    throw new \Exception('解密失败:' . json_encode($decrypted));
                                }
                                //后续不执行
                                return true;
                            } else {
                                throw new \Exception('退款申请失败1:' . $result['resp_desc'] . '   ' . json_encode($result) . ' 支付金额：' . $pay_log['notice']['order_amt']);
                            }
                        } else {
                            throw new \Exception('退款申请失败2:' . $result['resp_desc'] . '   ' . json_encode($result));
                        }
                    } else {
                        $this->type = 'alipay';
                        //先查询一下交易是否已退款
                        $find = json_decode(Pay::alipay($this->payConfig(SAAS, $pay_log['merchant_id']))->find(['trade_no' => $pay_log['trade_no']]), true);
                        if (array_get($find, 'code') != 10000) {
                            throw new \Exception('find:' . json_encode($find, JSON_UNESCAPED_UNICODE));
                        }
                        //交易状态：WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）
                        //交易成功继续执行退款

                        if ($find['trade_status'] == 'TRADE_SUCCESS') {
                            $data = [
                                'out_request_no' => $out_refund_no,
                                'refund_amount' => round($money, 2),
                                'trade_no' => $pay_log['trade_no'],
                                'refund_reason' => $memo,
                            ];
                            $result = json_decode(Pay::alipay($this->payConfig(SAAS, $pay_log['merchant_id']))->refund($data), true);
                            if (array_get($result, 'code') != 10000) {
                                throw new \Exception('result:' . json_encode($result, JSON_UNESCAPED_UNICODE));
                            }
                        } else if ($find['trade_status'] == 'TRADE_CLOSED') {
                            //已退款，默认执行后续操作
                        } else {
                            throw new \Exception('未查询到有效付款记录无法申请退款:' . json_encode($find, JSON_UNESCAPED_UNICODE));
                        }
                    }
                    $hookParams = ['action' => 'refund', 'order_id' => $pay_log['order_id']];
                    $refundRes = Hook::listen('multipayment', $hookParams);
                    \think\Log::info("MultiPay multipayment refund 退款 " . json_encode($refundRes));
                    model('\app\api\model\wanlshop\Pay')->update(['pay_state' => 2, 'refund_price' => $total_fee / 100], ['id' => $pay_log['id']]);
                    //后续不执行
                    return true;
                } else {
                    if ($status == 1) {
                        //判断是否使用当前方式支付过
                        if (!model("app\common\model\Currency{$mtypelog}Log")->where(['type' => 'pay', 'service_ids' => $ids, 'user_id' => $user_id])->find()) {
                            return false;
                        }
                        $before = $user->$mtype;
                        $after = function_exists('bcadd') ? bcadd($user->$mtype, $money, 5) : $user->$mtype + $money;
                        //更新会员信息
                        $user->save([$mtype => $after]);
                    } else {
                        $before = 0;
                        $after = 0;
                    }
                }
            } else {
                if ($status == 1) {
                    $before = $user->$mtype;
                    $after = function_exists('bcadd') ? bcadd($user->$mtype, $money, 5) : $user->$mtype + $money;
                    //更新会员信息
                    $user->save([$mtype => $after]);
                } else {
                    $before = 0;
                    $after = 0;
                }
            }
            if ($mtype == 'currency_nfr') {
                $nfr_sn = Random::alnum(24);
                $checkhv = true;
                while ($checkhv) {
                    $count = model("app\common\model\Currency{$mtypelog}Log")
                        ->where('nfr_sn', $nfr_sn)
                        ->count();
                    if ($count > 0) {
                        $nfr_sn = Random::alnum(24);
                    } else {
                        $checkhv = false;
                    }
                }
                //写入日志
                $row = model("app\common\model\Currency{$mtypelog}Log")->create([
                    'user_id' => $user_id,
                    'saas_id' => $user['saas_id'],
                    'money' => $money, // 操作金额
                    'before' => $before, // 原金额
                    'after' => $after, // 增加后金额
                    'memo' => $memo, // 备注
                    'type' => $type, // 类型
                    'status' => $status, // 状态
                    'service_ids' => $ids, // 业务ID
                    'goods_id' => $goods_id, // 业务ID
                    'nfr_sn' => $nfr_sn
                ]);
            } else {
                //通用积分备注优化
                if ($mtype == 'currency_ns') {
                    $memo = $this->getNewMemo($memo, $user_id, $ids);
                }
                //写入日志
                $row = model("app\common\model\Currency{$mtypelog}Log")->create([
                    'user_id' => $user_id,
                    'saas_id' => $user['saas_id'],
                    'money' => $money, // 操作金额
                    'before' => $before, // 原金额
                    'after' => $after, // 增加后金额
                    'memo' => $memo, // 备注
                    'type' => $type, // 类型
                    'status' => $status, // 状态
                    'service_ids' => $ids // 业务ID
                ]);
                \think\Log::info("wanlPayCurrency 添加 mtype=$mtype, user_id=$user_id, money=$money, memo=$memo, type=$type, status=$status, ids=$ids");
            }
            return $row;
        } else {
            return ['code' => 500, 'msg' => '变更金额失败'];
        }
    }

    /**
     * 获取新的积分记录备注
     * @param $memo
     * @param $user_id
     * @param $ids
     * @return void
     */
    protected function getNewMemo($memo, $user_id, $ids)
    {
        switch ($memo) {
            case '店长推荐';

                $uid = Order::where('order_no', $ids)->value('user_id');
                $user = model('app\common\model\User')->get($uid);
                $pay_log = model('\app\api\model\wanlshop\Pay')->where('order_no', $ids)->where('user_id', $uid)->where('pay_state', 1)->find();
                $money = $pay_log['price'];
                if ($user && $user->vipv_level == 0 && $money < 4995) {
                    $memo = '推荐' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '成为云店店长';
                }
                if ($user && $user->vipv_level == 2 && $money < 4995) {
                    $memo = '推荐' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '升级云店店长';
                }
                if ($user && $user->vipv_level == 0 && $money >= 4995) {
                    $memo = '推荐' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '成为旗舰店长';
                }
                if ($user && $user->vipv_level == 2 && $money >= 4995) {
                    $memo = '推荐' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '升级旗舰店长';
                }
                break;
            case '购物返';
                $memo = '推三返本：用户 ' . $this->getInviteUser($user_id, 3);
                break;
            case '团队奖励';
            case '九星奖励';
            case '九星邀请';
            case '首购共富奖'; // 店长共富
                $uid = Order::where('order_no', $ids)->value('user_id');
                $order_id = Order::where('order_no', $ids)->value('id');
                $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
                $good = $model_order_goods->where('order_id', $order_id)->find();
                $goodsData = model('app\api\model\wanlshop\Goods')->get($good['goods_id']);
                $user = model('app\common\model\User')->get($uid);
                $pay_log = model('\app\api\model\wanlshop\Pay')->where('order_no', $ids)->where('user_id', $uid)->where('pay_state', 1)->find();
                $money = $pay_log['price'];
                if ($user && $user->vipv_level == 0 && $goodsData['category_id'] == 108 && $money < 4995) {
                    $memo = '获得新增云店店长' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '首购共富奖';
                } else if ($user && $user->vipv_level == 0 && $goodsData['category_id'] == 108 && $money >= 4995) {
                    $memo = '获得新增旗舰店长' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '首购共富奖';
                } else if ($user && $user->vipv_level == 2 && $goodsData['category_id'] == 108 && $money < 4995) {
                    $memo = '获得升级云店长' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '首购共富奖';
                } else if ($user && $user->vipv_level == 2 && $user->vip_level < 5 && $goodsData['category_id'] == 108 && $money >= 4995) {
                    $memo = '获得升级旗舰店长' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '首购共富奖';
                } else {
                    $memo .= '：用户 ' . substr_replace(User::where('id', $uid)->value('username'), '****', 3, 4);
                }
                break;
            case '复购共富奖';
                $uid = Order::where('order_no', $ids)->value('user_id');
                $memo = '获得' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . $memo;
                break;
            case '推荐复购';
                $uid = Order::where('order_no', $ids)->value('user_id');
                $memo = '获得' . strChangeStars(User::where('id', $uid)->value('truename'), 1, 1) . '复购直推奖';
                break;


        }
        return $memo;
    }

    /**
     * 手机号脱敏处理
     * @param $phone
     * @return array|string|string[]
     */
    protected function mask_phone($phone)
    {
        return substr_replace($phone, '****', 3, 4);
    }

    /**
     * 获取最近$num个邀请的用户
     * @param $user_id
     * @param $num
     * @return void
     */
    protected static function getInviteUser($user_id, $num)
    {
        $array = User::where('inviter_id', $user_id)->order('id desc')->limit($num)->column('username');
        $start = 7; // 截取开始位置
        $length = 4; // 截取长度
        $sub_array = array_map(function ($arr) use ($start, $length) {
            return substr($arr, $start, $length);
        }, $array);
        return join('/', $sub_array);
    }

    /**
     * 获取微信配置信息-获取对应的SAAS微信支付配置
     * @return string[]
     */
    public function payConfig($saas = null, $merchant_id = '')
    {
        if (!$saas) {
            $saas = SAAS;
        }
        $config = SassConfigLoader::getAppInfoBySaasId($saas);
        $pay_config = [];
        if ($this->type == 'wechat') {
            $pay_config = [
                // 必要配置
                'app_id' => $config['sdk_qq']['wx_appid'],
                'mch_id' => $merchant_id,// $config['sdk_qq']['mch_id'],
                'key' => $config['sdk_qq']['key'],   // API 密钥
            ];
            //获取对应SAAS的微信配置信息
            // $pay_config = $config[SAAS];
            $pay_config['notify_url'] = 'http://' . $_SERVER['HTTP_HOST'] . '/index/wanlshop/refund/notify';
            $pay_config['cert_path'] = ADDON_PATH . 'epay' . DS . 'certs' . DS . 'apiclient_cert.pem';
            $pay_config['key_path'] = ADDON_PATH . 'epay' . DS . 'certs' . DS . 'apiclient_key.pem';
            //获取对应SAAS的支付证书
            if (SAAS) {
                $pay_config['cert_path'] = ADDON_PATH . 'epay' . DS . 'certs' . DS . 'apiclient_cert' . SAAS . '.pem';
                $pay_config['key_path'] = ADDON_PATH . 'epay' . DS . 'certs' . DS . 'apiclient_key' . SAAS . '.pem';
            }

            // 找证书位置 addons/multimerchantpayment/certs/wechat/4/1607821060/
            $certPath = ADDON_PATH . 'multimerchantpayment' . DS . 'certs' . DS . 'wechat' . DS . SAAS . DS . $merchant_id . DS . 'apiclient_cert.pem';
            $keyPath = ADDON_PATH . 'multimerchantpayment' . DS . 'certs' . DS . 'wechat' . DS . SAAS . DS . $merchant_id . DS . 'apiclient_key.pem';
            if (file_exists($certPath)) {
                $pay_config['cert_path'] = $certPath;
                $pay_config['key_path'] = $keyPath;
                // 获取key
                $sysFran = SystemFranchisee::get(['merchant_id' => $merchant_id]);
                if ($sysFran) {
                    $pay_config['key'] = $sysFran['key'];
                }
            }
        }

        if ($this->type == 'alipay') {
            $pay_config = [
                'app_id' => $config['sdk_alipay']['app_id'],
                'notify_url' => $config['ini']['appurl'] . $config['sdk_alipay']['notify_url'] . '/saas/' . SAAS,
                'return_url' => $config['h5']['domain'] . ($config['h5']['router_mode'] == 'hash' ? '/#' : '') . '/pages/page/success?type=pay',
                'private_key' => $config['sdk_alipay']['private_key'],
                'log' => [ // optional
                    'file' => LOG_PATH . 'wanlpay' . DS . $this->type . '-' . date("Y-m-d") . '.log',
                    'level' => 'info', // 建议生产环境等级调整为 info，开发环境为 debug
                    'type' => 'single', // optional, 可选 daily.
                    'max_file' => 30, // optional, 当 type 为 daily 时有效，默认 30 天
                ],
                'http' => [ // optional
                    'timeout' => 5.0,
                    'connect_timeout' => 5.0
                ],
                // 'mode' => 'dev', // optional,设置此参数，将进入沙箱模式
            ];
            if (isset($config['sdk_alipay']['app_cert_public_key'])) {
                $pay_config['app_cert_public_key'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['app_cert_public_key']);
            }
            if (isset($config['sdk_alipay']['alipay_root_cert'])) {
                $pay_config['alipay_root_cert'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['alipay_root_cert']);
            }
            if (isset($config['sdk_alipay']['ali_public_key'])) {
                $pay_config['ali_public_key'] = ADDON_PATH . str_replace(['/', '.crt'], [DS, SAAS . '.crt'], $config['sdk_alipay']['ali_public_key']);
            }
            //存在商户号，表明是多商户支付
            if ($merchant_id) {
                $pay_config['app_id'] = $merchant_id;
                $pay_config['private_key'] = SystemFranchisee::where(['merchant_id' => $merchant_id])->value('key');
                $pre = ADDON_PATH . 'multimerchantpayment/certs/alipay/' . $merchant_id . '/';
                $pay_config['app_cert_public_key'] = $pre . 'appCertPublicKey_' . $merchant_id . '.crt';
                $pay_config['alipay_root_cert'] = $pre . 'alipayRootCert.crt';
                $pay_config['ali_public_key'] = $pre . 'alipayCertPublicKey_RSA2.crt';
            }
        }
        return $pay_config;
    }

    /**
     * 变更会员余额
     * @param int $mtype 类型
     * @param int $money 余额
     * @param int $user_id 会员ID
     * @param string $memo 备注
     * @param string $type 类型
     * @param string $ids 业务ID
     */
    public static function currencyGoods($money, $user_id, $memo, $type = '', $ids = '', string $mtype = 'currency_points')
    {
        $mtypeex = explode("_", $mtype);
        $mtypelog = ucfirst($mtypeex[1]);
        $user = model('app\common\model\User')->get($user_id);
        if ($user && $money != 0) {
            $before = $user->$mtype;
            $after = function_exists('bcadd') ? bcadd($user->$mtype, $money, 5) : $user->$mtype + $money;
            //更新会员信息
            $user->save([$mtype => $after]);
            //写入日志
            $row = model("app\common\model\Currency{$mtypelog}Log")->create([
                'user_id' => $user_id,
                'saas_id' => $user['saas_id'],
                'money' => $money, // 操作金额
                'before' => $before, // 原金额
                'after' => $after, // 增加后金额
                'memo' => $memo, // 备注
                'type' => $type, // 类型
                'service_ids' => $ids // 业务ID
            ]);
            return $row;
        } else {
            return ['code' => 500, 'msg' => '变更金额失败'];
        }
    }

    private function addCouponNum($order_id)
    {
        $pay = model('\app\api\model\wanlshop\Pay')->where('order_id', $order_id)->find();
        \think\Log::info("addCouponNum-> coupon_num=${pay['coupon_num']}");
        if ($pay && $pay['coupon_num'] > 0) {
            // 获取抵扣券数量
            $user_id = $pay['user_id'];
            $money = $pay['coupon_num'];
            $ids = $pay['order_no'];
            self::currency($money, $user_id, '购买商品赠送抵扣券', 'subsidy', $ids, 'currency_dkq', '1');
        }
    }

    private function changeOrderOwnerToFriend($order_id) {
        $order = Order::get($order_id);
        if(isset($order['friend_id']) && $order['friend_id'] > 0) {
            $friend_id = $order['friend_id'];
            Order::update(['user_id'=> $friend_id], ['id' => $order_id]);
            \app\api\model\wanlshop\Pay::update(['user_id'=> $friend_id], ['order_id'=> $order_id]);

            $origin_uid = $order['origin_uid'];

            $payObj = \app\api\model\wanlshop\Pay::where('order_id', $order_id)->find();
            $bonus = $payObj['order_price'] * 0.03;

            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($bonus, $origin_uid, '达人团队奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$bonus * 0.08, $origin_uid, '达人团队奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($bonus * 0.08, $origin_uid, '达人团队奖励', 'subsidy', $order['order_no'], 'currency_rmb', '0');
        }
    }
}
