<?php

namespace app\api\controller\wanlshop;

use app\common\controller\Api;
use addons\wanlshop\library\EasyWeChat\Easywechat;
use addons\wanlshop\library\WanlChat\WanlChat;

use app\common\library\Sms;
use app\common\library\Tencent;
use app\common\library\Tool;
use fast\Random;
use fast\Http;
use think\Cache;
use think\Config;
use think\Db;
use think\Hook;
use think\Log;
use think\Validate;
use think\Exception;

/**
 * WanlShop会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'logout', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'phone', 'perfect'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        //WanlChat 即时通讯调用
        $this->wanlchat = new WanlChat();
        // Auth 写入
        $this->auth->setAllowFields(['id', 'username', 'nickname', 'mobile', 'avatar', 'level', 'gender', 'birthday', 'bio', 'money', 'score', 'successions', 'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'jointime'
            , 'currency_bd', 'currency_lmt', 'currency_rmb', 'currency_cny', 'currency_ns', 'currency_points', 'currency_fhq', 'currency_xfq', 'currency_xnb', 'parent_code', 'inviter_id', 'invite_code', 'vip_level', 'svip_level', 'vip_status', 'realname', 'idcard', 'auth_time', 'auth_state'
        ]);
        // Auth 读取
        $this->auth->getAllowFields(['id', 'username', 'nickname', 'mobile', 'avatar', 'level', 'gender', 'birthday', 'bio', 'money', 'score', 'successions', 'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'jointime', 'parent_code', 'inviter_id', 'invite_code', 'realname', 'auth_time', 'auth_state']);
    }

    /**
     * 会员登录
     * @ApiMethod   (POST)
     * @param string $account 账号
     * @param string $password 密码
     */
    public function login()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $account = $this->request->post('account');
            $password = $this->request->post('password');
            $client_id = $this->request->post('client_id');
            if (!$account || !$password) {
                $this->error(__('Invalid parameters'));
            }
            $ret = $this->auth->login($account, $password);
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 手机验证码登录
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $captcha = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            if (!$mobile || !$captcha) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
                $this->error(__('Captcha is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if ($user) {
                if ($user->status != 'normal') {
                    $this->error(__('Account is locked'));
                }
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($user->id);
            } else {
                //拓展字段
                $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, $this->getExtendInfo());
            }
            if ($ret) {
                Sms::flush($mobile, 'mobilelogin');
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 小程序手机号登录
     * @ApiMethod   (POST)
     * @param string $encryptedData
     * @param string $iv
     */
    public function phone()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            if (!isset($post['iv'])) {
                $this->error(__('获取手机号异常'));
            }
            // 1.1.9升级 改为Easywechat
            try {
                $auth = Easywechat::app()
                    ->auth
                    ->session($post['code']);
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
            if (isset($auth['errcode'])) {
                $this->error($auth['errmsg']);
            }
            // 判断third是否存在ID,存在快速登录
            if (isset($auth['unionid'])) {
                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'unionid' => $auth['unionid']]);
            } else {
                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'openid' => $auth['openid']]);
            }

            //如果已经有账号则直接登录
            if ($third && $third['user_id'] != 0) {
                $ret = $this->auth->direct($third['user_id']);
            } else {
                // 手机号解码
                try {
                    $encryptor = Easywechat::app()
                        ->encryptor
                        ->decryptData($auth['session_key'], $post['iv'], $post['encryptedData']);
                } catch (\Exception $e) {
                    $this->error($e->getMessage());
                }
                // 开始登录
                $mobile = $encryptor['phoneNumber'];
                $user = \app\common\model\User::getByMobile($mobile);
                if ($user) {
                    if ($user->status != 'normal') {
                        $this->error(__('Account is locked'));
                    }
                    //如果已经有账号则直接登录
                    $ret = $this->auth->direct($user->id);
                } else {
                    $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, $this->getExtendInfo());
                }
            }
            if ($ret) {
                if (isset($post['client_id']) && $post['client_id'] != null) {
                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }


    /**
     * 注册会员
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $code 验证码
     */
    public function register()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $code = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $ret = Sms::check($mobile, $code, 'register');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, $this->getExtendInfo());
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Sign up successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 注销登录
     */
    public function logout($client_id = null)
    {
        // 踢出即时通讯 1.2.0升级
        foreach ($this->wanlchat->getUidToClientId($this->auth->id) as $client_id) {
            $this->wanlchat->destoryClient($client_id);
        }
        // 退出登录
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     * @ApiMethod   (POST)
     *
     * @param string $avatar 头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio 个人简介
     */
    public function profile()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
            if ($avatar) {
                $user->avatar = $avatar;
            } else {
                $username = $this->request->post('username');
                $nickname = $this->request->post('nickname');
                $bio = $this->request->post('bio');
                // 1.1.9升级 生日和性别并不会提交到后台保存
                $gender = $this->request->post('gender');
                $birthday = $this->request->post('birthday');
                // 1.1.9升级 优化为Easywechat
                if ($bio) {
                    $bioCheck = true;
                    try {
                        $security = Easywechat::app()
                            ->content_security
                            ->checkText($bio);
                        if ($security['errcode'] == 87014) {
                            $bioCheck = false;
                        }
                    } catch (\Exception $e) {
                        $this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：' . $e->getMessage());
                    }
                    if (!$bioCheck) {
                        $this->error(__('风控审核：签名包含敏感词汇'));
                    }
                }
                if ($nickname) {
                    $nicknameCheck = true;
                    try {
                        $security = Easywechat::app()
                            ->content_security
                            ->checkText($nickname);
                        if ($security['errcode'] == 87014) {
                            $nicknameCheck = false;
                        }
                    } catch (\Exception $e) {
                        $this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：' . $e->getMessage());
                    }
                    if (!$nicknameCheck) {
                        $this->error(__('风控审核：昵称包含敏感词汇'));
                    }
                }
                if ($username) {
                    $usernameCheck = true;
                    try {
                        $security = Easywechat::app()
                            ->content_security
                            ->checkText($username);
                        if ($security['errcode'] == 87014) {
                            $usernameCheck = false;
                        }
                    } catch (\Exception $e) {
                        $this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：' . $e->getMessage());
                    }
                    if (!$usernameCheck) {
                        $this->error(__('风控审核：用户名包含敏感词汇'));
                    }
                    $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
                    if ($exists) {
                        $this->error(__('Username already exists'));
                    }
                    $user->username = $username;
                }
                $user->nickname = $nickname;
                $user->bio = $bio;
                // 1.1.9升级 生日和性别并不会提交到后台保存
                $user->gender = $gender;
                $user->birthday = $birthday;

            }
            $user->save();
            $this->success('返回成功', $user);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 修改手机号
     * @ApiMethod   (POST)
     * @param string $email 手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $mobile = $this->request->request('mobile');
            $captcha = $this->request->request('captcha');
            if (!$mobile || !$captcha) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
                $this->error(__('Mobile already exists'));
            }
            $result = Sms::check($mobile, $captcha, 'changemobile');
            if (!$result) {
                $this->error(__('Captcha is incorrect'));
            }
            $verification = $user->verification;
            $verification->mobile = 1;
            $user->verification = $verification;
            $user->mobile = $mobile;
            $user->save();

            Sms::flush($mobile, 'changemobile');
            $this->success();
        }
        $this->error(__('非法请求'));
    }

    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $newpassword 新密码
     * @param string $captcha 验证码
     */
    public function resetpwd()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post("mobile");
            $newpassword = $this->request->post("newpassword");
            $captcha = $this->request->post("captcha");
            if (!$newpassword || !$captcha || !$mobile) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changepwd($newpassword, '', true);
            if ($ret) {
                $this->success(__('Reset password successful'));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }


     /**
     * 重置支付密码
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $newpassword 新密码
     * @param string $captcha 验证码
     */
    public function resetpaypwd()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post("mobile");
            $newpassword = $this->request->post("newpassword");
            $captcha = $this->request->post("captcha");
            if (!$newpassword || !$captcha || !$mobile) {
                $this->error(__('Invalid parameters'));
            }
           if (!Validate::regex($mobile, "^1\d{10}$")) {
               $this->error(__('Mobile is incorrect'));
           }
            $user = $this->auth;
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpaypwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpaypwd');
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changepaypwd($newpassword, '', true);
            if ($ret) {
                $this->success(__('Reset password successful'));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 第三方登录-web登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     */
    public function third_web()
    {
        $this->error(__('暂未开放'));
    }


    /**
     * 第三方登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     * @param string $code Code码
     */
    public function third()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 获取登录配置
            $config = get_addon_config('wanlshop');
            // 获取前端参数
            $post = $this->request->post();
            // 登录项目
            $time = time();
            $platform = $post['platform'];
            // 开始登录
            switch ($platform) {
                // 微信小程序登录
                case 'mp_weixin':
                    // 1.1.9升级 改为Easywechat
                    try {
                        $auth = Easywechat::app()
                            ->auth
                            ->session($post['loginData']['code']);
                    } catch (\Exception $e) {
                        $this->error($e->getMessage());
                    }
                    if (isset($auth['errcode'])) {
                        $this->error($auth['errmsg']);
                    }
                    if (isset($auth['unionid'])) {
                        $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $auth['unionid']]);
                    } else {
                        $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $auth['openid']]);
                    }
                    // 成功登录
                    if ($third) {
                        $user = model('app\common\model\User')->get($third['user_id']);
                        if (!$user) {
                            $this->success('尚未绑定用户', [
                                'binding' => 0,
                                'token' => $third['token']
                            ]);
                        }
                        $third->save([
                            'access_token' => $auth['session_key'],
                            'expires_in' => 7776000,
                            'logintime' => $time,
                            'expiretime' => $time + 7776000
                        ]);
                        $ret = $this->auth->direct($user->id);
                        if ($ret) {
                            if (isset($post['client_id']) && $post['client_id'] != null) {
                                $this->wanlchat->bind($post['client_id'], $this->auth->id);
                            }
                            $this->success(__('Sign up successful'), self::userInfo());
                        } else {
                            $this->error($this->auth->getError());
                        }
                    } else {
                        // 新增$third
                        $third = model('app\api\model\wanlshop\Third');
                        $third->platform = 'weixin_open';
                        if (isset($auth['unionid'])) {
                            $third->unionid = $auth['unionid'];
                        } else {
                            $third->openid = $auth['openid'];
                        }
                        $third->access_token = $auth['session_key'];
                        $third->expires_in = 7776000;
                        $third->logintime = $time;
                        $third->expiretime = $time + 7776000;
                        // 判断当前是否登录
                        if ($this->auth->isLogin()) {
                            if (isset($post['client_id']) && $post['client_id'] != null) {
                                $this->wanlchat->bind($post['client_id'], $this->auth->id);
                            }
                            $third->user_id = $this->auth->id;
                            $third->save();
                            // 直接绑定自动完成
                            $this->success('绑定成功', [
                                'binding' => 1
                            ]);
                        } else {
                            $third->token = Random::uuid();
                            $third->save();
                            // 通知客户端绑定
                            $this->success('尚未绑定用户', [
                                'binding' => 0,
                                'token' => $third->token
                            ]);
                        }
                    }
                    break;

                // 微信App登录
                case 'app_weixin':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token'],
                        'openid' => $post['loginData']['authResult']['openid']
                    ];
                    $result = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $params, 'GET');
                    if ($result['ret']) {
                        $json = (array)json_decode($result['msg'], true);
                        if (isset($json['unionid'])) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $json['unionid']]);
                        } else {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                        }
                        // 成功登录
                        if ($third) {
                            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'refresh_token' => $post['loginData']['authResult']['refresh_token'],
                                'expires_in' => $post['loginData']['authResult']['expires_in'],
                                'logintime' => $time,
                                'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'weixin_open';
                            if (isset($json['unionid'])) {
                                $third->unionid = $json['unionid'];
                            } else {
                                $third->openid = $json['openid'];
                            }
                            $third->access_token = $post['loginData']['authResult']['access_token'];
                            $third->refresh_token = $post['loginData']['authResult']['refresh_token'];
                            $third->expires_in = $post['loginData']['authResult']['expires_in'];
                            $third->logintime = $time;
                            $third->expiretime = $time + $post['loginData']['authResult']['expires_in'];
                            // 判断当前是否登录,否则注册
                            if ($this->auth->isLogin()) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $username = $json['nickname'];
                                $auth = [];
                                $mobile = '';
                                $gender = $json['sex'] == 1 ? 1 : 0;
                                $avatar = $json['headimgurl'];
                                // 1.1.3升级
                                if (isset($json['unionid'])) {
                                    // 1.1.3升级 查询其他unionid的user_id进行登录
                                    $unionid = model('app\api\model\wanlshop\Third')
                                        ->where('user_id', '<>', 0)
                                        ->where('unionid', '=', $json['unionid'])
                                        ->find();
                                    if ($unionid) {
                                        $auth = $this->auth->direct($unionid['user_id']);
                                    } else {
                                        // 注册账户
                                        $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                                'gender' => $gender,
                                                'nickname' => $username,
                                                'avatar' => $avatar
                                            ] + $this->getExtendInfo());
                                    }
                                } else {
                                    // 注册账户
                                    $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                            'gender' => $gender,
                                            'nickname' => $username,
                                            'avatar' => $avatar
                                        ] + $this->getExtendInfo());
                                }
                                if ($auth) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        }
                    } else {
                        $this->error('API异常，App登录失败');
                    }
                    break;
                // 微信公众号登录
                case 'h5_weixin':
                    $params = [
                        'appid' => $config['sdk_qq']['gz_appid'],
                        'secret' => $config['sdk_qq']['gz_secret'],
                        'code' => $post['code'],
                        'grant_type' => 'authorization_code'
                    ];
                    $result = Http::sendRequest('https://api.weixin.qq.com/sns/oauth2/access_token', $params, 'GET');
                    if ($result['ret']) {
                        $access = (array)json_decode($result['msg'], true);
                        //获取用户信息
                        $queryarr = [
                            "access_token" => $access['access_token'],
                            "openid" => $access['openid']
                        ];
                        $ret = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $queryarr, 'GET');
                        if ($ret['ret']) {
                            $json = (array)json_decode($ret['msg'], true);
                            if (isset($json['unionid'])) {
                                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'unionid' => $json['unionid']]);
                            } else {
                                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'openid' => $json['openid']]);
                            }
                            // 成功登录
                            if ($third) {
                                $third->save([
                                    'openid' => $json['openid'], // 1.1.2升级
                                    'access_token' => $access['access_token'],
                                    'refresh_token' => $access['refresh_token'],
                                    'expires_in' => $access['expires_in'],
                                    'logintime' => $time,
                                    'expiretime' => $time + $access['expires_in']
                                ]);
                                // 登录客户端
                                $ret = $this->auth->direct($third['user_id']);
                                if ($ret) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model('app\api\model\wanlshop\Third');
                                $third->platform = 'weixin_h5';
                                // 1.1.2升级
                                if (isset($json['unionid'])) {
                                    $third->unionid = $json['unionid'];
                                    $third->openid = $json['openid'];
                                } else {
                                    $third->openid = $json['openid'];
                                }
                                $third->access_token = $access['access_token'];
                                $third->refresh_token = $access['refresh_token'];
                                $third->expires_in = $access['expires_in'];
                                $third->logintime = $time;
                                $third->expiretime = $time + $access['expires_in'];
                                // 获取到的用户信息
                                $username = $json['nickname'];
                                $auth = [];
                                $mobile = '';
                                $gender = $json['sex'] == 1 ? 1 : 0;
                                $avatar = $json['headimgurl'];

                                // 1.1.3升级
                                if (isset($json['unionid'])) {
                                    // 1.1.3升级 查询其他unionid的user_id进行登录
                                    $unionid = model('app\api\model\wanlshop\Third')
                                        ->where('user_id', '<>', 0)
                                        ->where('unionid', '=', $json['unionid'])
                                        ->find();

                                    if ($unionid) {
                                        $auth = $this->auth->direct($unionid['user_id']);
                                    } else {
                                        // 注册账户
                                        $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                                'gender' => $gender,
                                                'nickname' => $username,
                                                'avatar' => $avatar
                                            ] + $this->getExtendInfo());
                                    }
                                } else {
                                    // 注册账户
                                    $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                            'gender' => $gender,
                                            'nickname' => $username,
                                            'avatar' => $avatar
                                        ] + $this->getExtendInfo());
                                }

                                if ($auth) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        } else {
                            $this->error('获取用户信息失败！');
                        }
                    } else {
                        $this->error('获取openid失败！');
                    }
                    break;
                // QQ小程序登录
                case 'mp_qq':
                    $params = [
                        'appid' => $config[$platform]['appid'],
                        'secret' => $config[$platform]['appsecret'],
                        'js_code' => $post['loginData']['code'],
                        'grant_type' => 'authorization_code'
                    ];
                    $result = Http::sendRequest("https://api.q.qq.com/sns/jscode2session", $params, 'GET');
                    if ($result['ret']) {
                        $json = (array)json_decode($result['msg'], true);
                        if (isset($json['unionid'])) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'unionid' => $json['unionid']]);
                        } else {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
                        }
                        // 成功登录
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third['token']
                                ]);
                            }
                            $third->save([
                                'access_token' => $json['session_key'],
                                'expires_in' => 7776000,
                                'logintime' => $time,
                                'expiretime' => $time + 7776000
                            ]);
                            $ret = $this->auth->direct($user->id);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'qq_open';
                            if (isset($json['unionid'])) {
                                $third->unionid = $json['unionid'];
                            } else {
                                $third->openid = $json['openid'];
                            }
                            $third->access_token = $json['session_key'];
                            $third->expires_in = 7776000;
                            $third->logintime = $time;
                            $third->expiretime = $time + 7776000;
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                // 1.1.4升级
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $third->token = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third->token
                                ]);
                            }
                        }
                    } else {
                        $this->error('API异常，微信小程序登录失败');
                    }
                    break;

                // QQ App登录
                case 'app_qq':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token']
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ]
                    ];
                    $result = Http::sendRequest("https://graph.qq.com/oauth2.0/me", $params, 'GET', $options);
                    if ($result['ret']) {
                        $json = (array)json_decode(str_replace(" );", "", str_replace("callback( ", "", $result['msg'])), true);
                        if ($json['openid'] == $post['loginData']['authResult']['openid']) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
                            if ($third) {
                                $user = model('app\common\model\User')->get($third['user_id']);
                                if (!$user) {
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'token' => $third['token']
                                    ]);
                                }
                                $third->save([
                                    'access_token' => $post['loginData']['authResult']['access_token'],
                                    'expires_in' => $post['loginData']['authResult']['expires_in'],
                                    'logintime' => $time,
                                    'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                                ]);
                                $ret = $this->auth->direct($third['user_id']);
                                if ($ret) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model('app\api\model\wanlshop\Third');
                                $third->platform = 'qq_open';
                                $third->openid = $json['openid'];
                                $third->access_token = $post['loginData']['authResult']['access_token'];
                                $third->expires_in = $post['loginData']['authResult']['expires_in'];
                                $third->logintime = $time;
                                $third->expiretime = $time + $post['loginData']['authResult']['expires_in'];
                                // 判断当前是否登录
                                if ($this->auth->isLogin()) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    $third->user_id = $this->auth->id;
                                    $third->save();
                                    // 直接绑定自动完成
                                    $this->success('绑定成功', [
                                        'binding' => 1
                                    ]);
                                } else {
                                    $third->token = Random::uuid();
                                    $third->save();
                                    // 通知客户端绑定
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'token' => $third->token
                                    ]);
                                }
                            }
                        } else {
                            $this->error(__('非法请求，机器信息已提交'));
                        }
                    } else {
                        $this->error('API异常，App登录失败');
                    }
                    break;
                // QQ 网页登录
                case 'h5_qq':
                    // 后续版本上线
                    break;
                // 微博App登录
                case 'app_weibo':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token']
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ],
                        CURLOPT_POSTFIELDS => http_build_query($params),
                        CURLOPT_POST => 1
                    ];
                    $result = Http::post("https://api.weibo.com/oauth2/get_token_info", $params, $options);
                    $json = (array)json_decode($result, true);
                    if ($json['uid'] == $post['loginData']['authResult']['uid']) {
                        $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weibo_open', 'openid' => $json['uid']]);
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third['token']
                                ]);
                            }
                            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'expires_in' => $json['expire_in'],
                                'logintime' => $json['create_at'],
                                'expiretime' => $json['create_at'] + $json['expire_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'weibo_open';
                            $third->openid = $json['uid'];
                            $third->access_token = $post['loginData']['authResult']['access_token'];
                            $third->expires_in = $json['expire_in'];
                            $third->logintime = $json['create_at'];
                            $third->expiretime = $json['create_at'] + $json['expire_in'];
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                // 1.1.4升级
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $third->token = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'token' => $third->token
                                ]);
                            }
                        }
                    } else {
                        $this->error(__('非法请求，机器信息已提交'));
                    }
                    break;

                // 小米App登录
                case 'app_xiaomi':

                    break;

                // 苹果登录
                case 'apple':
                    // 后续版本上线
                    break;
                default:
                    $this->error('暂并不支持此方法登录');
            }
        }
        $this->error(__('10086非正常请求'));
    }

    /**
     * 进一步完善资料
     * @ApiMethod   (POST)
     */
    public function perfect()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 判断token没有绑定 1.1.4升级
            $third = model('app\api\model\wanlshop\Third')
                ->where('token', '=', $post['token'])
                ->find();

            // 当user_id 不为空可以绑定
            if ($third['user_id'] == 0 && $third) {
                $username = $post['nickName'];
                $auth = [];
                $mobile = '';
                $gender = $post['gender'];
                $avatar = $post['avatarUrl'];
                // 1.1.9升级
                if ($username) {
                    $usernameCheck = true;
                    try {
                        $security = Easywechat::app()
                            ->content_security
                            ->checkText($username);
                        if ($security['errcode'] == 87014) {
                            $usernameCheck = false;
                        }
                    } catch (\Exception $e) {
                        $this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：' . $e->getMessage());
                    }
                    if (!$usernameCheck) {
                        $this->error(__('风控审核：用户名包含敏感词汇'));
                    }
                }
                // 1.1.4升级
                if ($third['unionid']) {
                    // 1.1.3升级 查询其他unionid的user_id进行登录
                    $unionid = model('app\api\model\wanlshop\Third')
                        ->where('id', '<>', $third['id'])
                        ->where('unionid', '=', $third['unionid'])
                        ->find();
                    if ($unionid) {
                        $auth = $this->auth->direct($unionid['user_id']);
                    } else {
                        $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                'gender' => $gender,
                                'nickname' => $username,
                                'avatar' => $avatar
                            ] + $this->getExtendInfo());
                    }
                } else {
                    $auth = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                            'gender' => $gender,
                            'nickname' => $username,
                            'avatar' => $avatar
                        ] + $this->getExtendInfo());
                }
                if ($auth) {
                    // 1.1.4升级
                    if (isset($post['client_id']) && $post['client_id'] != null) {
                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                    }
                    // 更新第三方登录
                    $third->save([
                        'user_id' => $this->auth->id,
                        'openname' => $username
                    ]);
                    $this->success(__('Sign up successful'), self::userInfo());
                } else {
                    $this->error($this->auth->getError());
                }
            } else {
                $this->error(__('非法请求，机器信息已提交'));
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 刷新用户中心
     * @ApiMethod   (POST)
     */
    public function refresh()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $this->success(__('刷新成功'), self::userInfo());
        }
        $this->error(__('非法请求'));
    }

    /**
     * 数据统计 - 内部使用，开发者不要调用
     */
    private function userInfo()
    {
        $user_id = $this->auth->id;

        // 查询订单
        $order = model('app\api\model\wanlshop\Order')
            ->where('user_id', $user_id)
            ->select();
        $orderCount = array_count_values(array_column($order, 'state'));

        // 物流列表
        $logistics = [];
        foreach ($order as $value) {
            if ($value['state'] >= 3 && $value['state'] <= 6) {
                //需要查询的订单
            }
        }
        // 统计数量
        $collection = [];
        $concern = [];
        // 1.1.0升级
        $footgoodsprint = [];
        $footgroupsprint = [];
        foreach (model('app\api\model\wanlshop\GoodsFollow')->where('user_id', $user_id)->select() as $row) {
            if ($row['goods_type'] === 'goods') {
                if (model('app\api\model\wanlshop\Goods')->get($row['goods_id'])) {
                    $collection[] = $row['id'];
                }
            } else if ($row['goods_type'] === 'groups') {
                if (model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])) {
                    $collection[] = $row['id'];
                }
            }
        }
        // 1.0.8升级  通过uuid查询足迹
        $uuid = $this->request->server('HTTP_UUID');
        if (!isset($uuid)) {
            $charid = strtoupper(md5($this->request->header('user-agent') . $this->request->ip()));
            $uuid = substr($charid, 0, 8) . chr(45) . substr($charid, 8, 4) . chr(45) . substr($charid, 12, 4) . chr(45) . substr($charid, 16, 4) . chr(45) . substr($charid, 20, 12);
        }
        foreach (model('app\api\model\wanlshop\Record')->where('uuid', $uuid)->select() as $row) {
            if ($row['goods_type'] === 'goods') {
                if (model('app\api\model\wanlshop\Goods')->get($row['goods_id'])) {
                    $footgoodsprint[] = $row['goods_id'];
                }
            } else if ($row['goods_type'] === 'groups') {
                if (model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])) {
                    $footgroupsprint[] = $row['goods_id'];
                }
            }
        }

        // 查询动态 、收藏夹、关注店铺、足迹、红包卡券
        $finish = isset($orderCount[6]) ? $orderCount[6] : 0;
        $pay = isset($orderCount[1]) ? $orderCount[1] : 0;
        $delive = isset($orderCount[2]) ? $orderCount[2] : 0;
        $receiving = isset($orderCount[3]) ? $orderCount[3] : 0;
        $evaluate = isset($orderCount[4]) ? $orderCount[4] : 0;
        // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
        $groups = model('app\api\model\wanlshop\groups\Order')
            ->where('user_id', 'eq', $user_id)
            ->where('state', 'neq', 7)
            ->count();
        return [
            'userinfo' => $this->auth->getUserinfo(),
            'statistics' => [
                'dynamic' => [
                    'collection' => count($collection),
                    'concern' => model('app\api\model\wanlshop\find\Follow')->where('user_id', $user_id)->count(),
                    'footprint' => count(array_flip($footgoodsprint)) + count(array_flip($footgroupsprint)),
                    'coupon' => model('app\api\model\wanlshop\CouponReceive')->where(['user_id' => $user_id, 'state' => '1'])->count(),
                    'accountbank' => model('app\api\model\wanlshop\PayAccount')->where('user_id', $user_id)->count()
                ],
                'order' => [
                    'whole' => $finish + $pay + $delive + $receiving + $evaluate,
                    'groups' => $groups,
                    'pay' => $pay,
                    'delive' => $delive,
                    'receiving' => $receiving,
                    'evaluate' => $evaluate,
                    // 1.1.6升级 退款状态:0=申请退款,1=卖家同意,2=卖家拒绝,3=申请平台介入,4=成功退款,5=退款已关闭,6=已提交物流,7=第三方退款中,8=退款失败
                    'customer' => model('app\api\model\wanlshop\Refund')->where(['state' => ['in', '0,1,2,3,6,7,8'], 'user_id' => $this->auth->id])->count()
                ],
                'logistics' => $logistics
            ]
        ];
    }

    /**
     * 获取评论列表
     *
     * @ApiSummary  (WanlShop 获取我的所有评论)
     * @ApiMethod   (GET)
     *
     * @param string $list_rows 每页数量
     * @param string $page 当前页
     */
    public function comment()
    {
        $list = model('app\api\model\wanlshop\GoodsComment')
            ->where('user_id', $this->auth->id)
            ->field('id,images,score,goods_id,order_goods_id,state,content,createtime')
            ->order('createtime desc')
            ->paginate()
            ->each(function ($data, $key) {
                $data['order_goods'] = $data->order_goods ? $data->order_goods->visible(['id', 'title', 'image', 'price']) : '';
                return $data;
            });
        $this->success('返回成功', $list);
    }

    /**
     * 获取积分明细
     */
    public function scoreLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\common\model\ScoreLog')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }


    /**
     * 获取用户拓展数据信息
     * @return array{inviter_id: int, parent_code: int|array{inviter_id: int|mixed|string, parent_code: int|string}}
     */
    protected function getExtendInfo()
    {
        $invite_info = $this->getInviteInfo($this->request->param('invite_code', ''));
        $invite_info['invite_code'] = $this->getInviteCode();
        return $invite_info;
    }


    /**
     * 获取不重复的邀请码
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    protected function getInviteCode()
    {
        $flag = true;
        while ($flag) {
            $invite_code = Random::alnum(8);
            $flag = (bool)\app\common\model\User::where('invite_code', $invite_code)->find();
        }
        return $invite_code;
    }

    /**
     * 通过邀请码，查询对应的邀请人id
     * @param mixed $invite_code
     */
    protected function getInviteInfo($invite_code)
    {
        $inviter_id = 0;
        $parent_code = 0;
        if ($invite_code) {
            $invite = \app\common\model\User::where('invite_code', $invite_code)->find();
            if ($invite && $this->auth->id != $invite['id']) {//非本人邀请码
                $inviter_id = $invite['id'];
                $parent_code = $invite['parent_code'] . ',' . $inviter_id;
            }
        }
        return [
            'inviter_id' => $inviter_id,
            'parent_code' => $parent_code,
        ];
    }

    /**
     * 查询自己的邀请信息
     */
    public function getInviteUserInfo()
    {
        $user = $this->auth->getUser();
        //校验是否已存在邀请码
        if (!$this->auth->invite_code) {
            $user->invite_code = $this->getInviteCode();
            $user->save();
        }
        $site = Config::get("site");
        $regUrl = $site['regUrl'] . $user->invite_code;
        $this->success('', [
            'nickname' => $user->nickname,
            'username' => $user->username,
            'avatar' => $user->avatar,
            'invite_code' => $user->invite_code,
            'invite_url' => $regUrl,
            'invite_img' => Tool::qcCode($regUrl)
        ]);
    }

    /**
     * 查询自己的邀请信息
     */
    public function getInviteShopInfo()
    {
        $user = $this->auth->getUser();

        $site = Config::get("site");
        $shopUrl = $site['shopUrl'] . $user->id;
        $this->success('', [
            'nickname' => $user->nickname,
            'username' => $user->username,
            'avatar' => $user->avatar,
            'invite_code' => $user->invite_code,
            'invite_url' => $shopUrl,
            'invite_img' => Tool::qcCode($shopUrl)
        ]);
    }

    /**
     * 实名认证
     * @return void
     */
    public function auth()
    {
        $user = $this->auth->getUser();
        if ($user->auth_state == 2) {
            $this->error('您已认证完成，请勿重复认证');
        }
        $realname = $this->request->param('realname');
        $idcard = $this->request->param('idcard');
        if (!$realname || (mb_strlen($realname) < 1 || mb_strlen($realname) > 10)) {
            $this->error('请填写真实姓名');
        }
        if (!$idcard || mb_strlen($idcard) != 18) {
            $this->error('请填写真实身份证号');
        }
        $res = Tencent::getIdCardVerification($idcard, $realname);
        if ($res->Result != 0) {
            $this->error($res->Description, $res);
        }
        $user->realname = $realname;
        $user->idcard = $idcard;
        $user->auth_state = 2;//1未认证，2已认证
        $user->auth_time = date('Y-m-d H:i:s');
        if ($user->save()) {
            $this->success('认证成功', $res);
        }
        $this->error('认证异常，请稍后重试', $res);
    }

    /**
     * 升级-商务代表升级到3合伙人，支付BD
     */
    public function upgradeSvipLevel3()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $amount = 10000;
            $user = model('app\common\model\User')->get($this->auth->id);
            $svip_level = $user['svip_level'];
            $currency_bd = $user['currency_bd'];

            Log::info("upgradeSvipLevel3 商务代表升级到3合伙人 svip_level=$svip_level, currency_bd=$currency_bd");
            if ($svip_level < 2) {
                $this->error(__('请先成为商务代表再升级！'));
            } else if ($svip_level > 2) {
                $this->error(__('您已是商务合伙人无需再升级！'));
            }

            if (!$user || $currency_bd < $amount) {
                $this->error(__('余额不足本次升级支付'));
            }
            Db::startTrans();
            try {
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, $this->auth->id, '商务代表升级合伙人', 'sys', '', 'currency_bd', '1', 0, '商务代表升级合伙人');
                // 生成20个激活码
                $arr = [];
                for ($i = 1; $i <= 20; $i++) {
                    $cardKey = self::generateCardKey(20);
                    $arr[] = ['origin_id' => $this->auth->id,
                        'current_id' => $this->auth->id,
                        'code' => $cardKey,
                        'value' => 200,
                        'status' => 'normal'];
                }
                model('\app\common\model\user\Activecode')->saveAll($arr);
                model('app\common\model\User')->update(['svip_level' => '3'], ['id' => $this->auth->id, 'svip_level' => 2]);
                $row = model('app\admin\model\UserUpgradeLog')->create([
                    'pay_sn' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999),
                    'pay_user_id' => $this->auth->id,
                    'rec_user_id' => $this->auth->inviter_id,
                    'lv_old' => 2,
                    'lv_new' => 3,
                    'item' => '2',
                    'state' => '1',
                    'amount' => 10000
                ]);
                if ($row) {
                    $arr = array();
                    $arr['action'] = 'orderPay';
                    $arr['order_type'] = 'userUpgrade';
                    $arr['order_id'] = $row->id;
                    Hook::listen("com_auto_settlement", $arr);
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success(__('升级成功'));
        }
        $this->error(__('非法请求'));
    }

    private function generateCardKey($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    /**
     * 获取用户级别
     */
    public function getuserlvname()
    {
        $user = $this->auth->getUser();
        $data = \app\common\model\UserVipLevel::where('id', $user['vip_level'])->field('name level_name1')->find();
        if ($user['monthly_ns'] >= 9 && $user['monthly_ns'] == '1' && $user['invite_nums_ns'] >= 9) {
            $data['level_name1'] == '金卡VIP';
            if ($user['svip_level'] > 1) {
                $data['level_name1'] == '钻卡VIP';
            }
        }
        // 昵称下面 lv1 改成 会员/VIP/金卡VIP（monthly_ns >= 9 && ns_status == '1' && invite_nums_ns>= 9）/钻卡VIP （金卡 + svip_level > 1）用户积分去掉 商务代表或合伙人实时显示
        $data['level_name2'] = '';
        if ($user['svip_level'] > 1) {
            $svipObj = \app\common\model\UserSvipLevel::where('id', $user['svip_level'])->field('name')->find();
            if ($data) {
                $data['level_name2'] = $svipObj['name'];
            }
        }
        $this->success('返回成功', $data);
    }

    /**
     * 支付码验证
     */
    public function checkpwd()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 金额
            $type = $this->request->post('type');
            // 支付密码
            $password = $this->request->post('password');
            if ($type == 2) {
                if ($password == '') {
                    $this->error('请输入支付密码');
                }
                if ($password != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                    $this->error('支付密码输入错误');
                }
                //验证通过，存储一定时间，可用于其他接口校验
                Cache::set($this->payWdKey,1,120);
                $this->success('支付密码验证成功', 1);
            }
            $this->error('非法请求');
        }

    }

    /**
     * 首次初始邀请人
     * @return void
     */
    public function initInviter()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile'); // 邀请人手机号
            if (strlen($mobile) != 11) {
                    $this->error('请填写邀请人手机号！');
            }
            if ($this->auth->inviter_id) {
                $this->error('您已经有邀请人，只能设置一次！');
            }
            $inviter = model('\app\common\model\User')->where('mobile', $mobile)->find();
            if (!$inviter) {
                $this->error('邀请人不存在！');
            }
            
            if (strlen($inviter['inviter_id']) < 1) {
                $this->error('邀请人是游客身份，设置失败！');
            }
            

            $parent_code = $inviter['parent_code'].','.$inviter['id'];

            model('\app\common\model\User')->update(['inviter_id'=>$inviter['id'], 'parent_code' => $parent_code], ['id' => $this->auth->id]);

            $this->success('设置成功', 1);
        }
        $this->error('非法请求');
    }

    /**
     * 让利贡献值=fa_store_rebate_log points 累加 status<'2' 对应user_id
     * 消费贡献值=fa_user_rebate_log points 累加 status<'2'对应user_id
     * 最大配额 =fa_user_rebate_log (points 乘以 cap) 累加 status<'2'对应user_id
     * 剩余配额 =fa_user_rebate_log (points 乘以 cap - fh - js) 累加 status<'2'对应user_id
     */
    public function getUserRebateData()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user_id = $this->auth->id;
            $ret = Db::query("select user_id, ifnull(sum(points), 0) val1, ifnull(sum(points), 0) val2, ifnull(sum(points * cap), 0) val3, ifnull(sum(points * cap - fh - js), 0) val4  from fa_store_rebate_log where user_id = $user_id and status < '2'");
            $this->success('支付密码验证成功', $ret);
        }
        $this->error('非法请求');
    }
}