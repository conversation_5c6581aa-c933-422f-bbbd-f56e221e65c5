<?php
namespace app\api\controller\app;

use addons\wanlshop\library\WeixinSdk\Mp;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\OrderGoods;
use app\common\controller\Api;
use app\common\library\Sms as Smslib;
use app\common\library\WeiXin;
use app\common\model\MoneyLog;
use fast\Http;
use think\Cache;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\Hook;
use think\Log;

/**
 * WanlShop订单接口
 */
class Order extends Api
{
    protected $noNeedLogin = [];
	protected $noNeedRight = ['*'];
    
	/**
     * 获取订单列表
     *
     * @ApiSummary  (WanlShop 订单接口获取订单列表)
     * @ApiMethod   (GET)
	 * 2020年5月12日23:25:40
	 *
	 * @param string $state 状态
	 */
    public function getOrderList()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
    	$state = $this->request->request('state');
        if ($state && $state != 0) {
        	$where['state'] = $state;
        }

        $config = get_addon_config('wanlshop');
        $sdk_qq = $config['sdk_qq'];
        $mch_id = $sdk_qq['mch_id'];

        $where['status'] = 'normal';
        $where['user_id'] = $this->auth->id;
		// 列表	
		$list = model('app\api\model\wanlshop\Order')
			->where($where)
			->where('type','love')
			->field('id,shop_id,state,self_pickup,address_id')
			->order('updatetime desc')
			->paginate()
			->each(function($order, $key) use($mch_id) {
				$goods = model('app\api\model\wanlshop\OrderGoods')
					->where(['order_id'=> $order->id])

					->field('id,title,goods_id,image,difference,price,market_price,number,refund_status')
					->select();
				$order['goods'] = $goods;
                $order['self_pickup'] = (bool)$order['self_pickup'];
				// 获取支付 1.1.2升级
				$order['pay'] = model('app\api\model\wanlshop\Pay')
					->where(['order_id' => $order->id, 'type' => 'love'])
					->field('number,price,order_price,fil,order_fil,freight_price,discount_price,actual_payment,pay_no,trade_no,pay_type,integral,integraltype')
					->find();
                if(isset($order['pay'])) {
                    $order['pay']['mch_id'] = $mch_id;
                }
				$order['shop'] = $order->shop?$order->shop->visible(['shopname']):[];
				return $order;
			});
		$list?($this->success('ok',$list)):($this->error(__('网络繁忙')));
    }
    
    /**
	 * 获取购买过的商品
	 *
	 * @ApiSummary  (WanlShop 订单接口获取订单列表)
	 * @ApiMethod   (GET)
	 * 2020年5月12日23:25:40
	 *
	 * @param string $state 状态
	 */
	public function getBuyList()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    $order_ids = [];
	    foreach (model('app\api\model\wanlshop\Order')->where([ 'user_id' => ['eq', $this->auth->id], 'state' => ['in', '2,3,4,6'], 'status' => ['eq', 'normal']])->select() as $order) {
			  $order_ids[] = $order['id'];  
		}
	    $goods = model('app\api\model\wanlshop\OrderGoods')
			->where('order_id', 'in', $order_ids)
			->select();
		// 列表	
		$list = model('app\api\model\wanlshop\Goods')
			->where('id', 'in', array_keys(array_flip(array_column($goods, 'goods_id'))))
			->field('id, image, title, price')
			->order('updatetime desc')
			->paginate();
		$this->success('ok', $list);
	}
    
    
	/**
	 * 查询用户店铺订单记录 
	 *
	 * @ApiSummary  (查询用户店铺订单记录 1.0.2升级)
	 * @ApiMethod   (POST)
	 *
	 * @param string $shop_id 店铺ID
	 */
	public function getOrderListToShop()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		if($this->request->isPost()){
			$shop_id = $this->request->post('shop_id');
			$shop_id ? '':($this->error(__('Invalid parameters')));
			$list = model('app\api\model\wanlshop\Order')
				->where(['shop_id' => $shop_id, 'user_id' => $this->auth->id, 'status' => 'normal'])
				->field('id,shop_id,order_no,state')
				->order('updatetime desc')
				->select();
			// 订单状态:1=待支付,2=待发货,3=待收货,4=待评论,5=售后订单(已弃用),6=已完成,7=已取消
			foreach ($list as $row) {
			    $row['goods'] = model('app\api\model\wanlshop\OrderGoods')
			    	->where(['order_id'=> $row->id])
			    	->field('id,title,image,difference,price,market_price,number,refund_status')
			    	->select();
			}
			$this->success(__('发送成功'), $list);
		}
		$this->error(__('非法请求'));
	}
	
    /**
     * 获取订单详情
     *
     * @ApiSummary  (WanlShop 订单接口获取订单详情)
     * @ApiMethod   (GET)
	 * 
	 * @param string $id 订单ID
	 */
    public function getOrderInfo()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
		$id = $this->request->request('id');
		$id ? $id : ($this->error(__('非法请求')));
        $shopUid = $this->request->request('shopUid');
        $where = ['id' => $id];
        if(!$shopUid) {
            $where['user_id'] = $this->auth->id;
        }

		$order = model('app\api\model\wanlshop\Order')
			->where($where)
			->field('id,shop_id,user_id,order_no,isaddress,express_no,express_name,freight_type,state,createtime,paymenttime,delivertime,taketime,dealtime,verify_time,self_pickup,verify_state')
			->find();
		$order ? $order : ($this->error(__('网络异常')));
        $order['self_pickup'] = (bool)$order['self_pickup'];
        $order['verify_time_text'] = date('Y-m-d H:i:s', $order['verify_time']);
		// 输出配置
		$config = get_addon_config('wanlshop');
		$order['config'] = $config['order'];
		switch ($order['state']) {
			case 1:
				$express = [
					'context' => '付款后，即可将宝贝发出',
					'status' => '尚未付款',
					'time' => date('Y-m-d H:i:s', $order['createtime'])
				];
				break;
			case 2:
				$express = [
					'context' => '商家正在处理订单',
					'status' => '已付款',
					'time' => date('Y-m-d H:i:s', $order['paymenttime'])
				];
				break;
			default: // 获取物流
				$eData = model('app\api\model\wanlshop\KuaidiSub')
					->where(['express_no' => $order['express_no']])
					->find();
                $express = [
                    'status' => '已发货',
                    'context' => '包裹正在等待快递小哥揽收~',
                    'time' => date('Y-m-d H:i:s', $order['delivertime'])
                ];
                if (!$eData) break;
				$ybData = json_decode($eData['data'], true);
				if($ybData){
					$express = $ybData[0];
                    break;
				}
		}
		// 获取物流
		$order['logistics'] = $express;
		// 获取地址
		$order['address'] = model('app\api\model\wanlshop\OrderAddress')
			->where(['order_id' => $id])//, 'user_id' => $this->auth->id])
			->order('isaddress desc')
			->field('id,name,mobile,address,address_name')
			->find();
		// 获取店铺
		$order['shop'] = $order->shop?$order->shop->visible(['shopname','user_id']):[];
		// 获取订单商品
        $orderGoods = model('app\api\model\wanlshop\OrderGoods')
			->where(['order_id'=> $id])
			->field('id,goods_id,title,image,difference,price,market_price,actual_payment,discount_price,freight_price,number,refund_id,refund_status')
			->select();
        $order['goods'] =$orderGoods;
		// 获取支付 1.1.2升级
		$order['pay'] = model('app\api\model\wanlshop\Pay')
			->where(['order_id' => $order->id, 'type' => 'love'])
			->field('id, pay_no, number, price, order_price, freight_price, discount_price, actual_payment, pay_no, trade_no, pay_type,integral,integraltype')
			->find();
        if(isset($order['pay'])) {
            $config = get_addon_config('wanlshop');
            $sdk_qq = $config['sdk_qq'];
            $mch_id = $sdk_qq['mch_id'];
            $order['pay']['mch_id'] = $mch_id;
        }
        //自提信息
        $pickup = [];
        if ($order['self_pickup']) {
            $loginUser = \app\common\model\User::field('id, truename,mobile')->find($order->user_id);
            $good = Goods::field('id, verificate_user')->find($orderGoods[0]['goods_id']);
            $pickupShop = \app\api\model\wanlshop\Shop::field('user_id, return, pickup_address, business_hours')->where('user_id', $good->verificate_user)->find();
            $verUser = \app\common\model\User::field('id, mobile')->find($good->verificate_user);
            $pickup['pickup_address'] = $pickupShop['pickup_address'] ?? $pickupShop['return'];
            $pickup['business_hours'] = $pickupShop['business_hours'] ?? '8：00-18：00';
            $pickup['verifyuser_mobile'] = $verUser['mobile'] ?? '';
            $pickup['pickup_user'] = $loginUser['truename'] ?? '';
            $pickup['pickup_user_mobile'] = $loginUser['mobile'] ?? '';
        }
        $order['pickup'] = !empty($pickup) ? $pickup : new \stdClass();
		$this->success('ok',$order);
    }

    /**
     * 微信小程序订单自提核销
     *
     * @ApiSummary  (WanlShop 微信小程序订单自提核销)
     * @ApiMethod   (POST)
     */
    public function confirmWeChatMiniOrder()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $id ? $id : ($this->error(__('非法请求')));
            $order = model('app\api\model\wanlshop\Order')->find($id);
            if (empty($order['id'])) {
                $this->error(__('订单不存在'));
            }
            $pay = model('app\index\model\wanlshop\Pay')->where(['order_id'=>$order['id']])->find();
            if (empty($pay['trade_no'])){
                $this->error(__('交易号未找到'));
            }
            // 微信支付才需要
            if ($pay['pay_type'] == 1) {
                list($appid, $appsecret) = $this->getAppInfoBySaasId($order['saas_id']);
                if (empty($appid) || empty($appsecret)) {
                    $this->error(__('平台异常'));
                }
                $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret);
                if (!$access_token) {
                    $this->error(__('平台ID异常'));
                }
                $third_info = model('app\index\model\wanlshop\Third')->where(['user_id' => $order['user_id']])->find();
                if (empty($third_info['openid'])) {
                    $this->error(__('用户OpenID未找到'));
                }
                $payer = ['openid' => $third_info['openid']];
                $order_key = [
                    'order_number_type' => 2,
                    'transaction_id' => $pay['trade_no'],
                ];
                $shipping_item = [
                    'item_desc' => $this->goodsInfoByOrderId($order['id']),
                    'contact' => ['consignor_contact' => ''],
                ];
                $logistics_type = 4;
                $shipping_list = [
                    $shipping_item
                ];
                date_default_timezone_set('UTC');
                $upload_time = gmdate("Y-m-d\TH:i:s.120+08:00", time());
                $req = [
                    'order_key' => $order_key,
                    'logistics_type' => $logistics_type,
                    'delivery_mode' => 1,
                    'shipping_list' => $shipping_list,
                    'upload_time' => $upload_time,
                    'payer' => $payer
                ];
                $url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token={$access_token}";
                $result = Http::sendRequest($url, json_encode($req, JSON_UNESCAPED_UNICODE));
                if (isset($result['errcode']) && $result['errcode'] != 0) {
                    $this->error(__($result['errcode']));
                }
                if (isset($result['msg'])) {
                    $msg = json_decode($result['msg'], true);
                    if (isset($msg['errcode']) && $msg['errcode'] == "10060003") {
                        // 微信只有两次发货机会
                        $this->success(__('ok2'));
                    }
                }
            }
            $order->save([
                'delivertime' => time(),
                'state' => 3
            ]);
            $this->success(__('ok'));
        }
        $this->error(__('非法请求'));
    }

    private function goodsInfoByOrderId($order_id = 0) :string
    {
        $str = "";
        $goods = model('app\index\model\wanlshop\OrderGoods')->where(['order_id' => $order_id])->select();
        foreach ($goods as $item) {
            $str .= "{$item['title']}({$item['difference']})*{$item['number']}；";
        }
        return mb_substr($str ,0 , 120);
    }

    private function getAppInfoBySaasId($saas_id)
    {
        $configFile = APP_PATH . "../addons/wanlshop/config.php";
        if($saas_id > 0){
            $configFile = APP_PATH . "../addons/wanlshop/config{$saas_id}.php";
        }
        $config = [];
        if (is_file($configFile)) {
            $configArr = include_once $configFile;
            if (is_array($configArr)) {
                foreach ($configArr as $key => $value) {
                    $config[$value['name']] = $value['value'];
                }
                unset($configArr);
            }
        }
        $name = $config["ini"]["name"];
        return [$config["mp_weixin"]["appid"], $config["mp_weixin"]["appsecret"]];
    }
    
	/**
	 * 确认收货
	 *
	 * @ApiSummary  (WanlShop 订单接口确认收货)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID                       ------ 后续版本优化 Db::startTrans();
	 */
	public function confirmOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
            $shopUid = $this->request->post('shopUid');

			// 判断权限
            $where = ['id' => $id, 'state'=> 3];
            if(!$shopUid) {
                $where['user_id'] = $this->auth->id;
            }
			$order = model('app\api\model\wanlshop\Order')
				->where($where)
				->find();
			if(!$order){
				$this->error(__('订单异常'));
			}
            $goods = null;
            $orderGoods = OrderGoods::where('order_id', $id)->find();
            if ($orderGoods) {
                $goods = Goods::withTrashed()->where('id', $orderGoods->goods_id)->find();
            }
            //帮卖
            if ($goods->activity != 1 || $goods->activity_type != 'dragon') {
                $sc = model('app\common\model\Config')
                    ->where('group', 'settlement')
                    ->where('name', 'service_charge')
                    ->field('value')->find();
                // 获取支付 1.1.2升级
                $pay = model('app\api\model\wanlshop\Pay')->get(['order_id' => $id, 'type' => 'goods']);
                // 平台转款给商家
                controller('addons\wanlshop\library\WanlPay\WanlPay')->money(+($pay['price'] - $pay['commission']) * (1 - $sc['value']), $order['shop']['user_id'], '买家确认收货', 'pay', $order['order_no']);
            }
            // 查询是否有退款
            $refund = model('app\api\model\wanlshop\Refund')
                ->where(['order_id' => $id, 'state' => 4, 'order_type' => 'goods'])
                ->select();
            // 退款存在
            if($refund){
                foreach($refund as $value){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->money(-$value['price'], $order['shop']['user_id'], '该订单存在的退款', 'pay', $order['order_no']);
                }
            }
            // 更新退款
            $order->save(['state' => 4,'taketime' => time()],['id' => $id]);

            $arr = array();
            $arr['action'] = 'orderConfirm';
            $arr['order_id'] = $id;
            Hook::listen("com_auto_settlement", $arr);

            // 维品尚-确认收货调用
            $params = ['action'=>'receivedOrder', 'order_id'=>$id];
            Hook::listen('weipinshang', $params);

            $this->success('ok', $order ? true : false);
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 取消订单
	 *
	 * @ApiSummary  (WanlShop 订单接口取消订单)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function cancelOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			// 判断权限
			$this->getOrderState($id) != 1 ? ($this->error(__('订单异常'))):'';
			$row = model('app\api\model\wanlshop\Order')->get(['id' => $id, 'user_id' => $this->auth->id]);
			$result = $row->allowField(true)->save(['state' => 7]);
			// 还原优惠券 1.0.2升级
			if($row['coupon_id'] != 0){
				model('app\api\model\wanlshop\CouponReceive')->where(['id' => $row['coupon_id'], 'user_id' => $this->auth->id])->update(['state' => 1]);
			}
			// 释放库存 1.0.3升级
			foreach(model('app\api\model\wanlshop\OrderGoods')->all(['order_id' => $row['id']]) as $vo){
				// 查询商品是否需要释放库存
				if(model('app\api\model\wanlshop\Goods')->get($vo['goods_id'])['stock'] == 'porder'){
					model('app\api\model\wanlshop\GoodsSku')->where('id', $vo['goods_sku_id'])->setInc('stock', $vo['number']);
				}
			}

            // 取消订单归还提货券
            $arr = array();
            $arr['action'] = 'cancelOrder';
            $arr['order_id'] = $id;
            Hook::listen("com_auto_settlement", $arr);

            Log::info("cancelOrder: 取消订单".json_encode($arr));

            // 维品尚-确认收货调用
            $params = ['action'=>'cancelOrder', 'order_id'=>$id];
            Hook::listen('weipinshang', $params);

		    $this->success('ok', $result ? true : false);
		}
		$this->error(__('非法请求'));
	}
	
    /**
     * 删除订单
     *
     * @ApiSummary  (WanlShop 订单接口删除订单)
     * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
    public function delOrder()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			// 判断权限
			$state = $this->getOrderState($id);
			$state == 6 || $state == 7 ? '' :($this->error(__('非法请求')));
			$order = model('app\api\model\wanlshop\Order')
				->save(['status' => 'hidden'],['id' => $id]);
			$this->success('ok', $order ? true : false);
		}
		$this->error(__('非法请求'));
    }
	
    
	/**
	 * 修改地址
	 *
	 * @ApiSummary  (WanlShop 订单接口修改地址)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function editOrderAddress()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $order_id = $this->request->post('id');
			$address_id = $this->request->post('address_id');
			$order_id || $address_id ? $order_id : ($this->error(__('非法请求')));
			// 判断权限
			$this->getOrderState($order_id) > 3 ? ($this->error(__('订单异常'))):'';
			// 订单
			$order = model('app\api\model\wanlshop\Order')
				->where(['id' => $order_id, 'user_id' => $this->auth->id])
				->find();
			
			//判断是否修改过
			if($order['isaddress'] == 1){
				$this->error(__('已经修改过一次了'));
			}else{
				// 获取地址
				$address = model('app\api\model\wanlshop\Address')
					->where(['id' => $address_id, 'user_id' => $this->auth->id])
					->find();
				// 修改地址
				$data = model('app\api\model\wanlshop\OrderAddress')->save([
						'user_id' => $this->auth->id,
						'shop_id' => $order->shop_id,
						'order_id'  => $order_id,
						'isaddress' => 1,
						'name' => $address['name'],
						'mobile' => $address['mobile'],
						'address' => $address['province'].'/'.$address['city'].'/'.$address['district'].'/'.$address['address'],
						'address_name' => $address['address_name'],
						'location' => $address['location']
					]);
				$addressid = DB::getLastInsID();
				// 修改状态
				model('app\api\model\wanlshop\Order')->where(['id' => $order_id, 'user_id' => $this->auth->id])->update(['isaddress' => 1,'address_id'=>$addressid]);
				$this->success('ok',$data);
			}
		}
		$this->error(__('非法请求'));
	}
	
	
	/**
	 * 评论订单
	 *
	 * @ApiSummary  (WanlShop 订单接口评论订单)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function commentOrder()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $post = $this->request->post();
			$post ? $post : ($this->error(__('数据异常')));
			$user_id = $this->auth->id;
			// 判断权限
			$this->getOrderState($post['order_id']) != 4 ? ($this->error(__('已经评论过或订单异常'))):'';
			// 生成列表
			$commentData = [];
			foreach ($post['goodsList'] as $value) {
				$commentData[] = [
					'user_id' => $user_id,
					'shop_id' => $post['shop']['id'],
					'order_id' => $post['order_id'],
					'goods_id' => $value['goods_id'],
					'order_goods_id' => $value['id'],
					'order_type' => 'goods',
					'state' => $value['state'],
					'content' => $value['comment'],
					'suk' => $value['difference'],
					'images' => $value['imgList'],
					'score' => round((($post['shop']['describe'] + $post['shop']['service'] + $post['shop']['deliver'] + $post['shop']['logistics']) / 4) ,1),
					'score_describe' => $post['shop']['describe'],
					'score_service' => $post['shop']['service'],
					'score_deliver' => $post['shop']['deliver'],
					'score_logistics' => $post['shop']['logistics'],
					'switch' => 0
				];
				//评论暂不考虑并发，为列表提供好评付款率，减少并发只能写进商品中
				model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('comment');
				if($value['state'] == 0){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('praise');
				}else if($value['state'] == 1){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('moderate');
				}else if($value['state'] == 2){
					model('app\api\model\wanlshop\Goods')->where(['id' => $value['goods_id']])->setInc('negative');
				}
			}
			if(model('app\api\model\wanlshop\GoodsComment')->saveAll($commentData)){
				$order = model('app\api\model\wanlshop\Order')
					->where(['id' => $post['order_id'], 'user_id' => $user_id])
					->update(['state' => 6]);
			}
			//更新店铺评分
			$score = model('app\api\model\wanlshop\GoodsComment')
				->where(['user_id' => $user_id])
				->select();
			// 从数据集中取出
			$describe = array_column($score,'score_describe');
			$service = array_column($score,'score_service');
			$deliver = array_column($score,'score_deliver');
			$logistics = array_column($score,'score_logistics');
			// 更新店铺评分
			model('app\api\model\wanlshop\Shop')
				->where(['id' => $post['shop']['id']])
				->update([
					'score_describe' => bcdiv(array_sum($describe), count($describe), 1),
					'score_service' => bcdiv(array_sum($service), count($service), 1),
					'score_deliver' => bcdiv(array_sum($deliver), count($deliver), 1),
					'score_logistics' => bcdiv(array_sum($logistics), count($logistics), 1)
				]);
		    $this->success('ok',[]);
		}
		$this->error(__('非法请求'));
	}
	
	/**
	 * 获取订单物流状态
	 *
	 * @ApiSummary  (WanlShop 订单接口获取订单物流状态)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	public function getLogistics()
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    if ($this->request->isPost()) {
		    $id = $this->request->post('id');
			$id ? $id : ($this->error(__('非法请求')));
			//获取订单
			$order = model('app\api\model\wanlshop\Order')
				->where(['id' => $id, 'user_id' => $this->auth->id])
				->field('id,shop_id,express_name,express_no,order_no,createtime,paymenttime,delivertime')
				->find();
			// 获取快递
			switch ($order['state']) {
				case 1:
					$express[] = [
						'context' => '付款后，即可将宝贝发出',
						'status' => '尚未付款',
						'time' => date('Y-m-d H:i:s', $order['createtime'])
					];
					break;
				case 2:
					$express[] = [
						'context' => '商家接受到您的订单，准备出库',
						'status' => '已下单',
						'time' => date('Y-m-d H:i:s', $order['paymenttime'])
					];
					break;
				default: // 获取物流
					$express = model('app\api\model\wanlshop\KuaidiSub')
						->where(['express_no' => $order['express_no']])
						->find();
					if($express){
						$express = json_decode($express['data'], true);
					}else{
						$express[] = [
							'context' => '打包完成，正在等待快递小哥揽收~',
							'status' => '仓库处理中',
							'time' => date('Y-m-d H:i:s', $order['delivertime'])
						];
					}
			}
			$order['kuaidi'] = $order->kuaidi ? $order->kuaidi->visible(['name','logo','tel']) : [];
			$order['express'] = $express;
		    $this->success('ok',$order);
		}
		$this->error(__('非法请求'));
	}
	
	
    /**
     * 确认订单
     *
     * @ApiSummary  (WanlShop 订单接口确认订单)
     * @ApiMethod   (POST)
	 * 
	 * @param string $data 商品数据
	 */
    public function getOrderGoodsList()
    {
		//设置过滤方法
		$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
		    $post = $this->request->post();

		    // 订单数据
		    $order = array();
		    $map = array();
			
			// 1.0.5升级 修复客户端地址更新
			$where = !empty($post['address_id']) ? ['id' => $post['address_id'], 'user_id' => $this->auth->id] : ['user_id' => $this->auth->id, 'default' => 1];
			// 地址
			$address = model('app\api\model\wanlshop\Address')
				->where($where)
			    ->field('id,name,mobile,province,city,district,address')
				->find();
			// 1.0.5升级
			if(!$address) $this->error(__('地址不存在'));
		    // 合计
		    $statis = array(
				"allnum" => 0,
                "allsub" => 0,
                "allfil" => 0
			);

	    	// 商品详情
	    	$goods = model('app\api\model\wanlshop\Goods')
	    		->where('id', $post['goods_id'])
	    	    ->field('id,shop_id,shop_category_id,activity_type,activity_id,title,description,image,stock,freight_id,sales,verificate_user')
	    	    ->find();
	    	// 获取SKU
			$sku = model('app\api\model\wanlshop\GoodsSku')
	    		->where('id', $post['sku_id'])
	    	    ->field('id,difference,price,points,fil,gas,market_price,stock,weigh')
	    	    ->find();
            //自提信息
            $loginUser = $this->auth->getUser();
            $pickup = [];
            $goods->is_pickup = (isset($request['is_pickup']) && $request['is_pickup']);
            //自提信息
            $goods['pickup'] = !empty($pickup) ? $pickup : new \stdClass();

            // 1.1.0升级 库存新增Redis队列
			if ($sku['stock'] <= 0) {
				$this->error('商品'.$goods['title'].'库存不足无法购买');
			} else {
				$redis = self::wanlRedis();
				// 获取商品类型向后兼容
				$goods_activity_type = $goods['activity_type'] ? $goods['activity_type'] : 'goods';
				//当前商品的库存队列
				$goods_number_key = $goods_activity_type.'_'.$goods['id'].'_'.$sku['id'];
				//当前商品队列的用户情况
				$user_queue_key = $goods_number_key.'_user';
				$getUserRedis = $redis->hGetAll("{$user_queue_key}");
				$gnRedis = $redis->llen("{$goods_number_key}"); //返回列表key的长度
				/* 如果没有会员进来队列库存 */
				if(!count($getUserRedis) && !$gnRedis){            
					for ($i = 0; $i < $sku['stock']; $i ++) {
						$redis->lpush("{$goods_number_key}", 1);
					}
				}
				$resetRedis = $redis->llen("{$goods_number_key}");
				if(!$resetRedis){
					$this->error("系统繁忙，请稍后抢购！");
				}
			}
			// 获取快递及价格
			$goods['freight'] = $this->freight($goods['freight_id'], $sku['weigh'], $post['number'], $address['city']);
			// 获取SKU
	    	$goods['sku'] = $sku;
	    	// 数量
	    	$goods['number'] = $post['number'];
		    // 格式化
	        if (empty($map[$goods['shop_id']])) {
	            $order = array(
				    "shop_id"   => $goods['shop_id'],
				    "shop_name" => $goods->shop ? $goods->shop->visible(['shopname'])['shopname']:[],
				    "products"  => [$goods],
					"coupon" => [],
					"freight"  => [$goods['freight']],
				    "number"    => $goods['number'],
                    "sub_fil" => bcmul($sku['fil']+$sku['gas'], $goods['number'], 2),
                    "sub_price" => bcmul($sku['price'], $goods['number'], 2),
				);
	            $map[$goods['shop_id']] = $goods;
	        }
			// 所有店铺统计
			$statis['allnum'] += $goods['number'];

			// 获取运费策略-店铺循环

			$config = model('app\api\model\wanlshop\ShopConfig')
				->where('shop_id',$order['shop_id'])
				->find();
			if($config['freight'] == 0){
				// 运费叠加
				$order['freight'] = [
					'id' => $order['freight'][0]['id'],
					'name' => '运费叠加',
					'price' => array_sum(array_column($order['freight'],'price'))
				];
			}else if($config['freight'] == 1){
				$order['freight'] = [
					'id' => $order['freight'][0]['id'],
					'name' => $order['freight'][0]['name'],
					'price' => $order['freight'][0]['price']
				];
			}else if($config['freight'] == 2){
				$order['freight'] = [
					'id' => $order['freight'][0]['id'],
					'name' => $order['freight'][0]['name'],
					'price' => $order['freight'][0]['price']
				];
			}
			$order['order_price'] = $order['sub_price'];
			// 2020年9月19日12:10:59 添加快递价格备份,用于还原运费
			$order['freight_price_bak'] = $order['freight']['price'];
			// 1.0.8升级
			$order['sub_price'] = bcadd($order['sub_price'], $order['freight']['price'], 2);
            $statis['allsub'] = bcadd($statis['allsub'], $order['sub_price'], 2);
            $statis['allfil'] = bcadd($statis['allfil'], $order['sub_fil'], 2);

            //自提单
            $datalist['is_pickup'] = (isset($request['is_pickup']) && $request['is_pickup']);
			// 传递Token
			$datalist['token'] = self::creatToken();
		    // 地址
		    $datalist['addressData'] = $address;
			// 订单
		    $datalist['orderData'] = $order;
		    $datalist['orderData']['statis'] = $statis;


            $activity_data = model('app\index\model\wanlshop\Ninestar')->where(['id' => $goods['activity_id'], 'state' => 1])->field("back_rule,pv_value,commission,integral,integraltype,pretype,price,validity,startdate,enddate,TIMESTAMPDIFF(SECOND, now(), concat(enddate, \" 23:59:59\")) expire")->find();

            $datalist['integral'] = $activity_data['integral'];

            $datalist['integraltype'] = $activity_data['integraltype'];

            $datalist['integraltext'] = getCurrency($activity_data['integraltype']);


		    $this->success('ok', $datalist);
		} else {
		    $this->error(__('非法请求'));
		}
    }
    
    /**
     * 提交订单
     *
     * @ApiSummary  (WanlShop 订单接口提交订单)
     * @ApiMethod   (POST)
     * 
     * @param string $data 数组
     */
    public function addOrder()
    {
    	//设置过滤方法
    	$this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
			$result = false;
    		$params = $this->request->post();
			// 验证Token
			if(array_key_exists('token', $params)){
				if(!self::checkToken($params['token'])){
					$this->error(__('页面安全令牌已过期！请重返此页'));
				}
			}else{
				$this->error(__('非法提交，未传入Token'));
			}
    		$user_id = $this->auth->id; // 用户ID
			$addressList = [];
			$goodsList = [];
			$payList = [];
			// 1.0.6升级 修复跨店累加
			// $priceAll = 0; // 总价格
			// $numberAll = 0; // 总数量
			// $freightALL = [];
			// $coupon_price = 0; //优惠券金额
			// $discount_price = 0; // 优惠金额，因为后续版本涉及到活动减免，所以优惠金额要单独拎出来
    		// 判断是否有地址 1.0.2升级

            $lists[] = $params['order'];

            if(array_key_exists('address_id',$params['order'])){
                $address_id = $params['order']['address_id']; // 地址ID
            }else{
                $this->error(__('请点击上方添加收货地址'));
            }
          
            // 查询地址
            $address = model('app\api\model\wanlshop\Address')
                ->where(['id' => $address_id,'user_id' =>$user_id])
                ->find();
            // 1.0.4升级
            if(!isset($address)){
                $this->error(__('地址异常，没有找到该地址'));
            }


			// 数据库事务操作
    		Db::startTrans();
    		try {

                // 遍历已店铺分类列表
                foreach ($lists as $item) {
                    // 1.0.6升级 修复跨店累加
                    $priceAll = 0; // 总价格
                    $pointsAll = 0; // 总价格
                    $filAll = 0; // 总FIL
                    $filin = 0; // 总FIL
                    $numberAll = 0; // 总数量
                    $freightALL = [];
                    $coupon_price = 0; //优惠券金额
                    $discount_price = 0; // 优惠金额，因为后续版本涉及到活动减免，所以优惠金额要单独拎出来
                    // 获取店铺ID
                    $shop_id = $item['shop_id'];
                    // 查询店铺配置
                    $config = model('app\api\model\wanlshop\ShopConfig')
                        ->where('shop_id', $shop_id)
                        ->find();
                    // 如果不存在，按照累计运费
                    if(!$config){
                        $config['freight'] = 0;
                    }
                    // 生成订单
                    $order = new \app\api\model\wanlshop\Order;
                    $order->freight_type = $config['freight'];
                    $order->user_id = $user_id;
                    $order->saas_id = $this->auth->saas_id;
                    $order->shop_id = $shop_id;
                    $order->order_no = $shop_id.$user_id;
                    $order->type = 'love';
                    if(isset($item['remarks'])){
                        $order->remarks = $item['remarks'];
                    }
                    // 2020年9月19日 05:30:10 新增优惠券功能
                    // 2021年3月04日 06:54:11 修改优惠券逻辑
                    $coupon = model('app\api\model\wanlshop\CouponReceive')
                        ->where(['id' => $item['coupon_id'], 'user_id' => $user_id, 'shop_id' => $shop_id])
                        ->find();
                    $order->coupon_id = $coupon ? $coupon['id'] : 0;
                    // 要补充活动ID
                    if($order->save()){
                        // 计算订单价格
                        foreach ($item['products'] as $data){
                            // 查询商品
                            $goods = model('app\api\model\wanlshop\Goods')->get($data['goods_id']);
                            // 获取sku
                            $sku = model('app\api\model\wanlshop\GoodsSku')->get($data['sku_id']);
                            // 1.1.0升级
                            !$goods && $this->error("对不起当前商品不存在或已下架！");
                            // 效验shop_id是否正确 1.1.2升级
                            $goods['shop_id'] != $shop_id && $this->error("网络异常SHOPID错误！");
                            // 1.1.0升级 Redis列队
                            $redis = self::wanlRedis();
                            // 获取商品类型向后兼容
                            $goods_activity_type = $goods['activity_type'] ? $goods['activity_type'] : 'goods';
                            //当前商品的库存队列
                            $goods_number_key = $goods_activity_type.'_'.$goods['id'].'_'.$sku['id'];
                            //当前商品队列的用户情况
                            $user_queue_key = $goods_number_key.'_user';
                            /* 进入队列  */
                            $number_key = $redis->llen("{$goods_number_key}");
                            if (!$redis->hGet("{$user_queue_key}", $user_id)) {
                                $number_key = $redis->lpop("{$goods_number_key}");
                            }
                            if($number_key){
                                // 判断用户是否已在队列
                                if (!$redis->hGet("{$user_queue_key}", $user_id)) {
                                    // 插入抢购用户信息
                                    $userinfo = array(
                                        "user_id" => $user_id,
                                        "create_time" => time()
                                    );
                                    $redis->hSet("{$user_queue_key}", $user_id, serialize($userinfo));
                                }

                            }else{
                                $this->error("系统繁忙,请重试！");
                            }
                            // 库存计算方式:porder=下单减库存,payment=付款减库存
                            if($goods['stock'] == 'porder'){
                                $sku->setDec('stock', $data['number']); // 1.0.3升级
                            }
                            // 生成运费
                            $freight = $this->freight($goods['freight_id'], $sku['weigh'], $data['number'], $address['city']);
//                            $freight = [
//                                'id' => 0,
//                                'name' => '合并运费',
//                                'price' => 0
//                            ];
                            // 商品列表 actual_payment
                            $goodsList[] = [
                                'order_id' => $order->id, // 获取自增ID
                                'goods_id' => $goods['id'],
                                'shop_id' => $shop_id,
                                'title' => $goods['title'],
                                'image' => $goods['image'],
                                'goods_sku_sn' => $sku['sn'],
                                'goods_sku_id' => $sku['id'],
                                'difference' => join(',', $sku['difference']),
                                'market_price' => $sku['market_price'], // 市场价
                                'price' => $sku['price'], // 原价
                                'points' => $sku['points'], // 原价
                                'fil' => $sku['fil'], // 原价
                                'gas' => $sku['gas'], // 原价
                                'freight_price' => $freight['price'], //快递价格
                                'discount_price' => 0, // 优惠金额
                                'actual_payment' => bcmul($sku['price'], $data['number'], 2), // 1.0.6修复 实际支付，因为要和总价进行计算
                                'number' => $data['number']
                            ];
                            $freightALL[] = $freight;
                            $priceAll = bcadd($priceAll, bcmul($sku['price'], $data['number'], 2), 2); // 计算价格
                            $pointsAll = bcadd($pointsAll, bcmul($sku['points'], $data['number'], 2), 2); // 计算价格
                            $filAll = bcadd($filAll, bcmul($sku['fil']+$sku['gas'], $data['number'], 2), 2); // 计算价格
                            $filin = bcadd($filin, bcmul($sku['fil'], $data['number'], 2), 2); // 计算价格
                            $numberAll += $data['number']; // 计算数量
                        }
                        // 计算运费叠加方案
                        if($config['freight'] == 0){
                            // 运费叠加
                            $freight = [
                                'id' => $freightALL[0]['id'],
                                'name' => '合并运费',
                                'price' => array_sum(array_column($freightALL,'price'))
                            ];
                        }else if($config['freight'] == 1){ // 以最低结算
                            array_multisort(array_column($freightALL,'price'),SORT_ASC,$freightALL);
                            $freight = [
                                'id' => $freightALL[0]['id'],
                                'name' => $freightALL[0]['name'],
                                'price' => $freightALL[0]['price']
                            ];
                        }else if($config['freight'] == 2){ // 以最高结算
                            array_multisort(array_column($freightALL,'price'),SORT_DESC,$freightALL);
                            $freight = [
                                'id' => $freightALL[0]['id'],
                                'name' => $freightALL[0]['name'],
                                'price' => $freightALL[0]['price']
                            ];
                        }
                        $freight_price = $freight['price'];  //快递金额
                        $price = bcadd($priceAll, $freight_price, 2); // 总价格
                        $points = $pointsAll; // 总价格
                        $fil = $filAll; // 总价格

                        // 如果优惠券存在
                        if($coupon)
                            list($price, $coupon_price, $freight_price, $discount_price, $goodsList) = $this->coupon($coupon, $goodsList, $priceAll, $freight_price, $order->id);
                        // 生成支付
                        $payList[] = [
                            'user_id' => $user_id,
                            'shop_id' => $shop_id,
                            'order_id'  => $order->id,
                            'order_no'  => $order->order_no,
                            'pay_no' => $order->order_no,
                            'type' => 'love', // 1.0.8升级
                            'price'  => $price <= 0 ? 0.01 : $price,  // 支付价格，系统要求至少支付一分钱
                            'points'  => $points,
                            'fil'  => $fil <= 0 ? 0 : $fil,  // 支付价格，系统要求至少支付一分钱
                            'order_price' => $priceAll, // 订单总金额
                            'order_fil' => $filin, // 订单总金额
                            'coupon_price' => $coupon_price,  // 优惠券金额
                            'freight_price' => $freight_price, // 快递费
                            'discount_price' => $discount_price, // 优惠金额
                            'number'  => $numberAll,
                            'integral'  => 1,
                            'integraltype'  => 'ns',
                        ];
                        // 生成地址
                        $addressList[] = [
                            'user_id' => $user_id,
                            'shop_id' => $shop_id,
                            'order_id'  => $order->id,
                            'name' => $address['name'],
                            'mobile' => $address['mobile'],
                            'address' => $address['province'].'/'.$address['city'].'/'.$address['district'].'/'.$address['address'],
                            'address_name' => $address['address_name'],
                            'location' => $address['location']
                        ];
                    }else{
                        $this->error(__('网络繁忙，创建订单失败！'));
                    }
                }
                model('app\api\model\wanlshop\OrderAddress')->saveAll($addressList);
                $addressid = DB::getLastInsID();
                $order->address_id = $addressid;
                $order->save();
                model('app\api\model\wanlshop\OrderGoods')->saveAll($goodsList);
                $result = model('app\api\model\wanlshop\Pay')->saveAll($payList);

    		    Db::commit();
    		} catch (ValidateException $e) {
    		    Db::rollback();
    		    $this->error($e->getMessage());
    		} catch (PDOException $e) {
    		    Db::rollback();
    		    $this->error($e->getMessage());
    		} catch (Exception $e) {
    		    Db::rollback();
    		    $this->error($e->getMessage());
    		}
    		if ($result !== false) {
				$result[0]['token'] = self::creatToken();
				$this->success('返回成功', $result[0]);
    		} else {
    		    $this->error(__('订单异常，请返回重新下单'));
    		}
    	} else {
    	    $this->error(__('非法请求'));
    	}
    }
    
	/**
	 * 订单状态码（方法内使用）
	 *
	 * @ApiSummary  (WanlShop 返回订单状态码)
	 * @ApiMethod   (POST)
	 * 
	 * @param string $id 订单ID
	 */
	private function getOrderState($id = 0)
	{
		//设置过滤方法
		$this->request->filter(['strip_tags']);
	    $order = model('app\api\model\wanlshop\Order')
	    	->where(['id' => $id, 'user_id' => $this->auth->id])
	    	->find();
		return $order['state'];
	}
	
	/**
	 * 获取优惠券后金额 内部方法 
	 * 
	 * @param string $coupon  优惠券数据
	 * @param string $goodsList  商品列表
	 * @param string $priceAll  订单总价格
	 * @param string $freight_price  运费价格
	 * @param string $order_id  订单ID
	 */
	private function coupon($coupon = [], $goodsList = [], $priceAll = 0, $freight_price = 0, $order_id = 0)
	{
		// 判断是否可用
		if($priceAll >= $coupon['limit']){
			if($coupon['type'] == 'reduction' || ($coupon['type'] == 'vip' && $coupon['usertype'] == 'reduction')){
				$coupon_price = $coupon['price']; 
			}
			if($coupon['type'] == 'discount' || ($coupon['type'] == 'vip' && $coupon['usertype'] == 'discount')){
				// 排除异常折扣，还原百分之
				$discount = $coupon['discount'] > 10 ? bcdiv($coupon['discount'], 100, 2) : bcdiv($coupon['discount'], 10, 2);
				// 优惠金额 = 订单金额 - 订单金额 * 折扣
				$coupon_price = bcsub($priceAll, bcmul($priceAll, $discount, 2), 2);
			}
			if($coupon['type'] == 'shipping'){
				$coupon_price = $freight_price;
				$freight_price = 0;
			}
			// 总优惠金额
			$paycoupon = 0;
			// 总实际支付金额
			$payment = 0;   
			// 更新商品列表
			$newGoodsList = [];
			foreach ($goodsList as $row) {
				$price = bcmul($row['price'], $row['number'], 2);
				$discount_price = round($coupon_price * ( $price / $priceAll ), 2); // 优惠金额
				// 1.0.8升级,修复包邮
				$actual_payment = $coupon['type'] === 'shipping' ? $price : bcsub($price, $discount_price, 2);
				//优惠价格
				$row['discount_price'] = $discount_price; 
				// 实际支付 1.0.9升级
				$row['actual_payment'] = $actual_payment <= 0 ? '0.01' : $actual_payment; 
				// 1.0.8升级
				$paycoupon = bcadd($paycoupon, $discount_price, 2); 
				$payment = bcadd($payment, $actual_payment, 2);
				$row['freight_price'] = $freight_price;
				$newGoodsList[] = $row;
			}
			// 更新已使用数量
			model('app\api\model\wanlshop\Coupon')
				->where(['id' => $coupon['coupon_id']])
				->setInc('usenum');
			// 修改该优惠券状态 已用
			$coupon->allowField(true)->save(['state' => 2]);
			//总金额 = 总优惠后金额 + 运费，优惠券金额，快递费，优惠金额，商品列表
			return [bcadd($payment, $freight_price, 2), $paycoupon, $freight_price, $paycoupon, $newGoodsList];
		}else{
			model('app\api\model\wanlshop\Order')->destroy($order_id);
			$this->error('订单金额'.$priceAll.'元，不满'.$coupon['limit'].'元');
		}
	}
	
	
	/**
	 * 获取运费模板和子类 内部方法
	 * @param string $id  运费ID
	 * @param string $weigh  商品重量
	 * @param string $number  商品数量
	 * @param string $city  邮递城市
	 */
	private function freight($id = null, $weigh = null, $number = 0, $city = null)
	{
		// 运费模板
		$data = model('app\api\model\wanlshop\ShopFreight')->where('id', $id)->field('id,delivery,isdelivery,name,valuation')->find();
		$data['price'] = 0;
		// 是否包邮:0=自定义运费,1=卖家包邮
		if($data['isdelivery'] == 0){
			// 获取地址编码 1.1.0升级
			$list = model('app\api\model\wanlshop\ShopFreightData')
				->where([
					['EXP', Db::raw('FIND_IN_SET('.model('app\common\model\Area')->get(['name' => $city])->id.', citys)')],
					'freight_id' => $id
				])
				->find();
			// 查询是否存在运费模板数据
			if(!$list){
				$list = model('app\api\model\wanlshop\ShopFreightData')->get(['freight_id' => $id]);
			}
			
			// 计价方式:0=按件数,1=按重量,2=按体积  1.0.2升级 
			if($data['valuation'] == 0){
				if($number <= $list['first']){
					$price = $list['first_fee'];
				}else{
					$additional = $list['additional'] > 0 ? $list['additional'] : 1; //因为要更换vue后台，临时方案，为防止客户填写0
					$price = bcadd(bcmul(ceil(($number - $list['first']) / $additional), $list['additional_fee'], 2), $list['first_fee'], 2);
				}
			}else{
				$weigh = $weigh * $number; // 订单总重量
				if($weigh <= $list['first']){ // 如果重量小于等首重，则首重价格
					$price = $list['first_fee'];
				}else{
					$additional = $list['additional'] > 0 ? $list['additional'] : 1;
					$price = bcadd(bcmul(ceil(($weigh - $list['first']) / $additional), $list['additional_fee'], 2), $list['first_fee'], 2);
				}
			}
			$data['price'] = $price;
		}
		return $data;
	}
	
	/**
	 * Redis连接
	 */
	private function wanlRedis() 
	{
		if (!extension_loaded('redis')) {
		    $this->error('服务器不支持Redis，请安装Redis和php redis拓展');
		}
		$config = get_addon_config('wanlshop');
		$redis = new \Redis;
		if ($config['redis']['persistent'] == 'Y') {
		    $redis->pconnect($config['redis']['host'], $config['redis']['port'], $config['redis']['timeout'], 'persistent_id_' . $config['redis']['select']);
		} else {
		    $redis->connect($config['redis']['host'], $config['redis']['port'], $config['redis']['timeout']);
		}
		if ('' != $config['redis']['password']) {
		    $redis->auth($config['redis']['password']);
		}
		if (0 != $config['redis']['select']) {
		    $redis->select($config['redis']['select']);
		}
		if('+PONG' != $redis->ping()){
			$this->error($e->getMessage().'或未在后台配置Redis');
		}
		return $redis;
	}


    /**
     * 创建Token
     */
    private function creatToken()
    {
        $code = chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE));
        $key = "YOOYE";
        $code = md5($key . substr(md5($code), 8, 10));
        Cache::store('redis')->set($this->getKey(), $code, 180);
        return $code;
    }

    /**
     * 验证Token
     * @param {Object} $token
     */
    private function checkToken($token)
    {
        $key = $this->getKey();
        if ($token == Cache::store('redis')->get($key)) {
            Cache::store('redis')->set($key, NULL);
            return TRUE;
        } else {
            return FALSE;
        }
    }

    private function getKey()
    {
        return 'orderToken'.$this->auth->id;
    }



    public function checkpwd()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 金额
            $type = $this->request->post('type');
            // 支付密码
            $password = $this->request->post('password');
            if ($type == 2) {
                if ($password == '') {
                    $this->error('请输入支付密码');
                }
                if ($password != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                    $this->error('支付密码输入错误');
                }
                $this->success('支付密码验证成功', 1);
            }
            $this->error('非法请求');
        }

    }

    public function upomimg()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $img = $this->request->post('image');
            $remitinfo = model('app\api\model\wanlshop\OrderMatch')
                ->where(['id' => $id])
                ->where(['pay_user_id' => $this->auth->id])
                ->field('*')
                ->find();
            if ($remitinfo) {
                $params = array();
                $params['image'] = $img;
                if ($remitinfo['state'] == 0) {
                    $params['state'] = 1;
                    $params['paytime'] = time();
                    $uinfo = model('app\common\model\User')->where('id', $remitinfo['rec_user_id'])->field('mobile')->find();
                    Smslib::send($uinfo['mobile'], mt_rand(100000, 999999), 'matchrec');
                }
                model('app\api\model\wanlshop\OrderMatch')->where(['id' => $id, 'pay_user_id' => $this->auth->id])->update($params);
                if ($remitinfo['state'] == 0) $params['paytime_text'] = date("Y-m-d H:i:s", $params['paytime']);
                //所有凭证上传完毕则更改订单状态
                $arr = array();
                $arr['user_id'] = $this->auth->id;
                $arr['order_id'] = $remitinfo['order_id'];
                $arr['withdraw_id'] = $remitinfo['withdraw_id'];
                $arr['operator'] = 'member';
                $arr['action'] = 'payed';
                $params['status'] = Hook::listen("com_auto_settlement", $arr);
                $this->success('凭证上传成功', $params);
            }
        }
        $this->error('非法请求');
    }

    public function remitconfirm()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $remitinfo = model('app\api\model\wanlshop\OrderMatch')
                ->where(['id' => $id])
                ->field('*')
                ->find();
            if ($remitinfo) {
                $params = array();
                if ($remitinfo['state'] == 0) {
                    $this->error('打款凭证未上传无法确认收款');
                }
                if ($remitinfo['state'] == 1) {
                    $params['state'] = 2;
                    $params['confirmtime'] = time();
                }
                model('app\api\model\wanlshop\OrderMatch')->where(['id' => $id, 'rec_user_id' => $this->auth->id])->update($params);
                if ($remitinfo['state'] == 1) $params['confirmtime_text'] = date("Y-m-d H:i:s", $params['confirmtime']);
                //所有凭证上传完毕则更改订单状态
                $arr = array();
                $arr['order_id'] = $remitinfo['order_id'];
                $arr['withdraw_id'] = $remitinfo['withdraw_id'];
                $arr['operator'] = 'member';
                $arr['action'] = 'received';
                Hook::listen("com_auto_settlement", $arr);
                $this->success('确认收款成功', $params);
            }
        }
        $this->error('非法请求');
    }


    /**
     * 核销太阳码
     * @return array|void
     */
    public function getVerifyCode()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isGet()) {
            $params = $this->request->get();
            if (empty($params['page'])) {
                return ['code' => 400,'msg' => '缺少page参数'];
            }
            $params['verify_user'] = 0;
            $orderGood = OrderGoods::field('id, goods_id')->find($params['order_id']);
            if ($orderGood) {
                $good = Goods::field('id, verificate_user')->find($orderGood['goods_id']);
                $params['verify_user'] = $good['verification_user'] ?? 0;
            }

            $params['oid'] = 'order';
            echo WeiXin::instance()->getSunCode($params);
            echo 200;
            exit;
        }
        $this->error('非法请求');
    }

    /**
     * 核销订单
     * @return void
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public function verifyOrder()
    {
        //订单是否存在以及能否核销
        $id = $this->request->get('id', '');
        if (!$id) {
            $this->error('参数错误');
        }
        $order = model('app\api\model\wanlshop\Order')->get($id);

        if (empty($order) || $order->verify_state != 0) {
            $this->error('订单不存在或已核销');
        }

        //查询是否有已核销订单 判断是否为首次核销订单
        $orderone = model('app\api\model\wanlshop\Order')->where(['verify_state'=>1,'user_id'=>$order['user_id']])->find();

		if ($orderone) {
            $this->error('订单非首单核销');
        }

        if (($order['paymenttime']+(48*60*60))<time()) {
            $this->error('订单已超过核销时间，请在支付后48小时内核销。');
        }

        if (!$order->self_pickup) {
            $this->error('此订单不是自提订单');
        }
        $orderGoods = model('app\api\model\wanlshop\OrderGoods')
            ->field('id, goods_id')
            ->where('order_id', $id)
            ->find();
        if (empty($orderGoods)) {
            $this->error('订单不存在商品');
        }
        $goods = Goods::get($orderGoods['goods_id']);
        if (empty($goods)) {
            $this->error('商品不存在');
        }
        if (!$goods->self_pickup) {
            $this->error('此订单不可核销');
        }
        if ($this->auth->city_server != 1) {
            $this->error('您无操作权限');
        }

        //支付订单
        $where = [
            'user_id' => $order->user_id,
            'shop_id' => $order->shop_id,
            'order_id' => $order->id,
            'order_no' => $order->order_no,
            'pay_state' => 1
        ];
        $pay = \app\api\model\wanlshop\Pay::where($where)->find();
        if (empty($pay)) {
            $this->error('订单尚未支付');
        }
        //返佣金额
        $price = $pay->price;
        $backPrice = bcmul(round($price/99, 2), 40, 2);
        //余额
        $leftover = bcsub($price, $backPrice, 2);

        Db::startTrans();
        try {
            //修改订单状态
            $order->verify_state = 1;
            $order->verify_time = time();
            //$order->state = 4;
            $order->save();
            //向核销员反佣
            $this->keepARecordOfTheBalance($this->auth->id, $backPrice, $id);
            //给商家结算
            $shop = \app\api\model\wanlshop\Shop::get($order->shop_id);
            $this->keepARecordOfTheBalance($shop->user_id, $leftover, $id, 'shop');

            $verifyprice = bcmul($pay->price, 0.03, 2);
            //核销百分之三奖励，标记为未结算
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($verifyprice,$this->auth->id,"核销奖励,订单($id)", 'hxreward', '', 'currency_ns',0);

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('核销成功');
    }

    /**
     * 核销结算
     * @param $uid 被记录的id
     * @param $amount 结算金额
     * @param $orderId 订单
     * @return void
     * @throws DbException
     */
    private function keepARecordOfTheBalance($uid, $amount, $orderId, $type = 'user')
    {
        $log = [];
        $user = \app\common\model\User::get($uid);
        $log['before'] = $user->money ?? 0;
        $log['after'] = bcadd($user->money, $amount, 2);
        $user->money = $log['after'];
        $user->save();
        //记录返佣日志
        $log['user_id'] = $uid;
        $log['saas_id'] = SAAS;
        $log['money'] = $amount;
        $log['type'] = 'subsidy';
        $log['memo'] = $type == 'user' ? '核销返佣进账' : '店铺余额结算';
        $log['service_ids'] = $orderId;
        $logModel = new MoneyLog();
        $logModel->save($log);
    }
}