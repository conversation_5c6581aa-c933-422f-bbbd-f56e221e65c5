<?php
namespace app\api\controller\exten;

use app\common\controller\FacadeBase;
use app\common\model\User;
use fast\Random;
use think\Hook;

class UserOrder extends FacadeBase
{

    /** 注册 */
    protected function register($is_new_name = true){
        $user = model('app\common\model\User');
        foreach($this->params as $userdata){
            $key_id = $userdata['id'];
            $rType = 'username';
            if($userdata['username']) $rType = 'username';
//            if($userdata['email']) $rType = 'email';
//            if($userdata['mobile']) $rType = 'mobile';
            if($rType == 'email' || $rType == 'mobile' || $rType == 'username'){
                $checkUser = $user->where([$rType=>$userdata[$rType],'saas_id'=>$userdata['saas_id']])->find();
                if($checkUser) {
                    $checkUser->auth_state != 2 && $userdata['idcard'] && $checkUser->auth_state = 2;
                    !$checkUser->from_app && $checkUser->from_app = $userdata['from_app'];
                    !$checkUser->from_id &&  $checkUser->from_id = $userdata['from_id'];
                    ($checkUser->from_app || $checkUser->from_id) && $checkUser->save();
                    $this->resp[$key_id]['username'] = $checkUser['username'];
                    $this->resp[$key_id]['id'] = $checkUser['id'];
                    $this->resp[$key_id]['msg'] = 'success';
                    continue;
                }
            }
            if($userdata['username_iv']['username_gd'] != 'admin') {
                $inviter_id = 0;
                $parent_code = '0,';
                $userIv = [];
                if($userdata['username_iv']['id'] != null) {
                    $userIv = $user->where(['id' => $userdata['username_iv']['id'], 'saas_id' => $userdata['saas_id']])->field('id,parent_code,m_id')->find();
                    if($userIv) $inviter_id = $userIv['id'];
                }
                if($inviter_id == 0 && $userdata['username_iv']['username_gd'] != null) {
                    $userIv = $user->where(['from_un' => $userdata['username_iv']['username_gd'], 'saas_id' => $userdata['saas_id']])->field('id,parent_code,m_id')->find();
                    if($userIv) $inviter_id = $userIv['id'];
                }
                if($inviter_id == 0 && $userdata['username_iv']['mobile']){
                    $userIv = $user->where(['mobile' => $userdata['username_iv']['mobile'], 'saas_id' => $userdata['saas_id']])->field('id,parent_code,m_id')->find();
                    if($userIv) $inviter_id = $userIv['id'];
                }
                if($inviter_id == 0 && $userdata['username_iv']['email']){
                    $userIv = $user->where(['email' => $userdata['username_iv']['email'], 'saas_id' => $userdata['saas_id']])->field('id,parent_code,m_id')->find();
                    if($userIv) $inviter_id = $userIv['id'];
                }
                if($userIv){
                    $inviter_id = $userIv['id'];
                    $parent_code = $userIv['parent_code'].$userIv['id'].',';
                    $m_id = $userIv['m_id'];
                }
            }else{
                $inviter_id = 1;
                $parent_code = '0,1,';
                $m_id = 0;
            }
            if($inviter_id > 0){
                $userdata['idcard'] ? $userdata['auth_state'] = 2 : $userdata['auth_state'] = 1;
                $userdata['invite_code'] = Random::numeric(10);
                $userdata['inviter_id'] = $inviter_id;
                $userdata['parent_code'] = $parent_code;
                $userdata['m_id'] = $m_id;
                if($is_new_name) {
                    unset($userdata['id']);
                }
                unset($userdata['username_iv']);
                if(strlen($userdata['avatar']) > 255){
                    unset($userdata['avatar']);
                }
                unset($userdata['username_gd']);
                $this->resp[$key_id]['username'] = $userdata['username'];
//                $userdata['username'] = $userdata['mobile']?$userdata['mobile']:($userdata['email']?$userdata['email']:$userdata['username']);

                $this->resp[$key_id]['id'] = $user->allowField(true)->create($userdata,true)->id;
                $arr = array(
                    'action' => 'nsTeamCount',
                    'uid' => $this->resp[$key_id]['id'],
                    'nums' => 1
                );
                Hook::listen("com_auto_settlement", $arr);
                $this->resp[$key_id]['msg'] = 'success';
            }else{
                $this->resp[$key_id]['msg'] = 'not found inviter';
            }
        }
        return $this->resp;
    }

    protected function registerWid(){
        return $this->register(false);
    }

    protected function order(){
        $arr = array();
        $arr['action'] = 'orderAction';
        $arr['order'] = $this->params;
        $resU = [];
        $resO = [];
        if($arr['order']['user_id'] == 0){
            $this->params = [$arr['order']['userInfo']];
            $resU = $this->register();
            if($resU == [] || $resU[$arr['order']['userInfo']['id']]['msg'] != 'success' ){
                $this->resp = $resU;
                return $this->resp;
            }
            $arr['order']['user_id'] = $resU[$arr['order']['userInfo']['id']]['id'];
        }
        if(isset($arr['order']['is_new_name'])) {
            if ($arr['order']['is_new_name'] == false && $arr['order']['user_id'] > 0) {
                $checkUser = model('app\common\model\User')->where('id', $arr['order']['user_id'])->field('id')->find();
                if (!$checkUser) {
                    $this->params = [$arr['order']['userInfo']];
                    $resU = $this->register($arr['order']['is_new_name']);
                    if ($resU == [] || $resU[$arr['order']['userInfo']['id']]['msg'] != 'success') {
                        $this->resp = $resU;
                        return $this->resp;
                    }
                }
            }
        }
        if($arr['order']['user_id'] > 0) {
            $order_info = array();
//            id, createtime,updatetime,status 0new1结算2取消
            $checkOS = model('app\api\model\wanlshop\OrderSync')
//                ->where('act', $arr['order']['act'])
                ->where('order_id', $arr['order']['order_id'])
                ->where('order_no', 'FH'.$arr['order']['order_no'])
                ->find();
            if(!$checkOS){
                $order_info['user_id'] = $arr['order']['user_id'];
                $order_info['order_id'] = $arr['order']['order_id'];
                $order_info['order_no'] = 'FH'.$arr['order']['order_no'];
                $order_info['price'] = $arr['order']['price'];
                $order_info['number'] = $arr['order']['number'];
                $order_info['category_id'] = $arr['order']['category_id'];
                $order_info['activity_type'] = $arr['order']['activity_type'];
                $order_info['act'] = $arr['order']['act'];
                $order_info['status'] = '0';
                if (array_key_exists('flq', $arr['order'])) $order_info['flq'] = $arr['order']['flq'];
                $checkOS = model('app\api\model\wanlshop\OrderSync')->create($order_info);
            }
            if($arr['order']['act'] == 'confirm' && $checkOS['status'] == '1') {
                model('app\api\model\wanlshop\OrderSync')
                    ->where('order_id', $arr['order']['order_id'])
                    ->update(['status' => '2']);
            }
            if($arr['order']['act'] == 'cancel' || $arr['order']['act'] == 'refund') {
                model('app\api\model\wanlshop\OrderSync')
                    ->where('order_id', $arr['order']['order_id'])
                    ->update(['status' => '3']);
            }
            $resO = ['msg' => 'Success', 'order' => $checkOS];
//            $resO = Hook::listen("com_auto_settlement", $arr);
        }
        $this->resp = ['userInfo'=>$resU, 'order'=>$resO];
        return $this->resp;
    }

    protected function autoSyncLv(){
//        $this->setError('功能未开放');
        $res = [];
        $params= $this->params;
        $user = User::where('id', $params['user_id'])->find();
        if(!$user){
            $this->setError('未知用户信息');
        }
        if($params['lv'] > 5) $params['lv'] = 5;
        if($params['lv'] > $user->svip_level) {
            User::where('id', $user->id)->update(['svip_level' => $params['lv']]);
        }
        $this->resp = ['id'=>$params['id'],'user_id'=>$params['user_id'],'fhLv'=>$user->svip_level,'yflLv'=>$params['lv']];
        return $this->resp;
    }

    protected function drOrder(){
        $orderFhdr = model('app\common\model\OrderFhdr');
        $isOrder = true;
        $params = $this->params;
        if($isOrder){
            $user = model('app\common\model\User')->where('id', $params['user_id'])->find();
            if($user) {
                if($user->vip_status == '0') model('app\common\model\User')->where('id', $user->id)->update(['vip_status' => '1']);
                if($user->vip_level == 1) model('app\common\model\User')->where('id', $user->id)->update(['vip_level' => 2]);
                if($user->vip_leveldr == 1) model('app\common\model\User')->where('id', $user->id)->update(['vip_leveldr' => 2]);
                $orderFh = $orderFhdr->where('order_id', $params['id'])->find();
                if ($orderFh) {
                    $params['gd_order_id'] = $orderFh->id;
                    $params['order_id'] = $orderFh->order_id;
                    $params['status'] = $orderFh->status;
                } else {
                    $order_info = array(
                        'order_id' => $params['id'],
                        'user_id' => $params['user_id'],
                        'goods_id' => $params['goods_id'],
                        'price' => $params['price'],
                        'numbers' => $params['numbers'],
                        'paymenttime' => $params['paymenttime']
                    );
                    $params['gd_order_id'] = $orderFhdr->allowField(true)->create($order_info, true)->id;
                    $params['status'] = '0';
                }
                if ($params['status'] == '0') {
                    $userIv = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                    if ($userIv && $userIv->vip_leveldr > 1) $this->cExpertLog($userIv->id, $params['goods_id'], $params['price'], $params['paymenttime']);
                    if($params['numbers'] == 4){
                        if($user->vip_leveldr == 1){
                            $orderArr = array();
                            $orderArr['user_id'] = $user->id;
                            $orderArr['status'] = '2';
                            $orderArr['goods_id'] = $params['goods_id'];
                            $orderArr['price'] = $params['price'];
                            $orderArr['nums'] = 3;
                            model('app\common\model\UserOrderExpert')->create($orderArr);
                        }
                    }
                    $orderFhdr->where('id', $params['gd_order_id'])->update(['status' => '1']);
                    $params['status'] = '1';
                }
            }
            $this->resp = $params;
        }else {
            $UOE = model('app\common\model\UserOrderExpert');
            $params = $this->params;
            if ($params['user']['id_yfl'] != 39027 && $params['user']['id_yfl'] != 37205) $user = User::where('mobile', $params['user']['mobile'])->where('from_app', 'TMS')->where('from_id', $params['user']['id_yfl'])->find();
            else $user = User::where('from_app', 'TMS')->where('from_id', $params['user']['id_yfl'])->find();
            if (!$user) {
                $this->setError('未知用户信息');
            }
            $flag = $UOE->where('id', $params['order']['id'])->find();
            if (!$flag) {
                $params['order']['user_id'] = $user->id;
                $UOE->allowField(true)->create($params['order'], true)->id;
            }
//        if($user->vip_leveldr == 1) User::where('id', $user->id)->update(['vip_leveldr' => 2]);
            $this->resp = ['id' => $params['order']['id'], 'user_id' => $user->id, 'username' => $user->username, 'mobile' => $user->mobile];
        }
        return $this->resp;
    }

    protected function fhUser(){
        $params= $this->params;
        if($params['id_yfl'] != 39027 && $params['id_yfl'] != 37205) {
            $user = User::where('mobile', $params['mobile'])->where('from_app', 'TMS')->where('from_id', $params['id_yfl'])->find();
        } else {
            $user = User::where('from_app', 'TMS')->where('from_id', $params['id_yfl'])->find();
        }
        if(!$user){
            $this->setError('未知用户信息');
        }
        User::where('id', $user->id)->update(['from_fh_id' => $params['user_id']]);
//        if($params['vip_leveldr'] == 2 && $user->vip_leveldr == 1) User::where('id', $user->id)->update(['vip_leveldr' => 2]);
//        if($params['lsid'] > 0) User::where('id', $user->id)->update(['lsid' => $params['lsid']]);
        $this->resp = ['fh_user_id'=>$params['user_id'], 'user_id'=>$user->id, 'username'=>$user->username, 'mobile'=>$user->mobile];
        return $this->resp;
    }

    public function cExpertLog($user_id, $goods_id, $goods_price, $timeline){
        $iveOrder = model('app\common\model\UserOrderExpert')
            ->where('status', '1')
            ->where('user_id', $user_id)
            ->where('goods_id', $goods_id)
            ->where('nums', '<', 3)
            ->order('id', 'asc')->find();
        if ($iveOrder) {
            $orderArr = array(
                'nums' => $iveOrder->nums + 1
            );
            if ($iveOrder->nums == 2) {
                $orderArr['status'] = '2';
                $orderArr['price'] = $goods_price;
                $timestamp2 = time();
                $timestamp1 = $iveOrder->createtime;
                $diffInSeconds = abs($timestamp2 - $timestamp1);
                $days = $diffInSeconds / (60 * 60 * 24);
                $days = floor($days);
                if ($days > 30) $orderArr['price'] = $goods_price * 0.8;
                if ($days > 60) $orderArr['price'] = $goods_price * 0.6;
                if ($days > 90) $orderArr['price'] = $goods_price * 0.5;
            }
            model('app\common\model\UserOrderExpert')->where('id', $iveOrder->id)->update($orderArr);
        }else{
            model('app\common\model\UserOrderExpert')->create([
                'status'=> '1',
                'user_id'=> $user_id,
                'goods_id'=> $goods_id,
                'price'=> 0,
                'nums'=> 1,
                'createtime'=> $timeline
            ]);
        }
    }

    public function ayOrder(){
        $orderSync = model('app\api\model\wanlshop\OrderSync');
        $params = $this->params;
        $user = model('app\common\model\User')->where('id', $params['user_id_gd'])->find();
        if($user) {
            $orderAy = $orderSync->where('type', $params['order_type'])->where('order_id', $params['order_id'])->find();
            if ($orderAy) {
                if($params['orderToGd'] == '1') $params['gd_order_id'] = $orderAy->id;
                if($params['orderToGd'] == '2' && $orderAy->statusb == '2') {
                    $arr = [
                        'action'=>'orderConfirm',
                        'order_id'=>$orderAy->id,
                        'type'=>'OrderSync',
                    ];
                    Hook::listen("com_auto_settlement", $arr);
                    $orderAy = $orderSync->where('type', $params['order_type'])->where('order_id', $params['order_id'])->find();
                    if($orderAy->statusb == '3') $params['gd_order_id'] = $orderAy->id;
                }
            } else {
                $order_info = array(
                    'type' => $params['order_type'],
                    'order_id' => $params['order_id'],
                    'order_no' => $params['order_no'],
                    'user_id' => $params['user_id_gd'],
                    'user_id_ay' => $params['user_id'],
                    'am_gd' => $params['order_price'],
                    'rate_gd' => 0.2,
                    'points_gd' => $params['order_price'] * 0.2,
                    'paymenttime' => $params['order_time']
                );
                $params['gd_order_id'] = $orderSync->allowField(true)->create($order_info, true)->id;
//                $params['status'] = '0';
            }
        }
        $this->resp = $params;
        return $this->resp;
    }

}