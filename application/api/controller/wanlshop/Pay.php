<?php

namespace app\api\controller\wanlshop;

use addons\wanlshop\library\WanlPay\WanlPay;
use app\admin\model\UserViplcLevel;
use app\api\library\KqQuick;
use app\api\service\PriceSrv;
use app\common\controller\Api;
use app\common\model\CurrencyRmbLog;
use app\api\library\CombinationPayment;
use app\common\model\User;
use think\Cache;
use think\Db;
use think\Log;

/**
 * WanlShop支付接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['usdttrxrecharge'];
    protected $noNeedRight = ['*'];

    /**
     * 获取支付信息 ----
     *
     * @ApiSummary  (WanlShop 获取支付信息)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function getPay()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('order_id');
            $id ? $id : ($this->error(__('非法请求')));
            $order_type = $this->request->post('order_type');
            // 1.0.8升级 拼团
            if ($order_type == 'groups') {
                $model_order = model('app\api\model\wanlshop\groups\Order');
            } else {
                $model_order = model('app\api\model\wanlshop\Order');
            }
            // 判断权限
            $orderState = $model_order
                ->where(['id' => $id, 'user_id' => $this->auth->id])
                ->find();
            $orderState['state'] != 1 ? ($this->error(__('订单异常'))) : '';
            // 获取支付信息 1.1.2升级
            $pay = model('app\api\model\wanlshop\Pay')
                ->where(['order_id' => $id, 'type' => $order_type == 'groups' ? 'groups' : 'goods'])
                ->field('id,order_id,order_no,pay_no,price,points,fil')
                ->find();
            $pay['order_type'] = $order_type ? $order_type : 'goods';
            // 传递Token
            $pay['token'] = self::creatToken();
            $pay['flq'] = $orderState['flq'];

            // 查询是否是云店分类商品-前端使用永福莱豆支付
            $order_id = $orderState['id'];
            $vv = DB::query("select category_id,activity_type from fa_wanlshop_goods where id = (select goods_id from `fa_wanlshop_order_goods` where order_id = $order_id limit 1); ");
            $cid = $vv[0]['category_id'];
            $activity_type = $vv[0]['activity_type'];
            $pay['can_ns_pay'] = $cid == 108 || in_array($activity_type, ['vipgoods','shopgoods']); //

            $pay['can_rmb_pay'] = ! CurrencyRmbLog::get(['service_ids'=>$orderState['order_no']]); // 提货券支付过了，再支付就只用支付现金就可以。

            $this->success('ok', $pay);
        }
        $this->error(__('非法请求'));
    }
    public function kqAuthentication()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $order_id = $this->request->post('order_id');
            $order_id ? $order_id : ($this->error(__('非法请求')));
            $card_id = $this->request->post('card_id');
            $type = $this->request->post('type');
            $user_id = $this->auth->id;
            $order=model('app\api\model\wanlshop\Order')->find($order_id);
            if (!$order) {
                $this->error(__('没有找到任何要支付的订单'));
            }
            if($order['state'] != 1) {
                $this->error(__('订单已支付，或网络繁忙'));
            }
            $user= new User();
            $user=$user->find($user_id);
            if(!$user){
                $this->error(__('用户不存在'));
            }
            $card=model('app\api\model\wanlshop\PayAccount')->find($card_id);
            if(!$card){
                $this->error(__('银行卡不存在'));
            }
            // if(!$card->idCard){
            //     $this->error(__('用户未绑定身份证'));
            // }
            $payModel=model('app\api\model\wanlshop\Pay')->where('order_id',$order_id)->find();
            if(!$payModel){
                $this->error(__('没有找到任何要支付的订单'));
            }
            $C=new CombinationPayment($user);
            $pay=$C->calculateTheAmount($payModel->price, $type);
            if($pay===false) {
                $this->error(__('非法参数'));
            }
            $params=[
                'externalRefNumber'=>'YFL'.time().$user_id,
                'user_id'=>$user_id,
                'pan'=>$card->cardCode,
                'name'=>$card->username,
                'idcard'=>$card->idCard,
                'mobile'=>$card->mobile,
                'amount'=>$pay['pay'],
                'protocolVersion'=>'1.0',
            ];
            if($card['cardType']==1){
                $card['expired_date'] = date('m',strtotime($card['expired_date'])).substr(date('y',strtotime($card['expired_date'])),-2);
                $params['expiredDate'] = $card['expired_date'];
                $params['cvv2'] = $card['cvv2'];
            }
            $obj = new KqQuick();
            $ret = $obj->consumeSq($params);
            if(!$ret['code']){
                $this->error(__('鉴权失败:'.$ret['errorMsg']));
            }
            $ret=$ret['data'];
            if($ret['bizResponseCode'] != '0000'){
                $this->error(__($ret['bizResponseMessage']));
            }
            $ret['card_id']=$card_id;
            $order->extend_json=json_encode($ret,JSON_UNESCAPED_UNICODE);
            Db::startTrans();
            if(!$order->save()){
                Db::rollback();
                $this->error(__('鉴权失败,数据异常'));
            }
            if(isset($ret['payToken'])){
                $card['payToken']=$ret['payToken'];
                if(!$card->save()){
                    Db::rollback();
                    $this->error(__('鉴权失败,数据异常2'));
                }
            }
            Db::commit();
            $this->success('短信验证码已发送，请注意查收！');
        }
        $this->error(__('非正常请求'));
    }
    
    public function kqAuthentication2()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $card_id = $this->request->post('card_id');
            $type = $this->request->post('type');
            $price = $this->request->post('price');
            $num = $this->request->post('number');
            $up_user_id = $this->request->param('active_user_id',0);//激活用户id
            $user_id = $this->auth->id;
            $user = new User();
            $user =$user->find($user_id);
            if(!$user){
                $this->error(__('用户不存在'));
            }
            $card = model('app\api\model\wanlshop\PayAccount')->find($card_id);
            if(!$card){
                $this->error(__('银行卡不存在'));
            }
            if (!$up_user_id) {
                $this->error('请选择好友后帮他激活！');
            }
            $up_user = model('\app\common\model\User')->find($up_user_id);
            if (!$up_user) {
                $this->error('选择的好友不存在！');
            }
            if (($up_user['vip_level'] == 2 && $price < 3900) || ($up_user['vip_level'] == 3 && $price < 6500) || ($up_user['vip_level'] == 4 && $price < 13000)) {
                $this->error('该用户无法用此产品升级！');
            }
            $C = new CombinationPayment($user);
            $pay = $C->calculateTheAmount($price, $type);
            if($pay===false) {
                $this->error(__('非法参数'));
            }
            $params=[
                'externalRefNumber'=>'YFL'.time().$user_id,
                'user_id'=>$user_id,
                'pan'=>$card->cardCode,
                'name'=>$card->username,
                'idcard'=>$card->idCard,
                'mobile'=>$card->mobile,
                'amount'=>$pay['pay'],
                'protocolVersion'=>'1.0',
            ];
            if($card['cardType']==1){
                $card['expired_date'] = date('m',strtotime($card['expired_date'])).substr(date('y',strtotime($card['expired_date'])),-2);
                $params['expiredDate'] = $card['expired_date'];
                $params['cvv2'] = $card['cvv2'];
            }
            $obj = new KqQuick();
            $ret = $obj->consumeSq($params);
            if(!$ret['code']){
                $this->error(__('鉴权失败：'.$ret['errorMsg']));
            }
            $ret = $ret['data'];
            if($ret['bizResponseCode'] != '0000'){
                $this->error(__($ret['bizResponseMessage']));
            }
            $ret['card_id']=$card_id;
            $amount = bcmul($price, $num, 2);
            if ($amount % 1300 == 0) {
                $goods_num = bcdiv($amount, 1300, 0);
                $goods_price = 1300;
            } elseif ($amount % 3399 == 0) {
                $goods_num = bcdiv($amount, 3399, 0);
                $goods_price = 3399;
            }
            if(!isset($goods_price)){
                $this->error(__('缺少价格参数！'));
            }
            $flqType = 2;
            $cng_rate = 0.05;
            if ($type == 'yuePu') {
                $flqType = 4;
            } elseif ($type == 'yueCny') {
                if ($user->svip_lv_dkq > 1) {
                    $dkqInfo = \app\admin\model\UserSvipLvDkq::column('id,rate');
                    $cng_rate = $dkqInfo[$user->svip_lv_dkq];
                }
                $flqType = 3;
            }
            $order_no = 'YFL' . substr(time(), -8) . sprintf("%08d", $up_user->id) . mt_rand(1000, 9999);
            $order_info = array();
            $order_info['user_id'] = $up_user->id;
            $order_info['order_id'] = 1;
            $order_info['order_no'] = $order_no;
            $order_info['price'] = $goods_price;
            $order_info['number'] = $goods_num;
            $order_info['category_id'] = 108;
            $order_info['flq'] = $flqType;// 2/3/4
            $order_info['cny_rate'] = $cng_rate;
            $order_info['status'] = '1';
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'newpurchase';
            $order_info['extend_json'] = json_encode($ret,JSON_UNESCAPED_UNICODE);
            $order_info['up_user_id'] = $user_id;//上级用户id
            Db::startTrans();
            $order_id = model('app\api\model\wanlshop\OrderSync')->insertGetId($order_info);
            if(!$order_id){
                Db::rollback();
                $this->error(__('鉴权失败,数据异常'));
            }
            if(isset($ret['payToken'])){
                $card['payToken']=$ret['payToken'];
                if(!$card->save()){
                    Db::rollback();
                    $this->error(__('鉴权失败，数据异常2'));
                }
            }
            Db::commit();
            $this->success('短信验证码已发送，请注意查收！',$order_id);
        }
        $this->error(__('非正常请求'));
    }
    
    /**
     * 支付订单
     *
     * @ApiSummary  (WanlShop 支付订单)
     * @ApiMethod   (POST)
     *
     * @param string $order_id 订单ID
     * @param string $type 支付类型
     */
    public function payment()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $order_id = $this->request->post('order_id/a');
            $order_id ? $order_id : ($this->error(__('非法请求')));
            $order_type = $this->request->post('order_type');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $token = $this->request->post('token');
            $sms=$this->request->post('sms');
            $orderToGd=$this->request->post('orderToGd',0);
            // 验证Token
            if ($token) {
                if (!self::checkToken($token)) {
                    $this->error(__('页面安全令牌已过期！请重返此页'));
                }
            } else {
                $this->error(__('非法提交，未传入Token'));
            }
            $user_id = $this->auth->id;
            // $user_id=534;
            $type ? $type : ($this->error(__('未选择支付类型')));

            // 1.0.8升级 拼团
            if ($order_type == 'groups') {
                $model_order = model('app\api\model\wanlshop\groups\Order');
                $model_order_goods = model('app\api\model\wanlshop\groups\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\groups\Goods');
                $model_goods_sku = model('app\api\model\wanlshop\groups\GoodsSku');
            } else {
                $model_order = model('app\api\model\wanlshop\Order');
                $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\Goods');
                $model_goods_sku = model('app\api\model\wanlshop\GoodsSku');
            }
            
            if($method == 'newPay') {
                // $order_id=$order_id[0];
                $order=$model_order->find($order_id[0]);
                if (!$order) {
                    $this->error(__('没有找到任何要支付的订单'));
                }
                if($order['state'] != 1) {
                    $this->error(__('订单已支付，或网络繁忙'));
                }
                $wanlPay = new WanlPay($type, $method, $code,$orderToGd);
                $data=$wanlPay->newPay($order,$sms);
                if($data['code']==500){
                    $this->error($data['msg']);
                }elseif($data['code']==100){
                    $this->success($data['msg'],$data['data'],100);
                }
                $this->success($data['msg'],0);
            }
            
            if($method!='balancermb' && $type!='balancermb'){
                $this->error('APP请更新新版本，H5请退出重进');
            }
            
            
            // 判断权限
            $order = $model_order
                ->where('id', 'in', $order_id)
                ->where('user_id', $user_id)
                ->select();
            if (!$order) {
                $this->error(__('没有找到任何要支付的订单'));
            }
            foreach ($order as $item) {
                if ($item['state'] != 1) {
                    $this->error(__('订单已支付，或网络繁忙'));
                }
                // 1.0.5升级 修复付款减库存
                foreach ($model_order_goods->where('order_id', $item['id'])->select() as $data) {
                    // 获取sku
                    $sku = $model_goods_sku->get($data['goods_sku_id']);
                    // 查询商品
                    $goods = $model_goods
                        ->where(['id' => $data['goods_id'], 'stock' => 'payment'])
                        ->find();
                    // 库存计算方式:porder=下单减库存,payment=付款减库存 1.0.8升级
                    if ($goods) {
                        $sku->setDec('stock', $data['number']);
                    } else {
                        // 检查库存
                        if ($sku['stock'] < $data['number']) $this->error(__('无法购买，库存不足'));
                    }
                }
            }
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->pay($order_id, $order_type);
            // dump($data);
            if ($data['code'] == 200) {
                // 是否跳转提货宝
                $oid = implode(',', $order_id);
                $ret = DB::query("select gift_type,activity_type from fa_wanlshop_goods where id in(select goods_id from fa_wanlshop_order_goods where order_id in($oid)) limit 1");
                if($ret && count($ret) > 0) {
                    $data['data']['gift_type'] = $ret[0]['gift_type'];
                    $vType = $ret[0]['activity_type'];
                    $data['data']['tip'] = 'false';
//                    // 注释掉先不弹
//                    if($vType == 'vipgoods' || $vType == 'shopgoods') {
//                        $data['data']['tip'] = 'true';
//                        if($vType == 'vipgoods') {
//                            $data['data']['tipMsg'] = '尊敬的用户您好，恭喜您成为999云店店长，为了表达您对我们的信任和支持，平台特为您准备了一份惊喜，如果您能在未来48小时之内累计推荐任意3家云店，就可以得到价值999元的健康大礼包。';
//                        } else {
//                            $data['data']['tipMsg'] = '尊敬的用户您好，恭喜您成为旗舰店长，为了表达您对我们的信任和支持，平台特为您准备了一份惊喜，如果您能在未来48小时之内累计推荐3家旗舰云店，就可以得到价值4995元的健康大礼包。';
//                        }
//                    }
                }
                $this->success('ok', $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户充值
     */
    public function recharge()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $money = $this->request->post('money');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $remitimage = $this->request->post('remitimage') ? $this->request->post('remitimage') : '';
            if ($remitimage == '') $remitimage = $this->request->post('remitimageList') ? $this->request->post('remitimageList') : '';
            $ct = $this->request->post('cindex') ? $this->request->post('cindex') : '';
            $ct = $ct == 0 ? 'currency_bd' : ($ct == 1 ? 'currency_usdt' : ($ct == 2 ? 'currency_xnb' : ($ct == 3 ? 'currency_ns' : '')));
            if ($ct == 'currency_ns') {
                // 城市服务商 或 联创，可以充值积分
                if(! ($this->auth->city_server > 0 || $this->auth->is_securities_firms != 0)) {
                    $this->error("权限不足无法充值");
                }

                // 最低2000

            }

            $user_id = $this->auth->id;
            $type ? $type : ($this->error(__('未选择支付类型')));
            $money ? $money : ($this->error(__('为输入充值金额')));
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->recharge($money, $remitimage, $ct);
            if ($data['code'] == 200) {
                $this->success($data['msg'], $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户充值
     */
    public function rechargeold()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $money = $this->request->post('money');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $user_id = $this->auth->id;
            $type ? $type : ($this->error(__('未选择支付类型')));
            $money ? $money : ($this->error(__('为输入充值金额')));
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->recharge($money);
            if ($data['code'] == 200) {
                $this->success($data['msg'], $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户提现账户
     */
    public function getPayAccount()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $row = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $this->auth->id])
                ->order('createtime desc')
                ->select();
            $this->success('ok', $row);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 新增提现账户
     */
    public function addPayAccount()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post['user_id'] = $this->auth->id;
            if(!isset($post['cardType']) || $post['cardType']==''){
                $this->error(__('请选择卡类型'));
            }
            if($post['cardType']==1){
                $validate = \think\Validate::make([
                    'cardCode'      => 'require|length:4,20|alphaDash',
                    'username'      => 'require|length:2,12|chsAlpha',
                    'mobile'        => 'require|length:11|number',
                    'idCard'        => 'require|length:18',
                    'expired_date'  => 'require',
                    'cvv2'          => 'require'
                ],[
                    'cardCode.require'          =>'请填写账号',
                    'cardCode.length'           =>'账号长度限制为：4~20',
                    'cardCode.alphaDash'        =>'账号格式有误',
                    'username.require'          =>'请填写持卡人姓名',
                    'username.length'           =>'持卡人姓名长度限制：2~12',
                    'username.chsAlpha'         =>'持卡人姓名格式有误，仅支持汉字、字母',
                    'mobile.require'            =>'请填写手机号',
                    'mobile.length'             =>'手机号长度有误',
                    'mobile.number'             =>'手机号必须是数字',
                    'idCard.require'            =>'请填写身份证',
                    'idCard.length'             =>'身份证长度有误',
                    'expired_date.require'      =>'请填写信用卡的有效期',
                    'cvv2.require'              =>'请填写信用卡cvv2',
                ]);
            }else{
                if(isset($post['expired_date'])&&empty($post['expired_date'])){
                    unset($post['expired_date']);
                }
                $validate = \think\Validate::make([
                    'cardCode'  => 'require|length:4,20|alphaDash',
                    'username'  => 'require|length:2,12|chsAlpha',
                    'mobile'    => 'require|length:11|number',
                    'idCard'    => 'require|length:18',
                ],[
                    'cardCode.require'  =>'请填写账号',
                    'cardCode.length'   =>'账号长度限制为：4~20',
                    'cardCode.alphaDash'=>'账号格式有误',
                    'username.require'  =>'请填写持卡人姓名',
                    'username.length'   =>'持卡人姓名长度限制：2~12',
                    'username.chsAlpha' =>'持卡人姓名格式有误，仅支持汉字、字母',
                    'mobile.require'    =>'请填写手机号',
                    'mobile.length'     =>'手机号长度有误',
                    'mobile.number'     =>'手机号必须是数字',
                    'idCard.require'    =>'请填写身份证',
                    'idCard.length'     =>'身份证长度有误',
                ]);
            }
            if (!$validate->check($post)) {
                $this->error($validate->getError());
            }
            $row = model('app\api\model\wanlshop\PayAccount')->allowField(true)->save($post);
            if ($row) {
                $this->success('ok', $row);
            } else {
                $this->error(__('新增失败'));
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 删除提现账户
     */
    public function delPayAccount($ids = '')
    {
        $uid=$this->auth->id ?? 0;
        $card=model('app\api\model\wanlshop\PayAccount')
            ->where('id',  $ids)
            ->where(['user_id' => $uid])
            ->find();
        if(!$card){
            $this->error(__('银行卡不存在'));
        }
        if($card->payToken){
            $kq=new KqQuick();
//            $data=[
//                'pan'=>$card->cardCode,
//                'user_id'=>$uid,
//                'cardType'=>'0002'
//            ];
//            $ret[]=$kq->queryBandCard($data);
            $data=[
                'payToken'=>$card->payToken,
                'user_id'=>$uid
            ];
            $ret=$kq->unBindPCIData($data);
//            $ret='{"code": 1,"errorMsg": "成功","data": {"bizResponseCode": "0000","bizResponseMessage": "交易成功","merchantId": "***************","customerId": "70148"}}';
//            $ret=json_decode($ret,true);
            if($ret['code']==0){
                $this->error(__('删除失败'));
            }
            if(!isset($ret['data'])){
                $this->error(__('删除失败'));
            }
            if($ret['data']['bizResponseCode']!='0000') {
                $this->error(__('删除失败'));
            }
        }
        $row =$card->delete();
        if ($row) {
            $this->success('ok', $row);
        } else {
            $this->error(__('删除失败'));
        }
    }

    /**
     * 初始化提现
     */
    public function initialWithdraw()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $config = get_addon_config('wanlshop');
            $bank = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $this->auth->id])
                ->order('createtime desc')
                ->find();
            $this->success('ok', [
                'money' => $this->auth->money,
                'currencybd' => $this->auth->currency_bd,
                'currencycny' => $this->auth->currency_cny,
                'currencyxnb' => $this->auth->currency_xnb,
                'currencyns' => $this->auth->currency_ns,
                'servicefee' => $config['withdraw']['servicefee'],
                'bank' => $bank
            ]);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户提现
     */
    public function withdraw()
    {
//        $this->error('功能暂时关闭');
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $mtype = $this->request->post('mtype');
            // 金额
            $money = $this->request->post('money');
            // 账户
            $account_id = $this->request->post('account_id');
            if ($money <= 0) {
                $this->error('提现金额不正确');
            }
            if ($mtype != 0&&$mtype != 1) {// && $mtype != 1 && $mtype != 2) {
                $this->error("提现账户选择错误");
            }

            $invoice = '';

            //if($mtype == 0) $mtype = 'currency_cny';
            //提现配置
            $withdraw = 'withdraw';
            if ($mtype == 0){
              $mtype = 'money';  
            }else if($mtype == 1){
                $mtype = 'currency_ns';
                $withdraw = 'withdrawns';
                if($this->request->post('nvoice')!=null&&$this->request->post('nvoice')!=''){
                    $invoice = $this->request->post('nvoice');
                }else{
                    $this->error('申请补助需上传发票');
                }
            } 

//            if($mtype == 1) $mtype = 'currency_bd';
            if ($money > $this->auth->$mtype) {
                $this->error('提现金额超出可提现额度');
            }
            if (!$account_id) {
                $this->error("提现账户不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
                if ($this->auth->vip_level == 1) $this->error('未激活无法提现');
                if (date('H') < 9 || date('H') > 16)
                    $this->error("提现时间为9:00至17:00");
                if ($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
////                if(date('w') != 1 && date('w') != 4 ) $this->error("每周周一周四提现！");
//                if($this->auth->vip_level == 1) $this->error('未激活无法提现');
//                if($mtype != 'money') {
//                    if($this->auth->vip_level < 3) {
//                        $this->error('未达到提现要求');
//                    }else{
//                        $count = model('app\common\model\User')
//                            ->where('inviter_id', $this->auth->id)
//    //                        ->where('vip_status', 1)
//                            ->where('vip_level', 3)
//    //                        ->order('id','asc')
//    //                        ->limit(2)
//                            ->count();
//                        if ($count < 2) {
//                            $this->error("请帮助团队成为总监");
//                        }
//                    }
//                }
//                if (date('H') < 9 || date('H') > 14)
//                    $this->error("提现时间为9:00至14:00");
//                if($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
            }
            // 查询提现账户
            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
            if (!$account) {
                $this->error("提现账户不存在");
            }
            $config = get_addon_config('wanlshop');
//            $config['withdraw']['minmoney'] = 100;
//            $config['withdraw']['multiple'] = 100;
            if ($config[$withdraw]['state'] == 'N') {
                $this->error("系统该关闭提现功能，请联系平台客服");
            }
            if (isset($config[$withdraw]['minmoney']) && $money < $config[$withdraw]['minmoney']) {
                $this->error('提现金额不能低于' . $config[$withdraw]['minmoney'] . '元');
            }
            if (isset($config[$withdraw]['multiple']) && ($money % $config[$withdraw]['multiple'] != 0)) {
                $this->error('提现金额必须是' . $config[$withdraw]['multiple'] . '元的倍数');
            }
            if ($config[$withdraw]['daylimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'today')->count();
                if ($count >= $config[$withdraw]['daylimit']) {
                    $this->error("已达到今日最大可提现次数");
                }
            }
            if ($config[$withdraw]['weeklimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'week')->count();
                if ($count >= $config[$withdraw]['weeklimit']) {
                    $this->error("已达到本周最大可提现次数");
                }
            }
            if ($config[$withdraw]['monthlimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'month')->count();
                if ($count >= $config[$withdraw]['monthlimit']) {
                    $this->error("已达到本月最大可提现次数");
                }
            }
            if ($account['bankCode'] == 'USDT') {
                $this->error("USDT通道暂时关闭");
            }
            if ($account['bankCode'] == 'ALIPAY') {
                $this->error("支付宝通道暂时关闭");
            }
            if ($account['bankCode'] == 'WECHAT') {
                $this->error("微信通道暂时关闭");
            }
            if ($mtype=='money'&&$config[$withdraw]['servicefee'] && $config[$withdraw]['servicefee'] > 0) {
                $servicefee = number_format($money * $config[$withdraw]['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config[$withdraw]['servicefee'] / 1000, 2);
            } else {
//                $servicefee = 0;
                $servicefee = 5;
                $handingmoney = $money - $servicefee;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费
                    'mtype' => $mtype,
                    'type' => $account['bankCode'],
                    'account' => $account['cardCode'],
                    'truename' => $account['username'],
                    'bankname' => $account['bankName'],
                    'bankopen' => $account['bankOpen'],
                    'image' => $account['image'],
                    'nvoice'=> $invoice,
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $usermoney = model('app\common\model\User')->where('id', $this->auth->id)->field($mtype)->find();
                if ($usermoney[$mtype] < $money) {
                    $this->error("提现异常!");
                }
                $withdraw = \app\api\model\wanlshop\Withdraw::create($data);
                $pay = new WanlPay;
                if ($mtype == 'money') $pay->money(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id']);
                else $pay->currency(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id'], $mtype);
//                if($this->auth->id == 1320){
//				    $gluid = 1273;
//                    $glac = \app\api\model\wanlshop\PayAccount::where(['id' => 1310, 'user_id' => $gluid])->find();
//                    $gld = [
//                        'user_id' => $gluid,
//                        'money'   => $handingmoney,
//                        'handingfee' => $servicefee, // 手续费
//                        'type'    => $glac['bankCode'],
//                        'account' => $glac['cardCode'],
//                        'truename' => $glac['username'],
//                        'bankopen' => $glac['bankOpen'],
//                        'orderid' => date("Ymdhis") . sprintf("%08d", $gluid) . mt_rand(1000, 9999)
//                    ];
//                    $glw = \app\api\model\wanlshop\Withdraw::create($gld);
////                    $pay->money(-$money, $this->auth->id, '申请提现', 'withdraw', $glw['id']);
//                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提现申请成功！请等待后台审核', $this->auth->money);
        }
        $this->error(__('非正常请求'));
    }

    public function withdrawhg()
    {
//        $this->error('节假日功能暂时关闭');
        $rsprice = 60;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $mtype = $this->request->post('mtype');
            // 金额
            $money = $this->request->post('money');
            // 账户
            $account_id = $this->request->post('account_id');
            if ($money <= 0) {
                $this->error('回购数量不正确');
            }
            if ($mtype != 0) {// && $mtype != 1 && $mtype != 2) {
                $this->error("回购账户选择错误");
            }
            if ($mtype == 0) $mtype = 'currency_tz';
            if ($mtype == 1) $mtype = 'currency_bd';
            if ($money > $this->auth->$mtype) {
                $this->error('回购数量超出可回购额度');
            }
            if ($money > $this->auth->static_m * 166.5 / 2) {
                $this->error('回购数量超出日限额');
            }
            if (!$account_id) {
                $this->error("回购账户不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
//                if(date('w') != 1 && date('w') != 4 ) $this->error("每周周一周四回购！");
                if ($this->auth->vip_level == 1) $this->error('未激活无法回购');
                if (date('H') < 9 || date('H') > 16)
                    $this->error("回购时间为9:00至17:00");
                if ($this->auth->is_withdraw == 0) $this->error("暂停回购操作");
            }
            // 查询回购账户
            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
            if (!$account) {
                $this->error("回购账户不存在");
            }
//            if($account['bankCode'] == 'WECHAT'){
//                $this->error("微信支付暂时关闭");
//            }
            $config = get_addon_config('wanlshop');
            if ($config['withdraw']['state'] == 'N') {
                $this->error("系统该关闭回购功能，请联系平台客服");
            }
            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('回购数量不能低于' . $config['withdraw']['minmoney'] . '株');
            }
            if (isset($config['withdraw']['multiple']) && ($money % $config['withdraw']['multiple'] != 0)) {
                $this->error('回购数量必须是' . $config['withdraw']['multiple'] . '的倍数');
            }
            if ($config['withdraw']['multiple'] == 1 && strrpos($money, '.') !== false) {
                $this->error('回购数量必须是' . $config['withdraw']['multiple'] . '的倍数');
            }
            if ($config['withdraw']['daylimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'today')->where('mtype', '<>', 'currency_cny')->count();
                if ($count >= $config['withdraw']['daylimit']) {
                    $this->error("已达到今日最大可回购次数");
                }
            }
            if ($config['withdraw']['weeklimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'week')->count();
                if ($count >= $config['withdraw']['weeklimit']) {
                    $this->error("已达到本周最大可回购次数");
                }
            }
            if ($config['withdraw']['monthlimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'month')->count();
                if ($count >= $config['withdraw']['monthlimit']) {
                    $this->error("已达到本月最大可回购次数");
                }
            }
            if ($account['bankCode'] == 'USDT') {
                $this->error("USDT通道暂时关闭");
            }
            if ($account['bankCode'] == 'ALIPAY') {
                $this->error("支付宝通道暂时关闭");
            }
            if ($account['bankCode'] == 'WECHAT') {
                $this->error("微信通道暂时关闭");
            }
            if ($account['bankCode'] != 'SDR' && $config['withdraw']['servicefee'] && $config['withdraw']['servicefee'] > 0) {
                $servicefee = number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
            } else {
//                if($account['bankCode'] == 'USDT'){
//                    $usdtcny = PriceSrv::getCoinRate();
//                    $servicefee = $usdtcny/60;
//                }
//                else
//                $servicefee = 0;
                $servicefee = number_format($money * 0.05, 2);
                $handingmoney = $money - $servicefee;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费payment
                    'mtype' => $mtype,
                    'type' => $account['bankCode'],
                    'account' => $account['cardCode'],
                    'truename' => $account['username'],
                    'bankname' => $account['bankName'],
                    'bankopen' => $account['bankOpen'],
                    'image' => $account['image'],
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $usermoney = model('app\common\model\User')->where('id', $this->auth->id)->field($mtype)->find();
                if ($usermoney[$mtype] < $money) {
                    $this->error("回购异常!");
                }
                $withdraw = \app\api\model\wanlshop\Withdraw::create($data);
                $pay = new WanlPay;
                if ($mtype == 'money') $pay->money(-$money, $this->auth->id, '申请回购', 'withdraw', $withdraw['id']);
                else $pay->currency(-$money, $this->auth->id, '申请回购', 'withdraw', $withdraw['id'], $mtype);
//                if($this->auth->id == 1320){
//				    $gluid = 1273;
//                    $glac = \app\api\model\wanlshop\PayAccount::where(['id' => 1310, 'user_id' => $gluid])->find();
//                    $gld = [
//                        'user_id' => $gluid,
//                        'money'   => $handingmoney,
//                        'handingfee' => $servicefee, // 手续费
//                        'type'    => $glac['bankCode'],
//                        'account' => $glac['cardCode'],
//                        'truename' => $glac['username'],
//                        'bankopen' => $glac['bankOpen'],
//                        'orderid' => date("Ymdhis") . sprintf("%08d", $gluid) . mt_rand(1000, 9999)
//                    ];
//                    $glw = \app\api\model\wanlshop\Withdraw::create($gld);
////                    $pay->money(-$money, $this->auth->id, '申请回购', 'withdraw', $glw['id']);
//                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('回购申请成功！请等待后台审核', $this->auth->money);
        }
        $this->error(__('非正常请求'));
    }

    public function withdrawcoin()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $ctype = $this->request->post('ctype');
            if ($ctype != 'currency_usdt' && $ctype != 'currency_fil') {
                $this->error('提币账户选择异常');
            }
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            // 账户
            $addr = $this->request->post('addr');
            $usdtcny = PriceSrv::getCoinRate();
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('提币金额不正确');
            }
            if ($money > $this->auth->$ctype) {
                $this->error('提币金额超出可提现额度');
            }
            if (!$addr) {
                $this->error("钱包地址不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
                if (date('H') < 10 || date('H') >= 18)
//                    $this->error("提现时间为10:00至18:00");
                    if ($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
            }
            // 查询提现账户
//            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
//            if (!$account) {
//                $this->error("提现账户不存在");
//            }
            $config = get_addon_config('wanlshop');
            if ($config['withdraw']['state'] == 'N') {
                $this->error("系统该关闭提现功能，请联系平台客服");
            }
            if (isset($config['withdraw']['minmoney']) && $money * $usdtcny < $config['withdraw']['minmoney']) {
                $this->error('提现金额不能低于' . $config['withdraw']['minmoney']);
            }
            if ($config['withdraw']['daylimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'today')->count();
                if ($count >= $config['withdraw']['daylimit']) {
                    $this->error("已达到今日最大可提现次数");
                }
            }
            if ($config['withdraw']['weeklimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'week')->count();
                if ($count >= $config['withdraw']['weeklimit']) {
                    $this->error("已达到本周最大可提现次数");
                }
            }
            if ($config['withdraw']['monthlimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'month')->count();
                if ($count >= $config['withdraw']['monthlimit']) {
                    $this->error("已达到本月最大可提现次数");
                }
            }
            // 计算提现手续费
            if ($config['withdraw']['servicefee'] && $config['withdraw']['servicefee'] > 0) {
                $servicefee = number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
            } else {
                $servicefee = 0;
                $handingmoney = $money;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费
                    'type' => $ctype,
                    'account' => $addr,
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $withdraw = \app\api\model\wanlshop\WithdrawCoin::create($data);
                $pay = new WanlPay;
                $pay->currency(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id'], $ctype);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提现申请成功！请等待后台审核', $this->auth->$ctype - $money);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 获取充值日志
     */
    public function rechargeLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\api\model\wanlshop\RechargeOrder')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取提现日志
     */
    public function withdrawLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\api\model\wanlshop\Withdraw')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取支付日志
     */
    public function moneyLog()
    {
        //设置过滤方法
        $type = $this->request->post('type') ? $this->request->post('type') : 'Money';
        if ($type != 'Money') {
            $type = 'Currency' . ucfirst($type);
        }
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model("app\common\model\\{$type}Log")
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            foreach($list as $k=>$v) {
                if($list[$k]['memo'] == '绩效奖D') $list[$k]['memo'] = '绩效奖';
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取支付详情
     */
    public function details($id = null, $type = null)
    {
        if ($type == 'pay') {
            $field = 'id,shop_id,createtime,paymenttime';
            $order = model('app\api\model\wanlshop\Order')
                ->where('order_no', 'in', $id)
                ->where('user_id', $this->auth->id)
                ->field($field)
                ->select();
            //1.0.5升级 临时修改,后续升级版本重构
            if (!$order) {
                $shop = model('app\api\model\wanlshop\Shop')->get(['user_id' => $this->auth->id]);
                $order = model('app\api\model\wanlshop\Order')
                    ->where('order_no', 'in', $id)
                    ->where('shop_id', $shop['id'])
                    ->field($field)
                    ->select();
                if (!$order) $this->error(__('订单异常'));
            }
            foreach ($order as $vo) {
                // 1.1.2升级
                $vo['pay'] = model('app\api\model\wanlshop\Pay')
                    ->where(['order_id' => $vo['id'], 'type' => 'goods'])
                    ->field('price,pay_no,order_no,order_price,trade_no,actual_payment,freight_price,discount_price,total_amount')
                    ->find();
                $vo->shop->visible(['shopname']);
                $vo->goods = model('app\api\model\wanlshop\OrderGoods')
                    ->where(['order_id' => $vo['id']])
                    ->field('id,title,difference,image,price,number')
                    ->select();
            }
            $this->success('ok', $order);
        } else if ($type == 'groups') {
            $field = 'id,shop_id,createtime,paymenttime';
            $order = model('app\api\model\wanlshop\groups\Order')
                ->where('order_no', 'in', $id)
                ->where('user_id', $this->auth->id)
                ->field($field)
                ->select();
            //1.0.5升级 临时修改,后续升级版本重构
            if (!$order) {
                $shop = model('app\api\model\wanlshop\Shop')->get(['user_id' => $this->auth->id]);
                $order = model('app\api\model\wanlshop\groups\Order')
                    ->where('order_no', 'in', $id)
                    ->where('shop_id', $shop['id'])
                    ->field($field)
                    ->select();
                if (!$order) $this->error(__('订单异常'));
            }
            foreach ($order as $vo) {
                // 1.1.2升级
                $vo['pay'] = model('app\api\model\wanlshop\Pay')
                    ->where(['order_id' => $vo['id'], 'type' => 'groups'])
                    ->field('price,pay_no,order_no,order_price,trade_no,actual_payment,freight_price,discount_price,total_amount')
                    ->find();
                $vo->shop->visible(['shopname']);
                $vo->goods = model('app\api\model\wanlshop\groups\OrderGoods')
                    ->where(['order_id' => $vo['id']])
                    ->field('id,title,difference,image,price,number')
                    ->select();
            }
            $this->success('ok', $order);
        } else if ($type == 'recharge' || $type == 'withdraw') { // 用户充值
            if ($type == 'recharge') {
                $model = model('app\api\model\wanlshop\RechargeOrder');
                $field = 'id,paytype,orderid,memo';
            } else {
                $model = model('app\api\model\wanlshop\Withdraw');
                $field = 'id,money,handingfee,status,type,mtype,account,orderid,memo,transfertime';
            }
            $row = $model
                ->where(['id' => $id, 'user_id' => $this->auth->id])
                ->field($field)
                ->find();
            $this->success('ok', $row);
        } else if ($type == 'refund') {
            $order = model('app\api\model\wanlshop\Order')
                ->where('order_no', $id)
                ->where('user_id', $this->auth->id)
                ->field('id,shop_id,order_no,createtime,paymenttime')
                ->find();
            if (!$order) {
                $this->error(__('订单异常'));
            }
            $order->shop->visible(['shopname']);
            $order['refund'] = model('app\api\model\wanlshop\Refund')
                ->where(['order_id' => $order['id'], 'user_id' => $this->auth->id])
                ->field('id,price,type,reason,createtime,completetime')
                ->find();
            $this->success('ok', $order);
        } else { // 系统
            $this->success('ok');
        }
    }

    /**
     * 获取余额
     */
    public function getBalance()
    {
        $type = $this->request->post('type') ? $this->request->post('type') : 'money';
        $this->success('ok', $this->auth->$type);
    }

    /**
     * 获取待结算
     */
    public function getUnBalance()
    {
        $type = $this->request->post('type') ? $this->request->post('type') : 'money';
        $user = $this->auth->getUser();
        $userId = $user['id'];
        // 返回待结算字段 currency_ns fa_user_currency_ns_log
        $tabs = ['currency_ns' => 'CurrencyNsLog',
            'currency_bd' => 'CurrencyBdLog',
            'currency_bdlmt' => 'CurrencyBdlmtLog',
            'currency_nsd' => 'CurrencyNsdLog',
            'currency_cny' => 'CurrencyCnyLog',
            'currency_fil' => 'CurrencyFilLog',
            'currency_gq' => 'CurrencyGqLog',
            'currency_lmt' => 'CurrencyLmtLog',
            'currency_gp' => 'CurrencyGpLog',
            'currency_nfr' => 'CurrencyNfrLog',
            'currency_old' => 'CurrencyOldLog',
            'currency_points' => 'CurrencyPointsLog',
            'currency_pu' => 'CurrencyPuLog',
            'currency_rmb' => 'CurrencyRmbLog',
            'currency_ticket' => 'CurrencyTicketLog',
            'currency_tz' => 'CurrencyTzLog',
            'currency_tzu' => 'CurrencyTzuLog',
            'currency_usdt' => 'CurrencyUsdtLog',
            'currency_xnb' => 'CurrencyXnbLog',
            'currency_blc' => 'CurrencyBlcLog',
            'currency_gfz' => 'CurrencyGfzLog',
        ];
        $taName = $tabs[$type];
        $nsLog = model("app\common\model\\$taName")
            ->field('ifnull(sum(money), 0) money')
            ->where('user_id', '=', $userId)
            ->where('status', '=', '0')
            ->find();
        $this->success('ok', $nsLog);
    }

    /**
     * 获取余额
     */
    public function getBalancePay()
    {
        $res = array();
        $order_id = $this->request->post('order_id') ? $this->request->post('order_id') : 0;
        if ($order_id) {
            $vv = DB::query("select category_id,activity_type,id from fa_wanlshop_goods where id = (select goods_id from `fa_wanlshop_order_goods` where order_id = $order_id limit 1); ");
            $cid = $vv[0]['category_id'];
            $activity_type = $vv[0]['activity_type'];
            $res['can_ns_pay'] = $cid == 108 || in_array($activity_type, ['vipgoods','shopgoods']);
            $res['can_bank_pay'] = $vv[0]['id'] == 2198;
        }

        $res['currency_bd'] = $this->auth->currency_bd;
        $res['currency_rmb'] = $this->auth->currency_rmb;
        $res['currency_ns'] = $this->auth->currency_ns;
        $res['currency_flq'] = $this->auth->currency_flq;
        $res['currency_cny'] = $this->auth->currency_cny;
        $res['currency_pu'] = $this->auth->currency_pu;
//        $res['currency_xnb'] = $this->auth->currency_xnb;
//        $res['currency_usdt'] = $this->auth->currency_usdt;
        $res['currency_points'] = $this->auth->currency_points;
        $res['money'] = $this->auth->money;
//        $res['usdtcny'] = PriceSrv::getUsdtcny();
//        $res['sdrcny'] = PriceSrv::getSdrcny();

        //------ 判断是否可以使用组合支付 ------
        // 消费抵扣券比例 0.1
        $res['balancepu_pencent'] = 0.1;
        // 达人福利券比例
        $cng_rate = 0.05;
        if($this->auth->svip_lv_dkq > 1) {
            $dkqInfo = \app\admin\model\UserSvipLvDkq::column('id,rate');
            $cng_rate = $dkqInfo[$this->auth->svip_lv_dkq];
        }else {
//            $this->setting = model('app\common\model\Config')->where('group','basic')->column('name,value');
//            $cng_rate = $this->setting['cny_rate'] ?? 0.05; // 有问题取不到值
            $cng_rate = 0;
        }
        $res['balancecny_pencent'] = $cng_rate;

        $this->success('ok', $res);
    }

    /**
     * 获取Usdt_Cny汇率
     */
    public function getUsdtcny()
    {
//        $this->success('ok', PriceSrv::getCoinRate());
        $this->success('ok', PriceSrv::getUsdtcny());
    }

    /**
     * 获取Sdr_Cny汇率
     */
    public function getSdrcny()
    {
        $this->success('ok', PriceSrv::getSdrcny());
    }

    public function exchange()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $type = $this->request->post('type');
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('闪兑金额不正确');
            }
            if ($type != 0 && $type != 1) {
                $this->error("闪兑类型选择错误");
            }
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('闪兑数量不能低于' . $config['withdraw']['exmoney']);
            }
            Db::startTrans();
            try {
                $usdtcny = PriceSrv::getCoinRate();
                if ($type == 0) {
                    if ($money > $this->auth->currency_usdt) {
                        $this->error('USDT余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '闪兑USDT=>矿场余额', 'sys', '', 'currency_usdt');
                    $pay->currency($money * $usdtcny, $this->auth->id, '闪兑USDT=>矿场余额', 'sys', '', 'currency_bd');
                }
                if ($type == 1) {
                    if ($money > $this->auth->money) {
                        $this->error('收益余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->money(-$money, $this->auth->id, '闪兑收益余额=>USDT', 'sys', '');
                    $pay->currency($money / $usdtcny, $this->auth->id, '闪兑收益余额=>USDT', 'sys', '', 'currency_usdt');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('闪兑成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function exchangecny()
    {
        $this->error('兑换功能暂未开启');
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $type = $this->request->post('type');
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('兑换金额不正确');
            }
//            if (!$type) {
//                $this->error("兑换类型必须选择");
//            }
            if ($type != 0 && $type != 1) {
                $this->error("兑换类型选择错误");
            }
            $sdrcny = PriceSrv::getSdrcny();
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('兑换数量不能低于' . $config['withdraw']['exmoney']);
            }
            Db::startTrans();
            try {
//                if($type == 0){
//                    if (($money*1.02) > $this->auth->money) {
//                        $this->error('余额不足');
//                    }
//                    $pay = new WanlPay;
//                    $pay->money(-$money*1.02, $this->auth->id, '余额=>现金', 'sys', '');
//                    $pay->currency($money, $this->auth->id, '余额=>现金', 'sys', '','currency_bd');
//                }
//                if($type == 1){
//                    if ($money > $this->auth->money) {
//                        $this->error('余额不足');
//                    }
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, '余额=>购物积分', 'sys', '');
//                    $pay->currency($money, $this->auth->id, '余额=>购物积分', 'sys', '','currency_points');
//                }
                if ($type == 0) {
                    if ($money > $this->auth->currency_tz) {
                        $this->error('余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '人参=>购物积分', 'sys', '', 'currency_tz');
                    $pay->currency($money * 162, $this->auth->id, '人参=>购物积分', 'sys', '', 'currency_points');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_bd) {
                        $this->error('余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '现金=>产业积分', 'sys', '', 'currency_bd');
                    $pay->currency($money / $sdrcny, $this->auth->id, '现金=>产业积分', 'sys', '', 'currency_xnb');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('兑换成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function usdttrxrecharge()
    {
        if ($this->request->isPost()) {
            $data_get = $this->request->post();
            $data['Member_id'] = $data_get['Member_id'];
            $data['Amount'] = $data_get['Amount'];
            $data['TokenSymbol'] = $data_get['TokenSymbol'];
            $data['Addr'] = $data_get['Addr'];
            $data['DataTime'] = $data_get['DataTime'];
            $data['transaction_id'] = $data_get['transaction_id'];
            $key = 'sdrtoken2022fenghuazhejiangnb';
            $sign = MD5(MD5($data['Addr'] . $data['TokenSymbol'] . $data['Amount'] . $data['DataTime'] . $key) . $key);
            if ($sign == $data_get['Sign']) {
//                $coin_name = $data_get['coin'];
                $info['user_id'] = $data['Member_id'];
                $info['coin_name'] = $data['TokenSymbol'];
                $info['coin_id'] = 4;
                $info['number'] = $data['Amount'];
                $res = controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($data['Amount'], $data['Member_id'], 'USDT充值trc20', 'recharge', $data['transaction_id'], 'currency_usdt');
                $this->success('充值成功', $res);
            }
        }
        $this->error(__('充值失败!'));
    }

    /**
     * 进程锁定，针对每个用户的调用
     * @param mixed $action 方法名
     * @param mixed $time 时间内不允重复调用
     * @return void
     */
    protected function processLock($action,$time = 3){
        $key = $action.$this->auth->id;
        $cache = Cache::store('redis');
        if($cache->has($key)){
            $this->error('请勿频繁操作'.$time);
        }
        $cache->set($key,1,$time);
    }

    /**
     * 用户扫码转账
     */
    public function transfer()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $this->processLock('transfer');
//            if($this->auth->svip_level == 1)
//                $this->error('非服务中心无法转账');
            //if($this->auth->vip_level == 1)
            //$this->error('没有转账权限');
            // 类型
            $type = $this->request->post('ctype');
            // 金额
            $money = $this->request->post('money');
            // 收款用户
            $skusername = $this->request->post('skusername');
            // 支付密码
            $paypwd = $this->request->post('paypwd');

//            if ($this->auth->is_certified !=1) {
//                $this->error('转账必须先实名认证');
//            }

            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            $rpaypwd = $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt);
            if ($paypwd != $rpaypwd) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('转账金额不正确');
            }
            $skuser = \app\common\model\User::where('username', $skusername)->where('saas_id',SAAS)->field('id,username, svip_level, inviter_id,is_securities_firms,city_server,viplc_level,svip_lv_dkq,parent_code,lc_lam')->find();
            if (!$skuser) {
                $this->error(__('收款用户不存在'));
            }
            if ($skuser['svip_level'] <= 1 && $type == 2) {
                //$this->error(__('只有批发商才可以收款'));
            }
//            if (!$type) {
//                $this->error("转账类型必须选择");
//            }

            if ($type != 0 && $type != 1 && $type != 2 && $type != 3 && $type != 4 && $type != 5 && $type != 6 && $type != 7 && $type != 8) {
                $this->error("转账类型选择错误");
            }
//            if ($type == 1){
//                $this->error("该功能暂时关闭");
//            }
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('转账数量不能低于' . $config['withdraw']['exmoney']);
            }
//            if($type == 1){
//                $config['withdraw']['minmoney'] = 10;
//                $config['withdraw']['multiple'] = 10;
//            }
//            $minmoney = 2;
//            if ($money < $minmoney) {
            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('转账金额不能低于' . $config['withdraw']['minmoney']);
            }
            if (isset($config['withdraw']['multiple']) && ($money % 1 != 0)) {
                $this->error('转账金额必须是1的倍数');
            }

            //判断是否网体下
            $ivinfo = array();
            $ivinfo['inviter_id'] = $skuser['inviter_id'];
            $dline = false;
            if ($this->auth->is_transfer) $dline = true;
            if ($this->auth->id == $skuser['id']) {
                $this->error('不能给自己转账');
            } elseif ($type == 0 || $type == 2 || $type == 4 || $type == 5 || $type == 8) { // || $type == 6) {
                // 只能在网体内上下转账
                $skCode = $skuser['parent_code'].$skuser['id'].',';
                $loginUser = $this->auth->getUser();
                $fkCode = $loginUser['parent_code'].$loginUser['id'].',';
                $lUsername = $loginUser['username'];
                Log::info(" 1=$fkCode 2=$skCode skusername=$skusername, lusername=$lUsername");

                if(str_contains($skCode, $fkCode)) {
                    $dline = true;
                } else if(str_contains($fkCode, $skCode)) {
                    $dline = true;
                }
                Log::info("transfer 1=$fkCode 2=$skCode dline=$dline");
                if (!$dline) {
                    while(!$dline && $ivinfo['inviter_id']>0) {
                        $ivinfo = model('app\common\model\User')
                            ->where('id', $ivinfo['inviter_id'])
                            ->field('id, inviter_id')
                            ->find();
                        if($ivinfo['id'] == $this->auth->id){
                            $dline = true;
                        }
                    }
                    if(!$dline) $ivinfo['inviter_id'] = $this->auth->inviter_id;
                    while(!$dline && $ivinfo['inviter_id']>0) {
                        $ivinfo = model('app\common\model\User')
                            ->where('id', $ivinfo['inviter_id'])
                            ->field('id, inviter_id')
                            ->find();
                        if($ivinfo['id'] == $skuser['id']){
                            $dline = true;
                        }
                    }
                }
            }

            if($type == 0 || $type == 2 || $type == 4 || $type == 5 || $type == 8) { // || $type == 6) {
                if (!$dline) {
//                $this->error('只能往网体下转账');
                    $this->error('只能在网体内上下转账');
                }
            }
            Db::startTrans();
            try {
//                $usdtcny = PriceSrv::getCoinRate();
//                if($type == 0){
//                    if ($money > $this->auth->money) $this->error('金币不足');
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                    $pay->money($money, $skuser['id'], $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                }
//                if($type == 1){
//                    if ($money > $this->auth->money) $this->error('余额不足');
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                    $pay->money($money, $skuser['id'], $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                }
                if ($type == 0) {
                    if ($money > $this->auth->currency_bd) $this->error('余额不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_bd');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_bd');
                }
                if ($type == 8) {
                    if ($money > $this->auth->currency_blc) $this->error('余额不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_blc');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_blc');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_rmb) $this->error('提货券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_rmb');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_rmb');
                }
                if ($type == 2) {
                    if ($money > $this->auth->currency_ns) $this->error('永福莱豆不足');
                    $pay = new WanlPay;
                    //个人转个人收取5%
                    //个人转服务商收15%
                    //服务商转个人不收
                    //个人转线下门店收10% 分红池子收5（分红池子就是用户id为2的用户  分配到 currency_ns字段）
                    //

                    $remoney = $money;
                    $pond = 0;//池子获取的金额

                    $shoprow = model('app\api\model\wanlshop\Shop')
                        ->where(['user_id' => $skuser['id'], 'verify' => 3])
                        ->field('id,user_id,shopname,state')
                        ->find();

                    $cost = $money * 0;
                    $firmscost = $money * 0;
                    $remoney = $money;

                    // 联创 或 城市服务商
                    if ($this->auth->is_securities_firms == 1 || $this->auth->city_server > 0) {
                        $money = $money;
                    }

//                    if (!$shoprow || $shoprow && $shoprow['state'] != 4) {
//                        if (($money % 100) != 0) {
//                            $this->error('转账永福莱豆必须是100的倍数');
//                        }
//                    }

                    if($this->auth->city_server>0){

                    }else if ($skuser['is_securities_firms'] == 1 && $this->auth->is_securities_firms != 1) {
                        //服务商获得95 = 85+10 池子5
                        $pond = 0;
                        $cost = 0;
                        $remoney = $money;
                        if (($money + $pond + $cost) > $this->auth->currency_ns) $this->error('永福莱豆不足手续费需要'.($pond + $cost));
//                        $pay->currency(-($pond + $cost), $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_ns');
                        //$pay->currency($pond,2,'永福莱豆池子进账' , 'pond', '','currency_ns');
//                        $pay->currency($firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(永福莱豆转账)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
//                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                    } else if ($this->auth->is_securities_firms != 1 && $shoprow && $shoprow['state'] == 4) {

                        $nsday = model('app\common\model\CurrencyNsLog')
                            ->where(['user_id' => $this->auth->id,'status'=>'1', 'type' => 'nstransfer'])
                            ->where('money','<',0)
                            ->where('createtime','>=',strtotime(date('Y-m-d 00:00:00')))
                            ->sum('money');

                        $nsday = abs($nsday);

                        //消费限额
                        $consumption = 500;

                        if($money>$consumption){
                            $this->error('每日最多在线下消费'.$consumption);
                        }

                        if(($nsday+$money)>$consumption){
                            $this->error('每日最多在线下消费'.$consumption.',今日已消费'.$nsday);
                        }

                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0;
                        $pond = $money * 0;
                        $remoney = $money - $pond - $cost;
                        //$pay->currency($pond,2,'永福莱豆池子进账' , 'pond', '','currency_ns');
                        //取消线下门店 服务商 服务费
                        //$pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                        $pay->currency(($firmscost+$pond), $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(永福莱豆转账)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    } else if ($this->auth->is_securities_firms != 1 && $skuser['city_server'] == 1) {
                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0;
                        $pond = $money * 0;
                        $remoney = $money - $pond - $cost;
                        //$pay->currency($pond,2,'永福莱豆池子进账' , 'pond', '','currency_ns');
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                        $pay->currency(($firmscost+$pond), $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(永福莱豆转账)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    } else if ($skuser['is_securities_firms'] == 0 && $this->auth->is_securities_firms == 0) {
                        $cost = $money * 0;
                        $firmscost = 0;
                        if (($money+$firmscost) > $this->auth->currency_ns) $this->error('永福莱豆不足手续费需要'.$firmscost);
                        $pay->currency($cost, 2, '永福莱豆池子进账', 'pond', '', 'currency_ns');
//                        $pay->currency(-$firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_ns');
//                        $pay->currency($firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(永福莱豆转账)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    }

                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'nstransfer', '', 'currency_ns');
                    $pay->currency($remoney, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'nstransfer', '', 'currency_ns');
                }
                if ($type == 3) {
                    if ($money > $this->auth->currency_tz) $this->error('铭文不足');
                    $pay = new WanlPay;
                    //个人转个人收取5%
                    //个人转服务商收15%
                    //服务商转个人不收
                    //个人转线下门店收10% 分红池子收5（分红池子就是用户id为2的用户  分配到 currency_tz字段）
                    //
                    $remoney = $money;
                    $pond = 0;//池子获取的金额

                    $shoprow = model('app\api\model\wanlshop\Shop')
                        ->where(['user_id' => $skuser['id'], 'verify' => 3])
                        ->field('id,user_id,shopname,state')
                        ->find();

                    $cost = $money * 0;
                    $firmscost = $money * 0;
                    $remoney = $money;

                    // 联创 或 城市服务商
                    if ($this->auth->is_securities_firms == 1 || $this->auth->city_server > 0) {
                        $money = $money;
                    }

//                    if (!$shoprow || $shoprow && $shoprow['state'] != 4) {
//                        if (($money % 100) != 0) {
//                            $this->error('转账铭文必须是100的倍数');
//                        }
//                    }

                    if($this->auth->city_server>0){

                    }else if ($skuser['is_securities_firms'] == 1 && $this->auth->is_securities_firms != 1) {
                        //服务商获得95 = 85+10 池子5
                        $pond = 0;
                        $cost = 0;
                        $remoney = $money;
                        if (($money + $pond + $cost) > $this->auth->currency_tz) $this->error('铭文不足手续费需要'.($pond + $cost));
//                        $pay->currency(-($pond + $cost), $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_tz');
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_tz');
                    } else if ($this->auth->is_securities_firms != 1 && (($shoprow && $shoprow['state'] == 4) || $skuser['city_server'] == 1)) {
                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0;
                        $pond = $money * 0;
                        $remoney = $money - $pond - $cost;
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_tz');
                    } else if ($skuser['is_securities_firms'] == 0 && $this->auth->is_securities_firms == 0) {
                        $cost = $money * 0;
                        $firmscost = 0;
                        if (($money+$firmscost) > $this->auth->currency_tz) $this->error('铭文不足手续费需要'.($firmscost));
//                        $pay->currency(-$firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_tz');
                        $pay->currency($cost, 2, '铭文池子进账', 'pond', '', 'currency_tz');
                    }

                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tz');
                    $pay->currency($remoney, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tz');
                }
                Log::info("转账操作 type=$type");
                if ($type == 4) {
                    if ($money > $this->auth->currency_flq) $this->error('联创福利券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_flq');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_flq');

                    $currency_fhq = $this->auth->currency_fhq;
                    Log::info("转账操作 currency_fhq=$currency_fhq, money=$money");

                    if($money >= 80000) {
                        $fhqo = 0;
                        $fhqi = 0;
                        $moneysj = 0;
                        if($this->auth->viplc_level > $skuser['viplc_level'] && $this->auth->lc_lam >= 80000){
                            $actlv = 1;
                            $lclvinfo = UserViplcLevel::column('id,flq,fhq');
                            foreach($lclvinfo as $k=>$v){
                                if($k > 1 && $k < 6 && $k > $skuser['viplc_level']){
                                    if($this->auth->lc_lam >= $v['flq'] && $money >= $v['flq']){
                                        $actlv = $k;
                                    }
                                }
                            }
                            if($actlv > 1){
                                $array_re = array();
                                $array_re['lc_lam'] = $this->auth->lc_lam - $lclvinfo[$actlv]['flq'];
                                $this->auth->lc_lam = $array_re['lc_lam'];
                                model('app\common\model\User')->where('id', $this->auth->id)->update($array_re);
                                $array_ad = array();
                                $array_ad['viplc_level'] = $actlv;
                                if ($actlv > 2) $array_ad['lc_lam'] = $skuser['lc_lam'] + $lclvinfo[$actlv]['flq'];
                                model('app\common\model\User')->where('id', $skuser['id'])->update($array_ad);
                                $moneysj = $lclvinfo[$actlv]['flq'];
//                                $fhqo = $lclvinfo[$actlv]['fhq'];
                                $fhqi = $lclvinfo[$actlv]['fhq'];
                            }
                        }
                        $moneylf = $money - $moneysj;
                        if($moneylf >= 80000 && $this->auth->currency_fhq > 0){
                            $fhqz = floor($moneylf / 80000) * 1;
                            if ($fhqz > $this->auth->currency_fhq) $fhqz = $this->auth->currency_fhq;
                            $fhqo += $fhqz;
                            $fhqi += $fhqz;
                        }
                        if($fhqo > 0){
                            $pay->currency(-$fhqo, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_fhq');
                        }
                        if($fhqi > 0) {
                            $pay->currency($fhqi, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_fhq');
                        }
                        Log::info("转账操作 currency_fhq=$currency_fhq, money=$money fhq=$fhqi");
                    }
                }
                if ($type == 5) {
                    if($this->auth->svip_lv_dkq == 1) $this->error('无权进行该操作');
                    if($skuser->svip_lv_dkq == 1) $this->error('对方无权接受该项操作');
                    if ($money > $this->auth->currency_cny) $this->error('达人福利券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');

                    $currency_cny = $this->auth->currency_cny;
                    Log::info("转账操作 currency_fhq=$currency_cny, money=$money");
                }
                if ($type == 6) {
                    if ($money > $this->auth->currency_pu) $this->error('消费抵扣券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_pu');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_pu');

                    $currency_pu = $this->auth->currency_pu;
                    Log::info("转账操作 currency_pu=$currency_pu, money=$money");
                }
                if ($type == 7) {
                    if ($money > $this->auth->currency_gp) $this->error('消费抵扣券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_gp');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_gp');

                    $currency_gp = $this->auth->currency_gp;
                    Log::info("转账操作 currency_gp=$currency_gp, money=$money");
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('转账成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function transferunion()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $this->processLock('transferunion');
//            if($this->auth->svip_level == 1)
//                $this->error('非服务中心无法转账');
            if ($this->auth->vip_statusu == 0)
                $this->error('没有转账权限');
            // 类型
            $type = $this->request->post('ctype');
            // 金额
            $money = $this->request->post('money');
            // 收款用户
            $skusername = $this->request->post('skusername');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('转账金额不正确');
            }
            $skuser = \app\common\model\User::where('username', $skusername)->field('id, username, inviter_id')->find();
            if (!$skuser) {
                $this->error(__('收款用户不存在'));
            }
//            if (!$type) {
//                $this->error("转账类型必须选择");
//            }
            if ($type != 0 && $type != 1) {// && $type != 2) {
                $this->error("转账类型选择错误");
            }
            if ($type == 0) {// && $type != 2) {
                $this->error("转账功能暂时关闭");
            }
            $config = get_addon_config('wanlshop');
//            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
//                $this->error('转账数量不能低于' . $config['withdraw']['exmoney']);
//            }
//            if($type == 1){
//                $config['withdraw']['minmoney'] = 10;
//                $config['withdraw']['multiple'] = 10;
//            }
            $minmoney = 100;
            if ($money < $minmoney) {
//            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('转账金额不能低于' . $minmoney);
            }
            if (isset($config['withdraw']['multiple']) && ($money % 1 != 0)) {
                $this->error('转账金额必须是1的倍数');
            }
            //判断是否网体下
            $ivinfo = array();
            $ivinfo['inviter_id'] = $skuser['inviter_id'];
            $dline = true;
            if ($this->auth->id == $skuser['id']) {
                $this->error('不能给自己转账');
            } else {
                while (!$dline && $ivinfo['inviter_id'] > 0) {
                    $ivinfo = model('app\common\model\User')
                        ->where('id', $ivinfo['inviter_id'])
                        ->field('id, inviter_id')
                        ->find();
                    if ($ivinfo['id'] == $this->auth->id) {
                        $dline = true;
                    }
                }
                if (!$dline) $ivinfo['inviter_id'] = $this->auth->inviter_id;
                while (!$dline && $ivinfo['inviter_id'] > 0) {
                    $ivinfo = model('app\common\model\User')
                        ->where('id', $ivinfo['inviter_id'])
                        ->field('id, inviter_id')
                        ->find();
                    if ($ivinfo['id'] == $skuser['id']) {
                        $dline = true;
                    }
                }
            }
            if (!$dline) {
//                $this->error('只能往网体下转账');
                $this->error('只能在网体内转账');
            }
            Db::startTrans();
            try {
                if ($type == 0) {
                    if ($money > $this->auth->currency_cny) $this->error('余额不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_tzu) $this->error('甄株不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tzu');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tzu');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('转账成功', '');
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 查询用户信息
     * @return void
     */
    public function getUserInfo(){
        $userInfo = ['truename'=>'','nickname'=>''];
        $username = $this->request->param('username');
        $username && $userInfo = array_merge($userInfo,($user = \app\common\model\User::where('username',$username)->field('id,truename,nickname')->find()) ? $user->toArray() : []);
        $this->success('success',$userInfo);
    }

    /**
     * 创建Token
     */
    private function creatToken()
    {
        $code = chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE));
        $key = "YOOYE";
        $code = md5($key . substr(md5($code), 8, 10));
        Cache::store('redis')->set($this->getKey(), $code, 180);
        return $code;
    }

    /**
     * 验证Token
     * @param {Object} $token
     */
    private function checkToken($token)
    {
        $key = $this->getKey();
        if ($token == Cache::store('redis')->get($key)) {
            Cache::store('redis')->set($key, NULL);
            return TRUE;
        } else {
            return FALSE;
        }
    }

    private function getKey()
    {
        return 'orderToken'.$this->auth->id;
    }
    
    public function getPayDetail(){
        $order_id = $this->request->post('order_id','');
        $type = $this->request->post('type','');
        if(empty($order_id) || empty($type)){
            $this->error(__('参数错误'));
        }
        $user_id = $this->auth->id;
        $user= new User();
        $user= $user->find($user_id);
        $C = new CombinationPayment($user);
        $payModel = model('app\api\model\wanlshop\Pay')->where(['order_id'=>$order_id])->find();
        if(!$payModel){
            $this->error(__('支付订单不存在'));
        }
        $pay=$C->calculateTheAmount($payModel->price,$type);
        if($pay===false){
            $this->error(__('非法参数'));
        }
        // if($pay['pay']>0){
        //     $this->error(__('余额不足,请充值'));
        // }
        $this->success('success',$pay);
    }
    public function getPayfriDetail(){

        $price = $this->request->param('price',0);
        $num = $this->request->param('number',1);
        $type = $this->request->param('payType','');
        // $this->error($this->request->param());
        // if(!$price || !$type){
        //     $this->error(__('参数错误'));
        // }
        $price=bcmul($price,$num,2);
        $user_id = $this->auth->id;
        $user= new User();
        $user= $user->find($user_id);
        $C = new CombinationPayment($user);
        // $payModel = model('app\api\model\wanlshop\Pay')->where(['order_id'=>$order_id])->find();
        // if(!$payModel){
        //     $this->error(__('支付订单不存在'));
        // }
        $pay=$C->calculateTheAmount($price,$type);
        if($pay===false){
            $this->error(__('非法参数'));
        }
        // if($pay['pay']>0){
        //     $this->error(__('余额不足,请充值'));
        // }
        $this->success('success',$pay);
    }
    
    public function openKq()
    {
        $this->success('请求成功',true);
    }
    public function Withdrawal(){
        if(!$this->request->isPost()){
            $this->error('非法请求！');
        }
//        $today = date('Y-m-d');
//        $weekNum = date('w', strtotime($today));
//        if($weekNum != 2){
//            $this->error('每周三8点至20点开放提现！');
//        }
        $startOfDay = strtotime("today 08:00:00");
        $endOfDay = strtotime("today 20:00:00");
        if (time() < $startOfDay || time() > $endOfDay) {
            $this->error('每天8点至20点提现！');
        }
        $type=$this->request->post('type','');
        $amount=$this->request->post('amount',0);
        $card_id=$this->request->post('card_id',0);
//        $config=get_addon_config('wanlshop');
//        $config=$config['withdraw'];
//        $state=$config['state'];
//        if($state!='Y'){
//            $this->error('提现暂未开放！');
//        }
//        $minmoney=$config['minmoney'];
//        $multiple=$config['multiple'];
//        $servicefee=$config['servicefee']/1000;
        if(!$card_id){
            $this->error('请选择银行卡！');
        }
        if($amount<500){
            $this->error('提现金额不能低于500！');
        }
        if($amount%100 >0){
            $this->error('提现金额必须是100的倍数！');
        }
        $userModel=new User();
        $uid=$this->auth->id;
        // $uid=38571;
        $user=$userModel
            ->field('id,currency_nsd,currency_ns,currency_gp')
            ->find($uid);
        $pay = new WanlPay;
        try {
            Db::startTrans();
            if ($type == 'ns') {
                $this->error('提现类型选择错误！');
                if ($user->currency_nsd < $amount || $user->currency_ns < $amount) {
                    $this->error('提现额度或永福莱豆不足！');
                }
                $pay->currency(-$amount, $uid, '用户提现', 'withdraw', '', 'currency_ns');
                $pay->currency(-$amount, $uid, '用户提现', 'withdraw', '', 'currency_nsd');
            } elseif ($type == 'gp') {
//            dump($user->currency_gp);
                if ($user->currency_gp < $amount) {
                    $this->error('绿色积分不足！');
                }
                $pay->currency(-$amount, $uid, '用户提现', 'withdraw', '', 'currency_gp');
            } else {
                $this->error('提现类型选择错误！');
            }
            $card = model('app\api\model\wanlshop\PayAccount')->where('user_id', $uid)->find($card_id);
            $count = model('app\api\model\wanlshop\Withdraw')
                ->where('user_id', $uid)
                ->where('createtime', '>=', $startOfDay)
                ->where('createtime', '<=', $endOfDay)
                ->where('mtype','currency_' . $type)
                ->where('status','<>', 'rejected')
                ->count();
            if ($count > 0) {
                Db::rollback();
                $this->error('每天只能提现一次！');
            }
            $handingfee=bcmul($amount,0.07,2);
            $data = [
                'user_id' => $uid,
                'money' => bcsub($amount,$handingfee,2),
                'handingfee'=>$handingfee,
                'mtype' => 'currency_' . $type,
                'type' => $card->bankCode,
                'account' => $card->cardCode,
                'bankname' => $card->bankName,
                'bankopen' => $card->bankOpen,
                'name' => $card->username,
            ];
            $ret = model('app\api\model\wanlshop\Withdraw')->create($data);
            if(!$ret){
                Db::rollback();
                $this->error('提现失败！');
            }
            Db::commit();
            $this->success('提现申请已提交！');
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getline().$e->getMessage());
        }
    }
    public function getWithdraw()
    {
        $type=$this->request->post('type','');
        $uid=$this->auth->id ?? 0;
        $sql=model('app\api\model\wanlshop\Withdraw')->where('user_id',$uid);
        if($type=='tx_gp'){
            $sql->where('mtype','currency_gp');
        }elseif($type=='tx_nsd'){
            $sql->where('mtype','currency_ns');
        }else{
            $this->error('参数错误！');
        }
        $list=$sql->paginate();
        $this->success('ok',$list);
    }
}