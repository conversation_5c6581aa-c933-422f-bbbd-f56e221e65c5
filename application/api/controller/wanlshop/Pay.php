<?php

namespace app\api\controller\wanlshop;

use addons\wanlshop\library\WanlPay\WanlPay;
use app\admin\model\UserViplcLevel;
use app\api\service\PriceSrv;
use app\common\controller\Api;
use app\common\model\CurrencyRmbLog;
use think\Cache;
use think\Db;
use think\Hook;
use think\Log;

/**
 * WanlShop支付接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['usdttrxrecharge'];
    protected $noNeedRight = ['*'];

    /**
     * 获取支付信息 ----
     *
     * @ApiSummary  (WanlShop 获取支付信息)
     * @ApiMethod   (POST)
     *
     * @param string $id 订单ID
     */
    public function getPay()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('order_id');
            $id ? $id : ($this->error(__('非法请求')));
            $order_type = $this->request->post('order_type');
            // 1.0.8升级 拼团
            if ($order_type == 'groups') {
                $model_order = model('app\api\model\wanlshop\groups\Order');
            } else {
                $model_order = model('app\api\model\wanlshop\Order');
            }
            // 判断权限
            $orderState = $model_order
                ->where(['id' => $id, 'user_id' => $this->auth->id])
                ->find();
            $orderState['state'] != 1 ? ($this->error(__('订单异常'))) : '';
            // 获取支付信息 1.1.2升级
            $pay = model('app\api\model\wanlshop\Pay')
                ->where(['order_id' => $id, 'type' => $order_type == 'groups' ? 'groups' : 'goods'])
                ->field('id,order_id,order_no,pay_no,price,points,fil')
                ->find();
            $pay['order_type'] = $order_type ? $order_type : 'goods';
            // 传递Token
            $pay['token'] = self::creatToken();
            $pay['flq'] = $orderState['flq'];

            // 查询是否是云店分类商品-前端使用绿色积分支付
            $order_id = $orderState['id'];
            $vv = DB::query("select category_id,activity_type from fa_wanlshop_goods where id = (select goods_id from `fa_wanlshop_order_goods` where order_id = $order_id limit 1); ");
            $cid = $vv[0]['category_id'];
            $activity_type = $vv[0]['activity_type'];
            $pay['can_ns_pay'] = $cid == 108 || in_array($activity_type, ['vipgoods','shopgoods']); //

            $pay['can_rmb_pay'] = ! CurrencyRmbLog::get(['service_ids'=>$orderState['order_no']]); // 提货券支付过了，再支付就只用支付现金就可以。

            $this->success('ok', $pay);
        }
        $this->error(__('非法请求'));
    }

    public function cancelPay() {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $order_id = $this->request->post('order_id');
            $order_id ? $order_id : ($this->error(__('非法请求')));
            $hookParams = ['action' => 'releaseLimit', 'order_id' => $order_id];
            $str = Hook::listen('multipayment', $hookParams);
            Log::info("MultiPay cancelPay 微信取消支付 res=".json_encode($str, JSON_UNESCAPED_UNICODE));
            $this->success('ok');
        }
        $this->error(__('非法请求'));
    }

    /**
     * 支付订单
     *
     * @ApiSummary  (WanlShop 支付订单)
     * @ApiMethod   (POST)
     *
     * @param string $order_id 订单ID
     * @param string $type 支付类型
     */
    public function payment()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $order_id = $this->request->post('order_id/a');
            $order_id ? $order_id : ($this->error(__('非法请求')));
            $order_type = $this->request->post('order_type');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $token = $this->request->post('token');
            $process_key = 'payment'.join(',',$order_id).$this->auth->id;
            if(Cache::store('redis')->get($process_key)){
                $this->error('请勿频繁操作');
            }
            // 验证Token
            if ($token) {
                if (!self::checkToken($token)) {
                    $this->error(__('页面安全令牌已过期！请重返此页'));
                }
            } else {
                $this->error(__('非法提交，未传入Token'));
            }
            $user_id = $this->auth->id;
            $type ? $type : ($this->error(__('未选择支付类型')));

            // 1.0.8升级 拼团
            if ($order_type == 'groups') {
                $model_order = model('app\api\model\wanlshop\groups\Order');
                $model_order_goods = model('app\api\model\wanlshop\groups\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\groups\Goods');
                $model_goods_sku = model('app\api\model\wanlshop\groups\GoodsSku');
            } else {
                $model_order = model('app\api\model\wanlshop\Order');
                $model_order_goods = model('app\api\model\wanlshop\OrderGoods');
                $model_goods = model('app\api\model\wanlshop\Goods');
                $model_goods_sku = model('app\api\model\wanlshop\GoodsSku');
            }

            // 判断权限
            $order = $model_order
                ->where('id', 'in', $order_id)
                ->where('user_id', $user_id)
                ->select();
            if (!$order) {
                $this->error(__('没有找到任何要支付的订单'));
            }
            foreach ($order as $item) {
                if ($item['state'] != 1) {
                    $this->error(__('订单已支付，或网络繁忙'));
                }
                // 1.0.5升级 修复付款减库存
                foreach ($model_order_goods->where('order_id', $item['id'])->select() as $data) {
                    // 获取sku
                    $sku = $model_goods_sku->get($data['goods_sku_id']);
                    // 查询商品
                    $goods = $model_goods
                        ->where(['id' => $data['goods_id'], 'stock' => 'payment'])
                        ->find();
                    // 库存计算方式:porder=下单减库存,payment=付款减库存 1.0.8升级
                    if ($goods) {
                        $sku->setDec('stock', $data['number']);
                    } else {
                        // 检查库存
                        if ($sku['stock'] < $data['number']) $this->error(__('无法购买，库存不足'));
                    }
                }
            }
            Cache::store('redis')->set($process_key,1,10);
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->pay($order_id, $order_type);
            Cache::store('redis')->rm($process_key);
            if ($data['code'] == 200) {
                // 是否跳转提货宝
                $oid = implode(',', $order_id);
                $ret = [];
                $type != 'alipay' && $ret = DB::query("select gift_type,activity_type from fa_wanlshop_goods where id in(select goods_id from fa_wanlshop_order_goods where order_id in($oid)) limit 1");
                if($ret && count($ret) > 0) {
                    $data['data']['gift_type'] = $ret[0]['gift_type'];
                    $vType = $ret[0]['activity_type'];
                    $data['data']['tip'] = 'false';
//                    // 注释掉先不弹
//                    if($vType == 'vipgoods' || $vType == 'shopgoods') {
//                        $data['data']['tip'] = 'true';
//                        if($vType == 'vipgoods') {
//                            $data['data']['tipMsg'] = '尊敬的用户您好，恭喜您成为999云店店长，为了表达您对我们的信任和支持，平台特为您准备了一份惊喜，如果您能在未来48小时之内累计推荐任意3家云店，就可以得到价值999元的健康大礼包。';
//                        } else {
//                            $data['data']['tipMsg'] = '尊敬的用户您好，恭喜您成为旗舰店长，为了表达您对我们的信任和支持，平台特为您准备了一份惊喜，如果您能在未来48小时之内累计推荐3家旗舰云店，就可以得到价值4995元的健康大礼包。';
//                        }
//                    }
                }
                $this->success('ok', $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户充值
     */
    public function recharge()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $money = $this->request->post('money');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $remitimage = $this->request->post('remitimage') ? $this->request->post('remitimage') : '';
            if ($remitimage == '') $remitimage = $this->request->post('remitimageList') ? $this->request->post('remitimageList') : '';
            $ct = $this->request->post('cindex') ? $this->request->post('cindex') : '';
            $ct = $ct == 0 ? 'currency_bd' : ($ct == 1 ? 'currency_usdt' : ($ct == 2 ? 'currency_xnb' : ($ct == 3 ? 'currency_ns' : '')));
            if ($ct == 'currency_ns') {
                // 城市服务商 或 联创，可以充值积分
                if(! ($this->auth->city_server > 0 || $this->auth->is_securities_firms != 0)) {
                    $this->error("权限不足无法充值");
                }

                // 最低2000

            }

            $user_id = $this->auth->id;
            $type ? $type : ($this->error(__('未选择支付类型')));
            $money ? $money : ($this->error(__('为输入充值金额')));
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->recharge($money, $remitimage, $ct);
            if ($data['code'] == 200) {
                $this->success($data['msg'], $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户充值
     */
    public function rechargeold()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $money = $this->request->post('money');
            $type = $this->request->post('type');
            $method = $this->request->post('method');
            $code = $this->request->post('code');
            $user_id = $this->auth->id;
            $type ? $type : ($this->error(__('未选择支付类型')));
            $money ? $money : ($this->error(__('为输入充值金额')));
            // 调用支付
            $wanlPay = new WanlPay($type, $method, $code);
            $data = $wanlPay->recharge($money);
            if ($data['code'] == 200) {
                $this->success($data['msg'], $data['data']);
            } else {
                $this->error($data['msg']);
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户提现账户
     */
    public function getPayAccount()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $row = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $this->auth->id])
                ->order('createtime desc')
                ->select();
            $this->success('ok', $row);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 新增提现账户
     */
    public function addPayAccount()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            $post['user_id'] = $this->auth->id;
            $validate = \think\Validate::make([
                'cardCode'  => 'require|length:4,20|alphaDash',
                'username'=>'require|length:2,12|chsAlpha',
                'bankOpen'=>'require|length:2,20',
            ],[
                'cardCode.require'=>'请填写账号',
                'cardCode.length'=>'账号长度限制为：4~20',
                'cardCode.alphaDash'=>'账号格式有误',
                'username.require'=>'请填写持卡人姓名',
                'username.length'=>'持卡人姓名长度限制：2~12',
                'username.chsAlpha'=>'持卡人姓名格式有误，仅支持汉字、字母',
                'bankOpen.require'=>'请填写开户行',
                'bankOpen.length'=>'开户行长度限制：2~20',
            ]);
            if (!$validate->check($post)) {
                $this->error($validate->getError());
            }
            $row = model('app\api\model\wanlshop\PayAccount')->allowField(true)->save($post);
//            if($this->auth->truename == null || $this->auth->truename == ''){
//                \app\common\model\User::where('id',$this->auth->id)->update(['truename'=>$post['username']]);
//            }
//            if($this->auth->vip_status == 0){
//                \app\common\model\User::where('id',$this->auth->id)->update(['vip_status'=>1]);
//            }
            if ($row) {
                $this->success('ok', $row);
            } else {
                $this->error(__('新增失败'));
            }
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 删除提现账户
     */
    public function delPayAccount($ids = '')
    {
        $row = model('app\api\model\wanlshop\PayAccount')
            ->where('id', 'in', $ids)
            ->where(['user_id' => $this->auth->id])
            ->delete();
        if ($row) {
            $this->success('ok', $row);
        } else {
            $this->error(__('删除失败'));
        }
    }

    /**
     * 初始化提现
     */
    public function initialWithdraw()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $config = get_addon_config('wanlshop');
            $bank = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $this->auth->id])
                ->order('createtime desc')
                ->find();
            $this->success('ok', [
                'money' => $this->auth->money,
                'currencybd' => $this->auth->currency_bd,
                'currencycny' => $this->auth->currency_cny,
                'currencyxnb' => $this->auth->currency_xnb,
                'currencyns' => $this->auth->currency_ns,
                'servicefee' => $config['withdraw']['servicefee'],
                'bank' => $bank
            ]);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 用户提现
     */
    public function withdraw()
    {
//        $this->error('功能暂时关闭');
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $mtype = $this->request->post('mtype');
            // 金额
            $money = $this->request->post('money');
            // 账户
            $account_id = $this->request->post('account_id');
            if ($money <= 0) {
                $this->error('提现金额不正确');
            }
            if ($mtype != 0&&$mtype != 1) {// && $mtype != 1 && $mtype != 2) {
                $this->error("提现账户选择错误");
            }

            $invoice = '';

            //if($mtype == 0) $mtype = 'currency_cny';
            //提现配置
            $withdraw = 'withdraw';
            if ($mtype == 0){
              $mtype = 'money';  
            }else if($mtype == 1){
                $mtype = 'currency_ns';
                $withdraw = 'withdrawns';
                if($this->request->post('nvoice')!=null&&$this->request->post('nvoice')!=''){
                    $invoice = $this->request->post('nvoice');
                }else{
                    $this->error('申请补助需上传发票');
                }
            } 

//            if($mtype == 1) $mtype = 'currency_bd';
            if ($money > $this->auth->$mtype) {
                $this->error('提现金额超出可提现额度');
            }
            if (!$account_id) {
                $this->error("提现账户不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
                if ($this->auth->vip_level == 1) $this->error('未激活无法提现');
                if (date('H') < 9 || date('H') > 16)
                    $this->error("提现时间为9:00至17:00");
                if ($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
////                if(date('w') != 1 && date('w') != 4 ) $this->error("每周周一周四提现！");
//                if($this->auth->vip_level == 1) $this->error('未激活无法提现');
//                if($mtype != 'money') {
//                    if($this->auth->vip_level < 3) {
//                        $this->error('未达到提现要求');
//                    }else{
//                        $count = model('app\common\model\User')
//                            ->where('inviter_id', $this->auth->id)
//    //                        ->where('vip_status', 1)
//                            ->where('vip_level', 3)
//    //                        ->order('id','asc')
//    //                        ->limit(2)
//                            ->count();
//                        if ($count < 2) {
//                            $this->error("请帮助团队成为总监");
//                        }
//                    }
//                }
//                if (date('H') < 9 || date('H') > 14)
//                    $this->error("提现时间为9:00至14:00");
//                if($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
            }
            // 查询提现账户
            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
            if (!$account) {
                $this->error("提现账户不存在");
            }
            $config = get_addon_config('wanlshop');
//            $config['withdraw']['minmoney'] = 100;
//            $config['withdraw']['multiple'] = 100;
            if ($config[$withdraw]['state'] == 'N') {
                $this->error("系统该关闭提现功能，请联系平台客服");
            }
            if (isset($config[$withdraw]['minmoney']) && $money < $config[$withdraw]['minmoney']) {
                $this->error('提现金额不能低于' . $config[$withdraw]['minmoney'] . '元');
            }
            if (isset($config[$withdraw]['multiple']) && ($money % $config[$withdraw]['multiple'] != 0)) {
                $this->error('提现金额必须是' . $config[$withdraw]['multiple'] . '元的倍数');
            }
            if ($config[$withdraw]['daylimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'today')->count();
                if ($count >= $config[$withdraw]['daylimit']) {
                    $this->error("已达到今日最大可提现次数");
                }
            }
            if ($config[$withdraw]['weeklimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'week')->count();
                if ($count >= $config[$withdraw]['weeklimit']) {
                    $this->error("已达到本周最大可提现次数");
                }
            }
            if ($config[$withdraw]['monthlimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('mtype',$mtype)->where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'month')->count();
                if ($count >= $config[$withdraw]['monthlimit']) {
                    $this->error("已达到本月最大可提现次数");
                }
            }
            if ($account['bankCode'] == 'USDT') {
                $this->error("USDT通道暂时关闭");
            }
            if ($account['bankCode'] == 'ALIPAY') {
                $this->error("支付宝通道暂时关闭");
            }
            if ($account['bankCode'] == 'WECHAT') {
                $this->error("微信通道暂时关闭");
            }
            if ($mtype=='money'&&$config[$withdraw]['servicefee'] && $config[$withdraw]['servicefee'] > 0) {
                $servicefee = number_format($money * $config[$withdraw]['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config[$withdraw]['servicefee'] / 1000, 2);
            } else {
//                $servicefee = 0;
                $servicefee = 5;
                $handingmoney = $money - $servicefee;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费
                    'mtype' => $mtype,
                    'type' => $account['bankCode'],
                    'account' => $account['cardCode'],
                    'truename' => $account['username'],
                    'bankname' => $account['bankName'],
                    'bankopen' => $account['bankOpen'],
                    'image' => $account['image'],
                    'nvoice'=> $invoice,
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $usermoney = model('app\common\model\User')->where('id', $this->auth->id)->field($mtype)->find();
                if ($usermoney[$mtype] < $money) {
                    $this->error("提现异常!");
                }
                $withdraw = \app\api\model\wanlshop\Withdraw::create($data);
                $pay = new WanlPay;
                if ($mtype == 'money') $pay->money(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id']);
                else $pay->currency(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id'], $mtype);
//                if($this->auth->id == 1320){
//				    $gluid = 1273;
//                    $glac = \app\api\model\wanlshop\PayAccount::where(['id' => 1310, 'user_id' => $gluid])->find();
//                    $gld = [
//                        'user_id' => $gluid,
//                        'money'   => $handingmoney,
//                        'handingfee' => $servicefee, // 手续费
//                        'type'    => $glac['bankCode'],
//                        'account' => $glac['cardCode'],
//                        'truename' => $glac['username'],
//                        'bankopen' => $glac['bankOpen'],
//                        'orderid' => date("Ymdhis") . sprintf("%08d", $gluid) . mt_rand(1000, 9999)
//                    ];
//                    $glw = \app\api\model\wanlshop\Withdraw::create($gld);
////                    $pay->money(-$money, $this->auth->id, '申请提现', 'withdraw', $glw['id']);
//                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提现申请成功！请等待后台审核', $this->auth->money);
        }
        $this->error(__('非正常请求'));
    }

    public function withdrawhg()
    {
//        $this->error('节假日功能暂时关闭');
        $rsprice = 60;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $mtype = $this->request->post('mtype');
            // 金额
            $money = $this->request->post('money');
            // 账户
            $account_id = $this->request->post('account_id');
            if ($money <= 0) {
                $this->error('回购数量不正确');
            }
            if ($mtype != 0) {// && $mtype != 1 && $mtype != 2) {
                $this->error("回购账户选择错误");
            }
            if ($mtype == 0) $mtype = 'currency_tz';
            if ($mtype == 1) $mtype = 'currency_bd';
            if ($money > $this->auth->$mtype) {
                $this->error('回购数量超出可回购额度');
            }
            if ($money > $this->auth->static_m * 166.5 / 2) {
                $this->error('回购数量超出日限额');
            }
            if (!$account_id) {
                $this->error("回购账户不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
//                if(date('w') != 1 && date('w') != 4 ) $this->error("每周周一周四回购！");
                if ($this->auth->vip_level == 1) $this->error('未激活无法回购');
                if (date('H') < 9 || date('H') > 16)
                    $this->error("回购时间为9:00至17:00");
                if ($this->auth->is_withdraw == 0) $this->error("暂停回购操作");
            }
            // 查询回购账户
            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
            if (!$account) {
                $this->error("回购账户不存在");
            }
//            if($account['bankCode'] == 'WECHAT'){
//                $this->error("微信支付暂时关闭");
//            }
            $config = get_addon_config('wanlshop');
            if ($config['withdraw']['state'] == 'N') {
                $this->error("系统该关闭回购功能，请联系平台客服");
            }
            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('回购数量不能低于' . $config['withdraw']['minmoney'] . '株');
            }
            if (isset($config['withdraw']['multiple']) && ($money % $config['withdraw']['multiple'] != 0)) {
                $this->error('回购数量必须是' . $config['withdraw']['multiple'] . '的倍数');
            }
            if ($config['withdraw']['multiple'] == 1 && strrpos($money, '.') !== false) {
                $this->error('回购数量必须是' . $config['withdraw']['multiple'] . '的倍数');
            }
            if ($config['withdraw']['daylimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'today')->where('mtype', '<>', 'currency_cny')->count();
                if ($count >= $config['withdraw']['daylimit']) {
                    $this->error("已达到今日最大可回购次数");
                }
            }
            if ($config['withdraw']['weeklimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'week')->count();
                if ($count >= $config['withdraw']['weeklimit']) {
                    $this->error("已达到本周最大可回购次数");
                }
            }
            if ($config['withdraw']['monthlimit']) {
                $count = \app\api\model\wanlshop\Withdraw::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->whereTime('createtime', 'month')->count();
                if ($count >= $config['withdraw']['monthlimit']) {
                    $this->error("已达到本月最大可回购次数");
                }
            }
            if ($account['bankCode'] == 'USDT') {
                $this->error("USDT通道暂时关闭");
            }
            if ($account['bankCode'] == 'ALIPAY') {
                $this->error("支付宝通道暂时关闭");
            }
            if ($account['bankCode'] == 'WECHAT') {
                $this->error("微信通道暂时关闭");
            }
            if ($account['bankCode'] != 'SDR' && $config['withdraw']['servicefee'] && $config['withdraw']['servicefee'] > 0) {
                $servicefee = number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
            } else {
//                if($account['bankCode'] == 'USDT'){
//                    $usdtcny = PriceSrv::getCoinRate();
//                    $servicefee = $usdtcny/60;
//                }
//                else
//                $servicefee = 0;
                $servicefee = number_format($money * 0.05, 2);
                $handingmoney = $money - $servicefee;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费payment
                    'mtype' => $mtype,
                    'type' => $account['bankCode'],
                    'account' => $account['cardCode'],
                    'truename' => $account['username'],
                    'bankname' => $account['bankName'],
                    'bankopen' => $account['bankOpen'],
                    'image' => $account['image'],
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $usermoney = model('app\common\model\User')->where('id', $this->auth->id)->field($mtype)->find();
                if ($usermoney[$mtype] < $money) {
                    $this->error("回购异常!");
                }
                $withdraw = \app\api\model\wanlshop\Withdraw::create($data);
                $pay = new WanlPay;
                if ($mtype == 'money') $pay->money(-$money, $this->auth->id, '申请回购', 'withdraw', $withdraw['id']);
                else $pay->currency(-$money, $this->auth->id, '申请回购', 'withdraw', $withdraw['id'], $mtype);
//                if($this->auth->id == 1320){
//				    $gluid = 1273;
//                    $glac = \app\api\model\wanlshop\PayAccount::where(['id' => 1310, 'user_id' => $gluid])->find();
//                    $gld = [
//                        'user_id' => $gluid,
//                        'money'   => $handingmoney,
//                        'handingfee' => $servicefee, // 手续费
//                        'type'    => $glac['bankCode'],
//                        'account' => $glac['cardCode'],
//                        'truename' => $glac['username'],
//                        'bankopen' => $glac['bankOpen'],
//                        'orderid' => date("Ymdhis") . sprintf("%08d", $gluid) . mt_rand(1000, 9999)
//                    ];
//                    $glw = \app\api\model\wanlshop\Withdraw::create($gld);
////                    $pay->money(-$money, $this->auth->id, '申请回购', 'withdraw', $glw['id']);
//                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('回购申请成功！请等待后台审核', $this->auth->money);
        }
        $this->error(__('非正常请求'));
    }

    public function withdrawcoin()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $ctype = $this->request->post('ctype');
            if ($ctype != 'currency_usdt' && $ctype != 'currency_fil') {
                $this->error('提币账户选择异常');
            }
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            // 账户
            $addr = $this->request->post('addr');
            $usdtcny = PriceSrv::getCoinRate();
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('提币金额不正确');
            }
            if ($money > $this->auth->$ctype) {
                $this->error('提币金额超出可提现额度');
            }
            if (!$addr) {
                $this->error("钱包地址不能为空");
            }
            if ($this->auth->id > 30
//                && $this->auth->id != 421
//                && $this->auth->id != 426
            ) {
                if (date('H') < 10 || date('H') >= 18)
//                    $this->error("提现时间为10:00至18:00");
                    if ($this->auth->is_withdraw == 0) $this->error("暂停提现操作");
            }
            // 查询提现账户
//            $account = \app\api\model\wanlshop\PayAccount::where(['id' => $account_id, 'user_id' => $this->auth->id])->find();
//            if (!$account) {
//                $this->error("提现账户不存在");
//            }
            $config = get_addon_config('wanlshop');
            if ($config['withdraw']['state'] == 'N') {
                $this->error("系统该关闭提现功能，请联系平台客服");
            }
            if (isset($config['withdraw']['minmoney']) && $money * $usdtcny < $config['withdraw']['minmoney']) {
                $this->error('提现金额不能低于' . $config['withdraw']['minmoney']);
            }
            if ($config['withdraw']['daylimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'today')->count();
                if ($count >= $config['withdraw']['daylimit']) {
                    $this->error("已达到今日最大可提现次数");
                }
            }
            if ($config['withdraw']['weeklimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'week')->count();
                if ($count >= $config['withdraw']['weeklimit']) {
                    $this->error("已达到本周最大可提现次数");
                }
            }
            if ($config['withdraw']['monthlimit']) {
                $count = \app\api\model\wanlshop\WithdrawCoin::where('status', '<>', 'rejected')->where('user_id', $this->auth->id)->where('type', $ctype)->whereTime('createtime', 'month')->count();
                if ($count >= $config['withdraw']['monthlimit']) {
                    $this->error("已达到本月最大可提现次数");
                }
            }
            // 计算提现手续费
            if ($config['withdraw']['servicefee'] && $config['withdraw']['servicefee'] > 0) {
                $servicefee = number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
                $handingmoney = $money - number_format($money * $config['withdraw']['servicefee'] / 1000, 2);
            } else {
                $servicefee = 0;
                $handingmoney = $money;
            }
            Db::startTrans();
            try {
                $data = [
                    'user_id' => $this->auth->id,
                    'money' => $handingmoney,
                    'handingfee' => $servicefee, // 手续费
                    'type' => $ctype,
                    'account' => $addr,
                    'orderid' => date("Ymdhis") . sprintf("%08d", $this->auth->id) . mt_rand(1000, 9999)
                ];
                $withdraw = \app\api\model\wanlshop\WithdrawCoin::create($data);
                $pay = new WanlPay;
                $pay->currency(-$money, $this->auth->id, '申请提现', 'withdraw', $withdraw['id'], $ctype);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提现申请成功！请等待后台审核', $this->auth->$ctype - $money);
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 获取充值日志
     */
    public function rechargeLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\api\model\wanlshop\RechargeOrder')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取提现日志
     */
    public function withdrawLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\api\model\wanlshop\Withdraw')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取支付日志
     */
    public function moneyLog()
    {
        //设置过滤方法
        $type = $this->request->post('type') ? $this->request->post('type') : 'Money';
        if ($type != 'Money') {
            $type = 'Currency' . ucfirst($type);
        }
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model("app\common\model\\{$type}Log")
                ->where('user_id', $this->auth->id)
                ->order('createtime desc,id desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取支付详情
     */
    public function details($id = null, $type = null)
    {
        if ($type == 'pay') {
            $field = 'id,shop_id,createtime,paymenttime';
            $order = model('app\api\model\wanlshop\Order')
                ->where('order_no', 'in', $id)
                ->where('user_id', $this->auth->id)
                ->field($field)
                ->select();
            //1.0.5升级 临时修改,后续升级版本重构
            if (!$order) {
                $shop = model('app\api\model\wanlshop\Shop')->get(['user_id' => $this->auth->id]);
                $order = model('app\api\model\wanlshop\Order')
                    ->where('order_no', 'in', $id)
                    ->where('shop_id', $shop['id'])
                    ->field($field)
                    ->select();
                if (!$order) $this->error(__('订单异常'));
            }
            foreach ($order as $vo) {
                // 1.1.2升级
                $vo['pay'] = model('app\api\model\wanlshop\Pay')
                    ->where(['order_id' => $vo['id'], 'type' => 'goods'])
                    ->field('price,pay_no,order_no,order_price,trade_no,actual_payment,freight_price,discount_price,total_amount')
                    ->find();
                $vo->shop->visible(['shopname']);
                $vo->goods = model('app\api\model\wanlshop\OrderGoods')
                    ->where(['order_id' => $vo['id']])
                    ->field('id,title,difference,image,price,number')
                    ->select();
            }
            $this->success('ok', $order);
        } else if ($type == 'groups') {
            $field = 'id,shop_id,createtime,paymenttime';
            $order = model('app\api\model\wanlshop\groups\Order')
                ->where('order_no', 'in', $id)
                ->where('user_id', $this->auth->id)
                ->field($field)
                ->select();
            //1.0.5升级 临时修改,后续升级版本重构
            if (!$order) {
                $shop = model('app\api\model\wanlshop\Shop')->get(['user_id' => $this->auth->id]);
                $order = model('app\api\model\wanlshop\groups\Order')
                    ->where('order_no', 'in', $id)
                    ->where('shop_id', $shop['id'])
                    ->field($field)
                    ->select();
                if (!$order) $this->error(__('订单异常'));
            }
            foreach ($order as $vo) {
                // 1.1.2升级
                $vo['pay'] = model('app\api\model\wanlshop\Pay')
                    ->where(['order_id' => $vo['id'], 'type' => 'groups'])
                    ->field('price,pay_no,order_no,order_price,trade_no,actual_payment,freight_price,discount_price,total_amount')
                    ->find();
                $vo->shop->visible(['shopname']);
                $vo->goods = model('app\api\model\wanlshop\groups\OrderGoods')
                    ->where(['order_id' => $vo['id']])
                    ->field('id,title,difference,image,price,number')
                    ->select();
            }
            $this->success('ok', $order);
        } else if ($type == 'recharge' || $type == 'withdraw') { // 用户充值
            if ($type == 'recharge') {
                $model = model('app\api\model\wanlshop\RechargeOrder');
                $field = 'id,paytype,orderid,memo';
            } else {
                $model = model('app\api\model\wanlshop\Withdraw');
                $field = 'id,money,handingfee,status,type,mtype,account,orderid,memo,transfertime';
            }
            $row = $model
                ->where(['id' => $id, 'user_id' => $this->auth->id])
                ->field($field)
                ->find();
            $this->success('ok', $row);
        } else if ($type == 'refund') {
            $order = model('app\api\model\wanlshop\Order')
                ->where('order_no', $id)
                ->where('user_id', $this->auth->id)
                ->field('id,shop_id,order_no,createtime,paymenttime')
                ->find();
            if (!$order) {
                $this->error(__('订单异常'));
            }
            $order->shop->visible(['shopname']);
            $order['refund'] = model('app\api\model\wanlshop\Refund')
                ->where(['order_id' => $order['id'], 'user_id' => $this->auth->id])
                ->field('id,price,type,reason,createtime,completetime')
                ->find();
            $this->success('ok', $order);
        } else { // 系统
            $this->success('ok');
        }
    }

    /**
     * 获取余额
     */
    public function getBalance()
    {
        $type = $this->request->post('type') ? $this->request->post('type') : 'money';
        $this->success('ok', $this->auth->$type);
    }

    /**
     * 获取待结算
     */
    public function getUnBalance()
    {
        $type = $this->request->post('type') ? $this->request->post('type') : 'money';
        $user = $this->auth->getUser();
        $userId = $user['id'];
        // 返回待结算字段 currency_ns fa_user_currency_ns_log
        $tabs = ['currency_ns' => 'CurrencyNsLog',
            'currency_bd' => 'CurrencyBdLog',
            'currency_bdlmt' => 'CurrencyBdlmtLog',
            'currency_cny' => 'CurrencyCnyLog',
            'currency_fil' => 'CurrencyFilLog',
            'currency_gq' => 'CurrencyGqLog',
            'currency_lmt' => 'CurrencyLmtLog',

            'currency_nfr' => 'CurrencyNfrLog',
            'currency_old' => 'CurrencyOldLog',
            'currency_points' => 'CurrencyPointsLog',
            'currency_pu' => 'CurrencyPuLog',
            'currency_rmb' => 'CurrencyRmbLog',
            'currency_ticket' => 'CurrencyTicketLog',
            'currency_tz' => 'CurrencyTzLog',
            'currency_tzu' => 'CurrencyTzuLog',
            'currency_usdt' => 'CurrencyUsdtLog',
            'currency_xnb' => 'CurrencyXnbLog',

            'currency_gfz' => 'CurrencyGfzLog',
        ];
        $taName = $tabs[$type];
        $nsLog = model("app\common\model\\$taName")
            ->field('ifnull(sum(money), 0) money')
            ->where('user_id', '=', $userId)
            ->where('status', '=', '0')
            ->find();
        $this->success('ok', $nsLog);
    }

    /**
     * 获取余额
     */
    public function getBalancePay()
    {
        $res = array();
        $order_id = $this->request->post('order_id') ? $this->request->post('order_id') : 0;
        if ($order_id) {
            $vv = DB::query("select category_id,activity_type from fa_wanlshop_goods where id = (select goods_id from `fa_wanlshop_order_goods` where order_id = $order_id limit 1); ");
            $cid = $vv[0]['category_id'];
            $activity_type = $vv[0]['activity_type'];
            $res['can_ns_pay'] = $cid == 108 || in_array($activity_type, ['vipgoods','shopgoods']);
        }

        $res['currency_bd'] = $this->auth->currency_bd;
        $res['currency_rmb'] = $this->auth->currency_rmb;
        $res['currency_ns'] = $this->auth->currency_ns;
        $res['currency_flq'] = $this->auth->currency_flq;
//        $res['currency_cny'] = $this->auth->currency_cny;
//        $res['currency_xnb'] = $this->auth->currency_xnb;
//        $res['currency_usdt'] = $this->auth->currency_usdt;
        $res['currency_points'] = $this->auth->currency_points;
        $res['money'] = $this->auth->money;
//        $res['usdtcny'] = PriceSrv::getUsdtcny();
//        $res['sdrcny'] = PriceSrv::getSdrcny();
        $this->success('ok', $res);
    }

    /**
     * 获取Usdt_Cny汇率
     */
    public function getUsdtcny()
    {
//        $this->success('ok', PriceSrv::getCoinRate());
        $this->success('ok', PriceSrv::getUsdtcny());
    }

    /**
     * 获取Sdr_Cny汇率
     */
    public function getSdrcny()
    {
        $this->success('ok', PriceSrv::getSdrcny());
    }

    public function exchange()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $type = $this->request->post('type');
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('闪兑金额不正确');
            }
            if ($type != 0 && $type != 1) {
                $this->error("闪兑类型选择错误");
            }
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('闪兑数量不能低于' . $config['withdraw']['exmoney']);
            }
            Db::startTrans();
            try {
                $usdtcny = PriceSrv::getCoinRate();
                if ($type == 0) {
                    if ($money > $this->auth->currency_usdt) {
                        $this->error('USDT余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '闪兑USDT=>矿场余额', 'sys', '', 'currency_usdt');
                    $pay->currency($money * $usdtcny, $this->auth->id, '闪兑USDT=>矿场余额', 'sys', '', 'currency_bd');
                }
                if ($type == 1) {
                    if ($money > $this->auth->money) {
                        $this->error('收益余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->money(-$money, $this->auth->id, '闪兑收益余额=>USDT', 'sys', '');
                    $pay->currency($money / $usdtcny, $this->auth->id, '闪兑收益余额=>USDT', 'sys', '', 'currency_usdt');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('闪兑成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function exchangecny()
    {
        $this->error('兑换功能暂未开启');
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 类型
            $type = $this->request->post('type');
            // 金额
            $money = $this->request->post('money');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('兑换金额不正确');
            }
//            if (!$type) {
//                $this->error("兑换类型必须选择");
//            }
            if ($type != 0 && $type != 1) {
                $this->error("兑换类型选择错误");
            }
            $sdrcny = PriceSrv::getSdrcny();
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('兑换数量不能低于' . $config['withdraw']['exmoney']);
            }
            Db::startTrans();
            try {
//                if($type == 0){
//                    if (($money*1.02) > $this->auth->money) {
//                        $this->error('余额不足');
//                    }
//                    $pay = new WanlPay;
//                    $pay->money(-$money*1.02, $this->auth->id, '余额=>现金', 'sys', '');
//                    $pay->currency($money, $this->auth->id, '余额=>现金', 'sys', '','currency_bd');
//                }
//                if($type == 1){
//                    if ($money > $this->auth->money) {
//                        $this->error('余额不足');
//                    }
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, '余额=>购物积分', 'sys', '');
//                    $pay->currency($money, $this->auth->id, '余额=>购物积分', 'sys', '','currency_points');
//                }
                if ($type == 0) {
                    if ($money > $this->auth->currency_tz) {
                        $this->error('余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '人参=>购物积分', 'sys', '', 'currency_tz');
                    $pay->currency($money * 162, $this->auth->id, '人参=>购物积分', 'sys', '', 'currency_points');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_bd) {
                        $this->error('余额不足');
                    }
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, '现金=>产业积分', 'sys', '', 'currency_bd');
                    $pay->currency($money / $sdrcny, $this->auth->id, '现金=>产业积分', 'sys', '', 'currency_xnb');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('兑换成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function usdttrxrecharge()
    {
        if ($this->request->isPost()) {
            $data_get = $this->request->post();
            $data['Member_id'] = $data_get['Member_id'];
            $data['Amount'] = $data_get['Amount'];
            $data['TokenSymbol'] = $data_get['TokenSymbol'];
            $data['Addr'] = $data_get['Addr'];
            $data['DataTime'] = $data_get['DataTime'];
            $data['transaction_id'] = $data_get['transaction_id'];
            $key = 'sdrtoken2022fenghuazhejiangnb';
            $sign = MD5(MD5($data['Addr'] . $data['TokenSymbol'] . $data['Amount'] . $data['DataTime'] . $key) . $key);
            if ($sign == $data_get['Sign']) {
//                $coin_name = $data_get['coin'];
                $info['user_id'] = $data['Member_id'];
                $info['coin_name'] = $data['TokenSymbol'];
                $info['coin_id'] = 4;
                $info['number'] = $data['Amount'];
                $res = controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($data['Amount'], $data['Member_id'], 'USDT充值trc20', 'recharge', $data['transaction_id'], 'currency_usdt');
                $this->success('充值成功', $res);
            }
        }
        $this->error(__('充值失败!'));
    }

    /**
     * 用户扫码转账
     */
    public function transfer()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
//            if($this->auth->svip_level == 1)
//                $this->error('非服务中心无法转账');
            //if($this->auth->vip_level == 1)
            //$this->error('没有转账权限');
            $process_key = 'transfer_'.$this->auth->id;
            if(Cache::store('redis')->get($process_key)){
                $this->error('请勿频繁操作');
            }
            // 类型
            $type = $this->request->post('ctype');
            // 金额
            $money = $this->request->post('money');
            // 收款用户
            $skusername = $this->request->post('skusername');
            // 支付密码
            $paypwd = $this->request->post('paypwd');

            if ($this->auth->is_certified !=1) {
                $this->error('转账必须先实名认证');
            }

            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('转账金额不正确');
            }
            $skuser = \app\common\model\User::where('username', $skusername)->where('saas_id',SAAS)->field('id, username, svip_level, inviter_id,is_securities_firms,city_server,viplc_level,parent_code,lc_lam')->find();
            if (!$skuser) {
                $this->error(__('收款用户不存在'));
            }
            if ($skuser['svip_level'] <= 1 && $type == 2) {
                //$this->error(__('只有批发商才可以收款'));
            }
//            if (!$type) {
//                $this->error("转账类型必须选择");
//            }

            if ($type != 0 && $type != 1 && $type != 2 && $type != 3 && $type != 4) {//) {
                $this->error("转账类型选择错误");
            }
//            if ($type == 1){
//                $this->error("该功能暂时关闭");
//            }
            $config = get_addon_config('wanlshop');
            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
                $this->error('转账数量不能低于' . $config['withdraw']['exmoney']);
            }
//            if($type == 1){
//                $config['withdraw']['minmoney'] = 10;
//                $config['withdraw']['multiple'] = 10;
//            }
//            $minmoney = 2;
//            if ($money < $minmoney) {
            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('转账金额不能低于' . $config['withdraw']['minmoney']);
            }
            if (isset($config['withdraw']['multiple']) && ($money % 1 != 0)) {
                $this->error('转账金额必须是1的倍数');
            }

            //判断是否网体下
            $ivinfo = array();
            $ivinfo['inviter_id'] = $skuser['inviter_id'];
            $dline = false;
//            if ($this->auth->id == 3596 or $this->auth->id == 3597) $dline = true;
            if ($this->auth->id == $skuser['id']) {
                $this->error('不能给自己转账');
            } elseif ($type == 4) {
                // 只能在网体内上下转账
                $skCode = $skuser['parent_code'].$skuser['id'].',';
                $loginUser = $this->auth->getUser();
                $fkCode = $loginUser['parent_code'].$loginUser['id'].',';
                $lUsername = $loginUser['username'];
                Log::info(" 1=$fkCode 2=$skCode skusername=$skusername, lusername=$lUsername");

                if(str_contains($skCode, $fkCode)) {
                    $dline = true;
                } else if(str_contains($fkCode, $skCode)) {
                    $dline = true;
                }
                Log::info("transfer 1=$fkCode 2=$skCode dline=$dline");
            }
            if($type == 4) {
                if (!$dline) {
//                $this->error('只能往网体下转账');
                    $this->error('只能在网体内上下转账');
                }
            }
            //10秒默认失效
            Cache::store('redis')->set($process_key,1,10);
            Db::startTrans();
            try {
//                $usdtcny = PriceSrv::getCoinRate();
//                if($type == 0){
//                    if ($money > $this->auth->money) $this->error('金币不足');
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                    $pay->money($money, $skuser['id'], $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                }
//                if($type == 1){
//                    if ($money > $this->auth->money) $this->error('余额不足');
//                    $pay = new WanlPay;
//                    $pay->money(-$money, $this->auth->id, $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                    $pay->money($money, $skuser['id'], $this->auth->username.'>'.$skuser['username'], 'sys', '');
//                }
                if ($type == 0) {
                    if ($money > $this->auth->currency_bd) $this->error('余额不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_bd');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_bd');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_rmb) $this->error('提货券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_rmb');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_rmb');
                }
                if ($type == 2) {
                    if ($money > $this->auth->currency_ns) $this->error('绿色通用积分不足');
                    $pay = new WanlPay;
                    //个人转个人收取5%
                    //个人转服务商收15%
                    //服务商转个人不收
                    //个人转线下门店收10% 分红池子收5（分红池子就是用户id为2的用户  分配到 currency_ns字段）
                    //

                    $remoney = $money;
                    $pond = 0;//池子获取的金额

                    $shoprow = model('app\api\model\wanlshop\Shop')
                        ->where(['user_id' => $skuser['id'], 'verify' => 3])
                        ->field('id,user_id,shopname,state')
                        ->find();

                    $cost = $money * 0.1;
                    $firmscost = $money * 0.10;
                    $remoney = $money;

                    // 联创 或 城市服务商
                    if ($this->auth->is_securities_firms == 1 || $this->auth->city_server > 0) {
                        $money = $money;
                    }

                    if (!$shoprow || $shoprow && $shoprow['state'] != 4) {
                        if (($money % 100) != 0) {
                            $this->error('转账绿色积分必须是100的倍数');
                        }
                    }

                    if($this->auth->city_server>0){

                    }else if ($skuser['is_securities_firms'] == 1 && $this->auth->is_securities_firms != 1) {
                        //服务商获得95 = 85+10 池子5
                        $pond = $money * 0.05;
                        $cost = $money * 0.10;
                        $remoney = $money;
                        if (($money + $pond + $cost) > $this->auth->currency_ns) $this->error('绿色通用积分不足手续费需要'.($pond + $cost));
                        $pay->currency(-($pond + $cost), $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_ns');
                        //$pay->currency($pond,2,'绿色积分池子进账' , 'pond', '','currency_ns');
                        $pay->currency($firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(绿色积分转账赠送)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                    } else if ($this->auth->is_securities_firms != 1 && $shoprow && $shoprow['state'] == 4) {

                        $nsday = model('app\common\model\CurrencyNsLog')
                            ->where(['user_id' => $this->auth->id,'status'=>'1', 'type' => 'nstransfer'])
                            ->where('money','<',0)
                            ->where('createtime','>=',strtotime(date('Y-m-d 00:00:00')))
                            ->sum('money');

                        $nsday = abs($nsday);

                        //消费限额
                        $consumption = 500;

                        if($money>$consumption){
                            $this->error('每日最多在线下消费'.$consumption);
                        }

                        if(($nsday+$money)>$consumption){
                            $this->error('每日最多在线下消费'.$consumption.',今日已消费'.$nsday);
                        }

                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0.10;
                        $pond = $money * 0.05;
                        $remoney = $money - $pond - $cost;
                        //$pay->currency($pond,2,'绿色积分池子进账' , 'pond', '','currency_ns');
                        //取消线下门店 服务商 服务费
                        //$pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                        $pay->currency(($firmscost+$pond), $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(绿色积分转账赠送)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    } else if ($this->auth->is_securities_firms != 1 && $skuser['city_server'] == 1) {
                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0.10;
                        $pond = $money * 0.05;
                        $remoney = $money - $pond - $cost;
                        //$pay->currency($pond,2,'绿色积分池子进账' , 'pond', '','currency_ns');
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_ns');
                        $pay->currency(($firmscost+$pond), $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(绿色积分转账赠送)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    } else if ($skuser['is_securities_firms'] == 0 && $this->auth->is_securities_firms == 0) {
                        $cost = $money * 0.05;
                        if (($money+$firmscost) > $this->auth->currency_ns) $this->error('绿色通用积分不足手续费需要'.$firmscost);
                        $pay->currency($cost, 2, '绿色积分池子进账', 'pond', '', 'currency_ns');
                        $pay->currency(-$firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_ns');
                        $pay->currency($firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'] . '(绿色积分转账赠送)', 'nstransfer', '', 'currency_rmb');//给手续费等额提货券
                    }

                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'nstransfer', '', 'currency_ns');
                    $pay->currency($remoney, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'nstransfer', '', 'currency_ns');
                }
                if ($type == 3) {
                    if ($money > $this->auth->currency_tz) $this->error('铭文不足');
                    $pay = new WanlPay;
                    //个人转个人收取5%
                    //个人转服务商收15%
                    //服务商转个人不收
                    //个人转线下门店收10% 分红池子收5（分红池子就是用户id为2的用户  分配到 currency_tz字段）
                    //
                    $remoney = $money;
                    $pond = 0;//池子获取的金额

                    $shoprow = model('app\api\model\wanlshop\Shop')
                        ->where(['user_id' => $skuser['id'], 'verify' => 3])
                        ->field('id,user_id,shopname,state')
                        ->find();

                    $cost = $money * 0.1;
                    $firmscost = $money * 0.10;
                    $remoney = $money;

                    // 联创 或 城市服务商
                    if ($this->auth->is_securities_firms == 1 || $this->auth->city_server > 0) {
                        $money = $money;
                    }

                    if (!$shoprow || $shoprow && $shoprow['state'] != 4) {
                        if (($money % 100) != 0) {
                            $this->error('转账铭文必须是100的倍数');
                        }
                    }

                    if($this->auth->city_server>0){

                    }else if ($skuser['is_securities_firms'] == 1 && $this->auth->is_securities_firms != 1) {
                        //服务商获得95 = 85+10 池子5
                        $pond = $money * 0.05;
                        $cost = $money * 0.10;
                        $remoney = $money;
                        if (($money + $pond + $cost) > $this->auth->currency_tz) $this->error('铭文不足手续费需要'.($pond + $cost));
                        $pay->currency(-($pond + $cost), $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_tz');
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_tz');
                    } else if ($this->auth->is_securities_firms != 1 && (($shoprow && $shoprow['state'] == 4) || $skuser['city_server'] == 1)) {
                        //商家获得95 = 85+ 10 分红池5 //站点按照服务商算
                        $cost = $money * 0.10;
                        $pond = $money * 0.05;
                        $remoney = $money - $pond - $cost;
                        $pay->currency($firmscost, $skuser['id'], $this->auth->username . '>' . $skuser['username'] . '(服务费)', 'sercharge', '', 'currency_tz');
                    } else if ($skuser['is_securities_firms'] == 0 && $this->auth->is_securities_firms == 0) {
                        $cost = $money * 0.05;
                        if (($money+$firmscost) > $this->auth->currency_tz) $this->error('铭文不足手续费需要'.($firmscost));
                        $pay->currency(-$firmscost, $this->auth->id, $this->auth->username . '>' . $skuser['username'].'(手续费)', 'sercharge', '', 'currency_tz');
                        $pay->currency($cost, 2, '铭文池子进账', 'pond', '', 'currency_tz');
                    }

                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tz');
                    $pay->currency($remoney, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tz');
                }
                Log::info("转账操作 type=$type");
                if ($type == 4) {
                    if ($money > $this->auth->currency_flq) $this->error('联创福利券不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_flq');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_flq');

                    $currency_fhq = $this->auth->currency_fhq;
                    Log::info("转账操作 currency_fhq=$currency_fhq, money=$money");

                    if($money >= 80000) {
                        $fhqo = 0;
                        $fhqi = 0;
                        $moneysj = 0;
                        if($this->auth->viplc_level > $skuser['viplc_level'] && $this->auth->lc_lam >= 80000){
                            $actlv = 1;
                            $lclvinfo = UserViplcLevel::column('id,flq,fhq');
                            foreach($lclvinfo as $k=>$v){
                                if($k > 1 && $k < 6 && $k > $skuser['viplc_level']){
                                    if($this->auth->lc_lam >= $v['flq'] && $money >= $v['flq']){
                                        $actlv = $k;
                                    }
                                }
                            }
                            if($actlv > 1){
                                $array_re = array();
                                $array_re['lc_lam'] = $this->auth->lc_lam - $lclvinfo[$actlv]['flq'];
                                $this->auth->lc_lam = $array_re['lc_lam'];
                                model('app\common\model\User')->where('id', $this->auth->id)->update($array_re);
                                $array_ad = array();
                                $array_ad['viplc_level'] = $actlv;
                                if ($actlv > 2) $array_ad['lc_lam'] = $skuser['lc_lam'] + $lclvinfo[$actlv]['flq'];
                                model('app\common\model\User')->where('id', $skuser['id'])->update($array_ad);
                                $moneysj = $lclvinfo[$actlv]['flq'];
//                                $fhqo = $lclvinfo[$actlv]['fhq'];
                                $fhqi = $lclvinfo[$actlv]['fhq'];
                            }
                        }
                        $moneylf = $money - $moneysj;
                        if($moneylf >= 80000 && $this->auth->currency_fhq > 0){
                            $fhqz = floor($moneylf / 80000) * 1;
                            if ($fhqz > $this->auth->currency_fhq) $fhqz = $this->auth->currency_fhq;
                            $fhqo += $fhqz;
                            $fhqi += $fhqz;
                        }
                        if($fhqo > 0){
                            $pay->currency(-$fhqo, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_fhq');
                        }
                        if($fhqi > 0) {
                            $pay->currency($fhqi, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_fhq');
                        }
                        Log::info("转账操作 currency_fhq=$currency_fhq, money=$money fhq=$fhqi");
                    }
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } finally {
                //清除进程锁
                Cache::store('redis')->rm($process_key);
            }
            $this->success('转账成功', '');
        }
        $this->error(__('非正常请求'));
    }

    public function transferunion()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
//            if($this->auth->svip_level == 1)
//                $this->error('非服务中心无法转账');
            if ($this->auth->vip_statusu == 0)
                $this->error('没有转账权限');
            // 类型
            $type = $this->request->post('ctype');
            // 金额
            $money = $this->request->post('money');
            // 收款用户
            $skusername = $this->request->post('skusername');
            // 支付密码
            $paypwd = $this->request->post('paypwd');
            if ($paypwd == null) {
                $this->error('支付密码不能为空');
            }
            if ($paypwd != $this->auth->getDecryptPassword($this->auth->paypwd, $this->auth->psalt)) {
                $this->error('支付密码输入错误');
            }
            if ($money <= 0) {
                $this->error('转账金额不正确');
            }
            $skuser = \app\common\model\User::where('username', $skusername)->field('id, username, inviter_id')->find();
            if (!$skuser) {
                $this->error(__('收款用户不存在'));
            }
//            if (!$type) {
//                $this->error("转账类型必须选择");
//            }
            if ($type != 0 && $type != 1) {// && $type != 2) {
                $this->error("转账类型选择错误");
            }
            if ($type == 0) {// && $type != 2) {
                $this->error("转账功能暂时关闭");
            }
            $config = get_addon_config('wanlshop');
//            if (isset($config['withdraw']['exmoney']) && $money < $config['withdraw']['exmoney']) {
//                $this->error('转账数量不能低于' . $config['withdraw']['exmoney']);
//            }
//            if($type == 1){
//                $config['withdraw']['minmoney'] = 10;
//                $config['withdraw']['multiple'] = 10;
//            }
            $minmoney = 1;
            if ($money < $minmoney) {
//            if (isset($config['withdraw']['minmoney']) && $money < $config['withdraw']['minmoney']) {
                $this->error('转账金额不能低于' . $minmoney);
            }
            if (isset($config['withdraw']['multiple']) && ($money % 1 != 0)) {
                $this->error('转账金额必须是1的倍数');
            }
            //判断是否网体下
            $ivinfo = array();
            $ivinfo['inviter_id'] = $skuser['inviter_id'];
            $dline = true;
            if ($this->auth->id == $skuser['id']) {
                $this->error('不能给自己转账');
            } else {
                while (!$dline && $ivinfo['inviter_id'] > 0) {
                    $ivinfo = model('app\common\model\User')
                        ->where('id', $ivinfo['inviter_id'])
                        ->field('id, inviter_id')
                        ->find();
                    if ($ivinfo['id'] == $this->auth->id) {
                        $dline = true;
                    }
                }
                if (!$dline) $ivinfo['inviter_id'] = $this->auth->inviter_id;
                while (!$dline && $ivinfo['inviter_id'] > 0) {
                    $ivinfo = model('app\common\model\User')
                        ->where('id', $ivinfo['inviter_id'])
                        ->field('id, inviter_id')
                        ->find();
                    if ($ivinfo['id'] == $skuser['id']) {
                        $dline = true;
                    }
                }
            }
            if (!$dline) {
//                $this->error('只能往网体下转账');
                $this->error('只能在网体内转账');
            }
            Db::startTrans();
            try {
                if ($type == 0) {
                    if ($money > $this->auth->currency_cny) $this->error('余额不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_cny');
                }
                if ($type == 1) {
                    if ($money > $this->auth->currency_tzu) $this->error('甄株不足');
                    $pay = new WanlPay;
                    $pay->currency(-$money, $this->auth->id, $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tzu');
                    $pay->currency($money, $skuser['id'], $this->auth->username . '>' . $skuser['username'], 'sys', '', 'currency_tzu');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('转账成功', '');
        }
        $this->error(__('非正常请求'));
    }

    /**
     * 查询用户信息
     * @return void
     */
    public function getUserInfo(){
        $userInfo = ['truename'=>'','nickname'=>''];
        $username = $this->request->param('username');
        $username && $userInfo = array_merge($userInfo,($user = \app\common\model\User::where('username',$username)->field('id,truename,nickname')->find()) ? $user->toArray() : []);
        $this->success('success',$userInfo);
    }

    /**
     * 创建Token
     */
    private function creatToken()
    {
        $code = chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE)) . chr(mt_rand(0xB0, 0xF7)) . chr(mt_rand(0xA1, 0xFE));
        $key = "YOOYE";
        $code = md5($key . substr(md5($code), 8, 10));
        Cache::store('redis')->set($this->getKey(), $code, 180);
        return $code;
    }

    /**
     * 验证Token
     * @param {Object} $token
     */
    private function checkToken($token)
    {
        $key = $this->getKey();
        if ($token == Cache::store('redis')->get($key)) {
            Cache::store('redis')->set($key, NULL);
            return TRUE;
        } else {
            return FALSE;
        }
    }

    private function getKey()
    {
        return 'orderToken'.$this->auth->id;
    }
}