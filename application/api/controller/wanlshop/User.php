<?php

namespace app\api\controller\wanlshop;

use addons\wanlshop\library\Decrypt\weixin\wxBizDataCrypt;
use addons\wanlshop\library\WanlChat\WanlChat;
use addons\wanlshop\library\WeixinSdk\Security;
use addons\wanlshop\library\WanlPay\WanlPay;
use app\admin\model\UserTrident2n;
use app\api\library\CombinationPayment;
use app\common\controller\Api;
use app\common\library\Sms;
use app\common\library\Sms as Smslib;
use app\common\model\CurrencyRmbLog;
use app\common\model\OfflineActivity;
use fast\Random;
use fast\Http;
use think\Cache;
use think\Db;
use think\Exception;
use think\Hook;
use think\Log;
use think\Validate;
use think\Config;
use app\api\controller\exten\UserOrderReq;

/**
 * WanlShop会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['wxLogin', 'login', 'logout', 'mobilelogin', 'register', 'registerwup', 'resetpaypwd', 'resetpwd', 'changephone', 'changeemail', 'changemobile', 'third', 'phone', 'perfect'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        //WanlChat 即时通讯调用
        $this->wanlchat = new WanlChat();
        $this->auth->setAllowFields(['id', 'username', 'nickname', 'mobile', 'email', 'avatar', 'level', 'gender', 'birthday', 'bio', 'ivcode',
            'is_certified', 'currency_old', 'currency_bd', 'currency_xnb', 'currency_usdt', 'currency_fil', 'currency_flq', 'currency_gfz',
            'currency_lmt', 'currency_points', 'currency_tz', 'currency_gq', 'currency_pu', 'currency_tzu', 'currency_ans','currency_blc',
            'currency_nfr', 'currency_cny', 'currency_ns', 'currency_gp', 'currency_rmb', 'currency_adv', 'money', 'score', 'successions', 'plan','currency_nsd',
            'ns_pay', 'ns_dynamic', 'ns_static', 'is_distributor', 'self_amd0', 'city_server', 'vipd_level', 'vipv_level', 'invites_nums',
            'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'jointime', 'svip_level', 'viplc_level', 'vip_level', 'ns_status', 'invite_numsns', 'invite_nums', 'monthly_ns', 'monthly_nsn']);
    }

    /**
     * 会员登录
     * @ApiMethod   (POST)
     * @param string $account 账号
     * @param string $password 密码
     */
    public function login()
    {
//        $this->error('安永数据对接中停网一天！');
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $account = $this->request->post('account');
            $password = $this->request->post('password');
            $client_id = $this->request->post('client_id');
            $toLogSid = $this->request->post('toLogSid');

            if (!$account || !$password) {
                $this->error(__('Invalid parameters'));
            }
            Log::info("Auth-Login login account1=$account");
            $account = $this->_base64($account);
            Log::info("Auth-Login login account2=$account");
            $ret = $this->auth->login($account, $password, $this->saas_id, $toLogSid);
            if ($ret) {
                if ($this->auth->getUser()['vip_level'] < 2) {
//                    $this->error('您的等级不够无法登录！');
                }
                if ($client_id) {
                    try {
                        $this->wanlchat->bind($client_id, $this->auth->id);
                    } catch (Exception $e) {

                    }
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $saasList = $this->auth->getSaasList();
                if (count($saasList) > 0) {
                    //  选择一个SAAS登录

                    $this->error($this->auth->getError(), $saasList, 203);
                    return;
                }
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 手机验证码登录
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $captcha = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            $mobile = $this->_base64($mobile);
            if (!$mobile || !$captcha) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            if ($captcha != '886886' && !Sms::check($mobile, $captcha, 'mobilelogin')) {
                $this->error(__('Captcha is incorrect'));
            }
//			$user = \app\common\model\User::getByMobile($mobile);
            $user = \app\common\model\User::get(['saas_id' => $this->saas_id, 'mobile' => $mobile]);
            if ($user) {
                if ($user->status != 'normal') {
                    $this->error(__('Account is locked'));
                }
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($user->id);
            } else {
                $this->error(__('Account not exist'));
                //$ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
            }
            if ($ret) {
                Sms::flush($mobile, 'mobilelogin');
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 手机号登录
     * @ApiMethod   (POST)
     * @param string $encryptedData
     * @param string $iv
     */
    public function phone()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            if (!isset($post['iv'])) {
                $this->error(__('获取手机号异常'));
            }
            // 获取配置
            $config = get_addon_config('wanlshop');
            // 微信小程序一键登录
            $params = [
                'appid' => $config['mp_weixin']['appid'],
                'secret' => $config['mp_weixin']['appsecret'],
                'js_code' => $post['code'],
                'grant_type' => 'authorization_code'
            ];
            $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
            $json = (array)json_decode($result['msg'], true);
//		    var_dump($json);
            // 判断third是否存在ID,存在快速登录
            if (isset($json['unionid'])) {
                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'unionid' => $json['unionid']]);
            } else {
                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'mp_weixin', 'openid' => $json['openid']]);
            }

            if ($third && $third['user_id'] != 0) {
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($third['user_id']);
            } else {
                // 手机号解码
                $decrypt = new wxBizDataCrypt($config['mp_weixin']['appid'], $json['session_key']);
                $decrypt->decryptData($post['encryptedData'], $post['iv'], $data);
                $data = (array)json_decode($data, true);
                // 开始登录
                $mobile = $data['phoneNumber'];
                $user = \app\common\model\User::get(['saas_id' => $this->saas_id, 'mobile' => $mobile]);
                if ($user) {
                    if ($user->status != 'normal') {
                        $this->error(__('Account is locked'));
                    }
                    //如果已经有账号则直接登录
                    $ret = $this->auth->direct($user->id);
                } else {
                    $this->error(__('请先注册用户'), $mobile);
//    				$ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
                }
            }

            if ($ret) {
                if (isset($post['client_id']) && $post['client_id'] != null) {
                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                }
                $this->success(__('Logged in successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取微信前端用户openid用于发于微信用户互动使用
     */
    public function wxLogin()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            // 获取配置
            $config = get_addon_config('wanlshop');
            // 微信小程序一键登录
            $params = [
                'appid' => $config['mp_weixin']['appid'],
                'secret' => $config['mp_weixin']['appsecret'],
                'js_code' => $post['code'],
                'grant_type' => 'authorization_code'
            ];
            $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
            $json = (array)json_decode($result['msg'], true);

            // 判断third是否存在ID,存在快速登录
            $openid = isset($json['unionid']) ? $json['unionid'] : $json['openid'];
            $third = model('app\api\model\wanlshop\Third')->get(['openid' => $openid]);
            if (!$third) {
                $time = time();
                // 保存openid后续使用
                $third = model('app\api\model\wanlshop\Third');
                if (isset($json['unionid'])) {
                    $third->unionid = $json['unionid'];
                } else {
                    $third->openid = $json['openid'];
                }
                $third->access_token = $json['session_key'];
                $third->expires_in = 7776000;
                $third->logintime = $time;
                $third->expiretime = $time + 7776000;
                $third->save();
            }

            // 判断当前是否登录
            if ($this->auth->isLogin()) {
                $third->user_id = $this->auth->id;
                $third->save();
            }

            $this->success('ok');
        }
    }

    /**
     * 注册会员
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $code 验证码
     */
    public function register()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $code = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $ret = Sms::check($mobile, $code, 'register');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Sign up successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    public function regwithname()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
            $inviter = $this->request->post('inviter');
//			$code = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            $mobile = $this->_base64($mobile);
            if ($mobile && !Validate::is($mobile, "alphaDash")) {
                $this->error();
            }
//			if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
//				$this->error(__('Mobile is incorrect'));
//			}
//			$ret = Sms::check($mobile, $code, 'register');
//			if (!$ret) {
//				$this->error(__('Captcha is incorrect'));
//			}
            $password = $this->request->post("password");
            if (!$password) {
                $password = Random::alnum();
            }
            $ret = $this->auth->register($mobile, $password, '', '', [], $inviter);
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $data = [
                    'userinfo' => $this->auth->getUserinfo(),
                    'statistics' => $this->statistics()
                ];
                $this->success(__('Sign up successful'), $data);
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    public function registerwup()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $username = $this->request->post('username');
            $mobile = $this->request->post('mobile');
            $inviter = $this->request->post('inviter');
//            $puser = $this->request->post('puser');
            $code = $this->request->post('captcha');
            $client_id = $this->request->post('client_id');
            $step = $this->request->post('step') == 'step1' ? false : true;
            // $mobile = $this->_base64($mobile);
            // $username = $this->_base64($username);
            if (preg_match("/[\x{4e00}-\x{9fa5}]/u", $username)) {
                $this->error(__('用户名不能包含中文'));
            }
            if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            if ($step) {
                $ret = Sms::check($mobile, $code, 'register');
                if ($code != '394950' && !$ret) {
                    $this->error(__('Captcha is incorrect'));
                }

            }
            $password = $this->request->post("password");
            if (!$password) {
                $password = Random::alnum();
            }
            $paypwd = $this->request->post("paypwd");
            $username = trim($username);
            $mobile = trim($mobile);
            $ret = $this->auth->register($username, $password, '', $mobile, [], $inviter, $paypwd, $this->saas_id);
//            $ret = $this->auth->register($username, $password, '', '', [], $inviter, $paypwd);
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }
                $this->success(__('Sign up successful'), self::userInfo());
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 注销登录
     */
    public function logout($client_id = null)
    {
        // 踢出即时通讯 1.0.7升级
        if ($client_id) {
            try {
                $this->wanlchat->destoryClient($client_id);
            } catch (Exception $e) {
                // 写日志
            }
        }
        // 退出登录
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     * @ApiMethod   (POST)
     *
     * @param string $avatar 头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio 个人简介
     */
    public function profile()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
            if ($avatar) {
                $user->avatar = $avatar;
            } else {
                $username = $this->request->post('username');
                $nickname = $this->request->post('nickname');
                $bio = $this->request->post('bio');
                if ($username) {
                    $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
                    if ($exists) {
                        $this->error(__('Username already exists'));
                    }
                    $user->username = $username;
                }
                if ($nickname) {
                    $exists = \app\common\model\User::where('nickname', $nickname)
                        ->where('saas_id', SAAS)
                        ->where('id', '<>', $this->auth->id)->find();
                    if ($exists) {
                        $this->error(__('Username already exists'));
                    }
                    $user->nickname = $nickname;
                }
                $user->gender = $this->request->post('gender', '');
                $birthday = $this->request->post('birthday', '');
                if ($birthday) {
                    $user->birthday = $birthday;
                }

                $user->bio = $bio;
//                $mobile = $this->request->post('mobile');
//                $user->mobile = $mobile;
                $truename = $this->request->post('truename');
                if ($user->truename == null || $user->truename == '') $user->truename = $truename;
                $alipay = $this->request->post('alipay');
                if ($alipay) $user->alipay = $alipay;
//                $autopay = $this->request->post('autopay', '0');
//                $user->autopay = $autopay;

                $password = $this->request->post('password');
                if ($password) {
                    if (strlen($password) > 5 && strlen($password) < 33) {
                        $user->salt = Random::alnum();
                        $user->password = \app\common\library\Auth::instance()->getEncryptPassword($password, $user->salt);
                    } else {
                        $this->error(__('密码长度必须6-33位'));
                    }
                }
                $paypwd = $this->request->post('paypwd');
                if ($paypwd) {
                    if (strlen($paypwd) > 5 && strlen($paypwd) < 33) {
                        $user->psalt = Random::alnum();
                        $user->paypwd = \app\common\library\Auth::instance()->getEncryptPassword($paypwd, $user->psalt);
                    } else {
                        $this->error(__('密码长度必须6-33位'));
                    }
                }
            }
            $user->save();
            $this->success('返回成功', $user);
        }
        $this->error(__('非法请求'));
    }

    public function profilewechat()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
            if ($avatar) {
                $user->avatar = $avatar;
            } else {
                $username = $this->request->post('username');
                $nickname = $this->request->post('nickname');
                $bio = $this->request->post('bio');

                $config = get_addon_config('wanlshop');
                $security = new Security($config['mp_weixin']['appid'], $config['mp_weixin']['appsecret']);
                if ($bio) {
                    $bioCheck = $security->check('msg_sec_check', ['content' => $bio]);
                    if ($bioCheck['code'] !== 0) {
                        if ($bioCheck['code'] === 87014) {
                            $this->error(__('签名包含敏感词汇'));
                        } else {
                            $this->error(__($bioCheck['msg']));
                        }
                    }
                }
                if ($nickname) {
                    $nicknameCheck = $security->check('msg_sec_check', ['content' => $nickname]);
                    if ($nicknameCheck['code'] !== 0) {
                        if ($nicknameCheck['code'] === 87014) {
                            $this->error(__('昵称包含敏感词汇'));
                        } else {
                            $this->error(__($nicknameCheck['msg']));
                        }
                    }
                }
                if ($username) {
                    $usernameCheck = $security->check('msg_sec_check', ['content' => $username]);
                    if ($usernameCheck['code'] !== 0) {
                        if ($usernameCheck['code'] === 87014) {
                            $this->error(__('用户名包含敏感词汇'));
                        } else {
                            $this->error(__($usernameCheck['msg']));
                        }
                    }
                    $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
                    if ($exists) {
                        $this->error(__('Username already exists'));
                    }
                    $user->username = $username;
                }
                $user->nickname = $nickname;
                $user->bio = $bio;
//                $mobile = $this->request->post('mobile');
//                $user->mobile = $mobile;
                $truename = $this->request->post('truename');
                if ($user->truename == null || $user->truename == '') $user->truename = $truename;
                $alipay = $this->request->post('alipay');
                $user->alipay = $alipay;
//                $autopay = $this->request->post('autopay', '0');
//                $user->autopay = $autopay;
            }
            $user->save();
            $this->success('返回成功', $user);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 修改手机号
     * @ApiMethod   (POST)
     * @param string $email 手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $mobile = $this->request->post('mobile');
            $captcha = $this->request->post('captcha');
            $mobile = $this->_base64($mobile);
            if (!$mobile || !$captcha) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
                $this->error(__('Mobile already exists'));
            }
            $result = Sms::check($mobile, $captcha, 'changemobile');
            if (!$result) {
                $this->error(__('Captcha is incorrect'));
            }
            $verification = $user->verification;
            $verification->mobile = 1;
            $user->verification = $verification;
            $user->mobile = $mobile;
            $user->save();

            Sms::flush($mobile, 'changemobile');
            $this->success();
        }
        $this->error(__('非法请求'));
    }

    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $newpassword 新密码
     * @param string $captcha 验证码
     */
    public function resetpwd()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $username = $this->request->post("username");
            $mobile = $this->request->post("mobile");
            $newpassword = $this->request->post("newpassword");
            $captcha = $this->request->post("captcha");
            if (!$newpassword || !$captcha || !$mobile) {
                $this->error(__('Invalid parameters'));
            }
//            if (!Validate::regex($mobile, "^1\d{10}$")) {
//                $this->error(__('Mobile is incorrect'));
//            }

            $fieldName = 'mobile';
            if (!$mobile || !\think\Validate::regex($mobile, "^1\d{10}$")) {
                if (!Validate::is($mobile, 'email')) {
                    $this->error(__('手机号/邮箱不正确'));
                } else {
                    $fieldName = 'email';
                }
            } else {
                $fieldName = 'mobile';
            }

//			$user = \app\common\model\User::getByMobile($mobile);
            $user = \app\common\model\User::where('saas_id', $this->saas_id)->where("$fieldName", $mobile)->find();
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changepwd($newpassword, '', true);
            if ($ret) {
                $this->success(__('Reset password successful'));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $newpassword 新密码
     * @param string $captcha 验证码
     */
    public function resetpaypwd()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $username = $this->request->post("username");
            $mobile = $this->request->post("mobile");
            $newpassword = $this->request->post("newpassword");
            $captcha = $this->request->post("captcha");
            if (!$newpassword || !$captcha || !$mobile) {
                $this->error(__('Invalid parameters'));
            }
//            if (!Validate::regex($mobile, "^1\d{10}$")) {
//                $this->error(__('Mobile is incorrect'));
//            }
//            $user = \app\common\model\User::getByMobile($mobile);
//            $user = \app\common\model\User::where('username', $username)->where('mobile', $mobile)->find();
            $user = $this->auth;
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpaypwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpaypwd');
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changepaypwd($newpassword, '', true);
            if ($ret) {
                $this->success(__('Reset password successful'));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile 手机号
     * @param string $newpassword 新密码
     * @param string $captcha 验证码
     */
    public function changephone()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
//            $mobile = $this->request->post("mobile");
            $newphone = $this->request->post("newphone");
            $captcha = $this->request->post("captcha");
            if (!$newphone || !$captcha) {
                $this->error(__('Invalid parameters'));
            }
            if (!Validate::regex($newphone, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
//            $user = \app\common\model\User::getByMobile($mobile);
//            if (!$user) {
//                $this->error(__('User not found'));
//            }
            $user = $this->auth;
            $ret = Sms::check($newphone, $captcha, 'changephone');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($newphone, 'changephone');
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changephone($newphone);
            if ($ret) {
                $this->success(__('Reset mobile successful'));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 第三方登录-web登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     */
    public function third_web()
    {
        $this->error(__('暂未开放'));
    }


    /**
     * 第三方登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     * @param string $code Code码
     */
    public function third()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            // 获取登录配置
            $config = get_addon_config('wanlshop');
            // 获取前端参数
            $post = $this->request->post();
            // 登录项目
            $time = time();
            $platform = $post['platform'];
            // 开始登录
            switch ($platform) {
                // 微信小程序登录
                case 'mp_weixin':
                    $params = [
                        'appid' => $config[$platform]['appid'],
                        'secret' => $config[$platform]['appsecret'],
                        'js_code' => $post['loginData']['code'],
                        'grant_type' => 'authorization_code'
                    ];
                    $result = Http::sendRequest("https://api.weixin.qq.com/sns/jscode2session", $params, 'GET');
                    if ($result['ret']) {
                        $json = (array)json_decode($result['msg'], true);
                        if (isset($json['unionid'])) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $json['unionid']]);
                        } else {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                        }
                        // 成功登录
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third['id']
                                ]);
                            }
                            $third->save([
                                'access_token' => $json['session_key'],
                                'expires_in' => 7776000,
                                'logintime' => $time,
                                'expiretime' => $time + 7776000
                            ]);
                            $ret = $this->auth->direct($user->id);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'weixin_open';
                            if (isset($json['unionid'])) {
                                $third->unionid = $json['unionid'];
                            } else {
                                $third->openid = $json['openid'];
                            }
                            $third->access_token = $json['session_key'];
                            $third->expires_in = 7776000;
                            $third->logintime = $time;
                            $third->expiretime = $time + 7776000;
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third->id
                                ]);
                            }
                        }
                    } else {
                        $this->error('API异常，微信小程序登录失败');
                    }
                    break;

                // 微信App登录
                case 'app_weixin':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token'],
                        'openid' => $post['loginData']['authResult']['openid']
                    ];
                    $result = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $params, 'GET');
                    if ($result['ret']) {
                        $json = (array)json_decode($result['msg'], true);
                        if (isset($json['unionid'])) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'unionid' => $json['unionid']]);
                        } else {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_open', 'openid' => $json['openid']]);
                        }
                        // 成功登录
                        if ($third) {
                            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'refresh_token' => $post['loginData']['authResult']['refresh_token'],
                                'expires_in' => $post['loginData']['authResult']['expires_in'],
                                'logintime' => $time,
                                'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'weixin_open';
                            if (isset($json['unionid'])) {
                                $third->unionid = $json['unionid'];
                            } else {
                                $third->openid = $json['openid'];
                            }
                            $third->access_token = $post['loginData']['authResult']['access_token'];
                            $third->refresh_token = $post['loginData']['authResult']['refresh_token'];
                            $third->expires_in = $post['loginData']['authResult']['expires_in'];
                            $third->logintime = $time;
                            $third->expiretime = $time + $post['loginData']['authResult']['expires_in'];
                            // 判断当前是否登录,否则注册
                            if ($this->auth->isLogin()) {
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $username = $json['nickname'];
                                $mobile = '';
                                $gender = $json['sex'] == 1 ? 1 : 0;
                                $avatar = $json['headimgurl'];
                                // 注册账户
                                $result = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                    'gender' => $gender,
                                    'nickname' => $username,
                                    'avatar' => $avatar
                                ]);
                                if ($result) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        }
                    } else {
                        $this->error('API异常，App登录失败');
                    }
                    break;
                // 微信公众号登录
                case 'h5_weixin':
                    $params = [
                        'appid' => $config['sdk_qq']['gz_appid'],
                        'secret' => $config['sdk_qq']['gz_secret'],
                        'code' => $post['code'],
                        'grant_type' => 'authorization_code'
                    ];
                    $result = Http::sendRequest('https://api.weixin.qq.com/sns/oauth2/access_token', $params, 'GET');
                    if ($result['ret']) {
                        $access = (array)json_decode($result['msg'], true);
                        //获取用户信息
                        $queryarr = [
                            "access_token" => $access['access_token'],
                            "openid" => $access['openid']
                        ];
                        $ret = Http::sendRequest("https://api.weixin.qq.com/sns/userinfo", $queryarr, 'GET');
                        if ($ret['ret']) {
                            $json = (array)json_decode($ret['msg'], true);
                            if (isset($json['unionid'])) {
                                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'unionid' => $json['unionid']]);
                            } else {
                                $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weixin_h5', 'openid' => $json['openid']]);
                            }
                            // 成功登录
                            if ($third) {
                                $third->save([
                                    'openid' => $json['openid'], // 1.1.2升级
                                    'access_token' => $access['access_token'],
                                    'refresh_token' => $access['refresh_token'],
                                    'expires_in' => $access['expires_in'],
                                    'logintime' => $time,
                                    'expiretime' => $time + $access['expires_in']
                                ]);
                                // 登录客户端
                                $ret = $this->auth->direct($third['user_id']);
                                if ($ret) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model('app\api\model\wanlshop\Third');
                                $third->platform = 'weixin_h5';
                                // 1.1.2升级
                                if (isset($json['unionid'])) {
                                    $third->unionid = $json['unionid'];
                                    $third->openid = $json['openid'];
                                } else {
                                    $third->openid = $json['openid'];
                                }
                                $third->access_token = $access['access_token'];
                                $third->refresh_token = $access['refresh_token'];
                                $third->expires_in = $access['expires_in'];
                                $third->logintime = $time;
                                $third->expiretime = $time + $access['expires_in'];
                                // 获取到的用户信息
                                $username = $json['nickname'];
                                $mobile = '';
                                $gender = $json['sex'] == 1 ? 1 : 0;
                                $avatar = $json['headimgurl'];
                                // 注册账户
                                $result = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                                    'gender' => $gender,
                                    'nickname' => $username,
                                    'avatar' => $avatar
                                ]);
                                if ($result) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        } else {
                            $this->error('获取用户信息失败！');
                        }
                    } else {
                        $this->error('获取openid失败！');
                    }
                    break;
                // QQ小程序登录
                case 'mp_qq':
                    $params = [
                        'appid' => $config[$platform]['appid'],
                        'secret' => $config[$platform]['appsecret'],
                        'js_code' => $post['loginData']['code'],
                        'grant_type' => 'authorization_code'
                    ];
                    $result = Http::sendRequest("https://api.q.qq.com/sns/jscode2session", $params, 'GET');
                    if ($result['ret']) {
                        $json = (array)json_decode($result['msg'], true);
                        if (isset($json['unionid'])) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'unionid' => $json['unionid']]);
                        } else {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
                        }
                        // 成功登录
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third['id']
                                ]);
                            }
                            $third->save([
                                'access_token' => $json['session_key'],
                                'expires_in' => 7776000,
                                'logintime' => $time,
                                'expiretime' => $time + 7776000
                            ]);
                            $ret = $this->auth->direct($user->id);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'qq_open';
                            if (isset($json['unionid'])) {
                                $third->unionid = $json['unionid'];
                            } else {
                                $third->openid = $json['openid'];
                            }
                            $third->access_token = $json['session_key'];
                            $third->expires_in = 7776000;
                            $third->logintime = $time;
                            $third->expiretime = $time + 7776000;
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third->id
                                ]);
                            }
                        }
                    } else {
                        $this->error('API异常，微信小程序登录失败');
                    }
                    break;

                // QQ App登录
                case 'app_qq':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token']
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ]
                    ];
                    $result = Http::sendRequest("https://graph.qq.com/oauth2.0/me", $params, 'GET', $options);
                    if ($result['ret']) {
                        $json = (array)json_decode(str_replace(" );", "", str_replace("callback( ", "", $result['msg'])), true);
                        if ($json['openid'] == $post['loginData']['authResult']['openid']) {
                            $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'qq_open', 'openid' => $json['openid']]);
                            if ($third) {
                                $user = model('app\common\model\User')->get($third['user_id']);
                                if (!$user) {
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'third_id' => $third['id']
                                    ]);
                                }
                                $third->save([
                                    'access_token' => $post['loginData']['authResult']['access_token'],
                                    'expires_in' => $post['loginData']['authResult']['expires_in'],
                                    'logintime' => $time,
                                    'expiretime' => $time + $post['loginData']['authResult']['expires_in']
                                ]);
                                $ret = $this->auth->direct($third['user_id']);
                                if ($ret) {
                                    if (isset($post['client_id']) && $post['client_id'] != null) {
                                        $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                    }
                                    $this->success(__('Sign up successful'), self::userInfo());
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model('app\api\model\wanlshop\Third');
                                $third->platform = 'qq_open';
                                $third->openid = $json['openid'];
                                $third->access_token = $post['loginData']['authResult']['access_token'];
                                $third->expires_in = $post['loginData']['authResult']['expires_in'];
                                $third->logintime = $time;
                                $third->expiretime = $time + $post['loginData']['authResult']['expires_in'];
                                // 判断当前是否登录
                                if ($this->auth->isLogin()) {
                                    $third->user_id = $this->auth->id;
                                    $third->save();
                                    // 直接绑定自动完成
                                    $this->success('绑定成功', [
                                        'binding' => 1
                                    ]);
                                } else {
                                    $third->save();
                                    // 通知客户端绑定
                                    $this->success('尚未绑定用户', [
                                        'binding' => 0,
                                        'third_id' => $third->id
                                    ]);
                                }
                            }
                        } else {
                            $this->error(__('非法请求，机器信息已提交'));
                        }
                    } else {
                        $this->error('API异常，App登录失败');
                    }
                    break;
                // QQ 网页登录
                case 'h5_qq':
                    // 后续版本上线
                    break;
                // 微博App登录
                case 'app_weibo':
                    $params = [
                        'access_token' => $post['loginData']['authResult']['access_token']
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            'Content-Type: application/x-www-form-urlencoded'
                        ],
                        CURLOPT_POSTFIELDS => http_build_query($params),
                        CURLOPT_POST => 1
                    ];
                    $result = Http::post("https://api.weibo.com/oauth2/get_token_info", $params, $options);
                    $json = (array)json_decode($result, true);
                    if ($json['uid'] == $post['loginData']['authResult']['uid']) {
                        $third = model('app\api\model\wanlshop\Third')->get(['platform' => 'weibo_open', 'openid' => $json['uid']]);
                        if ($third) {
                            $user = model('app\common\model\User')->get($third['user_id']);
                            if (!$user) {
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third['id']
                                ]);
                            }
                            $third->save([
                                'access_token' => $post['loginData']['authResult']['access_token'],
                                'expires_in' => $json['expire_in'],
                                'logintime' => $json['create_at'],
                                'expiretime' => $json['create_at'] + $json['expire_in']
                            ]);
                            $ret = $this->auth->direct($third['user_id']);
                            if ($ret) {
                                if (isset($post['client_id']) && $post['client_id'] != null) {
                                    $this->wanlchat->bind($post['client_id'], $this->auth->id);
                                }
                                $this->success(__('Sign up successful'), self::userInfo());
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model('app\api\model\wanlshop\Third');
                            $third->platform = 'weibo_open';
                            $third->openid = $json['uid'];
                            $third->access_token = $post['loginData']['authResult']['access_token'];
                            $third->expires_in = $json['expire_in'];
                            $third->logintime = $json['create_at'];
                            $third->expiretime = $json['create_at'] + $json['expire_in'];
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success('绑定成功', [
                                    'binding' => 1
                                ]);
                            } else {
                                $third->save();
                                // 通知客户端绑定
                                $this->success('尚未绑定用户', [
                                    'binding' => 0,
                                    'third_id' => $third->id
                                ]);
                            }
                        }
                    } else {
                        $this->error(__('非法请求，机器信息已提交'));
                    }
                    break;

                // 小米App登录
                case 'app_xiaomi':

                    break;

                // 苹果登录
                case 'apple':
                    // 后续版本上线
                    break;
                default:
                    $this->error('暂并不支持此方法登录');
            }
        }
        $this->error(__('10086非正常请求'));
    }

    /**
     * 进一步完善资料
     * @ApiMethod   (POST)
     */
    public function perfect()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $post = $this->request->post();
            // 判断third_id没有绑定
            $third = model('app\api\model\wanlshop\Third')->get($post['third_id']);
            // 当user_id 不为空可以绑定
            if ($third['user_id'] == 0 && $third) {
                $username = $post['nickName'];
                $mobile = '';
                $gender = $post['gender'];
                $avatar = $post['avatarUrl'];
                $result = $this->auth->register('u_' . Random::alnum(6), Random::alnum(), '', $mobile, [
                    'gender' => $gender,
                    'nickname' => $username,
                    'avatar' => $avatar
                ]);
                if ($result) {
                    // 更新第三方登录
                    $third->save([
                        'user_id' => $this->auth->id,
                        'openname' => $username
                    ]);
                    $this->success(__('Sign up successful'), self::userInfo());
                } else {
                    $this->error($this->auth->getError());
                }
            } else {
                $this->error(__('非法请求，机器信息已提交'));
            }
        }
        $this->error(__('非法请求'));
    }

    /**
     * 刷新用户中心
     * @ApiMethod   (POST)
     */
    public function refresh()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $userInfo = self::userInfo();

            // 是否 超级会员
            $userInfo['userinfo']['svips'] = $this->getSvips();

            // 云店是否复购
            $userInfo['userinfo']['hasRPShop'] = date('d', time()) < 15; // 当前时间是否15号之后
            $userInfo['userinfo']['hasRP'] = $this->hasRepeatPurchase(); // true已复购；false未复购

            $shop = \app\api\model\wanlshop\shop\Shop::get(['user_id' => $this->auth->id]);

            if ($shop && $shop['state'] == 4) {
                $userInfo['userinfo']['is_store'] = 1;
            } else {
                $userInfo['userinfo']['is_store'] = 0;
            }

            // 手机号加密码base64
            $userInfo['userinfo'] = $this->_base64En($userInfo['userinfo']);

            $this->success(__('刷新成功'), $userInfo);
        }
        $this->error(__('非法请求'));
    }

    public function hasRepeatPurchase()
    {
        $user = $this->auth->getUser();
        $uptime = $user['uptime']; // 第3个月15开始提示
        $date1 = strtotime("first day of +2 month", $uptime);
        $data2 = strtotime('first day of this month');

        $ds1 = date('Y-m-d', $uptime);
        $ds2 = date('Y-m-d', $date1);
        $ds3 = date('Y-m-d', $data2);
        $user_id = $this->auth->id;
        $username = $user['username'];

        Log::info("hasRepeatPurchase user_id=$user_id,$username 云店时间=$ds1, 开始计算时间=$ds2, 本月01号=$ds3");
        if ($date1 < $data2) {
            // 查询本月是否有 复购


            $sql = <<<AAA
            select count(1) cc from fa_wanlshop_goods c where id in(
                select b.goods_id from (select * from fa_wanlshop_order aa where aa.user_id = $user_id and (aa.state > '1' and aa.state <> '7') AND aa.paymenttime >= $data2 ) a 
                inner join fa_wanlshop_order_goods b on a.id = b.order_id
            ) and activity_type = 'shopgoods';
AAA;
            $ret = DB::query($sql);

            $ss = DB::getLastSql();
            Log::info("hasRepeatPurchase sql=$ss");

            if ($ret[0]['cc'] < 1) {
                Log::info("hasRepeatPurchase user_id=$user_id, false");
                return false;
            }
        }
        return true;
    }

    public function getSvips()
    {
        $user = $this->auth->getUser();
        $vip_level = $user->vip_level;//1普通会员;2VIP会员;3云店店长;4资深店长;5高级店长

        $parent_code = $user['parent_code'] . $user['id'] . '%';

        // 直推9名VIP
        $directNum = model('\app\common\model\User')->where('vip_level', '>=', '2')->where(['inviter_id' => $user->id])->count();
        // 团队累计达99名VIP
        $user_id = $user->id;
        $sql = "SELECT count(1) vlNum FROM fa_user where vip_level >= 2 AND parent_code like '$parent_code'";
        $team = Db::query($sql);
        $vlNum = $team[0]['vlNum'];
        $svip_status = 0;
        if ($vip_level >= 2 && $directNum >= 9 && $vlNum >= 99) {
            $svip_status = 1;
        }
        return $svip_status;
    }


    /**
     * 数据统计 - 内部使用，开发者不要调用
     */
    private function userInfo()
    {
        $user_id = $this->auth->id;
        // 查询订单
        $order = model('app\api\model\wanlshop\Order')
            ->where('user_id', $user_id)
            ->select();
        $orderCount = array_count_values(array_column($order, 'state'));

        // 物流列表
        $logistics = [];
        foreach ($order as $value) {
            if ($value['state'] >= 3 && $value['state'] <= 6) {
                //需要查询的订单
            }
        }
        // 统计数量
        $collection = [];
        $concern = [];
        // 1.1.0升级
        $footgoodsprint = [];
        $footgroupsprint = [];
        foreach (model('app\api\model\wanlshop\GoodsFollow')->where('user_id', $user_id)->select() as $row) {
            if ($row['goods_type'] === 'goods') {
                if (model('app\api\model\wanlshop\Goods')->get($row['goods_id'])) {
                    $collection[] = $row['id'];
                }
            } else if ($row['goods_type'] === 'groups') {
                if (model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])) {
                    $collection[] = $row['id'];
                }
            }
        }
        // 1.0.8升级  通过uuid查询足迹
        $uuid = $this->request->server('HTTP_UUID');
        if (!isset($uuid)) {
            $charid = strtoupper(md5($this->request->header('user-agent') . $this->request->ip()));
            $uuid = substr($charid, 0, 8) . chr(45) . substr($charid, 8, 4) . chr(45) . substr($charid, 12, 4) . chr(45) . substr($charid, 16, 4) . chr(45) . substr($charid, 20, 12);
        }
        foreach (model('app\api\model\wanlshop\Record')->where('uuid', $uuid)->select() as $row) {
            if ($row['goods_type'] === 'goods') {
                if (model('app\api\model\wanlshop\Goods')->get($row['goods_id'])) {
                    $footgoodsprint[] = $row['goods_id'];
                }
            } else if ($row['goods_type'] === 'groups') {
                if (model('app\api\model\wanlshop\groups\Goods')->get($row['goods_id'])) {
                    $footgroupsprint[] = $row['goods_id'];
                }
            }
        }

        // 查询动态 、收藏夹、关注店铺、足迹、红包卡券
        $finish = isset($orderCount[6]) ? $orderCount[6] : 0;
        $pay = isset($orderCount[1]) ? $orderCount[1] : 0;
        $delive = isset($orderCount[2]) ? $orderCount[2] : 0;
        $receiving = isset($orderCount[3]) ? $orderCount[3] : 0;
        $evaluate = isset($orderCount[4]) ? $orderCount[4] : 0;
        // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
        $groups = model('app\api\model\wanlshop\groups\Order')
            ->where('user_id', 'eq', $user_id)
            ->where('state', 'neq', 7)
            ->count();
        // ----- 九星分红权 -----
        $drNum = $this->getDividendRights();
        $userinfo = $this->auth->getUserinfo();

        $userinfo['dr_num'] = $drNum;
        return [
            'userinfo' => $userinfo,
            'statistics' => [
                'dynamic' => [
                    'collection' => count($collection),
                    'concern' => model('app\api\model\wanlshop\find\Follow')->where('user_id', $user_id)->count(),
                    'footprint' => count(array_flip($footgoodsprint)) + count(array_flip($footgroupsprint)),
                    'coupon' => model('app\api\model\wanlshop\CouponReceive')->where(['user_id' => $user_id, 'state' => '1'])->count(),
                    'accountbank' => model('app\api\model\wanlshop\PayAccount')->where('user_id', $user_id)->count()
                ],
                'order' => [
                    'whole' => $finish + $pay + $delive + $receiving + $evaluate,
                    'groups' => $groups,
                    'pay' => $pay,
                    'delive' => $delive,
                    'receiving' => $receiving,
                    'evaluate' => $evaluate,
                    'customer' => model('app\api\model\wanlshop\Refund')->where(['state' => ['in', '1,2,3,6'], 'user_id' => $this->auth->id])->count()
                ],
                'logistics' => $logistics
            ]
        ];
    }

    private function getDividendRights()
    {
        $user = $this->auth->getUserinfo();
        $xf = $user['ns_pay'] - $user['ns_dynamic'] - $user['ns_static'];
        $fhq = floor($xf / 500);
        $bs = 1;
        if ($user['vip_level'] == 2) $bs = 2;
        if ($user['vip_level'] > 2) $bs = 5;
        if ($bs < 5) {
            if ($user['monthly_ns'] >= 9 && $user['ns_status'] == '1' && $user['invite_numsns'] >= 9) $bs = 3;
        }
        $fhq = $fhq * $bs;
        return $fhq;
    }


    /**
     * 获取评论列表
     *
     * @ApiSummary  (WanlShop 获取我的所有评论)
     * @ApiMethod   (GET)
     *
     * @param string $list_rows 每页数量
     * @param string $page 当前页
     */
    public function comment()
    {
        $list = model('app\api\model\wanlshop\GoodsComment')
            ->where('user_id', $this->auth->id)
            ->field('id,images,score,goods_id,order_goods_id,state,content,createtime')
            ->order('createtime desc')
            ->paginate()
            ->each(function ($data, $key) {
                $data['order_goods'] = $data->order_goods ? $data->order_goods->visible(['id', 'title', 'image', 'price']) : '';
                return $data;
            });
        $this->success('返回成功', $list);
    }

    /**
     * 获取积分明细
     */
    public function scoreLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\common\model\ScoreLog')
                ->where('user_id', $this->auth->id)
                ->order('createtime desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function getmarket()
    {
        $user = $this->auth->getUser();
        $data = array();
        $data['m1'] = $user['pam1n'];
        $data['m2'] = $user['pam2n'];
        $this->success('返回成功', $data);
    }

    /**
     * 获取推荐会员列表： 直推会员数/直推团队人数
     */
    public function invitelist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $postData = $this->request->post();

            $sort = array_get($postData, 'sort', 'yd2');

            $parent_code = $this->auth->parent_code;
            $userId = $this->auth->id;
            $lvname = model('app\admin\model\UserVipLevel')->column('id,name');
            $lvsname = model('app\admin\model\UserSvipLevel')->column('id,name');

            $firstDayOfWeek = strtotime("this week"); // 获取本周第一天
            $lastDayOfWeek = strtotime("this week +6 days"); // 获取本周最后一天

            $firstDayOfWeek2 = strtotime("this week -7 days"); // 获取本周第一天 - 7


            //
            $sql = <<<EOD
                aa.id,aa.username,ifnull(aa.truename,aa.nickname)nickname,aa.mobile,aa.avatar,aa.vipv_level,aa.vip_level,aa.svip_level,aa.invite_nums,
                aa.team_nums,aa.teams_nums,aa.team_am,aa.self_amt,aa.jointime,aa.self_amd0,
                (select count(1) from fa_user a where a.inviter_id=aa.id and a.vipv_level >= 2 ) svNum,
                (select count(1) from fa_user a where a.parent_code like CONCAT('$parent_code$userId,',aa.id,'%') and a.vipv_level >= 2) tsvNum,
                (select count(1) from fa_user a where a.inviter_id=aa.id ) anum,
                (select count(1) from fa_user b where b.self_amd0 = 4995 and b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) amdnum,
                (select count(1) from fa_user b where b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) bnum,
                (select count(1) from fa_user b where uptime < $firstDayOfWeek and b.vip_level>2 and b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) yd0,
                (select count(1) from fa_user b where uptime between $firstDayOfWeek and $lastDayOfWeek and b.vip_level>2 and b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) yd1,
                (select count(1) from fa_user b where b.vip_level>2 and b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) yd2,
                (select count(1) from fa_user b where uptime < $firstDayOfWeek2 and b.vip_level>2 and b.parent_code like CONCAT('$parent_code$userId,',aa.id,'%')) yd3
EOD;

//            echo $sql;exit;
            $list = model('app\common\model\User')->alias('aa')
                ->where('inviter_id', $this->auth->id)
                ->order($sort . ' desc, createtime asc')
                ->field($sql)
                ->paginate();

            foreach ($list as $key => $val) {
                $list[$key]['sort'] = $key + 1;
                $list[$key]['sort'] = $key + 1;
                if ($val['vipv_level'] == 2) {
                    $list[$key]['lvname'] = '高级会员';
                } else {
                    $list[$key]['lvname'] = '普通会员';
                }
//                if ($val['vip_level'] > 2) {
//                    if ($val['self_amd0'] == 4995) {
//                        $list[$key]['lvname'] .= "/旗舰店长";
//                    } else {
//                        $list[$key]['lvname'] .= "/" . $lvname[$val['vip_level']];
//                    }
//                }
                if (mb_strlen($val['nickname']) < 5) {
                    // 加星
                    $list[$key]['nickname'] = $this->deseName($val['nickname']);
                }
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    private function deseName($name)
    {
        return mb_substr($name, 0, 1) . '*' . mb_substr($name, mb_strlen($name) - 1, 1);
    }

    public function friends()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            //默认返回好友
            $where = ['inviter_id' => $this->auth->id];
            $username = $this->request->post('username');
            $username && $where = ['username' => $username];
            $list = model('\app\common\model\User')
                ->where($where)
                ->order('iv_sort asc, id asc')
                ->field('(select name from fa_user_vip_level where id = vip_level) vip_name, id,avatar,username,nickname,pid,pnet_iv,pnet,pnet_dz,pam1,pam2,pam1n,pam2n,pam1d,pam2d,pnums1,pnums2,vip_level,invites_nums,teams_nums,self_amd0,jointime,createtime,activetime')
                ->select();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function findUser()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            //默认返回好友
            $username = $this->request->post('username');
            if ($username) {
                $fUser = model('\app\common\model\User')
                    ->where('username', $username)
                    ->where('vip_level', '<', 4)
                    ->order('iv_sort asc, id asc')
                    ->field('(select name from fa_user_vip_level where id = vip_level) vip_name, id,inviter_id,avatar,username,nickname,pid,pnet_iv,pnet,pnet_dz,pam1,pam2,pam1n,pam2n,pam1d,pam2d,pnums1,pnums2,vip_level,invites_nums,teams_nums,self_amd0,jointime,createtime,activetime')
                    ->find();
                $ivUser = $fUser;
                $isMyTeam = false;
                while ($ivUser && !$isMyTeam) {
                    if ($this->auth->id == $ivUser->inviter_id) {
                        $isMyTeam = true;
                    } else {
                        $ivUser = model('\app\common\model\User')->where('id', $ivUser->inviter_id)->find();
                    }
//                    if ($this->auth->svip_lv_dkq == 1) break;
                }
                if ($isMyTeam) $this->success('ok', [$fUser]);
                else $this->error(__('没有找到该会员或该会员已经是最高级别'));
            }
        }
        $this->error(__('非法请求'));
    }

    public function setPnet()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $postData = $this->request->post();
            $fid = $postData['fid'];
            $pnet = $postData['pnet'];
            $yp_username = $this->request->param('yp_username', 0);
            $ypid = 0;
            if ($yp_username) {
                //查询对应会员信息
                $yp_info = model('\app\common\model\User')->where('username', $yp_username)->find();
                if (!$yp_info) {
                    $this->error('编号有误，请重新填写');
                }
                // 等级需激活才可以设置
                if ($pnet == 0 && $yp_info['vip_level'] < 2) {
                    $this->error('YFL编号未激活无法安置！');
                }
                if ($yp_info['id'] == $fid) {
                    $this->error('编号有误，不可使用本人编号');
                }
                $ypid = $yp_info['id'];
            }

            \app\common\model\User::update(['pnet' => $pnet, 'pnet_dz' => $pnet, 'ypid' => $ypid], ['id' => $fid, 'inviter_id' => $this->auth->id]);
            $this->success('ok', []);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取推荐云店关系列表
     */
    public function storelist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $postData = $this->request->post();

            $sort = array_get($postData, 'sort', 'market_kh');

            $loginId = $this->auth->id;
            $saasId = SAAS;

            $parent_code = UserTrident2n::where(['user_id' => $loginId])->value('parent_code');
            // 直推旗舰   团队旗舰总数
            // yd2:累计所有;    yd1:本周新增;   yd0:累积到上一周;     yd3:上上周云店人数;    yd4:上周新增
//            yd2:累计所有 team_nums;yd1:本周新增week_nums，yd0:累积到上一周yd2-yd1，
//            yd3:上上周云店人数yd0-yd4;yd4:上周新增weekl_nums

            // team_nums
            $sortStr = "a.$sort asc";
            if ($sort == 'team_nums') {
                $sortStr = "a.team_nums desc";
            }
            $sql = <<<ABC

            select 
            
            b.mobile,b.truename, b.uptime,b.vip_level,b.self_amd0,
            a.invite_nums,a.team_nums,a.week_nums,a.weekl_nums,a.week_nums2,a.month_nums, a.state,a.status,
            
            (a.team_nums - a.week_nums) yd0,
            a.week_nums yd1, 
            a.team_nums yd2,       
            (a.team_nums - a.week_nums - a.weekl_nums) yd3,
            a.weekl_nums yd4,
                   
            (select count(1) from fa_user_trident2n aa where aa.rid = a.user_id and exists(select id from fa_user c where c.id = aa.user_id and c.self_amd0 >= 4995)) iamd, 
            (select count(1) from fa_user_trident2n aa where aa.parent_code like CONCAT('$parent_code$loginId,',a.user_id,'%') and exists(select id from fa_user c where c.id = aa.user_id and c.self_amd0 >= 4995)) tamd
            
            from fa_user_trident2n a
            left join fa_user b on a.user_id = b.id
            where a.rid = $loginId
            and a.saas_id = $saasId
            and a.state = '1'
            and a.status = '0'
            
            order by $sortStr
ABC;

            $list = DB::query($sql);
            $arr = [];
            foreach ($list as $key => $val) {
                $list[$key]['sort'] = $key + 1;
                if ($key < 3) {
                    $list[$key]['index'] = $key;
                    $arr[] = &$list[$key];
                }
                $list[$key]['truename'] = $this->deseName($val['truename']);
            }
            // 前3按
            if (count($arr) > 0) {
                usort($arr, function ($a, $b) {
                    return $b['yd2'] <=> $a['yd2'];
                });
                $paiMing = ['第一名', '第二名', '第三名', '第四名'];
                foreach ($arr as $key => $item) {
                    $list[$item['index']]['sort2'] = $paiMing[$key];
                }
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    // vipv_level vip_level
    private function countUserLevel($userId, $type, $levelType, $lvname, $lvsname, $parent_code)
    {

        if ($type == 1) {// 直推
            $condition = " and inviter_id = $userId ";
        } else { // if ($type == 2) { // 团队
            $condition = " and parent_code like '$parent_code$userId,%' ";
        }

        if ($levelType == 'vip_level') { // 云店，旗舰店，星级统计
            $sql = <<<EOD
            select
                count(1) vlNum,
                case 
                    when vipd_level > 1 then concat(vipd_level,'星合伙人')
                    when self_amd0 = 4995 then '旗舰店'
                    when vip_level > 2 then '云店店长'
                end	'aaa'
            from fa_user where 1=1 $condition group by aaa having aaa is not null;
EOD;
        } else { // if($levelType == 'vipv_level') { // vipv_level 普通会员，高级会员
            $sql = "SELECT vipv_level,count(1) vlNum FROM fa_user where 1=1 $condition group by vipv_level";
        }

        $list = Db::query($sql);
        foreach ($list as $key => $val) {
            if (isset($val['vipv_level'])) {
                $list[$key]['lvname'] = '普通会员';
                if ($val['vipv_level'] == 2) {
                    $list[$key]['lvname'] = '高级会员';
                }
            }
            if (isset($val['aaa'])) {
                $list[$key]['lvname'] = $val['aaa'];
            }
        }
        return $list;
    }

    /**
     * @param $type 1直推分布；2团队分布；
     * @return void
     */
    public function countUserLevelDist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {

            $type = $this->request->post()['type'];
            $type = empty($type) ? 1 : $type;

            $uid = $this->request->post()['uid'];
            $uid = empty($uid) ? $this->auth->id : $uid;
            $parent_code = model('app\common\model\User')->where('id', $uid)->value('parent_code');

            $lvname = model('app\admin\model\UserVipLevel')->column('id,name');
            $lvsname = model('app\admin\model\UserSvipLevel')->column('id,name');

            $vipLevel = $this->countUserLevel($uid, $type, 'vipv_level', $lvname, $lvsname, $parent_code);
            $svipLevel = $this->countUserLevel($uid, $type, 'vip_level', $lvname, $lvsname, $parent_code);

            $retData = array(
                'vipLevel' => $vipLevel,
                'svipLevel' => $svipLevel,
            );
            $this->success('ok', $retData);
        }
        $this->error(__('非法请求'));
        exit;
    }

    /**
     * 云店分布
     * @param $type 1直推分布；2团队分布；
     * @return void
     */
    public function countStoreUserLevelDist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {

            $type = $this->request->post()['type'];
            $type = empty($type) ? 1 : $type;

            $uid = $this->request->post()['uid'];
            $uid = empty($uid) ? $this->auth->id : $uid;
            $parent_code = model('app\common\model\User')->where('id', $uid)->value('parent_code');

            $lvname = model('app\admin\model\UserVipLevel')->column('id,name');
            $lvsname = model('app\admin\model\UserSvipLevel')->column('id,name');

            $userids = model('app\admin\model\UserTrident2n')->getAllSubordinates($this->auth->id);

            $vipLevel = $this->countStoreUserLevel($uid, $type, 'vipv_level', $lvname, $lvsname, $parent_code, $userids);
            $svipLevel = $this->countStoreUserLevel($uid, $type, 'vip_level', $lvname, $lvsname, $parent_code, $userids);

            $retData = array(
                'vipLevel' => $vipLevel,
                'svipLevel' => $svipLevel,
            );
            $this->success('ok', $retData);
        }
        $this->error(__('非法请求'));
        exit;
    }

    // 云分布
    private function countStoreUserLevel($userId, $type, $levelType, $lvname, $lvsname, $parent_code, $ids)
    {

        if ($type == 1) {// 直推
            $condition = " and rid = $userId ";
        } else { // if ($type == 2) { // 团队
            $ids = implode(',', $ids);
            !$ids && $ids = $userId;
            $condition = " and user_id in ($ids)";
        }

        if ($levelType == 'vip_level') { // 云店，旗舰店，星级统计
            $sql = <<<EOD
            select
                count(1) vlNum,
                case 
                    when fa_user.vipd_level > 1 then concat(fa_user.vipd_level,'星合伙人')
                    when fa_user.self_amd0 = 4995 then '旗舰店'
                    when fa_user.vip_level > 2 then '云店店长'
                end 'aaa'
            from fa_user_trident2n left join fa_user on fa_user_trident2n.user_id = fa_user.id where 1=1 $condition group by aaa having aaa is not null;
EOD;
        } else { // if($levelType == 'vipv_level') { // vipv_level 普通会员，高级会员
            $sql = "SELECT fa_user.vipv_level,count(1) vlNum FROM fa_user_trident2n left join fa_user on fa_user_trident2n.user_id = fa_user.id where 1=1 $condition group by fa_user.vipv_level";
        }

        $list = Db::query($sql);
        foreach ($list as $key => $val) {
            if (isset($val['vipv_level'])) {
                $list[$key]['lvname'] = '普通会员';
                if ($val['vipv_level'] == 2) {
                    $list[$key]['lvname'] = '高级会员';
                }
            }
            if (isset($val['aaa'])) {
                $list[$key]['lvname'] = $val['aaa'];
            }
        }
        return $list;
    }

    /**
     * 获取推荐会员列表
     */
    public function nodelist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $lvname = model('app\admin\model\UserVipLevel')->column('id,name');
            $lvsname = model('app\admin\model\UserSvipLevel')->column('id,name');
            $list = model('app\admin\model\UserTrident30')
                ->where('state', '1')
                ->where('user_id', $this->auth->id)
                ->order('id', 'asc')
//                ->field('id,username,nickname,mobile,avatar,vip_level,svip_level,invite_nums,team_nums,teams_nums,team_am,self_amt,jointime')
                ->paginate();
            foreach ($list as $key => $val) {
                for ($i = 3; $i < 11; $i++) {
                    $ninfo = model('app\admin\model\UserTrident3')
                        ->where('nid', $val['nid'])
                        ->where('user_id', $val['user_id'])->find();
                    if ($ninfo) $list[$key] = $ninfo;
                }
                $list[$key]['username'] = $this->auth->username;
                $list[$key]['lvname'] = $lvname[$list[$key]['lv']];
                for ($i = -1; $i < 2; $i++) {
                    if ($list[$key]['lv'] == 2) {
                        $xinfo = model('app\admin\model\UserTrident30')
                            ->where('nid', $val['nid'] * 3 + $i)->field('user_id')->find();
                    } else {
                        $xinfo = model('app\admin\model\UserTrident3')
                            ->where('nid', $val['nid'] * 3 + $i)->field('user_id')->find();
                    }
                    if ($xinfo) {
                        $xu = model('app\common\model\User')->where('id', $xinfo['user_id'])->field('id,username,mobile')->find();
                        $list[$key]['xinfo_' . ($i + 1)] = $xu['username'];
                    }
                }
//                $list[$key]['lvname'] = $lvname[$val['vip_level']];
//                if($val['svip_level'] > 1) $list[$key]['lvname'] .= "(".$lvsname[$val['svip_level']].")";
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /**
     * 获取推荐会员列表
     */
    public function unioninvitelist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $lvname = model('app\admin\model\UserVipLevelu')->column('id,name');
            $lvsname = model('app\admin\model\UserSvipLevelu')->column('id,name');
            $list = model('app\common\model\User')
                ->where('inviter_id', $this->auth->id)
//                ->where('vip_statusu', 1)
                ->order('createtime desc')
                ->field('id,username,nickname,mobile,avatar,vip_levelu,svip_levelu,invite_numsu,team_numsu')
                ->paginate();
            foreach ($list as $key => $val) {
                $list[$key]['lvname'] = $lvname[$val['vip_levelu']];
                if ($val['svip_levelu'] > 1) $list[$key]['lvname'] .= "(" . $lvsname[$val['svip_levelu']] . ")";
            }

            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    /*
     * 获取安置列表
     */
    public function pnetlist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $data = [];
            $lvname = model('app\admin\model\UserVipLevel')->column('id,name');
            $loops = 1;
            $pid = $this->request->post('pid');
            // 可以看 7088	YFL00002011	吳麗彤
            if (!in_array($this->auth->id, [7088, -1])) {
                $pid = $this->auth->id;
            } else {
                $pid = $pid ? $pid : $this->auth->id;
            }
            //判断上下级
            $dline = false;
            $puser['pid'] = $pid;
            if ($pid == $this->auth->id) $dline = true;
            while (!$dline && $puser['pid'] > 0) {
                $puser = model('app\common\model\User')
                    ->where('id', $puser['pid'])
                    ->field('id, pid')
                    ->find();
                if ($puser['id'] == $this->auth->id) {
                    $dline = true;
                }
            }
            /**
             * sum_perf 总业绩
             * residue_perf 剩余业绩
             * remain_perf 留存业绩
             * week_perf 本周业绩
             */
            if (!$dline) $this->error(__('没有权限'));
            $data[0] = model('app\common\model\User')
                ->where('id', $pid)
                ->order('createtime desc')
                ->field('0 sum_perf,0 residue_perf,0 remain_perf,0 week_perf,id,username,nickname,pid,pnet,pam1,pam2,pam1n,pam2n,pam1d,pam2d,pnums1,pnums2,vip_level,invites_nums,teams_nums,self_amd0,jointime,createtime,activetime')
                ->find();
            $data[0]['lvname'] = $lvname[$data[0]['vip_level']];
            $list = model('app\common\model\User')
                ->where('pid', $pid)
                ->order('pnet asc')
                ->field('0 sum_perf,0 residue_perf,0 remain_perf,0 week_perf,id,username,nickname,pid,pnet,pam1,pam2,pam1n,pam2n,pam1d,pam2d,pnums1,pnums2,vip_level,invites_nums,teams_nums,self_amd0,jointime,createtime,activetime')
                ->select();
            foreach ($list as $key => $val) {
                $data[$key + 1] = $val;
                $data[$key + 1]['lvname'] = $lvname[$val['vip_level']];
                $loops++;
            }
            $this->success('ok', array('data' => $data));
        }
        $this->error(__('非法请求'));
    }

    public function communitylist()
    {
        $this->lvsq = array(
            1 => array(
                's' => 0,
                'x' => 0
            ),
            2 => array(
                's' => 5,
                'x' => 10
            ),
            3 => array(
                's' => 10,
                'x' => 20
            ),
            4 => array(
                's' => 15,
                'x' => 25
            )
        );
        $count = 0;
        $sid = 0;
        $xid = 0;
        $us = model('app\admin\model\UserSequential')->where('user_id', $this->auth->id)->find();
        if ($us) {
            $sid = (($us['id'] - $this->lvsq[$this->auth->vip_level]['s']) > 0) ? ($us['id'] - $this->lvsq[$this->auth->vip_level]['s']) : 1;
            $xid = $us['id'] + $this->lvsq[$this->auth->vip_level]['x'];
            $count = model('app\admin\model\UserSequential')
                ->where('id', '>=', $sid)
                ->where('id', '<=', $xid)
                ->order('id asc')
                ->count();
            $count = $count - 1;
        }
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model('app\admin\model\UserSequential')
                ->where('id', '>=', $sid)
                ->where('id', '<=', $xid)
                ->order('id asc')
                ->field('user_id,s_no,amount,state')
                ->paginate();
            foreach ($list as &$v) {
                $v['s_no'] = substr($v['s_no'], -18);
            }
            $this->success('ok', $list);
        } else return $count;
//        $this->error(__('非法请求'));
    }

    /**
     * 获取用户级别
     */
    public function getuserlvname()
    {
        $user = $this->auth->getUser();
        $data = \app\common\model\UserVipLevel::where('id', $user['vip_level'])->field('name')->find();
        if ($user["vip_level"] == 2 && $user["team_nums"] >= 99) {
            $data['name'] == 'SVIP会员';
        }
        if ($user['self_amd0'] == "4995") {
            $data['name'] = '旗舰店长';
        }
        $data2 = \app\common\model\UserSvipLevel::where('id', $user['svip_level'])->field('name')->find();
        $data['name2'] = $data2['name'];


        $data3 = \app\admin\model\UserSvipLvDkq::where('id', $user['svip_lv_dkq'])->field('name')->find();
        $data['lv3'] = $user['svip_lv_dkq'];
        $data['name3'] = $data3['name'];

        // 线下门店名称
        $shop = \app\api\model\wanlshop\shop\Shop::get(['user_id' => $this->auth->id]);
        $data['showName'] = $user['username'];
        if ($shop && $shop['state'] == 4) {
            $data['showName'] = $shop['shopname'];
        } else if ($user['truename']) {
            $data['showName'] = $user['truename'];
        } else if ($user['nickname']) {
            $data['showName'] = $user['nickname'];
        }

        $data['self_amt'] = $user['self_amt'];
        $data['invitenums'] = $user['invite_nums'];
        $data['teamnums'] = $user['team_nums'];
        $data['sqnums'] = $this->communitylist();
        $data['teamam'] = round($user['team_am'] / 10000, 2);
        // currency_points: '0.00', // 余额//XYT校园通证
        // currency_tz: '0.00', // 余额//ZJT中健通证
        // money: '0.00', // 余额//XYT校园通证
        // score: 0, // 积分
        $data['currency_bd'] = $user["currency_bd"];
        $data['currency_blc'] = $user["currency_blc"];
        $data['currency_rmb'] = $user["currency_rmb"];
        $data['currency_cny'] = $user["currency_cny"];
        $data['currency_ns'] = $user["currency_ns"];
        $data['currency_gp'] = $user["currency_gp"];
        $data['currency_xnb'] = $user["currency_xnb"];
        $data['currency_nfr'] = $user["currency_nfr"];
        $data['currency_pu'] = $user["currency_pu"];
//        $data['currency_usdt'] = $user["currency_usdt"];
//        $data['currency_fil'] = $user["currency_fil"];
//        $data['currency_old'] = $user["currency_old"];
//        $data['currency_lmt'] = $user["currency_lmt"];
        $data['currency_points'] = $user["currency_points"];
//        $data['currency_tz'] = $user["currency_tz"];
//        $data['currency_gq'] = $user["currency_gq"];
        $data['vip_level'] = $user["vip_level"];
        $data['svip_level'] = $user["svip_level"];
        $data['is_certified'] = $user['is_certified'];
        $data['money'] = $user["money"];
        $data['score'] = $user["score"];
        $data['static_bonus'] = $user['static_bonus'];
        $data['dynamic_bonus'] = $user['dynamic_bonus'];
        $data['useriv'] = model('app\common\model\User')
            ->where('id', $this->auth->inviter_id)
            ->field('id,username,mobile')
            ->find();
        //查询是否有线下活动
        $data['offline_activity'] = OfflineActivity::where(['end_time' => ['>', time()], 'saas_id' => SAAS, 'status' => 1])->order('id desc')->find() ? true : false;
        $this->success('返回成功', $data);
    }

    /**
     * 获取用户级别
     */
    public function getunioninfo()
    {
        $user = $this->auth->getUser();
        $data = \app\common\model\UserVipLevelu::where('id', $user['vip_levelu'])->field('name')->find();
        $data2 = \app\common\model\UserSvipLevelu::where('id', $user['svip_levelu'])->field('name')->find();
        $data['name'] = $user['vip_statusu'] == 0 ? '消费会员' : $data['name'];
        $data['name2'] = $data2['name'];
        $data['currency_bd'] = $user["currency_bd"];
        $data['currency_pu'] = $user["currency_pu"];
        $data['currency_tzu'] = $user["currency_tzu"];
        $data['currency_cny'] = $user["currency_cny"];
        $data['vip_statusu'] = $user["vip_statusu"];
        $data['vip_levelu'] = $user["vip_levelu"];
        $data['svip_levelu'] = $user["svip_levelu"];
        $data['invitenums'] = $user["invite_numsu"];
        $data['invitenumsnew'] = $user["invite_numsu_new"];
        $data['teamnums'] = $user["team_numsu"];
        $data['teamnumsnew'] = $user["team_numsu_new"];
        $data['teamam'] = $user["team_amu"];
        $data['teamamnew'] = $user["team_amu_new"];
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('shop_id', 4)
            ->where('state', '>', 1)
            ->where('state', '<', 7)
            ->where('statusb', '1')
            ->where('user_id', $user['id'])
            ->order('daysc desc')->find();
        $data['fgdays'] = $order['daysc'];

        $this->success('返回成功', $data);
    }

    public function getuserlv()
    {
        $user = $this->auth->getUser();
        $data = array();
        $lvname = model('app\admin\model\UserSvipLevel')->column('id,name');
        $data['uname'] = $user['username'];
        $data['tname'] = $user['truename'];
        $data['lv'] = $user['vip_level'];
        $data['slv'] = $user['svip_level'];
        $data['lvu'] = $user['vip_levels'];
        $data['slvn'] = $lvname[$user['svip_level']];
        if (array_key_exists($user['svip_level'] + 1, $lvname)) {
            $data['slvx'] = $lvname[$user['svip_level'] + 1];;
        } else {
            $data['slvx'] = 'NULL';
        }
        $this->success('返回成功', $data);
    }

    public function getuserpayinfo()
    {
        $user = $this->auth->getUser();
        $data = array();
        $data['truename'] = $user['truename'];
        $data['alipay'] = $user['alipay'];
        $data['autopay'] = $user['autopay'];
        $this->success('返回成功', $data);
    }

    public function getIvuserInfo()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $puser = model('app\common\model\User')
                ->where('id', $id)
                ->where('inviter_id', $this->auth->id)
                ->field('id,username,pid,vip_status')
                ->find();
            $pinfo = model('app\common\model\User')
                ->where('id', $puser['pid'])
                ->field('username')
                ->find();
            $puser['pusername'] = $pinfo ? $pinfo['username'] : '';
            $this->success('返回成功', $puser);
        }
        $this->error(__('非法请求'));
    }

    public function getuserauth()
    {
        $user = $this->auth->getUser();
        $data = array();
        $data['truename'] = $user['truename'];
        $data['idcard'] = $user['idcard'];
        $data['is_certified'] = $user['is_certified'];
        $this->success('返回成功', $data);
    }

    public function puserchange()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $username = $this->request->post('username');
            $pusername = $this->request->post('pusername');
            $user = model('app\common\model\User')
                ->where('username', $username)
                ->where('inviter_id', $this->auth->id)
                ->find();
            $puser = model('app\common\model\User')
                ->where('username', $pusername)
                ->find();
            if ($user && $puser) {
                if ($user['vip_status'] == '1') {
                    $this->error(__('会员已经激活无法更改安置商家'));
                }
                model('app\common\model\User')
                    ->where('username', $username)
                    ->update(['pid' => $puser['id']]);
                $this->success('修改成功', '');
            } else {
                $this->error(__('信息提交异常'));
            }
        }
        $this->error(__('非法请求'));
    }

    public function rankinglist()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $hour = date('H');
            $settletime = model('app\common\model\Settletime')->where('year', $year)->where('month', $month)->find();
            if ($settletime) {
                $thismonthtt = $settletime['thismonthtt'];
                $thisseasontt = model('app\common\model\Settletime')->where('season', $settletime['season'])->sum('thismonthtt');
            } else {
                $thismonthtt = 0;
                $thisseasontt = 0;
            }
            $btrate = array(
                1 => 0.5,
                2 => 0.20,
                3 => 0.15,
                4 => 0.1,
                5 => 0.05
            );
            $loops = 1;
            foreach (model('app\common\model\User')->where('id', '>', 107)->where('invite_amm', '>', 0)->order('invite_amm desc')->limit(5)->select() as $users) {
                $list['month'][$loops - 1]['nickname'] = $users['nickname'];
                $list['month'][$loops - 1]['invite_numsm'] = $users['invite_numsm'];
                $list['month'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['month'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['month'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['month'][$loops - 1]['amedu'] = round($thismonthtt * 0.05 * $btrate[$loops], 2);
                $list['month'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $loops = 1;
            foreach (model('app\common\model\User')->where('id', '>', 107)->where('invite_ams', '>', 0)->order('invite_ams desc')->limit(5)->select() as $users) {
                $list['season'][$loops - 1]['nickname'] = $users['nickname'];
                $list['season'][$loops - 1]['invite_numsm'] = $users['invite_numsm'];
                $list['season'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['season'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['season'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['season'][$loops - 1]['amedu'] = round($thisseasontt * 0.025 * $btrate[$loops], 2);
                $list['season'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $btratelv = array(
                4 => 0.1,
                5 => 0.2,
                6 => 0.3,
                7 => 0.4
            );
            $lvmemnums = array(
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0
            );
            $loops = 1;
            $managett[4] = model('app\common\model\Settletime')->where('area', 0)->sum('thismonthtt');
            $lvmemnums[4] = model('app\common\model\User')->where('vip_level', 4)->count();
            $list['manage4am'] = round($managett[4] * 0.025 * $btratelv[4], 2);
            foreach (model('app\common\model\User')->where('vip_level', 4)->order('vip_level desc')->limit(false)->select() as $users) {
                $list['manage4'][$loops - 1]['nickname'] = $users['nickname'];
                $list['manage4'][$loops - 1]['invite_numsm'] = round($managett[4] * 0.025 * $btratelv[$users['vip_level']], 2);//$users['invite_numsm'];
                $list['manage4'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['manage4'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['manage4'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['manage4'][$loops - 1]['amedu'] = round($managett[4] * 0.025 * $btratelv[$users['vip_level']] / $lvmemnums[$users['vip_level']], 2);
                $list['manage4'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $loops = 1;
            $managett[5] = model('app\common\model\Settletime')->where('city', 0)->sum('thismonthtt');
            $lvmemnums[5] = model('app\common\model\User')->where('vip_level', 5)->count();
            $list['manage5am'] = round($managett[5] * 0.025 * $btratelv[5], 2);
            foreach (model('app\common\model\User')->where('vip_level', 5)->order('vip_level desc')->limit(false)->select() as $users) {
                $list['manage5'][$loops - 1]['nickname'] = $users['nickname'];
                $list['manage5'][$loops - 1]['invite_numsm'] = round($managett[5] * 0.025 * $btratelv[$users['vip_level']], 2);//$users['invite_numsm'];
                $list['manage5'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['manage5'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['manage5'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['manage5'][$loops - 1]['amedu'] = round($managett[5] * 0.025 * $btratelv[$users['vip_level']] / $lvmemnums[$users['vip_level']], 2);
                $list['manage5'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $loops = 1;
            $managett[6] = model('app\common\model\Settletime')->where('province', 0)->sum('thismonthtt');
            $lvmemnums[6] = model('app\common\model\User')->where('vip_level', 6)->count();
            $list['manage6am'] = round($managett[6] * 0.025 * $btratelv[6], 2);
            foreach (model('app\common\model\User')->where('vip_level', 6)->order('vip_level desc')->limit(false)->select() as $users) {
                $list['manage6'][$loops - 1]['nickname'] = $users['nickname'];
                $list['manage6'][$loops - 1]['invite_numsm'] = round($managett[6] * 0.025 * $btratelv[$users['vip_level']], 2);//$users['invite_numsm'];
                $list['manage6'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['manage6'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['manage6'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['manage6'][$loops - 1]['amedu'] = round($managett[6] * 0.025 * $btratelv[$users['vip_level']] / $lvmemnums[$users['vip_level']], 2);
                $list['manage6'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $loops = 1;
            $managett[7] = model('app\common\model\Settletime')->where('partner', 0)->sum('thismonthtt');
            $lvmemnums[7] = model('app\common\model\User')->where('vip_level', 7)->count();
            $list['manage7am'] = round($managett[7] * 0.025 * $btratelv[7], 2);
            foreach (model('app\common\model\User')->where('vip_level', 7)->order('vip_level desc')->limit(false)->select() as $users) {
                $list['manage7'][$loops - 1]['nickname'] = $users['nickname'];
                $list['manage7'][$loops - 1]['invite_numsm'] = round($managett[7] * 0.025 * $btratelv[$users['vip_level']], 2);//$users['invite_numsm'];
                $list['manage7'][$loops - 1]['invite_numss'] = $users['invite_numss'];
                $list['manage7'][$loops - 1]['invite_amm'] = $users['invite_amm'] / 10000;
                $list['manage7'][$loops - 1]['invite_ams'] = $users['invite_ams'] / 10000;
                $list['manage7'][$loops - 1]['amedu'] = round($managett[7] * 0.025 * $btratelv[$users['vip_level']] / $lvmemnums[$users['vip_level']], 2);
                $list['manage7'][$loops - 1]['grade_name'] = model('app\common\model\UserVipLevel')->where('id', $users['vip_level'])->field('name')->find()['name'];
                $loops++;
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function upgradeinfo()
    {
        $data = array();
        $user = $this->auth->getUser();
        $iscan = false;
        $lvinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,moq,price');
//            当前级别
        $uplv = $user['svip_level'] + 1;
        $uulinfo = model('app\admin\model\UserUpgradeLog')
            ->where('pay_user_id', $this->auth->id)
            ->where('lv_old', $user['svip_level'])
            ->where('lv_new', $uplv)
            ->where('item', '>', 0)
            ->find();
        if (!$uulinfo) {
            if ($uplv < 5) {
                $iscan = true;
                $margin = $lvinfo[$uplv]['margin'] - $lvinfo[$user['svip_level']]['margin'];
                $orderam = $lvinfo[$uplv]['moq'] * $lvinfo[$uplv]['price'];
            } else {
                $count = model('app\common\model\User')
                    ->where('inviter_id', $this->auth->id)
                    ->count();
                if ($count >= $lvinfo[$uplv]['upgrade']) {
                    $iscan = true;
                    $margin = $lvinfo[$uplv]['margin'] - $lvinfo[$user['svip_level']]['margin'];
                    $orderam = $lvinfo[$uplv]['moq'] * $lvinfo[$uplv]['price'];
                } else {
                    $iscan = false;
                    $this->error(__('不符合升级条件'));
                }
            }
            $cid = 0;
            if ($margin > 0) $cid = 1;
            $pid = 1;
            if ($iscan) {
                $rid = $user['inviter_id'];
                $fsj = true;
                while ($fsj) {
                    $puser = model('app\common\model\User')
                        ->where('id', $rid)
                        ->find();
                    if ($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $orderam) {
                        $pid = $puser['id'];
                        $fsj = false;
                    }
                    $rid = $puser['inviter_id'];
                    if ($puser['id'] == 1) $fsj = false;
                }
            }
            $uul_arr = array();
            $uul_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $uul_arr['pay_user_id'] = $this->auth->id;
            $uul_arr['rec_user_id'] = $pid;
            $uul_arr['cpn_user_id'] = $cid;
            $uul_arr['lv_old'] = $user['svip_level'];
            $uul_arr['lv_new'] = $uplv;
            $uul_arr['orderam'] = $orderam;
            $uul_arr['margin'] = $margin;
            $uul_arr['amount'] = $orderam + $margin;
            if ($margin > 0) $uul_arr['item'] = '1';
            model('app\admin\model\UserUpgradeLog')->insert($uul_arr);

            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $this->auth->id)
                ->where('lv_old', $user['svip_level'])
                ->where('lv_new', $uplv)
                ->find();
        }

        $data = $uulinfo;
        $data['paytime1_text'] = $uulinfo['paytime1'] ? date("Y-m-d H:i:s", $uulinfo['paytime1']) : '';
        $data['paytime2_text'] = $uulinfo['paytime2'] ? date("Y-m-d H:i:s", $uulinfo['paytime2']) : '';
        $data['confirmtime1_text'] = $uulinfo['confirmtime1'] ? date("Y-m-d H:i:s", $uulinfo['confirmtime1']) : '';
        $data['confirmtime2_text'] = $uulinfo['confirmtime2'] ? date("Y-m-d H:i:s", $uulinfo['confirmtime2']) : '';
        if ($uulinfo['orderam'] > 0) {
            $data['pbinfo'] = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $uulinfo['rec_user_id']])
                ->order('createtime desc')
                ->select();
        }
        if ($uulinfo['margin'] > 0) {
            $data['cbinfo'] = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $uulinfo['cpn_user_id']])
                ->order('createtime desc')
                ->select();
        }
        $data['oldlv'] = $lvinfo[$data['lv_old']]['name'];
        $data['newlv'] = $lvinfo[$data['lv_new']]['name'];
        $this->success('ok', $data);
    }

    public function upgrade()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $act = $this->request->post('act');
            $lv = $this->request->post('lv');
            if ($act == 'up') {
                //匹配打款 预入网
                //判断是否首次入网
                $trident = 'trident' . $lv;
                if ($this->auth->vip_status == 1 && $this->auth->$trident == 0) {
                    model('app\common\model\User')
                        ->where('id', $this->auth->id)
                        ->update([$trident => 1]);
                    $this->success('ok');
                } else {
                    $this->error(__('无权申请课程'));
                }
            }
        }
        $this->error(__('非法请求'));
    }

    public function getUpgradeInfo()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $where['id'] = $id;
            $method = $this->request->post('method');
            if ($method == 'pay') $where['pay_user_id'] = $this->auth->id;
            else if ($method == 'rec') $where['rec_user_id'] = $this->auth->id;
            else $this->error(__('异常请求'));
            $data = null;
            $amount = 0;
            $minutewiat = 3600;
            $timeline = strtotime('now');
            foreach (model('app\admin\model\UserUpgradeLog')
                         ->where($where)
                         ->select() as $k => $uul) {
                $amount = $uul['amount'];
                $data[$k] = $uul;
                if ($method == 'pay') {
                    $timeline_c = $data[$k]['endtime'] = $data[$k]['createtime'] ? ($data[$k]['createtime'] + $minutewiat) : null;//半小时
                    if ($timeline_c > $timeline) {
                        $data[$k]['date'] = floor(($timeline_c - $timeline) / 86400);
                        $data[$k]['hour'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400) % 86400 / 3600);
                        $data[$k]['minute'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400 - $data[$k]['hour'] * 3600) % 86400 / 60);
                        $data[$k]['second'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400 - $data[$k]['hour'] * 3600 - $data[$k]['minute'] * 60) % 86400 % 60);
                    }
                }
                if ($method == 'rec' && $data[$k]['state'] == 1) {
                    $timeline_c = $data[$k]['endtime'] = $data[$k]['paytime'] ? ($data[$k]['paytime'] + $minutewiat) : null;//半小时
                    if ($timeline_c > $timeline) {
                        $data[$k]['date'] = floor(($timeline_c - $timeline) / 86400);
                        $data[$k]['hour'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400) % 86400 / 3600);
                        $data[$k]['minute'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400 - $data[$k]['hour'] * 3600) % 86400 / 60);
                        $data[$k]['second'] = floor(($timeline_c - $timeline - $data[$k]['date'] * 86400 - $data[$k]['hour'] * 3600 - $data[$k]['minute'] * 60) % 86400 % 60);
                    }
                }
                $data[$k]['paytime_text'] = $uul['paytime'] ? date("Y-m-d H:i:s", $uul['paytime']) : '';
                $data[$k]['confirmtime_text'] = $uul['confirmtime'] ? date("Y-m-d H:i:s", $uul['confirmtime']) : '';
                $data[$k]['payuser'] = model('app\common\model\User')->where('id', $uul['pay_user_id'])->field('id,username,mobile')->find();
                $data[$k]['recuser'] = model('app\common\model\User')->where('id', $uul['rec_user_id'])->field('id,username,mobile')->find();
                $data[$k]['recuser']['bankinfo'] = model('app\api\model\wanlshop\PayAccount')->where('user_id', $uul['rec_user_id'])->select();
            }
            if ($data) {
                $this->success('ok', ['amount' => $amount, 'data' => $data]);
            } else {
                $this->error(__('数据异常'));
            }
        }
        $this->error(__('非法请求'));
    }

    public function getDealerInfo()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $state = $this->request->post('state');
            $limg = $this->request->post('limg');
            $lpname = $this->request->post('lpname');
            $cpname = $this->request->post('cpname');
            $mobile = $this->request->post('mobile');
            $city = $this->request->post('city');
            $data = array();
            $data['lpname'] = $lpname;
            $data['cpname'] = $cpname;
            $data['mobile'] = $mobile;
            $data['state'] = $state;
            $data['city'] = $city;
            if ($limg != '') $data['license_img'] = $limg;
            $data['verify'] = '0';
            $udinfo = model('app\admin\model\DealerInfo')
                ->where('user_id', $this->auth->id)
                ->find();
            if ($udinfo) {
                $ret = model('app\admin\model\DealerInfo')->where('user_id', $this->auth->id)->update($data);
            } else {
                $data['createtime'] = time();
                $data['user_id'] = $this->auth->id;
                $ret = model('app\admin\model\DealerInfo')->insert($data);
            }
            if ($ret) {
                $this->success('ok', $data);
            } else {
                $this->error(__('提交失败'));
            }
        }
        $udinfo = model('app\admin\model\DealerInfo')
            ->where('user_id', $this->auth->id)
            ->field('state,lpname,cpname,mobile,license_img,city,verify')
            ->find();
        if ($udinfo) {
            $this->success('ok', ['msg' => 'ok', 'data' => $udinfo]);
        } else {
            $this->success('ok', ['msg' => 'nodata']);
        }
    }

    public function dealerUpgrade()
    {
        $data = array();
        $user = $this->auth->getUser();
        $iscan = false;
        $lvinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,orderam,discount,upgrade');
//            当前级别
        $uplv = $user['svip_level'] + 1;
        $uulinfo = model('app\admin\model\UserUpgradeLog')
            ->where('pay_user_id', $this->auth->id)
            ->where('lv_old', $user['svip_level'])
            ->where('lv_new', $uplv)
            ->where('item', '1')
            ->find();
        $this->request->filter(['strip_tags']);
        if ($this->request->isGet()) {
            $method = $this->request->get('method') ? $this->request->get('method') : '';
            if ($method == 'check') {
                if ($uulinfo) {
                    if ($uplv == 2 && $uulinfo['state'] == 1) {
                        $this->success('ok', ['msg' => 'tobuy']);
                    } else {
                        $this->success('ok', ['msg' => 'yes']);
                    }
                } else $this->success('ok', ['msg' => 'no']);
            }
        }
        if (!$uulinfo) {
            if ($uplv < 4) {
                $iscan = true;
                $margin = $lvinfo[$uplv]['margin'] - $lvinfo[$user['svip_level']]['margin'];
                $orderam = $lvinfo[$uplv]['orderam'];
            } else {
                $this->error(__('不符合升级条件'));
                $count = model('app\common\model\User')
                    ->where('inviter_id', $this->auth->id)
                    ->where('svip_level', '>=', $user['svip_level'])
                    ->count();
                if ($count >= $lvinfo[$uplv]['upgrade']) {
                    $iscan = false;
                    if ($uplv == 5) {
//                        连续三个月
                    } else {
//                        上个月
                    }
                    $iscan = true;
                    $margin = 0;
                    $orderam = $lvinfo[$uplv]['orderam'];
                }
                if (!$iscan) {
                    $this->error(__('不符合升级条件'));
                }
            }
            $cid = 0;
            if ($margin > 0) $cid = 1;
            $pid = 1;
            if ($iscan) {
                $rid = $user['inviter_id'];
                $fsj = true;
                while ($fsj) {
                    $puser = model('app\common\model\User')
                        ->where('id', $rid)
                        ->find();
                    if ($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $orderam / $lvinfo[$uplv]['discount']) {
                        $pid = $puser['id'];
                        $fsj = false;
                    }
                    $rid = $puser['inviter_id'];
                    if ($puser['id'] == 1) $fsj = false;
                }
            }
            $uul_arr = array();
            $uul_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $uul_arr['pay_user_id'] = $this->auth->id;
            $uul_arr['rec_user_id'] = $pid;
            $uul_arr['cpn_user_id'] = $cid;
            $uul_arr['item'] = '1';
            $uul_arr['lv_old'] = $user['svip_level'];
            $uul_arr['lv_new'] = $uplv;
            $uul_arr['orderam'] = $orderam;
            $uul_arr['margin'] = $margin;
            $uul_arr['amount'] = $orderam + $margin;
            $uul_arr['createtime'] = time();
            if ($margin > 0) $uul_arr['item'] = '1';
            $uulid = model('app\admin\model\UserUpgradeLog')->insertGetId($uul_arr);
            if ($uulid) {
                if ($margin > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $this->auth->id;
                    $remit_arr['rec_user_id'] = $cid;
                    $remit_arr['amount'] = $margin;
                    $remit_arr['payamount'] = $margin;
                    $remit_arr['discount'] = 1;
                    $remit_arr['rtype'] = '2';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                }
                if ($orderam > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $this->auth->id;
                    $remit_arr['rec_user_id'] = $pid;
                    $remit_arr['amount'] = $orderam / $lvinfo[$uplv]['discount'];
                    $remit_arr['payamount'] = $orderam / $lvinfo[$uplv]['discount'];
                    $remit_arr['discount'] = 1;//$lvinfo[$uplv]['discount'];
                    $remit_arr['rtype'] = '1';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                    if ($pid > 5) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$orderam / $lvinfo[$uplv]['discount'], $pid, '批发商进货', 'subsidy', $remit_arr['pay_sn'], 'currency_rmb');
                    }
                }
            } else {
                $this->error(__('数据异常'));
            }
            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $this->auth->id)
                ->where('lv_old', $user['svip_level'])
                ->where('lv_new', $uplv)
                ->find();
        }

        $data = $uulinfo;
        if ($uulinfo['orderam'] > 0) {
            $data['pbinfo'] = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $uulinfo['rec_user_id']])
                ->order('createtime desc')
                ->select();
            $data['porder'] = model('app\admin\model\wanlshop\RemitOrder')
                ->where(['uplogid' => $uulinfo['id']])
                ->where(['user_id' => $uulinfo['pay_user_id']])
                ->where(['rtype' => '1'])
                ->order('id desc')
                ->find();
            $data['pdealer'] = model('app\admin\model\DealerInfo')
                ->where('user_id', $uulinfo['rec_user_id'])
                ->find();
        }
        if ($uulinfo['margin'] > 0) {
            $data['cbinfo'] = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $uulinfo['cpn_user_id']])
                ->order('createtime desc')
                ->select();
            $data['corder'] = model('app\admin\model\wanlshop\RemitOrder')
                ->where(['uplogid' => $uulinfo['id']])
                ->where(['user_id' => $uulinfo['pay_user_id']])
                ->where(['rtype' => '2'])
                ->order('id desc')
                ->find();
            $data['cdealer'] = model('app\admin\model\DealerInfo')
                ->where('user_id', $uulinfo['cpn_user_id'])
                ->find();
        }
        $data['oldlv'] = $lvinfo[$data['lv_old']]['name'];
        $data['newlv'] = $lvinfo[$data['lv_new']]['name'];
        $data['createtime_text'] = $data['createtime'] ? date("Y-m-d H:i:s", $data['createtime']) : '';
        $this->success('ok', $data);
    }

    public function upomimg()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $img = $this->request->post('image');
            $type = $this->request->post('type');
            $remitinfo = model('app\admin\model\UserUpgradeLog')
                ->where('id', $id)
                ->where('pay_user_id', $this->auth->id)
                ->find();
            if ($remitinfo) {
                $params = array();
                $params['image' . $type] = $img;
                if ($remitinfo['state' . $type] == 0) {
                    $params['state' . $type] = 1;
                    $params['paytime' . $type] = time();
                    $uinfo = model('app\common\model\User')->where('id', $remitinfo[$type == 1 ? 'rec_user_id' : 'cpn_user_id'])->field('mobile')->find();
                    Smslib::send($uinfo['mobile'], mt_rand(100000, 999999), 'matchrec');
                }
                model('app\admin\model\UserUpgradeLog')->where(['id' => $id, 'pay_user_id' => $this->auth->id])->update($params);
                if ($remitinfo['state' . $type] == 0) $params['paytime' . $type . '_text'] = date("Y-m-d H:i:s", $params['paytime' . $type]);
                $this->success('凭证上传成功', $params);
            } else {
                $this->error(__('数据异常'));
            }
        }
        $this->error('非法请求');
    }

    public function remitorder()
    {
        $data = array();
        $iscan = true;
        $isorder = false;
        $isorderid = false;
        $user = $this->auth->getUser();
        $lvinfo = model('app\admin\model\UserSvipLevel')->column('id,name,orderam,discount,moq');

        $uplv = $user['svip_level'] + 1;
        $data['porder']['amount'] = $lvinfo[$this->auth->svip_level]['moq'];
        $data['porder']['discount'] = $lvinfo[$this->auth->svip_level]['discount'];
        $this->request->filter(['strip_tags']);
        if ($this->request->isGet()) {
            $id = $this->request->get('id') ? $this->request->get('id') : '';
            if ($id != '' && $id > 0) {
                $porder = model('app\admin\model\wanlshop\RemitOrder')
                    ->where(['id' => $id])
                    ->find();
                if ($porder) {
                    if ($porder['uplogid'] > 0) {
                        $data = model('app\admin\model\UserUpgradeLog')
                            ->where('id', $porder['uplogid'])
                            ->find();
                    }
                    $porder['createtime_text'] = date("Y-m-d H:i:s", $porder['createtime']);
                    if ($porder['status'] == 'paid') $porder['paytime_text'] = date("Y-m-d H:i:s", $porder['paytime']);
                    if ($porder['status'] == 'confirm') $porder['confirmtime_text'] = date("Y-m-d H:i:s", $porder['confirmtime']);
                    if ($porder['status'] == 'expired') $porder['canceltime_text'] = date("Y-m-d H:i:s", $porder['canceltime']);
                    $data['porder'] = $porder;
                    $data['pbinfo'] = model('app\api\model\wanlshop\PayAccount')
                        ->where(['user_id' => $porder['rec_user_id']])
                        ->order('createtime desc')
                        ->select();
                    $data['pdealer'] = model('app\admin\model\DealerInfo')
                        ->where('user_id', $porder['rec_user_id'])
                        ->find();
                    $data['sdealer'] = model('app\admin\model\DealerInfo')
                        ->where('user_id', $porder['user_id'])
                        ->find();
                    $this->success('ok', $data);
                } else {
                    $this->error('无权查看改订单');
                }
            }
        }
        if ($this->request->isPost()) {
            $orderam = $this->request->post('amount');
            if ($orderam < $data['porder']['amount']) {
                $this->error('进货金额必须大于' . $data['porder']['amount']);
            }
            if ($orderam % $data['porder']['amount'] != 0) {
                $this->error('进货金额必须是' . $data['porder']['amount'] . '的倍数');
            }
            $data['porder']['amount'] = $orderam;
            $isorder = true;
        }
        $data['porder']['payamount'] = $lvinfo[$this->auth->svip_level]['moq'] * $lvinfo[$this->auth->svip_level]['discount'];
        $data['porder']['status'] = 'cteated';
        $pid = 1;
        if ($iscan) {
            $rid = $user['inviter_id'];
            $fsj = true;
            while ($fsj) {
                $puser = model('app\common\model\User')
                    ->where('id', $rid)
                    ->find();
                if ($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $data['porder']['amount']) {
                    $pid = $puser['id'];
                    $fsj = false;
                }
                $rid = $puser['inviter_id'];
                if ($puser['id'] == 1) $fsj = false;
            }
        }
        $data['pdealer'] = model('app\admin\model\DealerInfo')
            ->where('user_id', $pid)
            ->find();
        $this->request->filter(['strip_tags']);
        if ($isorder) {
            $remit_arr = array();
            $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $remit_arr['user_id'] = $this->auth->id;
            $remit_arr['rec_user_id'] = $pid;
            $remit_arr['amount'] = $data['porder']['amount'];
            $remit_arr['payamount'] = $data['porder']['amount'] * $lvinfo[$user['svip_level']]['discount'];
            $remit_arr['discount'] = $lvinfo[$user['svip_level']]['discount'];
            $remit_arr['rtype'] = '0';
            $remit_arr['createtime'] = time();
            $remit_arr['status'] = 'pending';
            $roid = model('app\admin\model\wanlshop\RemitOrder')->insertGetId($remit_arr);
            if ($pid > 5) {
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$data['porder']['amount'], $pid, '批发商进货', 'subsidy', $remit_arr['pay_sn'], 'currency_rmb');
            }
            $data['pbinfo'] = model('app\api\model\wanlshop\PayAccount')
                ->where(['user_id' => $pid])
                ->order('createtime desc')
                ->select();
            $data['porder'] = model('app\admin\model\wanlshop\RemitOrder')
                ->where(['id' => $roid])
                ->find();
        }
        $this->success('ok', $data);
    }

    public function remitpay()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $img = $this->request->post('image');
            $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
                ->where('id', $id)
                ->where('user_id', $this->auth->id)
                ->find();
            if ($remitinfo) {
                $params = array();
                $params['remitimage'] = $img;
                if ($remitinfo['status'] == 'pending') {
                    $params['status'] = 'paid';
                    $params['paytime'] = time();
                    $uinfo = model('app\common\model\User')->where('id', $remitinfo['rec_user_id'])->field('mobile')->find();
                    Smslib::send($uinfo['mobile'], mt_rand(100000, 999999), 'matchrec');
                }
                model('app\admin\model\wanlshop\RemitOrder')->where(['id' => $id, 'user_id' => $this->auth->id])->update($params);
                if ($remitinfo['status'] == 'pending') $params['paytime_text'] = date("Y-m-d H:i:s", $params['paytime']);
                $this->success('凭证上传成功', $params);
            } else {
                $this->error(__('数据异常'));
            }
        }
        $id = $this->request->get('id');
        $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
            ->where('id', $id)
            ->where('user_id', $this->auth->id)
            ->find();
        if ($remitinfo) {
            $remitinfo['dealer'] = model('app\admin\model\DealerInfo')
                ->where('user_id', $remitinfo['rec_user_id'])
                ->find();
            $this->success('ok', $remitinfo);
        }
        $this->error('非法请求');
    }

    public function remitconfirm()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
                ->where('id', $id)
                ->where('rec_user_id', $this->auth->id)
                ->find();
            if ($remitinfo) {
                $params = array();
                if ($remitinfo['status'] == 'expired') {
                    $this->error('订单已取消无法完成收款');
                }
                if ($remitinfo['status'] == 'created' || $remitinfo['status'] == 'pending') {
                    $this->error('打款凭证未上传无法确认收款');
                }
                if ($remitinfo['status'] == 'confirm') {
                    $this->error('已完成收款无需重复操作');
                }
                if ($remitinfo['status'] == 'paid') {
                    $params['status'] = 'confirm';
                    $params['confirmtime'] = time();
                }
                model('app\admin\model\wanlshop\RemitOrder')->where(['id' => $id, 'rec_user_id' => $this->auth->id])->update($params);
                if ($remitinfo['status'] == 'paid') {
                    $params['status'] = 'confirm';
                    $params['status_text'] = '已审核';
                    $params['confirmtime_text'] = date("Y-m-d H:i:s", $params['confirmtime']);
                }
                //所有凭证上传完毕则更改订单状态
                $arr = array();
                $arr['id'] = $remitinfo['id'];
                $arr['action'] = 'received';
                Hook::listen("com_auto_settlement", $arr);
                $this->success('确认收款成功', $params);
            }
        }
        $this->error('非法请求');
    }

    public function remitcancel()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
                ->where('id', $id)
                ->find();
            if ($remitinfo) {
                if ($this->auth->id != $remitinfo['user_id'] && $this->auth->id != $remitinfo['rec_user_id']) {
                    $this->error('您无权操作该订单');
                }
                $params = array();
                if ($remitinfo['status'] == 'expired') {
                    $this->error('订单已取消无需重复操作');
                }
                if ($remitinfo['status'] == 'confirm') {
                    $this->error('订单已确认无法取消');
                }
                if ($remitinfo['status'] == 'paid') {
                    $this->error('订单已付款无法取消');
                }
                if ($remitinfo['status'] == 'created' || $remitinfo['status'] == 'pending') {
                    $params['status'] = 'expired';
                    $params['canceltime'] = time();
                }
                model('app\admin\model\wanlshop\RemitOrder')->where(['id' => $id])->update($params);
                if ($remitinfo['status'] == 'created' || $remitinfo['status'] == 'pending') {
                    $params['status'] = 'expired';
                    $params['status_text'] = '已驳回';
                    $params['canceltime'] = date("Y-m-d H:i:s", $params['canceltime']);
                }
                //所有凭证上传完毕则更改订单状态
                $arr = array();
                $arr['id'] = $remitinfo['id'];
                $arr['action'] = 'cancel';
                Hook::listen("com_auto_settlement", $arr);
                $this->success('确认收款成功', $params);
            }
        }
        $this->error('非法请求');
    }

    public function getRemitPayList()
    {
        $list = model('app\admin\model\wanlshop\RemitOrder')
            ->with('userrec')
            ->where('user_id', $this->auth->id)
            ->field('remit_order.*')
            ->order('createtime desc')
            ->paginate();
        foreach ($list as $key => $row) {
            $row->getRelation('userrec')->visible(['username', 'truename']);
            $list[$key]['createtime_text'] = date("Y-m-d H:i:s", $row['createtime']);
        }
        $this->success('返回成功', $list);
    }

    public function getRemitConfirmList()
    {
        $list = model('app\admin\model\wanlshop\RemitOrder')
            ->with('userpay')
            ->where('rec_user_id', $this->auth->id)
            ->field('remit_order.*')
            ->order('createtime desc')
            ->paginate();
        foreach ($list as $key => $row) {
            $row->getRelation('userpay')->visible(['username', 'truename']);
            $list[$key]['createtime_text'] = date("Y-m-d H:i:s", $row['createtime']);
        }
        $this->success('返回成功', $list);
    }

    public function remitconfirm2()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $remitinfo = model('app\admin\model\UserUpgradeLog')
                ->where('id', $id)
                ->where('rec_user_id', $this->auth->id)
                ->find();
            if ($remitinfo) {
                $params = array();
                if ($remitinfo['state'] == 0) {
                    $this->error('打款凭证未上传无法确认收款');
                }
                if ($remitinfo['state'] == 1) {
                    $params['state'] = 2;
                    $params['confirmtime'] = time();
                }
                model('app\admin\model\UserUpgradeLog')->where(['id' => $id, 'rec_user_id' => $this->auth->id])->update($params);
                if ($remitinfo['state'] == 1) $params['confirmtime_text'] = date("Y-m-d H:i:s", $params['confirmtime']);
                //所有凭证上传完毕则更改订单状态
                $arr = array();
                $arr['id'] = $remitinfo['id'];
                $arr['operator'] = 'member';
                $arr['action'] = 'received';
                Hook::listen("com_auto_settlement", $arr);
                $this->success('确认收款成功', $params);
            }
        }
        $this->error('非法请求');
    }

    public function upgradeLog()
    {
        //设置过滤方法
        $type = $this->request->post('type') ? $this->request->post('type') : 'pay';
        $where = array();
        if ($type == 'pay') $where['pay_user_id'] = $this->auth->id;
        if ($type == 'rec') $where['rec_user_id'] = $this->auth->id;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $list = model("app\admin\model\UserUpgradeLog")
                ->where($where)
                ->order('createtime desc')
                ->paginate();
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function TridentLog()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $type = $this->request->post('type');
            if ($type == 0) {
                $trid = 21;
                $this->lvinfo = model('app\admin\model\UserSvipLevel')->column('id,name,bonus,upgrade,share,compound,excitation,profit');
            } else if ($type == 1) {
                $trid = 11;
                $this->lvinfo = model('app\admin\model\UserVipLevel')->column('id,name,bonus,upgrade,share,compound,excitation,profit');
            }
            $count = model("app\admin\model\UserTrident" . $trid)
                ->where('state', 1)
                ->count();
            $list = model("app\admin\model\UserTrident" . $trid)
                ->where('user_id', $this->auth->id)
                ->where('state', 1)
                ->order('id asc')
                ->paginate();
            foreach ($list as $k => &$v) {
                for ($i = $v['lv']; $i > 1; $i--) {
                    if ($v['lv'] == $i) {
                        $v['upgrade'] = $this->lvinfo[$i]['upgrade'] - $v['upgrade'];
                        $v['share'] = $this->lvinfo[$i]['share'] - $v['share'];
                        $v['compound'] = $this->lvinfo[$i]['compound'] - $v['compound'];
                        $v['excitation'] = $this->lvinfo[$i]['excitation'] - $v['excitation'];
                        $v['profit'] = $this->lvinfo[$i]['profit'] - $v['profit'];
                    } else {
                        $v['bonus'] = $v['bonus'] + $this->lvinfo[$i]['bonus'];
                        $v['upgrade'] = $v['upgrade'] + $this->lvinfo[$i]['upgrade'];
                        $v['share'] = $v['share'] + $this->lvinfo[$i]['share'];
                        $v['compound'] = $v['compound'] + $this->lvinfo[$i]['compound'];
                        $v['excitation'] = $v['excitation'] + $this->lvinfo[$i]['excitation'];
                        $v['profit'] = $v['profit'] + $this->lvinfo[$i]['profit'];
                    }
                    $v['lv_text'] = ($type == 1) ? 'VIP' . ($v['lv'] - 1) : 'SVIP' . ($v['lv'] - 1);
                }
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function WeekList()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isPost()) {
            $type = $this->request->post('type');
            $list = array();
            if ($type == 0) {
                foreach (model('app\common\model\User')
                             ->where('id', '>', 0)
                             ->where('invite_numsw', '>', 0)
                             ->order('invite_numsw desc')
                             ->limit(3)
                             ->select() as $k => $ph1) {
                    $list[$k]['id'] = $ph1['id'];
                    $list[$k]['username'] = $ph1['username'];
                    $list[$k]['mobile'] = $ph1['mobile'][0] . $ph1['mobile'][1] . $ph1['mobile'][2] . '****' . $ph1['mobile'][7] . $ph1['mobile'][8] . $ph1['mobile'][9] . $ph1['mobile'][10];
                    $list[$k]['numsw'] = $ph1['invite_numsw'];
                }
            } else if ($type == 1) {
                foreach (model('app\common\model\User')
                             ->where('id', '>', 0)
                             ->where('invites_numsw', '>', 0)
                             ->order('invites_numsw desc')
                             ->limit(3)
                             ->select() as $k => $ph2) {
                    $list[$k]['id'] = $ph2['id'];
                    $list[$k]['username'] = $ph2['username'];
                    $list[$k]['mobile'] = $ph2['mobile'][0] . $ph2['mobile'][1] . $ph2['mobile'][2] . '****' . $ph2['mobile'][7] . $ph2['mobile'][8] . $ph2['mobile'][9] . $ph2['mobile'][10];
                    $list[$k]['numsw'] = $ph2['invites_numsw'];
                }
            }
            $this->success('ok', $list);
        }
        $this->error(__('非法请求'));
    }

    public function getTrc20addr()
    {
        $coin0_name = request()->param('token');
        $coin_name = request()->param('coin');
        $user = $this->auth->getUser();
        if ($coin0_name && $user) {
            $user_id = $user->id;
            if ($coin0_name == 'TRX') $coin_id = 1;
            if ($coin0_name == 'ETH') $coin_id = 2;
            if ($coin0_name == 'BTC') $coin_id = 3;

            if ($coin_id == 1) {
                $url = 'http://trc.winapps.xyz/api/Cybermreg/reg';
                $postdata['shopId'] = 5;
                $postdata['tokenSymbol'] = 'TRC20';
                $postdata['Symbol'] = $coin_name;
                $postdata['memberId'] = $user_id;
                $postdata['dataTime'] = time();
                $key = 'sdrtoken2022fenghuazhejiangnb';
                $postdata['sign'] = MD5(MD5($postdata['shopId'] . $postdata['memberId'] . $postdata['dataTime'] . $key) . $key);

                $curl = curl_init();
                curl_setopt($curl, CURLOPT_URL, $url);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
                curl_setopt($curl, CURLOPT_POST, 1);
                curl_setopt($curl, CURLOPT_POSTFIELDS, $postdata);
                //curl_setopt($curl,CURLOPT_HTTPHEADER,$headerArray);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
                $output = curl_exec($curl);
                curl_close($curl);
                $res = json_decode($output, true);
                if ($res['result'] != 1) {
                    $this->error(__('钱包地址生成异常!'));
                } else {
                    $default_coin['coin_address'] = $res['data']['coin_address'];
                    $default_coin['coin_addresstrx'] = $res['data']['coin_address'];
                    $this->success('获取数据', $default_coin);
                }
            }
            if ($coin_id == 2) {
                //ETH钱包创建
            }
        }
        $this->error(__('暂时无法充币!'));
    }

    /**
     * 积分兑换
     * 产业积分兑换铭文
     */
    public function integralExchange()
    {

        $type = request()->param('type');
        $amount = intval(request()->param('amount'));
        if ($this->request->isPost()) {


            $user = $this->auth->getUser();
            $aimn = floatval(Config::get("site.aimnprice"));


            $user_id = $user->id;
            //判断amount 是否合法
            if (!is_numeric($amount) || $amount < 0) {
                $this->error(__('非法参数'));
            }

            //判断是产业积分兑换还是广告共享积分兑换
            if ($type == 'xnb') {


                //判断amount 是否大于余额
                if ($amount > $user['currency_xnb']) {
                    $this->error(__('余额不足' . $amount));
                }

                $xnb_after = floatval($user->currency_xnb) - $amount;
                $money = intval($amount * get_integral_rate($aimn));

                $tz_after = floatval($user->currency_tz) + $money;
                //修改产业积分 修改铭文
                model('app\common\model\User')
                    ->where('id', $this->auth->id)
                    ->update(['currency_xnb' => $xnb_after, 'currency_tz' => $tz_after]);

                //添加产业日志
                $xnb_arr = array();
                $xnb_arr['saas_id'] = $this->saas_id;
                $xnb_arr['user_id'] = $this->auth->id;
                $xnb_arr['money'] = -$amount;
                $xnb_arr['before'] = $user->currency_xnb;
                $xnb_arr['after'] = $xnb_after;
                $xnb_arr['memo'] = "兑换铭文扣除 $amount 个产业贡献积分";
                $xnb_arr['type'] = 'exchange';
                $xnb_arr['status'] = 1;
                $xnb_arr['createtime'] = time();
                model('app\common\model\CurrencyXnbLog')->insert($xnb_arr);

                //添加铭文日志


                $tz_arr = array();
                $tz_arr['saas_id'] = $this->saas_id;
                $tz_arr['user_id'] = $this->auth->id;
                $tz_arr['money'] = $money;
                $tz_arr['before'] = $user->currency_tz;
                $tz_arr['after'] = $tz_after;
                $tz_arr['memo'] = "产业积分兑换增加 $money 个铭文";
                $tz_arr['type'] = 'exchange';
                $tz_arr['status'] = 1;
                $tz_arr['createtime'] = time();
                model('app\common\model\CurrencyTzLog')->insert($tz_arr);


                $this->success('兑换成功' . $money . "个铭文，消耗产业积分" . $amount, 1);
            } else if ($type == 'adv') {

                //判断amount 是否大于余额
                if ($amount > $user['currency_adv']) {
                    $this->error(__('余额不足' . $amount));
                }

                $adv_after = floatval($user->currency_adv) - $amount;
                $money = intval($amount * get_yibao_integral_rate($aimn));
                $tz_after = floatval($user->currency_tz) + $money;
                //修改产业积分 修改铭文
                model('app\common\model\User')
                    ->where('id', $this->auth->id)
                    ->update(['currency_adv' => $adv_after, 'currency_tz' => $tz_after]);

                //添加产业日志
                $adv_arr = array();
                $adv_arr['saas_id'] = $this->saas_id;
                $adv_arr['user_id'] = $this->auth->id;
                $adv_arr['money'] = -$amount;
                $adv_arr['before'] = $user->currency_adv;
                $adv_arr['after'] = $adv_after;
                $adv_arr['memo'] = "兑换铭文扣除 $amount 个广告共享积分";
                $adv_arr['type'] = 'ybchange';
                $adv_arr['status'] = 1;
                $adv_arr['createtime'] = time();
                model('app\common\model\CurrencyAdvLog')->insert($adv_arr);

                //添加铭文日志

                $tz_arr = array();
                $tz_arr['saas_id'] = $this->saas_id;
                $tz_arr['user_id'] = $this->auth->id;
                $tz_arr['money'] = $money;
                $tz_arr['before'] = $user->currency_tz;
                $tz_arr['after'] = $tz_after;
                $tz_arr['memo'] = "广告共享积分兑换增加 $money 个铭文";
                $tz_arr['type'] = 'ybchange';
                $tz_arr['status'] = 1;
                $tz_arr['createtime'] = time();
                model('app\common\model\CurrencyTzLog')->insert($tz_arr);

                $this->success('兑换成功' . $money . "个铭文，消耗广告共享积分" . $amount, 1);
            } else if ($type == 'advrmb') {

                //判断amount 是否大于余额
                if ($amount > $user['currency_adv']) {
                    $this->error(__('余额不足' . $amount));
                }

                if ($amount % 100 != 0) {
                    $this->error(__('兑换数量必须是100的整数倍'));
                }

                $adv_after = floatval($user->currency_adv) - $amount;
                $money = floatval($amount * 0.1);
                $tz_after = floatval($user->currency_rmb) + $money;
                //修改产业积分 修改铭文
                model('app\common\model\User')
                    ->where('id', $this->auth->id)
                    ->update(['currency_adv' => $adv_after, 'currency_rmb' => $tz_after]);

                //添加产业日志
                $adv_arr = array();
                $adv_arr['saas_id'] = $this->saas_id;
                $adv_arr['user_id'] = $this->auth->id;
                $adv_arr['money'] = -$amount;
                $adv_arr['before'] = $user->currency_adv;
                $adv_arr['after'] = $adv_after;
                $adv_arr['memo'] = "兑换铭文扣除 $amount 个广告共享积分";
                $adv_arr['type'] = 'advrmbchange';
                $adv_arr['status'] = 1;
                $adv_arr['createtime'] = time();
                model('app\common\model\CurrencyAdvLog')->insert($adv_arr);

                //添加永福莱豆日志

                $rmb_arr = array();
                $rmb_arr['saas_id'] = $this->saas_id;
                $rmb_arr['user_id'] = $this->auth->id;
                $rmb_arr['money'] = $money;
                $rmb_arr['before'] = $user->currency_rmb;
                $rmb_arr['after'] = $tz_after;
                $rmb_arr['memo'] = "广告共享积分兑换提货券增长" . $money;
                $rmb_arr['type'] = 'advrmbchange';
                $rmb_arr['status'] = 1;
                $rmb_arr['createtime'] = time();
                model('app\common\model\CurrencyRmbLog')->insert($rmb_arr);

                $this->success('兑换成功' . $money . "个提货券，消耗广告共享积分" . $amount, 1);

            } else if ($type == 'ns') {

                if ($amount > $user['currency_adv']) {
                    $this->error(__('余额不足' . $amount));
                }

                $nslower = Config::get("site.nslower");

                if ($amount < $nslower || ($amount % 100) != 0) {
                    $this->error(__('兑换永福莱豆最少需要 ' . $nslower . ' 个广告贡献积分且为100的整数倍'));
                }

                $yb_change_green_ratio = floatval(Config::get("site.yb_change_green_ratio"));//广告共享积分兑换永福莱豆比例

                $amount = $amount - ($amount % ($yb_change_green_ratio * 100));

                $adv_after = floatval($user->currency_adv) - $amount;
                $money = $amount * $yb_change_green_ratio;
                $tz_after = floatval($user->currency_ns) + $money;

                //修改产业积分 修改铭文
                model('app\common\model\User')
                    ->where('id', $this->auth->id)
                    ->update(['currency_adv' => $adv_after, 'currency_ns' => $tz_after]);

                //添加产业日志
                $adv_arr = array();
                $adv_arr['saas_id'] = $this->saas_id;
                $adv_arr['user_id'] = $this->auth->id;
                $adv_arr['money'] = -$amount;
                $adv_arr['before'] = $user->currency_adv;
                $adv_arr['after'] = $adv_after;
                $adv_arr['memo'] = "兑换永福莱豆扣除 $amount 个广告共享积分";
                $adv_arr['type'] = 'nschange';
                $adv_arr['status'] = 1;
                $adv_arr['createtime'] = time();
                model('app\common\model\CurrencyAdvLog')->insert($adv_arr);

                //添加铭文日志

                $ns_arr = array();
                $ns_arr['saas_id'] = $this->saas_id;
                $ns_arr['user_id'] = $this->auth->id;
                $ns_arr['money'] = $money;
                $ns_arr['before'] = $user->currency_ns;
                $ns_arr['after'] = $tz_after;
                $ns_arr['memo'] = "广告共享积分兑换增加 $money 个永福莱豆";
                $ns_arr['type'] = 'nschange';
                $ns_arr['status'] = 1;
                $ns_arr['createtime'] = time();
                model('app\common\model\CurrencyNsLog')->insert($ns_arr);

                $this->success('兑换成功' . $money . "个永福莱豆，消耗广告共享积分" . $amount, 1);

            } else if ($type == 'nstz') {

                if ($amount > $user['currency_ns']) {
                    $this->error(__('余额不足' . $amount));
                }

                $nslower = Config::get("site.nstzlower");

                if ($amount < $nslower) {
                    $this->error(__('兑换铭文积分最少兑换' . $nslower . ' 个永福莱豆'));
                }

                $ns_change_tz_ratio = 1 / floatval($aimn);//永福莱豆兑换铭文比例 永福莱豆价值1元  铭文是0.01198

                $amount = intval($amount);

                $ns_after = floatval($user->currency_ns) - $amount;
                $money = intval($amount * $ns_change_tz_ratio);
                $tz_after = floatval($user->currency_tz) + $money;

                //修改产业积分 修改铭文
                model('app\common\model\User')
                    ->where('id', $this->auth->id)
                    ->update(['currency_ns' => $ns_after, 'currency_tz' => $tz_after]);

                //添加产业日志
                $ns_arr = array();
                $ns_arr['saas_id'] = $this->saas_id;
                $ns_arr['user_id'] = $this->auth->id;
                $ns_arr['money'] = -$amount;
                $ns_arr['before'] = $user->currency_ns;
                $ns_arr['after'] = $ns_after;
                $ns_arr['memo'] = "兑换铭文扣除 $amount 个永福莱豆";
                $ns_arr['type'] = 'nstzchange';
                $ns_arr['status'] = 1;
                $ns_arr['createtime'] = time();
                model('app\common\model\CurrencyNsLog')->insert($ns_arr);

                //添加铭文日志

                $tz_arr = array();
                $tz_arr['saas_id'] = $this->saas_id;
                $tz_arr['user_id'] = $this->auth->id;
                $tz_arr['money'] = $money;
                $tz_arr['before'] = $user->currency_tz;
                $tz_arr['after'] = $tz_after;
                $tz_arr['memo'] = "永福莱豆兑换增加 $money 个铭文";
                $tz_arr['type'] = 'nstzchange';
                $tz_arr['status'] = 1;
                $tz_arr['createtime'] = time();
                model('app\common\model\CurrencyTzLog')->insert($tz_arr);

                $this->success('兑换成功' . $money . "个铭文，消耗永福莱豆" . $amount, 1);

            } else if ($type == 'rmbtz') {

                $this->error(__('暂停服务！'));

                if ($amount > $user['currency_rmb']) {
                    $this->error(__('余额不足' . $amount));
                }

                $rmblower = Config::get("site.rmblower");

                if ($amount < $rmblower) {
                    $this->error(__('兑换铭文积分最少兑换' . $rmblower . '个提货券'));
                }

                $ns_change_tz_ratio = 1 / floatval($aimn);//提货券兑换铭文比例 提货券价值1元  铭文是0.01198

                $amount = intval($amount);

                $ns_after = floatval($user->currency_rmb) - $amount;
                $money = intval($amount * $ns_change_tz_ratio);
                $tz_after = floatval($user->currency_tz) + $money;
                $pay = new WanlPay;
                //添加提货券日志
                $pay->currency(-$amount, $this->auth->id, "兑换铭文扣除 $amount 个提货券", 'rmbtzchange', '', 'currency_rmb');
                //添加铭文日志
                $pay->currency($money, $this->auth->id, "提货券兑换增加 $money 个铭文", 'rmbtzchange', '', 'currency_tz');

                $this->success('兑换成功' . $money . "个铭文，消耗提货券" . $amount, 1);

            } else if ($type == 'nsbd') {

                if ($amount > $user['currency_ns']) {
                    $this->error(__('余额不足' . $amount));
                }

                $bdlower = 1;

                if ($amount < $bdlower) {
                    $this->error(__('兑换铭文积分最少兑换' . $bdlower . '个余额'));
                }

                $ns_change_bd_ratio = 1; // 兑换比例

                $amount = intval($amount);

                $ns_after = floatval($user->currency_ns) - $amount;
                $money = intval($amount * $ns_change_bd_ratio);
                $tz_after = floatval($user->currency_bd) + $money;
                $pay = new WanlPay;
                //添加NS日志
                $pay->currency(-$amount, $this->auth->id, "兑换余额扣除 $amount 个永福莱豆", 'nsbdchange', '', 'currency_ns');
                //添加BD日志
                $pay->currency($money, $this->auth->id, "永福莱豆兑换增加 $money 个余额", 'nsbdchange', '', 'currency_bd');

                $this->success('兑换成功' . $money . "个余额，消耗永福莱豆" . $amount, 1);
            }else if($type=='nsToGd' || $type=='gpToGd'){
                if($amount<1){
                    $this->error(__('兑换数量不能小于1' ));
                }
                $to=new UserOrderReq();
                $rate=bcmul($amount,0.07,2);
                $m=bcsub($amount,$rate,2);
                $mtype=$type=='nsToGd' ? 'currency_ns' : 'currency_gp';
                if($amount>$user->$mtype){
                    $this->error(__('余额不足' . $amount));
                }
                $pay = new WanlPay;
                //添加提货券日志
                $pay->currency(-$amount, $user_id, "兑换消费抵用券", 'pay', '', $mtype);
                $param=[
                    'uid'=>$user_id,
                    'xfq'=>$m,
                    'username'=>$user->username
                ];
                $ret=$to->transferToGd($param);
                if($ret['code']!=1){
                    $this->error($ret['msg']);
                }
                $this->success('ok','兑换成功');
            } else {
                $this->error(__('非法请求!'));
            }

        }
        $this->error(__('非法请求!'));

    }


    /**
     * 产业积分兑换铭文记录
     */
    public function integralExchangeList()
    {

        $type = request()->param('type');
        $page = request()->param('page');


        $pagesize = 10;
        $page = $this->request->get('page', 1);
        $start = ($page - 1) * $pagesize;

        if ($type == 0) {
            $sqltype = 'exchange';
        } else if ($type = 1) {
            $sqltype = 'ybchange';
        }

        $total = model('app\common\model\CurrencyTzLog')
            ->where(['status' => 1, 'type' => 'exchange'])
            ->count();

        $query = model('app\common\model\CurrencyTzLog')
            ->field('id,money,createtime,memo')
            ->where(['status' => 1, 'type' => $sqltype, 'user_id' => $this->auth->id])
            ->order('createtime', 'desc')
            ->limit($start, $pagesize);


        $finds = $query->select();
        $this->success('ok', [
            'pagesize' => $pagesize,
            'page' => $page,
            'list' => $finds,
            'totalPage' => ($total % $pagesize == 0 ? $total / $pagesize : (int)($total / $pagesize) + 1)
        ]);


    }


    /**
     * 获取积分兑换比例
     */
    public function integralExchangeRate()
    {

        $type = request()->param('type');

        $aimn = floatval(Config::get("site.aimnprice"));

        $cyrate = get_integral_rate($aimn);

        $ybrate = get_yibao_integral_rate($aimn);

        $user = $this->auth->getUser();

        $yb_change_green_ratio = floatval(Config::get("site.yb_change_green_ratio"));//广告共享积分兑换绿色积分比例

        $nslower = Config::get("site.nslower");

        $rmblower = Config::get("site.rmblower");

        $data = [
            'nsrate' => $yb_change_green_ratio * 100,
            'nstzrate' => 1 / $aimn,
            'rmbtzrate' => 1 / $aimn,
            'cyrate' => $cyrate,
            'ybrate' => $ybrate,
            'aimn' => $aimn,
            'cyamount' => floatval(number_format($user->currency_xnb, 2)),
            'ybamount' => floatval(number_format($user->currency_adv, 2)),
            'nsamount' => floatval(number_format($user->currency_ns, 2)),
            'rmbamount' => floatval(number_format($user->currency_rmb, 2)),
            'nslower' => intval($nslower),
            'rmblower' => intval($rmblower),
            'nsbdrate' => 1
        ];


        $this->success('获取数据成功', $data);

    }

    /**
     * 不要了
     * @return void
     * @deprecated
     */
    private function distributor()
    {

        if ($this->request->isPost()) {
            $mt = 3900;
            $user_id = $this->auth->id;
            $postData = $this->request->post();

            // 永福莱豆判断 3900
            $city = $postData['city'];

            $user = $this->auth->getUser();
            $ns = $user->currency_ns;
            if ($ns < $mt) {
                $this->error('永福莱豆余额不足' . $ns);
            }

            // 是否已是-渠道分销商
            $is_distributor = isset($user->is_distributor) ? $user->is_distributor : 0;
            if ($is_distributor > 0) {
                $this->error('您已是渠道分销商！无需升级');
            }

            // 减掉3900
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$mt, $user_id, '渠道分销商升级', 'subsidy', 1, 'currency_ns', '1');

            // 设置区域
            \app\common\model\User::update(['dist_address' => $city], ['id' => $user_id], ['dist_address']);

            // 升级和变成提货券
            $arr = array();
            $arr['action'] = 'distributor';
            $arr['user_id'] = $user_id;
            $arr['amount'] = $mt;//3900;
            Hook::listen("com_auto_settlement", $arr);

            $this->success('升级成功！');
        }
    }
    /**
     * 帮好友升级
     */

    public function upUser()
    {
        $up_user_id = $this->request->param('user_id',0);
        $goods_id = $this->request->param('goods_id');
        $sku_id = $this->request->param('sku_id');
        $price = $this->request->param('price');
        $num = $this->request->param('number');
        $payType = $this->request->param('payType');
        $sms = $this->request->param('sms');
        $orderToGd = $this->request->param('orderToGd',0);
        $order_id = $this->request->param('order_id/a');
//      用户是否需要升级
        $uid=$this->auth->id;
//        $uid=528;
        $user=model('\app\common\model\User')->find($uid);
       // 用户是否是自己的好友
        if (!$goods_id) {
            $this->error('请选择商品');
        }
        if (!$sku_id) {
            $this->error('请选择商品规格');
        }
        if ($num < 1) {
            $this->error('购买数量有误');
        }
        if(!$up_user_id){
            $this->error('请选择好友后帮他激活！');
        }
        $up_user=model('\app\common\model\User')->find($up_user_id);
        if(!$up_user){
            $this->error('选择的好友不存在！');
        }
        if(($up_user['vip_level'] == 2 && $price < 3900) || ($up_user['vip_level'] == 3 && $price < 6500) || ($up_user['vip_level'] == 4 && $price < 13000)){
            $this->error('该用户无法用此产品升级！');
        }
//        if($up_user['inviter_id'] != $user->id){
//            $this->error('该用户不是您的好友');
//        }
        $amount=bcmul($price,$num,2);
        $gp=bcmul($amount,0.03,2);
        $C=new CombinationPayment($user);
        $pay=$C->calculateTheAmount($amount,$payType);
        if($pay['pay']>0){
            if($sms){
                $order_id ? $order_id : ($this->error(__('非法请求')));
                $order = model('app\api\model\wanlshop\OrderSync')->find($order_id[0]);
                if (!$order) {
                    $this->error(__('没有找到任何要支付的订单'));
                }
                $wanlPay = new WanlPay();
                $ret = $wanlPay->kqQuick2($order,$pay,$sms,$orderToGd,$price);
                if($ret['code']==200){
                    $this->success($ret['msg']);
                }
                $this->error($ret['msg']);
            }
        }
        $t=0;
        if($amount%1300==0){
            $t=($amount/1300)*2000;
            $goods_num=bcdiv($amount,1300,0);
            $goods_price=1300;
        }elseif ($amount%3399==0){
            $t=($amount/3399)*5000;
            $goods_num=bcdiv($amount,3399,0);
            $goods_price=3399;
        }
        $flqType=2;
        $cng_rate=0.05;

        if($payType=='yuePu'){
            $flqType = 4;
        }elseif ($payType=='yueCny'){
            if ($user->svip_lv_dkq > 1) {
                $dkqInfo = \app\admin\model\UserSvipLvDkq::column('id,rate');
                $cng_rate = $dkqInfo[$user->svip_lv_dkq];
            }
            $flqType = 3;
        }
        $process_key = 'UP_USER_N' . $up_user_id;
        if (Cache::store('redis')->get($process_key)) {
            $this->error('请勿频繁操作');
        }
        //锁住60秒
        Cache::store('redis')->set($process_key, 1, 120);

        $order_no = 'YFL' . substr(time(), -8) . sprintf("%08d", $up_user->id) . mt_rand(1000, 9999);
//        $res = controller('app\api\controller\exten\UserOrderReq')->addOrder(['user_id'=>$up_user->form_id,'price'=>$price,'number'=>$num,'goods_id'=>$goods_id,'sku_id'=>$sku_id]);
        $order_info = array();
        $order_info['user_id'] = $up_user->id;
        $order_info['order_id'] = 1;
        $order_info['order_no'] = $order_no;
        $order_info['price'] = $goods_price;
        $order_info['number'] = $goods_num;
        $order_info['category_id'] = 108;
        $order_info['flq'] = $flqType;// 2/3/4
        $order_info['cny_rate'] = $cng_rate;
        $order_info['status'] = '1';
        $order_info['activity_type'] = 'shopgoods';
        $order_info['act'] = 'newpurchase';
        if($orderToGd){
            $order_info['orderToGd'] = $orderToGd;
        }
//        $this->success($order_info);
        Db::startTrans();
        $checkOS = model('app\api\model\wanlshop\OrderSync')->create($order_info);
        if(!$checkOS){
            Db::rollback();
            Cache::store('redis')->rm($process_key);
            $this->error('操作异常!');
        }
        $bool=$C->operationPoints($pay,0,$order_no);
        if(!$bool){
            Db::rollback();
            Cache::store('redis')->rm($process_key);
            $this->error('操作异常!');
        }
        if($user->dealer==1){
            (new WanlPay())->currency($gp, $this->auth->id, '服务费', 'subsidy', $order_info['order_no'], 'currency_gp');
        }
        (new WanlPay())->currency($t, $up_user_id, '好友帮助升级', 'pay', $order_info['order_no'], 'currency_rmb');
        $arr = array();
        $arr['action'] = 'orderAction';
        $arr['currency']=$pay['bd']>0 ? 'currency_gp' : 'currency_ns';
        $arr['order'] = $order_info;
        Hook::listen("com_auto_settlement", $arr);
        Db::commit();
        return $this->success('操作成功!');
    }
   /**
     * 帮好友升级
     */

    public function upUserOld()
    {
        $up_user_id = $this->request->param('user_id',0);
        $goods_id = $this->request->param('goods_id');
        $sku_id = $this->request->param('sku_id');
        $price = $this->request->param('price');
        $num = $this->request->param('number');
        $payType = $this->request->param('payType');
//      用户是否需要升级
        $uid=$this->auth->id;
        // $uid=528;
        $user=model('\app\common\model\User')->find($uid);
       // 用户是否是自己的好友
        if (!$goods_id) {
            $this->error('请选择商品');
        }
        if (!$sku_id) {
            $this->error('请选择商品规格');
        }
        if ($num < 1) {
            $this->error('购买数量有误');
        }
        if(!$up_user_id){
            $this->error('请选择好友后帮他激活！');
        }
        $up_user=model('\app\common\model\User')->find($up_user_id);
        if(!$up_user){
            $this->error('选择的好友不存在！');
        }
//        if($up_user['vip_level'] > 1){
//            $this->error('该用户无需升级');
//        }
        if($up_user['inviter_id'] != $user->id){
            $this->error('该用户不是您的好友');
        }
        $amount=bcmul($price,$num,2);
        $C=new CombinationPayment($user);
        $pay=$C->calculateTheAmount($amount,$payType);
        if($pay['pay']>0){
            $this->error('余额不足!',$pay);
        }
        $t=0;
        if($amount%1300==0){
            $t=($amount/1300)*2000;
        }elseif ($amount%3399==0){
            $t=($amount/3399)*5000;
        }elseif ($amount%3900==0){
            $t=($amount/3900)*6000;
        }
        $flqType=2;
        $cng_rate=0.05;

        if($payType=='yuePu'){
            $flqType = 4;
        }elseif ($payType=='yueCny'){
            if ($user->svip_lv_dkq > 1) {
                $dkqInfo = \app\admin\model\UserSvipLvDkq::column('id,rate');
                $cng_rate = $dkqInfo[$user->svip_lv_dkq];
            }
            $flqType = 3;
        }
        $order_no = 'YFL' . substr(time(), -8) . sprintf("%08d", $up_user->id) . mt_rand(1000, 9999);
//        $res = controller('app\api\controller\exten\UserOrderReq')->addOrder(['user_id'=>$up_user->form_id,'price'=>$price,'number'=>$num,'goods_id'=>$goods_id,'sku_id'=>$sku_id]);
        $order_info = array();
        $order_info['user_id'] = $up_user->id;
        $order_info['order_id'] = 1;
        $order_info['order_no'] = $order_no;
        $order_info['price'] = $price;
        $order_info['number'] = $num;
        $order_info['category_id'] = 108;
        $order_info['flq'] = $flqType;// 2/3/4
        $order_info['cny_rate'] = $cng_rate;
        $order_info['status'] = '1';
        $order_info['activity_type'] = 'shopgoods';
        $order_info['act'] = 'newpurchase';
        Db::startTrans();
        $checkOS = model('app\api\model\wanlshop\OrderSync')->create($order_info);
        if(!$checkOS){
            Db::rollback();
            $this->error('支付失败!');
        }
        $bool=$C->operationPoints($pay,0,$order_no);
        if(!$bool){
            Db::rollback();
            $this->error('支付失败!');
        }
        (new WanlPay())->currency($t, $up_user_id, '好友帮助升级', 'pay', $order_info['order_no'], 'currency_rmb');
        $arr = array();
        $arr['action'] = 'orderAction';
        $arr['order'] = $order_info;
        Hook::listen("com_auto_settlement", $arr);
        Db::commit();
        return $this->success('支付成功!');
    }
    /**
     * 用户升级
     * @return void
     * @throws \think\exception\DbException
     */
    public function upUserOl()
    {
        $this->error('功能未开放',$this->request->param());
        $user_id = $this->request->param('user_id');
        $goods_id = $this->request->param('goods_id');
        $sku_id = $this->request->param('sku_id');
        $price = $this->request->param('price');
        $num = $this->request->param('number');
        $totalPriVal = $price * $num;

        $goodsAm = $price * $num;
        $goodsPrice = 999;
        $goodsNums = 1;
        if ($goodsAm % 999 == 0) {
            $goodsNums = floor($goodsAm / 999);
        } else if ($goodsAm % 1300 == 0) {
            $goodsPrice = 1300;
            $goodsNums = floor($goodsAm / 1300);
        } else if ($goodsAm % 1133 == 0) {
            $goodsPrice = 1133;
            $goodsNums = floor($goodsAm / 1133);
        }

        // 判断帮助好- 好友帮助升级 - 一天只能操作一次，避免重复
        $money = $goodsPrice == 1300 ? 2000 * $goodsNums : 1000 * $goodsNums;
        if ($goodsPrice == 1133) $money = $goodsNums / 3 * 5000;
        $midnight = strtotime("today midnight"); // 获取今天0点的时间戳
        $rmb = CurrencyRmbLog::where(['money' => $money, 'user_id' => $user_id, 'status' => '1', 'memo' => '好友帮助升级', 'type' => 'pay', 'createtime' => ['>', $midnight]])->find();
        if ($rmb) {
            $this->error('好友已激活无需重复操作');
        }
        /**
         * balancebd
         * balancens
         * balancecny_balancens
         * balancecny_balancebd
         * balancepu_balancens
         * balancepu_balancebd
         */
        $payType = $this->request->param('payType');
        if (!$user_id) {
            $this->error('请选择用户');
        }
        //校验用户信息-是否需升级
        $user = \app\common\model\User::get($user_id);
        if (!$user) {
            $this->error('未知用户');
        }
//        if($user->form_app != 'FHXQ' || !$user->form_id){
//            $this->error('用户来源未知');
//        }
        //用户是否需要升级
//        if($user['vip_level'] > 1){
//            $this->error('该用户无需升级');
//        }
        //用户是否是自己的好友
        // if($user['inviter_id'] != $this->auth->id){
        //     $this->error('该用户不是您的好友');
        // }
        if (!$goods_id) {
            $this->error('请选择商品');
        }
        if (!$sku_id) {
            $this->error('请选择商品规格');
        }
        if ($num < 1) {
            $this->error('购买数量有误');
        }
        //查询商品金额是否合法
//        if(!in_array($price,[999,4995])){
//            $this->error('支付金额有误');
//        }
        $cng_rate = 0.05;
        $dkPrice = 0;
        $flqType = 2;

        // balancens balancecny_balancens balancepu_balancens
        if ($payType == 'balancecny_balancens' || $payType == 'balancecny_balancebd') { // 使用----start-----达人福利券
            if ($this->auth->svip_lv_dkq > 1) {
                $dkqInfo = \app\admin\model\UserSvipLvDkq::column('id,rate');
                $cng_rate = $dkqInfo[$this->auth->svip_lv_dkq];
            } else {
//                $this->setting = model('app\common\model\Config')->where('group','basic')->column('name,value');
//                $cng_rate = $this->setting['cny_rate'] ?? 0.05; // 有问题取不到值
                $cng_rate = 0;
            }

            $max_cny = round($totalPriVal * $cng_rate, 2);
            $dkPrice = $this->auth->currency_cny;
            if ($this->auth->currency_cny > $max_cny) {
                $dkPrice = $max_cny;
            }
            \think\Log::info("CNY-Pay cng_rate=$cng_rate, max_cny=$max_cny, use_cny=$dkPrice");
            $flqType = 3;
        } else if ($payType == 'balancepu_balancens' || $payType == 'balancepu_balancebd') {
            $cng_rate = 0.1;
            $max_pu = round($totalPriVal * 0.1, 2);
            $flqType = 4;
            $dkPrice = $this->auth->currency_pu;
            if ($this->auth->currency_pu > $max_pu) {
                $dkPrice = $max_pu;
            }
            \think\Log::info("PU-Pay max_pu=$max_pu, use_pu=$dkPrice");
        }

        // 冻结提货券
        $order_no = 'YFL' . substr(time(), -8) . sprintf("%08d", $user->id) . mt_rand(1000, 9999);
        if ($dkPrice > 0) {
            $total_price = round($totalPriVal - $dkPrice, 2);
        } else {
            $total_price = round($totalPriVal, 2);
        }
        // 使用----end-----达人福利券

        //校验ns金额是否充足
        if (strstr($payType, 'balancens')) {
            if ($this->auth->currency_ns < $total_price) {
                $this->error('永福莱豆不足');
            }
        } else if (strstr($payType, 'balancebd')) {
            if ($this->auth->currency_bd < $total_price) {
                $this->error('余额不足');
            }
        }

        $process_key = 'UP_USER_N' . $user_id;
        if (Cache::store('redis')->get($process_key)) {
            $this->error('请勿频繁操作');
        }
        //锁住60秒
        Cache::store('redis')->set($process_key, 1, 120);
        //调用接口写入订单
        $res = controller('app\api\controller\exten\UserOrderReq')->addOrder(['user_id'=>$user->form_id,'price'=>$price,'number'=>$num,'goods_id'=>$goods_id,'sku_id'=>$sku_id]);

        $order_info = array();
        $order_info['user_id'] = $user->id;
        $order_info['order_id'] = 1;
        $order_info['order_no'] = $order_no;
        $order_info['price'] = $goodsPrice;
        $order_info['number'] = $goodsNums;
        $order_info['category_id'] = 108;
        $order_info['flq'] = $flqType;// 2/3/4
        $order_info['cny_rate'] = $cng_rate;
        $order_info['status'] = '1';
        $order_info['activity_type'] = 'shopgoods';
        $order_info['act'] = 'newpurchase';
        $checkOS = model('app\api\model\wanlshop\OrderSync')->create($order_info);
        if ($checkOS) {
            if ($dkPrice > 0) {
                if ($payType == 'balancecny_balancens' || $payType == 'balancecny_balancebd') {
                    (new WanlPay())->currency(-$dkPrice, $this->auth->id, '达人福利券支付商品订单', 'pay', $order_no, 'currency_cny');
                } else if ($payType == 'balancepu_balancens' || $payType == 'balancepu_balancebd') {
                    (new WanlPay())->currency(-$dkPrice, $this->auth->id, '消费抵扣券支付商品订单', 'pay', $order_no, 'currency_pu');
                }

            }
            //成功-扣除ns积分
            $pos = stripos($payType, "balancens");
            if ($pos !== false) {
                (new WanlPay())->currency(-$total_price, $this->auth->id, '永福莱豆支付升级用户' . $user->username, 'pay', $order_info['order_no'], 'currency_ns');
            } else {
                $pos = stripos($payType, "balancebd");
                if ($pos !== false) {
                    (new WanlPay())->currency(-$total_price, $this->auth->id, '余额现金支付升级用户' . $user->username, 'pay', $order_info['order_no'], 'currency_bd');
                }
            }
            //成功-赠送提货券
            (new WanlPay())->currency($money, $user->id, '好友帮助升级', 'pay', $order_info['order_no'], 'currency_rmb');
            $arr = array();
            $arr['action'] = 'orderAction';
            $arr['order'] = $order_info;
            Hook::listen("com_auto_settlement", $arr);

//            $usern = model('app\common\model\User')->where('id', $user->id)->find();
//            if(($usern['vip_level'] > $user->vip_level) && $usern['vip_level'] == 3){
//                model('app\common\model\User')->where('id', $user->id)->setInc('vip_level', 1);
//                model('app\admin\model\UserUpgradeLog')->create([
//                    'pay_sn' => date("Ymdhis") . sprintf("%08d", $user->id) . mt_rand(1000, 9999),
//                    'pay_user_id' => $user->id,
//                    'rec_user_id' => $user->inviter_id,
//                    'lv_old' => $usern['vip_level'],
//                    'lv_new' => $usern['vip_level'] + 1,
//                    'state' => 1,
//                    'amount' => 0
//                ]);
//            }

            $this->success('操作成功');
        } else {
            Cache::store('redis')->rm($process_key);
            $this->error('操作异常');
        }
    }

    private function _base64($str)
    {
        try {
            return base64_decode($str);
        } catch (Exception $ee) {
            return $str;
        }
    }

    private function _base64En($user)
    {
//        try {
//            $user['username'] = base64_encode($user['username']);
//            $user['mobile'] = base64_encode($user['mobile']);
//            return $user;
//            return base64_encode($str);
//        } catch (Exception $ee) {
//            return $user;
//        }
        return $user;
    }

    public function showNick()
    {
        if ($this->request->isPost()) {
            $postData = $this->request->post();

            $mobile = $postData['mobile'];
            $user = \app\common\model\User::where('username', $mobile)->find();
            if (!$user) {
                $this->error('用户不存在');
            }

            $showname = $user['truename'];
            if(!$showname) {
                $showname = $user['nickname'];
            } else {
                $showname = "**".mb_substr($showname, mb_strlen($showname)-1, 1, 'UTF-8');
            }

            $this->success('成功！', ['nickname' => $showname]);
        }
        $this->error('失败了！');
    }
}