<?php

namespace app\api\controller\wanlshop;

use app\common\controller\Api;
use app\common\library\Tool;
use app\common\model\ShopCode;
use fast\Tree;
use think\Cache;
use think\Config;
use think\Db;
use think\Exception;
use think\exception\PDOException;

/**
 * WanlShop店铺接口
 */
class Shop extends Api
{
    protected $noNeedLogin = ['getShopInfo','lists','searchCondition','details'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('app\api\model\wanlshop\Shop');
    }

    /**
     * 一次性获取店铺相关数据 1.0.8升级
     *
     * @ApiSummary  (WanlShop 一次性获取店铺相关数据)
     * @ApiMethod   (GET)
     *
     * @param string $id 页面ID
     */
    public function getShopInfo($id = null)
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        // 获取店铺信息
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('未找到此商家'));
        }
        // 获取商家类目
        $tree = Tree::instance();
        $tree->init(
            model('app\api\model\wanlshop\ShopSort')
                ->where(['shop_id' => $row['id']])
                ->field('id, pid, name, image')
                ->order('weigh asc')
                ->select()
        );
        $row['category'] = $tree->getTreeArray(0);
        // 查看是否被关注
        $row['isFollow'] = model('app\api\model\wanlshop\find\Follow')
            ->where([
                'user_no' => $row['find_user']['user_no'],
                'user_id' => $this->auth->id
            ])
            ->count();
        $row['isLive'] = model('app\api\model\wanlshop\Live')
            ->where(['shop_id' => $row['id'], 'state' => 1])
            ->field('id')
            ->find();
        // 获取类目样式配置
        $shopConfig = model('app\api\model\wanlshop\ShopConfig')
            ->where(['shop_id' => $row['id']])
            ->find();
        $row['categoryStyle'] = (int)$shopConfig['category_style'];
        // 获取商家自定义页面
        $row['page'] = model('app\api\model\wanlshop\Page')
            ->where([
                'shop_id' => $row['id'],
                'type' => 'shop'
            ])
            ->field('id, name, page, item')
            ->find();
        $this->success('返回成功', $row);
    }

    /**
     * 筛选条件
     */
    public function searchCondition(){
        $this->success('',[
            'range'=> [
                '500'=>'500m',
                '1000'=>'1km',
                '3000'=>'3km',
                '5000'=>'5km',
                '10000'=>'10km',
                '20000'=>'20km',
                '30000'=>'30km',
            ],
            'sort_type'=> [
                '1'=>'智能排序',
                '2'=>'距离最近',
                '3'=>'好评优先',
                // '4'=>'人均消费由低到高',
                // '5'=>'人均消费由高到低',
            ]
        ]);
    }


    /**
     * 商家列表
     */
    public function lists(){
        $page = $this->request->param('page',1);
        $pageSize = $this->request->param('pageSize',10);
        $channel = $this->request->param('channel','');//渠道
        $keyword = $this->request->param('keyword','');//关键字
        $range = $this->request->param('range','');//范围
        $sort_type = $this->request->param('sort_type','');//排序类型
        $category_id = $this->request->param('category_id','');//店铺分类
        switch ($sort_type){
            case 2:
                $sort = 'distance asc,score_service desc,like desc,person_consume asc,id asc';
                break;
            case 3:
                $sort = 'score_service desc,distance asc,like desc,person_consume asc,id asc';
                break;
            case 4:
                $sort = 'person_consume asc,like desc,score_service desc,distance asc,id asc';
                break;
            case 5:
                $sort = 'person_consume desc,like desc,score_service desc,distance asc,id asc';
                break;
            default:
                $sort = 'like desc,distance asc,score_service desc,id asc';
                break;
        }
        $where = ['status'=>'normal'];
//		$this->area && $where['city_id'] = $this->area['id'];
        $category_id && $category_id != 195 && $where['category_id'] = $category_id;
        $channel && $where['channel'] = ['in',$channel.',all'];
        $keyword && $where['shopname|keywords'] = ['like','%'.$keyword.'%'];
        $range && $range = "distance <= $range/1000";
        $lng = $this->lnglat['lng'];
        $lat = $this->lnglat['lat'];
        $sql = $this->model->where($where)->having($range)->field('*,(2 * 6378.138* ASIN(SQRT(POW(SIN(PI()*('.$lat.'-lat)/360),2)+COS(PI()*'.$lat.'/180)* COS(lat * PI()/180)*POW(SIN(PI()*('.$lng.'-lng)/360),2)))) as distance')->order($sort)->buildSql();
        $row = $this->model->table($sql)->alias('s')->cache(60)->paginate(['page'=>$page,'list_rows'=>$pageSize]);
        $this->success('success',$row);
    }

    /**
     * 商家详情
     * @return void
     */
    public function details(){
        $id = $this->request->param('id');
        $mark = $this->request->param('mark');
        if($mark){
            $shopCode = ShopCode::where('mark',$mark)->find();
            if(!$shopCode){
                $this->error('无效二维码');
            }
            if($shopCode['bind_status'] != 1){
                $this->error('二维码未绑定',$shopCode,5);
            }
            $id = $shopCode['shop_id'];
        }
        if(!$id){
            $this->error('未知信息');
        }
        $row = $this->model->get($id);
        $row && $row['images'] = explode(',',$row['images']);
        $this->success('success',$row);
    }


    // 提交店铺信息
    public function stepthree()
    {
        // 获取用户下的申请
        $model = new \app\index\model\wanlshop\Auth;
        $entry = $model->where(['user_id' => $this->auth->id])->find();
        if ($this->request->isPost()) {
            $data = $this->request->post();
            $config = get_addon_config('wanlshop');
            $verify = $config['config']['store_audit'] == 'N' ? 3:2;
            // 更新提交信息
            $data['user_id'] = $this->auth->id;
            $data['verify'] = $verify;
            $entry ? $entry->allowField(true)->save($data) : $model->allowField(true)->save($data);
            // 自动审核
            if($config['config']['store_audit'] == 'N'){
                $row = model('app\index\model\wanlshop\Auth')->where(['user_id' => $this->auth->id])->find();
                // 新增店铺
                $shop = model('app\index\model\wanlshop\Shop');
                $shop->user_id = $this->auth->id;
                $shop->state = $row['state'];
                $shop->shopname = $row['shopname'];
                $shop->avatar = $row['avatar'];
                $shop->bio = $row['content'];
                $shop->description = $row['bio'];
                $shop->city = $row['city'];
                $shop->city_id = model('app\common\model\Area')->where('name',explode('/',$row['city'])[1])->value('id') ?? 0;
                $shop->verify = $verify;
                $shop->address = $row['address'];
                $shop->service_phone = $row['service_phone'];
                $lnglat = geocode(str_replace('/','',$row['city']).$row['address']);
                // if($lnglat['code']){
                // 	$this->error($lnglat['msg']);
                // }
                $shop->lng = $lnglat['data'][0];
                $shop->lat = $lnglat['data'][1];
                // 新增店铺配置
                if($shop->save()){
                    $config = model('app\index\model\wanlshop\ShopConfig');
                    $config->shop_id = $shop->id;
                    $config->save();
                }
            }
            $this->success();
        }
        $this->success('',$entry);
    }


    /**
     * 获取商家收款码
     * @return void
     */
    public function paymentCode(){
        $this->serviceLock(1);
        $shop = model('app\api\model\wanlshop\Shop')->where(['user_id'=>$this->auth->id,'channel'=>['in',['offline','all']]])->find();
        if(!$shop){
            $this->error('非线下商家');
        }
        //查询对应收款码
        $shop_code = model('app\common\model\ShopCode')->where(['shop_id'=>$shop['id'],'default'=>1])->find();
        if(!$shop_code){
            $shop_code = model('app\common\model\ShopCode');
            $shop_code->name = $shop['shopname'];
            $shop_code->shop_id = $shop['id'];
            $shop_code->default = 1;
            $shop_code->bind_status = 1;
            $shop_code->bind_time = date('Y-m-d H:i:s');
            $shop_code->save();
        }else if($shop_code['status'] != 'normal'){
            $this->error('收款功能已被禁用，请联系平台！');
        }
        $shop_code['qr_code'] = Cache::get($shop_code->terminal_no);
        if(!$shop_code['qr_code']){
            $site = Config::get("site");
            $payUrl = $site['payUrl'] . $shop_code->mark;
            $shop_code['qr_code'] = Tool::qcCode($payUrl,$shop_code->terminal_no,300,14);
            Cache::set($shop_code->terminal_no,$shop_code['qr_code'],120);
            // $shop_code['qr_code'] = '';
        }
        $this->success('',$shop_code);
    }


    /**
     * 商家绑定收款码
     * @return void
     */
    public function bindPaymentCode(){
        $this->serviceLock(1);
        $mark = $this->request->param('mark');
        if(!$mark){
            $this->error('请填写指定参数');
        }
        $shop = model('app\api\model\wanlshop\Shop')->where(['user_id'=>$this->auth->id,'channel'=>['in',['offline','all']]])->find();
        if(!$shop){
            $this->error('未入驻商家，无法绑定');
        }
        //查询对应收款码
        $shop_code = model('app\common\model\ShopCode')->where('mark',$mark)->find();
        if(!$shop_code){
            $this->error('未知标识');
        }
        if($shop_code['bind_status'] == 1){
            $this->error('收款码状态异常，无法重复绑定');
        }
        $shop_code->shop_id = $shop->id;
        $shop_code->name = $shop->shopname;
        $shop_code->bind_status = 1;
        $shop_code->bind_time = date('Y-m-d H:i:s');
        $shop_code->save();
        $this->success('绑定成功',$shop_code);
    }

    /**
     * 店铺资料
     */
    public function profile()
    {
        $row = model('app\api\model\wanlshop\Shop')->where(['user_id'=>$this->auth->id])->find();
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            // 1.1.9升级 还原屏蔽html
            $this->request->filter([]);
            $params = $this->request->post();
            if ($params) {
                $result = false;
                Db::startTrans();
                try {
                    // 1.1.9升级优化 更新指定字段
                    $lnglat = geocode(str_replace('/','',$params['city']).$params['address']);
                    // if($lnglat['code']){
                    // 	$this->error($lnglat['msg']);
                    // }
                    $params['lng'] = $lnglat['data'][0];
                    $params['lat'] = $lnglat['data'][1];
                    is_array($params['images']) ? $params['images'] = join(',',$params['images']) : $params['images'] = rtrim($params['images'],',');
                    if(!$params['images']){
                        $this->error('请上传门店详图');
                    }
                    $params['city_id'] = model('app\common\model\Area')->where('name',explode('/',$params['city'])[1])->value('id') ?? 0;
                    $result = $row
                        ->allowField(['rate_gd','city_id','avatar','shopname','keywords','description','service_ids','city','bio','address','lng','lat','images','bus_stime','bus_etime','business_status','person_consume','service_phone'])
                        ->save($params);
                    Db::commit();
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
    }


    /**
     * 直接邀请的商家
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function subShopList()
    {
        //交易周期：today=当天，month=当月，year=年，all=全部
        $cycle = $this->request->param('cycle','all');
        (!$cycle || $cycle == 'all') && $cycle = [0,time()];
        $shop_list = collection(model('app\api\model\wanlshop\Shop')
            ->where('user_id','in',model('app\common\model\User')
                ->where('inviter_id_td',$this->auth->id)
                ->column('id'))
            ->field('id,user_id,shopname,avatar,city,address,lng,lat')
            ->order('id','desc')
            ->cache(60)
            ->select())->toArray();
        foreach ($shop_list as &$shop){
            $shop['commission'] = number_format(0,2);
            $shop['turnover'] = number_format(model('app\api\model\wanlshop\PayOrder')->where(['shop_id'=>$shop['id'],'status'=>'paid'])->whereTime('paytime',$cycle)->sum('payamount-refundamount'),2);
        }
        //根据要求排序
        $turnoverSort = $this->request->param('turnover_sort') == 'asc' ? SORT_ASC : SORT_DESC;
        $commissionSort = $this->request->param('commission_sort') == 'asc' ? SORT_ASC : SORT_DESC;
        $turnoverArr = array_column($shop_list,'turnover');
        $commissionArr = array_column($shop_list,'commission');
        array_multisort($turnoverArr,$turnoverSort,$commissionArr,$commissionSort,$shop_list);
        $this->success('',$shop_list);
    }
}