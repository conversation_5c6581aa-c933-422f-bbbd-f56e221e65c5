<?php
/**
 * Notes:
 * User: Administrator
 * DateTime: 2024/5/13 14:42
 * @return
 */

namespace app\api\controller\wanlshop;

use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\HelpSell as Sell;
use app\common\controller\Api;
use app\common\enum\ActivityEnum;
use app\common\enum\HelpSellEnum;
use app\common\library\WeiXin;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class Helpsell extends Api
{
    protected $noNeedLogin = ['lists','gettest'];
    protected $noNeedRight = ['*'];

    private $pv = '0.3';

    protected function _initialize()
    {
        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->model = new Sell();
    }

    public function goods()
    {
        $this->request->filter(['strip_tags']);
        $params = $this->request->get();
        $keyword = $params['keyword'] ?? ''; //搜索关键字
        $category = $params['category'] ?? 0; //类目
        $sort = $params['sort'] ?? 0; // 排序
        $sell = $params['sell'] ?? 0; //

        $saas = $this->saas_id;
        $uid = $this->auth->id;
        $goods = Goods::with(['shop', 'category'])
                ->where([
                    'activity' => 1,
                    'activity_id' => ['>', 0],
                    'activity_type' => ActivityEnum::DRAGON,
                    'fa_wanlshop_ninestar.saas_id' => $this->saas_id
                ])
                ->join('fa_wanlshop_ninestar', 'fa_wanlshop_goods.id = fa_wanlshop_ninestar.range', 'left');
        if ($keyword) {
            $goods = $goods->where('title', 'like', '%' . $keyword . '%');
        }
        if ($category) {
            $goods = $goods->where('category_id', $category);
        }
        $ids = Sell::field('good_id')->where('saas_id', $saas)->where('user_id', $uid)->select();
        $ids = array_column((array)$ids, 'good_id');
        $ids = empty($ids) ? [0] : $ids;
        if ($ids) {
            if (!$sell) {
                //未加入帮卖的商品
                $goods = $goods->where('fa_wanlshop_goods.id', 'not in', $ids);
            } else {
                //我的帮卖
                $goods = $goods->where('fa_wanlshop_goods.id', 'in', $ids);
            }
        }

        if ($sort) {//commission
            switch ($sort) {
                case HelpSellEnum::SORT_HOT:
                    $goods = $goods->order('sales desc');
                    break;
                case HelpSellEnum::SORT_COMMISSION:
                    $goods = $goods->order('fa_wanlshop_ninestar.commission desc');
                    break;
                case HelpSellEnum::SORT_NEW:
                    $goods = $goods->order('createtime desc');
                    break;
                case HelpSellEnum::SORT_ALL:
                default:
                    $goods = $goods->order('weigh desc');
                    break;
            }
        }

        $goods = $goods->paginate()
            ->each(function ($row, $key) use($saas, $uid) {
                $row->getRelation('shop')->visible(['city', 'shopname', 'state', 'isself']);
                $row->getRelation('category')->visible(['id','pid','name']);
                $row->isLive = model('app\api\model\wanlshop\Live')->where(['shop_id' => $row['shop_id'], 'state' => 1])->field('id')->find();
                $row->sales = $row->sales > $row->show_sales ? $row->sales : $row->show_sales;
                if ($row['activity'] == '1') {
                    $row['activity_data'] = model('app\index\model\wanlshop\Ninestar')->where(['id' => $row['activity_id']])->field("pretype,price,pv_value,back_rule,commission")->find(); //,validity,startdate,enddate,TIMESTAMPDIFF(SECOND, now(), concat(enddate, \" 23:59:59\")) expire")->find(); //
                    $row['activity_data']['name'] = ActivityEnum::$activity[$row['activity_type']] ?? '';
                    if ($row['activity_data']) {
                        $row['activity_data']['price'] = $row->price * bcmul($this->pv, $row['activity_data']['pv_value'], 2);//帮卖的pv值
                        $row['activity_data']['back_rule_txt'] = HelpSellEnum::$policy[$row['activity_data']['back_rule']] ?? '';
                        $row['coupon'] = [];
                    }
                }
                return $row;
            });
        $this->success('请求成功', $goods);
    }

    /**
     * 加入帮卖
     * @return void
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public function add()
    {
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $id = $params['id'] ?? '';
            if (!$id) {
                $this->error('参数错误');
            }
            $good = Goods::field('id, shop_id, activity, activity_id, activity_type')->find($id);
            if (!$good) {
                $this->error('商品不存在');
            }
            if ($good->activity != 1) {
                $this->error('商品未参与该活动');
            }
            if (!$good->activity_id || $good->activity_type != ActivityEnum::DRAGON) {
                $this->error('商品未参与该活动');
            }
            $data = [
                'saas_id' => $this->saas_id,
                'shop_id' => $good->shop_id,
                'user_id' => $this->auth->id,
                'good_id' => $good->id
            ];
            $sell = $this->model->withTrashed()->where($data)->find();
            if ($sell && is_null($sell->deletetime)) {
                $this->error('不可重复帮卖此商品');
            }
            if ($sell && !is_null($sell->deletetime)) {
                $sell->restore();
            }
            if (!$sell) {
                $this->model->save($data);
            }
            $this->success('加入成功');
        }
        $this->error('非法请求');
    }

    /**
     * 退出帮卖
     * @param $id
     * @return void
     * @throws DbException
     */
    public function del($id = null)
    {
        $good = Goods::field('id, shop_id, activity, activity_id, activity_type')->find($id);
        if (!$good) {
            $this->error('商品不存在');
        }
        $data = [
            'saas_id' => $this->saas_id,
            'shop_id' => $good->shop_id,
            'user_id' => $this->auth->id,
            'good_id' => $good->id
        ];
        $help = $this->model->where($data)->find();
        if (!$help) {
            $this->error('你无权限操作');
        }
        $help->delete();
        $this->success('成功退出商品帮卖');
    }

    /**
     * 分享记录
     * @return void
     * @throws \think\Exception
     */
    public function shareRecords()
    {
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $data['share_user'] = $params['share_user'] ?? '';
            $data['good_id'] = $params['good_id'] ?? '';
            $data['shop_id'] = $params['shop_id'] ?? '';
            if (!$data['share_user'] || !$data['good_id']) {
                $this->error('参数错误');
            }
            $goods = Goods::field('id,shop_id')->find($data['good_id']);
            if (!$goods) {
                $this->error('商品不存在');
            }
            $data['user_id'] = $this->auth->id;
            //先判断有没有记录了还没有买的,或者买了退单退款的
            $shares = GoodsShare::where($data)->where('state', 0)->count();
            if ($shares > 0) {
                $this->success();
            }
            //没有多余记录就新增
            (new GoodsShare)->save($data);
            $this->success();
        }
        $this->error('非法请求');
    }

    /**
     * 太阳码
     * @return array|void
     */
    public function qrCode()
    {
        $this->request->filter(['strip_tags']);
        if ($this->request->isGet()) {
            $params = $this->request->get();
            if (empty($params['page'])) {
                $this->error('缺少page参数');
            }
            echo WeiXin::instance()->getSunCode($params);
            exit;
        }
        $this->error('非法请求');
    }

    /**
     * 分享图片二维码
     * @return void
     */
    public function qrCode1()
    {
        $fontPath = ROOT_PATH . 'public/assets/fonts/simsun.ttc';
        $this->request->filter(['strip_tags']);
        $params = $this->request->get();
        if (empty($params['page'])) {
            $this->error('缺少page参数');
        }
        if ($params['oHelp']) {
            $help = explode(' ', $params['oHelp']);
        }
        $id = $help[0] ?? 0;
        if (!$id) {
            $this->error('参数错误');
        }
        $goods = Goods::get($id);
        if (!$goods) {
            $this->error('商品不存在');
        }
        // 初始化画布
        $width = 580;
        $height = 700;
        $canvas = imagecreatetruecolor($width, $height);

        // 设置背景色（可选，如果背景图已包含颜色）
        $bgColor = imagecolorallocate($canvas, 255, 255, 255); // 白色背景
        imagefill($canvas, 0, 0, $bgColor);

        // 加载背景图（如果有的话）
        $bgImagePath = $this->checkImage($goods->image);
        [$bgImagePath] = $this->handleImg($bgImagePath, 500, 500);
        $avatar = imagecreatefrompng($bgImagePath);
        imagecopy($canvas, $avatar, 41, 108, 0, 0, 500, 500);
        imagedestroy($avatar); // 释放头像资源

        // 加载用户头像（假设是 PNG 格式）
        $user = $this->auth->getUser();
        $avatarPath = $this->checkImage($user->avatar);
        [$avatarPath] = $this->handleImg($avatarPath, 50, 50);
        $avatar = imagecreatefrompng($avatarPath);
        imagecopy($canvas, $avatar, 41, 29, 0, 0, 50, 50);
        imagedestroy($avatar); // 释放头像资源

        // 添加名称
        $name = $user->username;
        $name = $this->truncateString($name, 18);
        $nameColor = imagecolorallocate($canvas, 255, 119, 15); // 黑色文本
        $fontSize = 18;
        $nameX = 130;
        $nameY = 60;
        imagettftext($canvas, $fontSize, 0, $nameX, $nameY, $nameColor, $fontPath, $name);

        // 添加商品描述和价格（这里简化处理，只添加价格）
        $description = $goods->title;
        $description = $this->truncateString($description, 18);
        $descriptionColor = imagecolorallocate($canvas, 0, 0, 0);
        $descriptionX = 41;
        $descriptionY = 640;
        $descriptionSize = 20;
        imagettftext($canvas, $descriptionSize, 0, $descriptionX, $descriptionY, $descriptionColor, $fontPath, $description);

        // 添加商品描述和价格（这里简化处理，只添加价格）
        $price = $goods->price;
        $priceColor = imagecolorallocate($canvas, 255, 0, 0); // 红色文本
        $priceX = 41;
        $priceY = 670;
        imagettftext($canvas, $fontSize, 0, $priceX, $priceY, $priceColor, $fontPath, $price);

        // 加载二维码（假设是 PNG 格式
        $qr_code = WeiXin::instance()->getSunCode($params);
        [$codeUrl] = $this->handleImg('', '', '', $qr_code);
        [$codeUrl] = $this->handleImg($codeUrl, 100, 100);
        $qrCode = imagecreatefrompng($codeUrl);
        imagecopy($canvas, $qrCode, 410, 480, 0, 0, 100, 100);
        imagedestroy($qrCode);

        // 输出图像
        header('Content-type: image/png');
        imagepng($canvas);
        imagedestroy($canvas); // 释放画布资源
        exit();
    }

    /**
     * 检查图片的地址
     * @param $url
     * @return string
     */
    private function checkImage($url): string
    {
        $filename = basename($url);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $public_path = ROOT_PATH . 'public';
        // 检查地址是否包含"http"或"https"
        if (strpos($url, 'http://') !== false || strpos($url, 'https://') !== false) {
            $content = file_get_contents($url);
            if ($content !== false) {
                $newPath =ROOT_PATH . 'public/images/' . date('Ymd');
                if (!file_exists($newPath)) {
                    mkdir($newPath, 0777, true);
                }
                $newPath =$newPath . '/' . time() . '.' . $extension;
                file_put_contents($newPath, $content);
                return $newPath;
            }
        } else {
            return 'https://dmnuo.oss-cn-hangzhou.aliyuncs.com' . $url;
        }
    }

    /**
     * @param $imageType
     * @param $sourcePath
     * @return false|\GdImage|resource|void
     */
    private function createImg($sourcePath, $imageType)
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $img = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $img = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $img = imagecreatefromgif($sourcePath);
                break;
            default:
                die('Unsupported image type.');
        }
        return $img;
    }

    /**
     * 固定图片长度
     * @param $sourcePath
     * @param $newWidth
     * @param $newHeight
     * @param null $file
     * @return array
     */
    private function handleImg($sourcePath, $newWidth = 200, $newHeight = 200, $file = null): array
    {
        $newPath =ROOT_PATH . 'public/images/' . date('Ymd');
        if (!file_exists($newPath)) {
            mkdir($newPath, 0777, true);
        }
        $newPath =$newPath . '/' . time() . '.' . 'png';
        if ($file) {
            file_put_contents($newPath, $file);
            return [$newPath];
        }
        // 根据图片类型创建图像资源
        list($width, $height, $imageType, $attr) = getimagesize($sourcePath);
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            default:
                die('Unsupported image type.');
        }

        // 创建一个新的图像资源
        $newImage = imagecreatetruecolor($newWidth, $newHeight);

        // 将原始图像缩放到新图像中
        imagecopyresampled(
            $newImage,
            $sourceImage,
            0, 0, // 新图像的 x, y 坐标点
            0, 0, // 原始图像的 x, y 坐标点
            $newWidth, $newHeight, // 新图像的宽度和高度
            imagesx($sourceImage), imagesy($sourceImage) // 原始图像的宽度和高度
        );

        // 将新图像保存为 JPEG 格式（你可以根据需要改为 PNG 或 GIF）
        imagepng($newImage, $newPath, 9); // 第三个参数是图像质量，范围是 0（最差质量，文件最小）到 100（最佳质量，文件最大）

        // 释放内存
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        return [$newPath, $imageType];
    }

    /**
     * 处理过长文本
     * @param $string
     * @param int $length
     * @param string $suffix
     * @return mixed|string
     */
    private function truncateString($string, int $length = 30, string $suffix = '...') {
        if (mb_strlen($string, 'UTF-8') > $length) {
            $string = mb_substr($string, 0, $length - mb_strlen($suffix, 'UTF-8'), 'UTF-8') . $suffix;
        }
        return $string;
    }

}