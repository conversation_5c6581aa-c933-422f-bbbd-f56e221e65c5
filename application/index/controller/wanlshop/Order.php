<?php
// 2020年2月17日22:05:38
namespace app\index\controller\wanlshop;
use addons\wanlshop\library\WanlChat\WanlChat;
use addons\wanlshop\library\WeixinSdk\Mp;
use app\common\controller\Wanlshop;
use addons\wanlshop\library\WanlSdk\Ehund;
use app\common\enum\PayTypeEnum;
use app\common\library\SassConfigLoader;
use fast\Http;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\Db;
use think\Exception;
use think\Log;

//快递100订阅

/**
 * 订单管理
 *
 * @icon fa fa-circle-o
 */
class Order extends Wanlshop
{
    // protected $noNeedLogin = ['sendAll'];
    protected $noNeedRight = '*';
    /**
     * Order模型对象
     */
    protected $model = null;

    protected $wx_config = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\index\model\wanlshop\Order;
        $kuaidi = new \app\index\model\wanlshop\Kuaidi;
		$this->wanlchat = new WanlChat();
        $this->view->assign("kuaidiList", $kuaidi->field('name,code')->select());
        $this->view->assign("stateList", $this->model->getStateList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("statesList", $this->model->getStatesList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['user','ordergoods','address'])
                    ->where($where)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['user','ordergoods','address'])
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id','username','nickname','avatar']);
				$row->pay = model('app\index\model\wanlshop\Pay')
					->where(['order_id' => $row['id'], 'type' => $row['type']])
					->field('pay_no,pay_type, price, number, order_price, freight_price, discount_price, actual_payment, notice')
					->find();
                if ($row->pay) {
                    $notice = $row->pay->notice;
                    if($notice) {
                        $obj = json_decode($notice, true);
                        if (array_get($obj, 'type') == 'balancens') {
                            $row->pay_type_text = '永福莱豆支付';
                        }else if (array_get($obj, 'type') == 'balancermb') {
                            $row->pay_type_text = '提货券';
                        } else {
                            $row->pay_type_text = PayTypeEnum::$payType[$row->pay->pay_type];
                        }
                    }
                } else {
                    $row->pay_type_text = '';
                }
            }
            $list = collection($list)->toArray();
			
			
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 详情
     */
    public function detail($id = null, $order_no = null)
    {
    	$where = $order_no ? ['order_no' => $order_no] : ['id' => $id];
        $row = $this->model
    		->where($where)
    		->find();
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        // 判断权限
        if ($row['shop_id'] != $this->shop->id) {
            $this->error(__('You have no permission'));
        }
        $row['address'] = model('app\index\model\wanlshop\OrderAddress')
            ->where(['order_id' => $row['id'], 'shop_id' => $this->shop->id])
            ->order('isaddress desc')
            ->field('id,name,mobile,address,address_name')
            ->find();
			
		$row['pay'] = model('app\index\model\wanlshop\Pay')
			->where(['order_id' => $row['id'], 'type' => 'goods'])
			->find();
			
		// 查询快递状态
		switch ($row['state']) {
			case 1:
				$express = [
					'context' => '付款后，即可将宝贝发出',
					'status' => '尚未付款',
					'time' => date('Y-m-d H:i:s', $row['createtime'])
				];
				break;
			case 2:
				$express = [
					'context' => '商家正在处理订单',
					'status' => '已付款',
					'time' => date('Y-m-d H:i:s', $row['paymenttime'])
				];
				break;
			default: // 获取物流
				$eData = model('app\api\model\wanlshop\KuaidiSub')
					->where(['express_no' => $row['express_no']])
					->find();
				// 获取数据
				$ybData = json_decode($eData['data'], true);
				if($ybData){
					// 运单状态 1.0.6升级
					$statusText = ['在途','揽收','疑难','签收','退签','派件','退回','转投'];
					$status = $statusText[0];
					if(in_array('status', $ybData[0])){
						$status = $ybData[0]['status'];
					}else{
						if($eData['ischeck'] === 1){
							$status = $statusText[3];
						}else{
							$status = $statusText[$eData['state']];
						}
					}
					$express = [
						'status' => $status,
						'context' => $ybData[0]['context'],
						'time' => $ybData[0]['time'],
					];
				}else{
					$express = [
						'status' => '已发货',
						'context' => '包裹正在等待快递小哥揽收~',
						'time' => date('Y-m-d H:i:s', $row['delivertime']??$row['updatetime'])
					];
				}
		}
        $row['pay_type_text'] = '';
        if ($row['pay']) {
            $notice = $row['pay']['notice'];
            if($notice) {
                $obj = json_decode($notice, true);
                if (array_get($obj, 'type') == 'balancens') {
                    $row['pay_type_text'] = '永福莱豆支付';
                }else if (array_get($obj, 'type') == 'balancermb') {
                    $row['pay_type_text'] = '提货券';
                } else {
                    $row['pay_type_text'] = PayTypeEnum::$payType[$row['pay']['pay_type']];
                }
            }
        } else {
            $row['pay_type_text'] = '';
        }
        $row['discount_type'] = '优惠金额';
        $mtype_arr = array(
            'currency_rmb'=>'提货券抵扣',
            'currency_flq'=>'福利券抵扣',
        );
        foreach ($mtype_arr as $mtype => $title) {
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            if(model("app\common\model\Currency{$mtypelog}Log")->where('service_ids', $row['order_no'])->where('type', 'pay')->find()){
                $row['discount_type'] = $title;
            }
        }
		$this->view->assign("kuaidi", $express);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
	
	/**
	 * 快递查询
	 */
	public function relative($id = null)
	{
		$row = $this->model->get($id);
		if (!$row) {
			$this->error(__('No Results were found'));
		}
		// 判断权限
		if ($row['shop_id'] != $this->shop->id) {
		    $this->error(__('You have no permission'));
		}
		$data = model('app\index\model\wanlshop\KuaidiSub')
			->where(['express_no' => $row['express_no']])
			->find();
		$data = json_decode($data['data'], true);
		$list = [];
		$week = array("0"=>"星期日","1"=>"星期一","2"=>"星期二","3"=>"星期三","4"=>"星期四","5"=>"星期五","6"=>"星期六");
		if($data){
			foreach($data as $vo){
				$list[] = [
					'time' => strtotime($vo['time']),
					'status' => in_array('status', $vo) ? $vo['status'] : '在途', // 1.0.6升级
					'context' => $vo['context'],
					'week' => $week[date('w', strtotime($vo['time']))]
				];
			}
		}
		$this->view->assign("week", $week);
		$this->view->assign("list", $list);
		$this->view->assign("row", $row);
		return $this->view->fetch();
	}
	
    
    /**
     * 打印发货单
     */
    public function invoice($ids = null)
    {
        $row = $this->model->all($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        foreach ($row as $data) {
            // 判断权限
            if ($data['shop_id'] != $this->shop->id) {
                $this->error(__('You have no permission'));
            }
            $data['address'] = model('app\index\model\wanlshop\OrderAddress')
                ->where(['order_id' => $data['id'], 'shop_id' => $this->shop->id])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();
            $data['pay'] = model('app\index\model\wanlshop\Pay')
                ->where(['order_id' => $data['id'], 'type' => 'goods'])
                ->find();
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /**
     * 发货 & 批量发货
     */
    public function delivery($ids = null)
    {
        //设置执行不超时，内存不做限制
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time','0');
        $data = [];
        $lists = [];
        $orderMap = [];
        $row = $this->model->all($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        foreach ($row as $vo) {
            if ($vo['shop_id'] != $this->shop->id) {
                $this->error(__('You have no permission'));
            }
            $vo['address'] = model('app\index\model\wanlshop\OrderAddress')
                ->where(['order_id' => $vo['id'], 'shop_id' => $this->shop->id])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();
            if ($vo['state'] == 2) {
                $lists[] = $vo;
            } else {
                $data[] = $vo;
            }
            $orderMap['key_'.$vo['id']] = $vo;
        }
        if ($this->request->isAjax()) {
            $request = $this->request->post();
            if (!array_key_exists("order", $request['row'])) {
                $this->success(__('没有发现可以发货订单~'));
            }
			if(!$this->wanlchat->isWsStart()){
				$this->error('平台未启动IM即时通讯服务，暂时不可以发货');
			}
            $config = get_addon_config('wanlshop');
            $callbackUrl = $config['ini']['appurl'].$config['kuaidi']['callbackUrl'].'/saas/'.SAAS;
            $ehund = new Ehund($config['kuaidi']['secretKey'], $callbackUrl);
            $order = [];
			$express = [];
			foreach ($request['row']['order']['id'] as $key => $id) {
                //校验状态
                $info = \app\index\model\wanlshop\Order::get($id);
                if(!$info){
                    continue;
                }
                if($info['state'] != 2){
                    continue;
                }
                $express_no = "";
                $express_name = "自提";
                $express_type = array_get($request['row']['order']['express_type'], $key, ''); // checkbox 数据，为空说明是快递
                if (empty($express_type)) {
                    $express_no = $request['row']['order']['express_no'][$key];
                    $express_name = $request['row']['express_name'];
                }
                // $order[] = [
                $update = [
                    'id' => $id,
                    'express_name' => $express_name,
                    'express_no' => $express_no,
                    'delivertime' => time(),
                    'state' => 3
                ];
                // $express_type为空说明是快递
                $express = [];
                if (empty($express_type) && 'xunifahuo' != $express_name) { // 虚拟发货不走快递
                    // 1.0.5升级 查询是否存在,如果存在绕过快递100订阅
                    $is_express_no = model('app\index\model\wanlshop\KuaidiSub')->where(['express_no' => $express_no])->count();
                    // 订阅快递查询
                    if ($config['kuaidi']['secretKey'] && $is_express_no == 0) {
                        $phone = $orderMap['key_'.$id]['address']['mobile'];
                        Log::info("delivery $express_name $express_no $phone");
                        $returncode = $ehund->subScribe($express_name, $express_no, $phone);
                        if ($returncode['returnCode'] != 200 && $express_name != 'ziti') {
//                        $this->error('快递订阅接口异常-'.$returncode['message']); // 多个订单使用一个快递号时忽略错误
                        }
                        $express = [
                            'sign' => $ehund->sign($express_no),
                            'express_no' => $express_no,
                            'returncode' => $returncode['returnCode'],
                            'message' => $returncode['message']
                        ];
                    }
                }
				// 推送消息
				$this->pushOrder($id,'已发货');
                // 微信发货消息
                $this->notifyWeChatMini($id,$express_name,$express_no,$express_type);
                //成功一条修改一条
                $this->model->update($update);
                $express && model('app\index\model\wanlshop\KuaidiSub')->save($express);
            }
            // $this->model->saveAll($order);
            // 写入快递订阅列表
            // if ($express) model('app\index\model\wanlshop\KuaidiSub')->saveAll($express);
            $this->success();
        }
        $this->view->assign("lists", $lists); //可以发货
        $this->view->assign("data", $data);
        $this->view->assign("ids", $ids);
        return $this->view->fetch();
    }

    /**
     * 发送已导入单号快递信息
     * @return void
     */
    public function sendImportOrder($code, $return=true) {

        if ( ! $this->request->isPost()) {
            $this->error();
        }
        //设置执行不超时，内存不做限制
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time','0');

        Log::info("sendImportOrder $code ！");

        $row = $this->model->where('express_no','not null')->where('state', '2')->select();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $config = get_addon_config('wanlshop');
        $callbackUrl = $config['ini']['appurl'].$config['kuaidi']['callbackUrl'].'/saas/'.SAAS;
        $ehund = new Ehund($config['kuaidi']['secretKey'], $callbackUrl);

        foreach ($row as $vo) {
            if ($vo['shop_id'] != $this->shop->id) {
                Log::info("sendImportOrder 不是此商家订单 o-shop-id=".$vo['shop_id'].'---login-shop-id='.$this->shop->id);
//                    continue;
            }
            $vo['address'] = model('app\index\model\wanlshop\OrderAddress')
//                    ->where(['order_id' => $vo['id'], 'shop_id' => $this->shop->id])
                ->where(['order_id' => $vo['id']])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();

            if ($vo['state'] != '2') {
                continue;
            }

            $express_no = $vo['express_no'];
            $express_name = $code;
            $id = $vo['id'];

            Log::info("sendImportOrder $express_name $express_no $id 成功！".$vo['state']);

            $update = [
                'id' => $id,
                'express_name' => $express_name,
                'express_no' => $express_no,
                'delivertime' => time(),
                'state' => 3
            ];
            // $express_type为空说明是快递
            $express = [];
            if ('xunifahuo' != $express_name) { // 虚拟发货不走快递
                // 1.0.5升级 查询是否存在,如果存在绕过快递100订阅
                $is_express_no = model('app\index\model\wanlshop\KuaidiSub')->where(['express_no' => $express_no])->count();
                // 订阅快递查询
                if ($config['kuaidi']['secretKey'] && $is_express_no == 0) {
                    $phone = $vo['address']['mobile'];
                    Log::info("sendImportOrder $express_name $express_no $phone");
                    $returncode = $ehund->subScribe($express_name, $express_no, $phone);
                    $express = [
                        'sign' => $ehund->sign($express_no),
                        'express_no' => $express_no,
                        'returncode' => $returncode['returnCode'],
                        'message' => $returncode['message']
                    ];
                }
            }
            // 推送消息
            $this->pushOrder($id,'已发货');
            // 微信发货消息
            $this->notifyWeChatMini($id,$express_name,$express_no);
            //成功一条修改一条
            $this->model->update($update);
            $express && model('app\index\model\wanlshop\KuaidiSub')->save($express);

            Log::info("sendImportOrder $express_name $express_no 成功！");
        }
        if($return){
            $this->success();
        }
    }

    /**
     * 重新调用快递100
     * @return void
     */
    public function reDelivery($ids) {

        Log::info("reDelivery ids=$ids");

        echo "hello $ids";
        $row = $this->model->all($ids);
        $config = get_addon_config('wanlshop');
        $callbackUrl = $config['ini']['appurl'].$config['kuaidi']['callbackUrl'].'/saas/'.SAAS;
        $ehund = new Ehund($config['kuaidi']['secretKey'], $callbackUrl);

        foreach ($row as $vo) {
            if ($vo['shop_id'] != $this->shop->id) {
                $this->error(__('You have no permission'));
            }
            $vo['address'] = model('app\index\model\wanlshop\OrderAddress')
                ->where(['order_id' => $vo['id'], 'shop_id' => $this->shop->id])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();

            $phone = $vo['address']['mobile'];
            $express_name = array_get($vo, 'express_name', '');
            if($express_name) {
                $express_no = $vo['express_no'];
                Log::info("delivery $express_name $express_no $phone");
                echo "delivery $express_name $express_no $phone";
                $returncode = $ehund->subScribe($express_name, $express_no, $phone);

                echo "hello ".json_encode($returncode);
                Log::info("reDelivery returncode " . json_encode($returncode));
            }
        }
    }

    /**
     * 导出订单
     * @return void
     */
    public function exportOrder()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        $title = [
            'order_no'=>'订单编号',
            'createtime_text'=>'下单时间',
            'paymenttime_text'=>'付款时间',
            'state_text'=>'订单状态',
            'user'=>[
                'field'=>'user',
                'title'=>'下单人',
                'params'=>'username,nickname'
            ],
            'ordergoods'=>[
                'field'=>'ordergoods',
                'title'=>'商品信息',
                'params'=>'title,difference',
            ],
            'pay1'=>[
                'field'=>'pay',
                'title'=>'付款单号',
                'params'=>'pay_no',
            ],
            'pay4'=>[
                'field'=>'pay',
                'title'=>'交易订单号',
                'params'=>'trade_no',
            ],
            'pay2'=>[
                'field'=>'pay',
                'title'=>'订单金额',
                'params'=>'order_price',
            ],
            'pay5'=>[
                'field'=>'pay',
                'title'=>'实际支付金额',
                'params'=>'price',
            ],
            'pay3'=>[
                'field'=>'pay',
                'title'=>'付款方式',
                'params'=>'pay_type_text',
            ],
        ];
        list($where, $sort, $order) = $this->buildparams();
        $total = $this->model
            ->with(['user','ordergoods'])
            ->where($where)
            ->order($sort, $order)
            ->count();
        //分批次查询
        $limit = 1000;//单次查询条数
        $totalPages = ceil($total / $limit); //总页码
        //excel类
        $spreadSheet = new Spreadsheet();
        $workSheet = $spreadSheet->getActiveSheet();
        $columnT = '65';
        foreach ($title as $key => $value){
            !is_string($value) && $value = $value['title'];
            $workSheet->setCellValue(chr($columnT).'1', $value);
            $columnT++;
        }
        $index = 2;
        for ($i = 0; $i < $totalPages; $i++) {
            $list = $this->model
                ->with(['user','ordergoods'])
                ->where($where)
                ->order('id asc')
                ->limit($i, $limit)
                ->select();
            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id','username','nickname','avatar']);
                $row->pay = model('app\index\model\wanlshop\Pay')
                    ->where(['order_id' => $row['id'], 'type' => 'goods'])
                    ->field('pay_no,pay_type,trade_no, price, number, order_price, freight_price, discount_price, actual_payment, notice')
                    ->find();
                $column = '65';
                foreach ($title as $kk => $vv){
                    if(is_string($vv)){
                        $pValue = $row[$kk].' ';
                    }else{
                        $arr = explode(',', $vv['params']);
                        $pValue = '';
                        foreach ($arr as $kkk => $vvv){
                            if(isset($row[$vv['field']][$vvv])){
                                $pValue .= $row[$vv['field']][$vvv]."__";
                            }else{
                                foreach ($row[$vv['field']] as $info){
                                    $pValue .= $info[$vvv].'__';
                                }
                                $pValue = rtrim($pValue,'__');
                                $pValue .= "\r\n";
                            }

                        }
                    }

                    if($kk == 'pay3') {
                        $notice = $row->pay->notice;
                        if ($notice) {
                            $obj = json_decode($notice, true);
                            if (array_get($obj, 'type') == 'balancens') {
                                $pValue = '永福莱豆支付';
                            }else if (array_get($obj, 'type') == 'balancermb') {
                                $pValue = '提货券';
                            }
                        }
                    }

                    $workSheet->setCellValue(chr($column).$index, rtrim($pValue,'__'));
                    $column++;
                }
                $index++;
            }
        }
        // 重命名表
        $fileName = iconv("utf-8", "gbk", '订单列表_'.date('ymdHis').'.xlsx');
        // 设置活动单指数到第一个表,所以Excel打开这是第一个表
        $spreadSheet->setActiveSheetIndex(0);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=$fileName");
        header('Cache-Control: max-age=0');
        $writer = new Xlsx($spreadSheet);
        $writer->save('php://output'); // 文件通过浏览器下载
        exit();
    }

    /**
     * 导出吉客云订单
     * @return void
     */
    public function exportOrderJky() {
        // select * from fa_wanlshop_order where state = 2 and deletetime is null and exists (select * from fa_wanlshop_goods where supplier = 3 and id in(select goods_id from fa_wanlshop_order_goods where order_id = fa_wanlshop_order.id));
        $row = $this->model->alias('aa')
            ->where(['state' => 2])
            ->where('exists (select * from fa_wanlshop_goods bb where bb.supplier = 3 and bb.id in(select goods_id from fa_wanlshop_order_goods where order_id = aa.id))')
            ->select();

//        echo DB::getLastSql();


        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $lists = [];
        foreach ($row as $vo) {
//            $vo->user;
            $vo->ordergoods;
            $vo['address'] = model('app\index\model\wanlshop\OrderAddress')
                ->where(['order_id' => $vo['id']//, 'shop_id' => $this->shop->id
                ])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();

            $lists[] = $vo;

        }

//        echo json_encode($lists);die;
        //excel类
        $templateFile = APP_PATH.'../public/template/jky-tmp.xlsx';
        $spreadSheet = IOFactory::load($templateFile);
        $workSheet = $spreadSheet->getActiveSheet();

//        $workSheet->setCellValue('A1', '导入编号');
//        $workSheet->setCellValue('B1', '客户账号');
//        $workSheet->setCellValue('C1', '网店订单号');
//        $workSheet->setCellValue('D1', '收货人');
//        $workSheet->setCellValue('E1', '手机');
//        $workSheet->setCellValue('F1', '收货地址');
//        $workSheet->setCellValue('G1', '应收合计');
//        $workSheet->setCellValue('H1', '销售渠道名称');
//        $workSheet->setCellValue('I1', '条码');
//        $workSheet->setCellValue('J1', '数量');
//        $workSheet->setCellValue('K1', '单价');
        $i = 2;
        foreach ($lists as $k => $v){
            $num = $v['ordergoods'][0]['number'];
            $goods = \app\index\model\wanlshop\Goods::get($v['ordergoods'][0]['goods_id']);
            $skuNum = $goods['item_id'];  // AKG6971756691597 喷瓶
            $skuTitle = $this->conformity($v['ordergoods']);
            // 5件 5瓶
            if($goods['extend_json'] > 0) {
                $num = $goods['extend_json'] * $num;
            }

            $workSheet->setCellValue('A'.$i, $v['id']);
            $workSheet->setCellValue('B'.$i, $v['id']);
            $workSheet->setCellValue('C'.$i, $v['order_no'].' ');
            $workSheet->setCellValue('D'.$i, $v['address']['name']);
            $workSheet->setCellValue('E'.$i, $v['address']['mobile'].' ');
            $workSheet->setCellValue('F'.$i, $v['address']['address']);
            $workSheet->setCellValue('G'.$i, 0); //
            $workSheet->setCellValue('H'.$i, '导单店铺');
            $workSheet->setCellValue('I'.$i, $skuNum); // 名称
            $workSheet->setCellValue('J'.$i, $num);
            $workSheet->setCellValue('K'.$i, 0);
            $workSheet->setCellValue('M'.$i, $skuTitle);

            $i++;
        }

        // 重命名表
        $fileName = iconv("utf-8", "gbk", '批量发货订单_'.date('ymdHis').'.xlsx');
        // 设置活动单指数到第一个表,所以Excel打开这是第一个表
        $spreadSheet->setActiveSheetIndex(0);
        $writer = new Xlsx($spreadSheet);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=$fileName");
        header('Cache-Control: max-age=0');
        $writer->save('php://output'); // 文件通过浏览器下载
        exit();
    }

    public function importJkyOrder() {
        $file = request()->file('file');
        if (!$file) {
            return json(['msg' =>  'Parameter file can not be empty', 'code' => '500']);
        }

//        var_dump($file->getInfo());
//        die;
        $data = read_excel($file->getInfo()['tmp_name']);
        if (empty($data)) {
            return json(['msg' =>  'No Results were empty', 'code' => '500']);
        }

        $expressNames = ['中通快递', '顺丰', '顺丰快递', '顺丰快递【填舱】', '圆通速递', '句容圆通', '韵达快运寄付'];
        $expressDic = ['中通快递'=>'zhongtong', '顺丰' => 'shunfeng', '顺丰快递' => 'shunfeng', '顺丰快递【填舱】' => 'shunfeng',
            '圆通速递'=> 'yuantong', '句容圆通'=> 'yuantong', '韵达快运寄付'=> 'yunda'];
        
        foreach ($data as $key => $val) {
            // 8:中通快递; 9:单号 16:订单ID
            if($key == 0) {
                continue;
            }
            // 校验数据
            if(count($val) != 21) {
                continue;
            }
            $expressName = $val[8];
            $expressNo = $val[9];
            $orderNo = $val[10];
            $orderId = $val[16];

            if( ! in_array($expressName, $expressNames)) {
                echo '快递未配置：'.$val[8].'  '.$val[9].$val[16];
                continue;
            }

            if(strstr($orderNo, ',')) {
                continue;
            }

            $order = \app\index\model\wanlshop\Order::where('order_no', $orderNo)->find();
            if($order && $order['state'] == '2') {
                \app\index\model\wanlshop\Order::update(['express_no' => $expressNo], ['order_no'=>$orderNo]);
                $code = $expressDic[$expressName];
                $this->sendImportOrder($code, false);

                echo '成功'.$val[8].'-'.$val[9].'-'.$val[10].'-'.$val[16].'code='.$code;
                continue;
            }
            echo '订单状态不正确：state='.$order['state'].','.$val[8].'  '.$val[9].$val[16].'-id=' . $order['id'].'<br>';

//            break; // 测试一条
        }
    }

    /**
     * 导出
     */
    public function Export($ids = null)
    {

        $row = $this->model->all($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $lists = [];
        foreach ($row as $vo) {
            $vo->user;
            $vo->ordergoods;
            $vo['address'] = model('app\index\model\wanlshop\OrderAddress')
                ->where(['order_id' => $vo['id'], 'shop_id' => $this->shop->id])
                ->order('isaddress desc')
                ->field('id,name,mobile,address,address_name')
                ->find();

            $lists[] = $vo;

        }

        //excel类
        $spreadSheet = new Spreadsheet();
        $workSheet = $spreadSheet->getActiveSheet();

        $workSheet->setCellValue('A1', 'ID');
        $workSheet->setCellValue('B1', '订单号');
        $workSheet->setCellValue('C1', '状态');
        $workSheet->setCellValue('D1', '姓名');
        $workSheet->setCellValue('E1', '手机');
        $workSheet->setCellValue('F1', '地区');
        $workSheet->setCellValue('G1', '商品 -- sku -- 数量');
        $workSheet->setCellValue('H1', '快递单号');
        $workSheet->setCellValue('I1', '快递公司');
        $i = 2;
        foreach ($lists as $k => $v){
            $workSheet->setCellValue('A'.$i, $v['id']);
            $workSheet->setCellValue('B'.$i, $v['order_no'].' ');
            $workSheet->setCellValue('C'.$i, $v['state_text']);
            $workSheet->setCellValue('D'.$i, $v['address']['name']);
            $workSheet->setCellValue('E'.$i, $v['address']['mobile'].' ');
            $workSheet->setCellValue('F'.$i, $v['address']['address']);
            $workSheet->setCellValue('G'.$i, $this->conformity($v['ordergoods']));
            $workSheet->setCellValue('H'.$i, '');
            $workSheet->setCellValue('I'.$i, '');

            $i++;
        }

        // 重命名表
        $fileName = iconv("utf-8", "gbk", '批量发货订单_'.date('ymdHis').'.xlsx');
        // 设置活动单指数到第一个表,所以Excel打开这是第一个表
        $spreadSheet->setActiveSheetIndex(0);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=$fileName");
        header('Cache-Control: max-age=0');
        // $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xlsx');
        $writer = new Xlsx($spreadSheet);
        $writer->save('php://output'); // 文件通过浏览器下载
        exit();
    }

    /**
     * 整合商品
     * @param array $orderGoods
     * @return string
     */
    public function conformity(array $orderGoods): string
    {
        if (empty($orderGoods)) {
            return '';
        }
        $content = '';
        foreach ($orderGoods as $goods) {
            $content = $goods['title'] . '--' . $goods['difference'] . '--' . $goods['number'] . PHP_EOL;
        }
        return $content;
    }

    /**
     * 导入订单
     * @return \think\response\Json
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function Import()
    {
        $filePath = ROOT_PATH . 'public' . DS . 'import';
        $file = request()->file('file');
        if (!$file) {
            return json(['msg' =>  'Parameter file can not be empty', 'code' => '500']);
        }
        $ids = $this->request->request('ids');
        if (!$ids) {
            return json(['msg' =>  'Parameter ids can not be empty', 'code' => '500']);
        }
        $info = $file->move( $filePath);
        if (!$info) {
            $this->error($file->getError());
        }

        $excelFields = ['id', 'order_no', 'state', 'name', 'mobile', 'address', 'goods', 'express_no', 'express_name'];

        $data = read_excel($filePath . DS . $info->getSaveName());
        if (empty($data)) {
            return json(['msg' =>  'No Results were empty', 'code' => '500']);
        }
        $title = $data[0];
        if (count($title) !== count($excelFields)) {
            return json(['msg' =>  '请先导出模板，再上传表格', 'code' => '500']);
        }
        //去掉表头
        array_shift($data);

        foreach ($data as &$val) {
            $val = array_combine($excelFields, $val);
        }
        unset($val);

        $sameIds = array_intersect(explode(',', $ids), array_column($data, 'id'));

        if (empty($sameIds)) {
            return json(['msg' => 'No Results were found', 'code' => '500']);
        }
        //填充快递单号
        $rows = $this->model->all($sameIds);
        $params = array_column($data, null, 'id');
        try {
            foreach ($rows as $row) {
                //替换空信息
                $no = trim($params[$row['id']]['express_no'],' ') ?? null;
                if($no){
                    $param = [
                        'express_no' => $no
                    ];
                    if ($row['state'] == 2) {
                        $row->save($param);
                    }
                }
            }
            return json(['msg' => '导入成功', 'code' => '200']);
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }

    }
    
    /**
     * 评论管理
     */
    public function comment()
    {
        return $this->view->fetch('wanlshop/comment/index');
    }
	
	/**
	 * 订单推送消息（方法内使用）
	 * 
	 * @param string order_id 订单ID
	 * @param string state 状态
	 */
	private function pushOrder($order_id = 0, $state = '已发货')
	{
		$order = $this->model->get($order_id);
		$orderGoods = model('app\index\model\wanlshop\OrderGoods')
			->where(['order_id' => $order_id])
			->select();
		$msgData = [];
		foreach ($orderGoods as $goods) {
			$msg = [
				'user_id' => $order['user_id'], // 推送目标用户
				'shop_id' => $this->shop->id, 
				'title' => '您的订单'.$state, // 推送标题
				'image' => $goods['image'], // 推送图片
				'content' => '您购买的商品 '.(mb_strlen($goods['title'],'utf8') >= 25 ? mb_substr($goods['title'],0,25,'utf-8').'...' : $goods['title']).' '.$state, 
				'type' => 'order',  // 推送类型
				'modules' => 'order',  // 模块类型
				'modules_id' => $order_id,  // 模块ID
				'come' => '订单'.$order['order_no'] // 来自
			];
			$msgData[] = $msg;
			$this->wanlchat->send($order['user_id'], $msg);
		}
		$notice = model('app\index\model\wanlshop\Notice')->saveAll($msgData);
	}

    //
    // public function sendAll()
    // {
    //     $list = \app\index\model\wanlshop\Order::where(['delivertime'=>['>',1721372400],'express_name'=>'zhongtong'])->order('id desc')->page(1)->limit(80)->select();
    //     $i = 0;
    //     foreach ($list as $info){
    //         $i++;
    //         echo $info['id'].',';
    //         $info['express_no'] && $this->notifyWeChatMini($info['id'],$info['express_name'],$info['express_no']);
    //     }
    //     var_dump($i);
    // }

    /**
     * @param $order_id     订单ID
     * @param $express_name 快递名称 // 拼音
     * @param $express_no   快递单号
     * @return bool
     */
    private function notifyWeChatMini($order_id, $express_name, $express_no, $express_type = "")
    {
        $pay = model('app\index\model\wanlshop\Pay')->where(['order_id'=>$order_id])->find();
        if (empty($pay['trade_no']))
            return false;
        // 微信支付才需要
        if ($pay['pay_type'] != 1) {
            return false;
        }
        $order = $this->model->find($order_id);
        list($appid, $appsecret) = $this->getAppInfoBySaasId($order['saas_id']);
        if (empty($appid) || empty($appsecret))
            return false;
        $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret);
        if (!$access_token)
            $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret, true);
            if (!$access_token)
                return false;
        // $address = model('app\index\model\wanlshop\OrderAddress')
        //     ->where(['order_id' => $order['id'], 'shop_id' => $this->shop->id])
        //     ->order('isaddress desc')
        //     ->field('id,name,mobile,address,address_name')
        //     ->find();
        $order_key = [
            'order_number_type' => 2,
            'transaction_id' => $pay['trade_no'],
        ];
        $shipping_item = [
            'tracking_no' => $express_no,
            'express_company' => $this->expressID($express_name),
            'item_desc' => $this->goodsInfoByOrderId($order_id),
            'contact' => ['consignor_contact'=>''],
        ];

        // 物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提
        $logistics_type = 1;
        if ($express_type == "on") {
            $logistics_type = 4;
            unset($shipping_item["tracking_no"]);
            unset($shipping_item["express_company"]);
        }
        if('xunifahuo' == $express_name) {
            $logistics_type = 3; // 买云店发货
            unset($shipping_item["tracking_no"]);
            unset($shipping_item["express_company"]);
        }

        $shipping_list = [
            $shipping_item
        ];
        //查询支付记录中的通知是否含有openid
        $pay['notice'] = json_decode($pay['notice'],true);
        $openid = array_get($pay['notice'], 'openid');
        if(!$openid){
            $third_info = model('app\index\model\wanlshop\Third')->where(['user_id'=>$order['user_id']])->find();
            if (!$third_info){
                return false;
            }
            $openid = $third_info['openid'];
        }
        $payer = ['openid'=>$openid];
        date_default_timezone_set('UTC');
        $upload_time = gmdate("Y-m-d\TH:i:s.120+08:00", time());
        $req = [
            // 'access_token' => $access_token,
            'order_key' => $order_key,
            'logistics_type' => $logistics_type,
            'delivery_mode' => 1,
            // 'is_all_delivered' => '', // 分拆发货模式时必填
            'shipping_list' => $shipping_list,
            'upload_time' => $upload_time, // "2024-04-11T08:09:35.120+08:00", // 上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`
            'payer' => $payer
        ];

        Log::info("自动发货-虚拟发货 configFile=".json_encode($req, JSON_UNESCAPED_UNICODE));

        $url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token={$access_token}";
        $result = Http::sendRequest($url, json_encode($req, JSON_UNESCAPED_UNICODE));
        if (isset($result['errcode']) && $result['errcode'] != 0) {
            return false;
        }
        return true;
    }

    private function goodsInfoByOrderId($order_id = 0) :string
    {
        $str = "";
        $goods = model('app\index\model\wanlshop\OrderGoods')->where(['order_id' => $order_id])->select();
        foreach ($goods as $item) {
            $str .= "{$item['title']}({$item['difference']})*{$item['number']}；";
        }
        return mb_substr($str ,0 , 120);
    }

    private function getAppInfoBySaasId($saas_id)
    {
        // $configFile = APP_PATH . "../addons/wanlshop/config.php";
        // if($saas_id > 0){
        //     $configFile = APP_PATH . "../addons/wanlshop/config{$saas_id}.php";
        // }
        // $config = [];
        // if (is_file($configFile)) {
        //     $configArr = include_once $configFile;
        //     if (is_array($configArr)) {
        //         foreach ($configArr as $key => $value) {
        //             $config[$value['name']] = $value['value'];
        //         }
        //         unset($configArr);
        //     }
        // }
        if(!$this->wx_config){
            $this->wx_config = SassConfigLoader::getAppInfoBySaasId($saas_id);
        }
        return [$this->wx_config["mp_weixin"]["appid"], $this->wx_config["mp_weixin"]["appsecret"]];
    }

    private function expressID($name): string
    {
        // $token = Mp::getAccessTokenWithWeChat("wxed2b86a0f402a3f9","9de65cf1a7b19d948a64533e16740a80");
        // // $url = "https://api.weixin.qq.com/shop/delivery/get_company_list?access_token={$token}";
        // // $url = "https://api.weixin.qq.com/cgi-bin/express/delivery/open_msg/get_delivery_list?access_token={$token}";
        // $url = "https://api.weixin.qq.com/cgi-bin/express/business/delivery/getall?access_token={$token}";
        // $result = Http::post($url);
        $map = [
            "shunfeng" => "SF",
            "jd" => "JDL",
            "youzhengguonei" => "EMS",
            "yunda" => "YUNDA",
            "ems" => "EMS",
            "yuantong" => "YTO",
            "zhongtong" => "ZTO",
            "huitongkuaidi" => "BEST",
            "shentong" => "STO",
            "tiantian" => "HHTT",
            "debangkuaidi" => "DB",
        ];
        return isset($map[$name]) ? $map[$name] : $name;
    }

}
