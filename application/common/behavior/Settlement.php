<?php

namespace app\common\behavior;

use addons\wanlshop\library\WeixinSdk\Mp;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\Order;
use app\api\model\wanlshop\OrderGoods;
use app\api\model\wanlshop\Pay;
use app\api\model\wanlshop\Shop;
use app\common\enum\VipEnum;
use app\common\model\CurrencyNsLog;
use app\common\model\CurrencyRmbLog;
use app\common\model\User;
use app\index\model\wanlshop\Ninestar;
use EasyWeChat\Factory;
use fast\Date;
use fast\Http;
use fast\Random;
use MongoDB\Driver\Query;
use think\Cache;
use think\Db;
use think\Exception;
use think\Log;

class Settlement
{
    public function __construct()
    {
        $this->merchantId = 6996282976456978432;
        $this->key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
        $this->nottest = true;
        $this->setting = model('app\common\model\Config')->where('group','settlement')->column('name,value');
        $this->lvname = model('app\admin\model\UserVipLevel')->column('id,name');
        $this->lvinfo = model('app\admin\model\UserVipLevel')->column('id,name,cost,bonus,upgrade,share,share1,share2,compound,excitation,profit');
        $this->lvsinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,orderam,discount,upgrade,bonus,share,compound,excitation,profit');
        $this->slvsinfo = model('app\admin\model\UserSvipLevels')->column('id,name,amount,rate');
        $this->union_iv = model('app\admin\model\UserVipLevelu')->column('id,iv_rate');
        $this->union_jc = model('app\admin\model\UserVipLevelu')->column('id,jc_rate');
        $this->saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
        $this->dkqInfo = model('\app\admin\model\UserSvipLvDkq')->column('id,rate');
        $this->fxs_rate = array(
            1 => 0,
            2 => 0.5,
            3 => 0.3,
            4 => 0.1,
            5 => 0.1
        );
//        1：消费
//        2：联创
//        3：合伙人
//        4：卓越
        $this->input = 1111;
        $this->suid = 3;
        $this->bonusrate = 0.005;
        $this->fday = 10;
        $this->eday = 1;
        $this->jckh = array(
            0 => 0,
            1 => 100000,
            2 => 1000000
        );
        $this->jc = array(
            0 => 0,
            1 => 0.5,
            2 => 1
        );
        $this->planA = [2];
        $this->planrate = array(
            'A' => array(
                'ratePv' => 1,
                'rateBs' => 7,
                'rateBs2' => 8,
                'dpMax' => 5,
                'tj' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0.15,
                    3 => 0.2,
                    4 => 0.3,
                    5 => 0.3,
                    6 => 0.3,
                    7 => 0.3,
                    8 => 0.3
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.1,
                    3 => 0.12,
                    4 => 0.14,
                    5 => 0.15,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 300000,
                    4 => 700000,
                    5 => 1000000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0
                ),
                'glrate' => array(
                    1 => 0.1,
                    2 => 0.1,
                    3 => 0.1,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 4,
                    4 => 8,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8
                )
            ),
            'B' => array(
                'ratePv' => 8,
                'rateBs' => 1,
                'dpMax' => 4,
                'tj' => array(
                    0 => 0.1,
                    1 => 0.1,
                    2 => 1,
                    3 => 0.2
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.08,
                    3 => 0.1,
                    4 => 0.12,
                    5 => 0.14,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 300000,
                    4 => 700000,
                    5 => 1000000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0
                ),
                'glrate' => array(
                    1 => 0.0,
                    2 => 0.4,
                    3 => 0.2,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 4,
                    4 => 8,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8
                )
            ),
            'C' => array(
                'ratePv' => 1,
                'rateBs' => 7,
                'rateBs2' => 8,
                'dpMax' => 5,
                'tj' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0.15,
                    3 => 0.2,
                    4 => 0.25,
                    5 => 0.3,
                    6 => 0.3,
                    7 => 0.3,
                    8 => 0.3,
                    9 => 0.3
                ),
                'zy' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.19,
                    9 => 0.2
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.1,
                    3 => 0.11,
                    4 => 0.12,
                    5 => 0.14,
                    6 => 0.15,
                    7 => 0.16,
                    8 => 0.18,
                    9 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 200000,
                    4 => 300000,
                    5 => 700000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000,
                    9 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0,
                    9 => 0
                ),
                'glrate' => array(
                    1 => 0.1,
                    2 => 0.1,
                    3 => 0.1,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 3,
                    4 => 4,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8,
                    9 => 8
                )
            )
        );

        $this->calcData = array(
            '0' => array(
                'shop_id' => 1,
                'category_id_lv1' => 107,//会员专享
                'category_id_lv2' => 108,//店长专享
                'category_id_lv3' => 228
            )
        );
        $this->ns_rate = 0.28;
        $this->ns_top_std = 2000;
        $this->fgDP = false;
//        $this->currency = $this->setting['isgp'] == 0?'currency_ns':'currency_gp';
        $this->currency = 'currency_gp';
        $this->feeRate = 0.08;
        $this->istoGd = false;
    }


    public function run(&$params)
    {
//        if($params['action'] == 'test'){
//            $this->calcAmountB(129,3999,3618596952981129);
//        }
//        if($params['action'] == 'autoone'){
//
//        }
//        if($params['action'] == 'auto'){
//
//        }
        if(
//            $params['action'] == 'payed' ||
            $params['action'] == 'received'
        ){
            $this->doconfirm($params);
        }
        if($params['action'] == 'certified'){
            $this->certified($params['user_id']);
        }

        if($params['action'] == 'orderPay'){
            $this->calcOrderPay($params['order_id']);
        }

        if($params['action'] == 'orderAction'){
            return $this->calcOrderAction($params['order'], $params['isOrderNet']??false, $params['currency']??null);
        }

        if($params['action'] == 'orderConfirm'){
            $this->calcOrderConfirm($params['order_id']);
        }

        if($params['action'] == 'orderRefund'){
            $this->calcOrderRefund($params['order_id']);
        }

        if($params['action'] == 'cancelOrder'){
            $this->calcCancelOrder($params['order_id']);
        }

        if($params['action'] == 'ivc'){
            $this->invitationCode($params['user_id']);
        }

        if($params['action'] == 'net2'){
            $this->intoNet2($params['user_id'], 'HT',0,0,false);
        }

        if($params['action'] == 'distributor'){
            $this->CalcDistributor($params['user_id'], $params['amount']);
        }

        if($params['action'] == 'intoNet'){
            $this->intonet($params['user_id'], 'HT', false);
        }
        if($params['action'] == 'wslvUp'){
            $this->wslvUp($params['user_id'], $params['uplv']);
        }

        if($params['action'] == 'autoSync'){
            $this->autoSync();
        }

        if($params['action'] == 'autoOrderSync'){
            $this->autoOrderSync();
        }

        if($params['action'] == 'marketAct'){
            $this->marketAct(1050);
        }

        if($params['action'] == 'newAccount'){
            $this->newAccount();
        }

        if($params['action'] == 'actAccount'){
            $this->actAccount(8259, 10000);
        }
        if($params['action'] == 'handBonusRefund'){
            $this->handBonusRefund();
        }
        if($params['action'] == 'Net2DPOne'){
            exit;
            $this->Net2DPOne(30084, '****************', 4000, 0, 0, 1);
            $this->Net2DPOne(30081, '****************', 4000, 0, 0, 1);
        }
        if($params['action'] == 'Net2Modify'){
//        0 notdo 1showAll 2ShowDP 3Do
            $teamAct = 2;
            $orderGL = false;
            $userR = false;
//            $this->Net2Modify(39283,'******************',4000,1,1);
//            $this->Net2Modify(39377	,'******************',800,1,1, $teamAct, $orderGL);
//            $this->Net2Modify(39100	,'******************',0,-1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41139	,'YFL28789639000411394795',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41350	,'****************',4000,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(208	,'NA',0,-1,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(30028	,'2907991463230028',400,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41431	,'2917972653241431',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41523	,'FH2915574583241313',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41668	,'FH2934708603241456',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(3150	,'NA',2400,63,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(3150	,'NA',-2400,-63,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(7091	,'NA',0,-51,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41790	,'FH2958137443241578',400,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41863	,'2958242063241863',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41836	,'2957087123241836',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41920	,'2964086893241920',1600,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41363	,'FH2964510213241156',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41365	,'2896926563241365',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(2050	,'NA',-1600,-2,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(917	,'NA',0,15,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(6273	,'NA',0,-15,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41919	,'NA',0,-15,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(6273	,'NA',0,-15,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(39741	,'2994676613239741',800,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(42010	,'NA',800,1,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(42265	,'FH3000026393242053',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(42149	,'NA',0,1,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(39499	,'3010640303239499',400,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41505	,'3017419973241505',400,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(41505	,'3017419973241505',400,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(42299	,'3001684003242299',800,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(42299	,'3001684003242299',800,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(43508	,'3043504153243508',0,1,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(43475	,'3039053503243475',4000,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(40959	,'FH3035122173240752',150,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(36171	,'FH3046204873236171',150,0,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(43088	,'FH3029110793242878',4000,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(43356	,'3052034173243356',800,1,1, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(39331	,'3044797433239331',2000,0,1, $teamAct, $orderGL, $userR);
            $this->Net2Modify(43584	,'NA',8800,13,0, $teamAct, $orderGL, $userR);








        }
        if($params['action'] == 'Net2Show'){
//            $this->Net2Show(43506,'up', 1);
//            $this->Net2Show(41919,'up', 1);
//            $this->Net2Show(41363,'up', 1);
//            $this->Net2Show(41365,'up', 1);
//            $this->Net2Show(3150,'up', 1);
//            $this->Net2Show(30679,'up', 1);
//            $this->Net2Show(41505,'up', 1);
//            $this->Net2Show(38199,'down', 34033);
//            $this->Net2Show(30049,'downL', 30000);
//            $this->Net2Show(32272,'downL', 30000);
//            $this->Net2Show(7091,'downL', 30000);
//            $this->Net2Show(41932,'up', 1);
//            $this->Net2Show(41894,'up', 1);
//            $this->Net2Show(37483,'downL', 1);
//            $this->Net2Show(39149,'downL', 1);
//            $this->Net2Show(41702,'up', 1);
//            $this->Net2Show(7587,'up', 1);
            $this->Net2Show(43594,'up', 1);
            $this->Net2Show(43628,'up', 1);
        }

        if($params['action'] == 'NetIvFind'){
            $this->NetIvFind();
        }
        if($params['action'] == 'Supplement'){
            $this->calcOrderConfirmSupplement();
        }
        if($params['action'] == 'dpSupplement'){
//            $this->dpSupplement();
            $this->dpSupplementFH();
        }
        if($params['action'] == 'Net2NumsCounts'){
            $this->Net2NumsCounts();
        }
        if($params['action'] == 'daily'){
            // $this->autoCalcDaliy();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $hour = date('H');
            if($this->setting['isgp'] == 0 && $day == 21) {
                model('app\common\model\Config')->where('name', 'isgp')->update(['value' => 1]);
                $this->setting = model('app\common\model\Config')->where('group', 'settlement')->column('name,value');
            }
            $settletime = model('app\common\model\Settletime')->where('year', $year)->where('month', $month)->where('day', $day)->find();
            if (!$settletime) {
                model('app\common\model\Settletime')->create([
                    'year' => $year,
                    'month' => $month,
                    'day' => $day, // 备注
                    'hour' => $hour
                ]);
                $this->autoCalcDaliy();
//                $this->autoCalcDaliyYY();
            }
        }

        if($params['action'] == 'handDaily'){
            $this->calcOrderConfirmDaily(false);
//            $this->OrderDailyCount();
        }
        if($params['action'] == 'weekly'){
            $this->autoWeekly();
        }
        if($params['action'] == 'monthly'){
            $this->autoMonthly();
        }
        if($params['action'] == 'sandpay_out'){
//            var_dump($this->checkMerchant());
            $bl = $this->checkBalance($params['gateway']?$params['gateway']:'sandpay');
            if($bl['code'] == 200){
                return $this->OutTransfer($params['ids'], $params['gateway']?$params['gateway']:'sandpay');
            }else{
//                var_dump($bl);
                return $bl;
            }
        }
        if($params['action'] == 'sandpay_bl'){
//            var_dump($this->checkMerchant());
            $gateway = 'yeepay';
            $bl = $this->checkBalance();
//            var_dump($bl);
        }
        if($params['action'] == 'TestClear'){
            $this->YTest('Clear');
        }
        if($params['action'] == 'TestNew'){
            $this->YTest('New');
        }
        if($params['action'] == 'userOrder'){
            $this->userOrder();
        }
        if($params['action'] == 'cUsers'){
            $this->cUsers();
        }
        if($params['action'] == 'Test'){
            echo 1;
//            $this->calcOrderPay(16267);

//            $order_info = array();
//            $order_info['user_id'] = 43006;
//            $order_info['order_id'] = 1;
//            $order_info['order_no'] = 'YFL31323107000430068566';
//            $order_info['price'] = 1300;
//            $order_info['number'] = 1;
//            $order_info['category_id'] = 108;
//            $order_info['flq'] = 3;
//            $order_info['cny_rate'] = 0.06;
//            $order_info['status'] = '1';
//            $order_info['activity_type'] = 'shopgoods';
//            $order_info['act'] = 'newpurchase';
//            $this->calcOrderAction($order_info);
//            echo 1;
            exit;
//            $this->test();
//            $this->calcOrderPay(348);
//            $this->wslvUp(139, 3);
//            $this->intonet(168,$order_no='1206242000531168', $calc=true);
//            $this->calcOrderPay(16);
//            $this->shopApply(106);
//            $this->calcOrderConfirm(63);
//            $this->intonet(1,1);
//            $bl = $this->checkBalance('yeepay');
//            var_dump($bl);
//            $msg = $this->OutTransfer(186974, 'yeepay');
//            $msg = $this->checkOrderOut(106078);
//            $msg = $this->checkOrderIn('202301282316531255493153100519');
//            $this->autoCalcDaliyYY();
//            $this->calcOrder(25359);
//             var_dump($msg);
        }
    }

    public function certified($user_id){
        return true;
        //赠送1张优惠券
//        $coupon_id = 4;
        $userinfo = model('app\common\model\User')->where('id', $user_id)->field('saas_id')->find();
//        $coupon_id = $userinfo['saas_id'] + 28;
        $coupon_id = $this->saas_info[$userinfo['saas_id']]['coupon_id'];
        $coupon = model('app\api\model\wanlshop\Coupon')->get($coupon_id);
        if($coupon){
            // 领取优惠券并保留备份
            $result = model('app\api\model\wanlshop\CouponReceive');
            $result->state = 1;
            $result->coupon_id = $coupon_id;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $user_id;
            $result->shop_id = $coupon['shop_id'];
            $result->type = $coupon['type'];
            $result->name = $coupon['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $coupon['price'];
            $result->discount = $coupon['discount'];
            $result->limit = $coupon['limit'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $coupon['validity'];
            $result->startdate = $coupon['startdate'];
            $result->enddate = $coupon['enddate'];
            $result->save();
            if($result){
                if($coupon['grant'] != '-1'){
                    // 剩余数量
                    $data['surplus'] = $coupon['surplus'] - 1;
                    // 即将过期，强制失效
                    if($coupon['surplus'] == 1){
                        $data['invalid'] = 1;
                    }
                }
                $data['alreadygrant'] = $coupon['alreadygrant'] + 1;
                // 更新优惠券领取+1
                $coupon->allowField(true)->save($data);
            }
        }
    }

    public function shopApply($user_id){
        $userinfo = model('app\common\model\User')->where('id', $user_id)->find();
        $shopauth = model('app\api\model\wanlshop\Auth')->where('user_id', $user_id)->find();
        if(!$shopauth){
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['verify'] = '3';
            $shoparr['status'] = 'normal';
            $shoparr['bio'] = $userinfo['username'];

            model('app\api\model\wanlshop\Auth')->insert($shoparr);
        }
        $shopinfo = model('app\api\model\wanlshop\Shop')->where('user_id', $user_id)->find();
        if (!$shopinfo) {
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['shopname'] = $userinfo['username'];
            $shoparr['avatar'] = '';
            $shoparr['bio'] = $userinfo['username'];
            $shoparr['description'] = $userinfo['username'];
            $shoparr['pickup_address'] = '';
            $shoparr['business_hours'] = '';
            $shoparr['city'] = '';
            $shoparr['verify'] = '3';

            model('app\api\model\wanlshop\Shop')->insert($shoparr);
        }
    }

    /**
     * 订单支付
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderPay($order_id)
    {
        $goods_id = 1;
        $goodsnums = 0;
        $amount = 0;
        $points = 0;
        $ns_am = 0;
        $ws_am = 0;
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
//            ->where('shop_id', 1)
            ->where('state', '>', 1)
            ->where('state', '<', 7)
            ->where('statusb', '0')
            ->where('id',$order_id)->find();
        if($order) $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user && $user['saas_id'] > 0){
            $uplv = 0;
            $uplv2 = 0;
            //查询支付记录，是否含快递费用20
            $pay = Pay::where(['order_id'=>$order_id])->find();
            $freight_price = 20;
            if($pay && $pay['pay_state'] == 1 && $pay['freight_price'] == $freight_price && $pay['price'] == $freight_price){
                // 20快递费
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $user['id'], '购买云店赠送', 'subsidy', $order['order_no'], 'currency_rmb', '0');
            }
//            if($pay->bd > 0 || $pay->pay > 0) $this->currency = 'currency_gp';
//            else $this->currency = 'currency_ns';
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $fwsc = false;
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] || $goodsData['activity_type'] == 'vipgoods' || $goodsData['activity_type'] == 'shopgoods') {
//                if (($goods['shop_id'] - 28) == $user['saas_id'] && $goods['shop_id'] > 29) {
//                if ($goods['shop_id'] == $this->calcData[0]['shop_id']) {
                    $amount = $amount + $goods['price'] * $goods['number'];
                    $goodsnums = $goodsnums + $goods['number'];
                    $activate = false;
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv1'] || $goodsData['activity_type'] == 'vipgoods'){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price']*$goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                        if ($user['vipv_level'] < 2) {
                            $uplv = 2;
                            $activate = true;
                        } else {
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20*$goods['number'], $user['inviter_id'], '推V复购', 'subsidy', $order['order_no'], $this->currency, '0');
                            if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20*$goods['number'], $user['inviter_id'], '推V复购', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                        }
                    }
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods'){
                        if($goodsData['gift_type'] == 'rmb') { //  买云店赠送提货券
                            try {
                                // 送两份提货券
                                $nnNum = 2; // 送几倍提货券
                                $giftRmb = $goods['price'] * $goods['number'] * $nnNum;
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($giftRmb, $user['id'], '购买云店赠送', 'subsidy', $order['order_no'], 'currency_rmb', '1');
                                // 自动发货-虚拟发货
                                $this->autoDelivery($order_id);
                            } catch (Exception $e) {
                                $message = $e->getMessage();
                                Log::error("自动发货-虚拟发货 -- error=".$message);
                            }
                        }

                        Log::info("calcOrderPay 订音类型 flq=" . $order['flq']);
                        $goodam = $goods['price'] * $goods['number'];
                        $price = 999;
                        if($goodam % 999 == 0){
                            $nums = floor($goodam / 999);
                        }else if($goodam % 1300 == 0){
                            $price = 1300;
                            $nums = floor($goodam / 1300);
                        }else if($goodam % 1133 == 0){
                            $price = 1133;
                            $nums = floor($goodam / 1133);
                        }

                        if($user['vip_level'] <= 2) {
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price'] * $goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                            $uplv = 3;
//                            $fwsc = true;
                            $activate = true;
                            //注册用户
//                            $res = controller('app\api\controller\exten\UserOrderReq')->register($user['id']);
                            $order_info = array();
                            $order_info['user_id'] = $user['id'];
                            $order_info['order_id'] = $order['id'];
                            $order_info['order_no'] = $order['order_no'];
                            $order_info['price'] = $price;
                            $order_info['number'] = $nums;
                            $order_info['flq'] = $order['flq'];
                            $order_info['cny_rate'] = $order['cny_rate'];
                            $order_info['category_id'] = $goodsData['category_id'];
                            $order_info['activity_type'] = $goodsData['activity_type'];
//                            controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'newpurchase');
                            $order_info['act'] = 'newpurchase';
                            $order_info['orderToGd'] = (isset($order->orderToGd) && $order->orderToGd === '1') ? '1' : '0';
                            $this->calcOrderAction($order_info);

//                            $usern = model('app\common\model\User')->where('id', $user['id'])->find();
//                            if(($usern['vip_level'] > $user['vip_level']) && $usern['vip_level'] == 3){
//                                model('app\common\model\User')->where('id', $user['id'])->setInc('vip_level', 1);
//                                model('app\admin\model\UserUpgradeLog')->create([
//                                    'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
//                                    'pay_user_id' => $user['id'],
//                                    'rec_user_id' => $user['inviter_id'],
//                                    'lv_old' => $usern['vip_level'],
//                                    'lv_new' => $usern['vip_level']+1,
//                                    'state' => 1,
//                                    'amount' => 0
//                                ]);
//                            }
//                        开通店铺
//                            $this->shopApply($user['id']);
                        } else {
                            $order_info = array();
                            $order_info['user_id'] = $user['id'];
                            $order_info['order_id'] = $order['id'];
                            $order_info['order_no'] = $order['order_no'];
                            $order_info['price'] = $price;
                            $order_info['number'] = $nums;
                            $order_info['flq'] = $order['flq'];
                            $order_info['cny_rate'] = $order['cny_rate'];
                            $order_info['category_id'] = $goodsData['category_id'];
                            $order_info['activity_type'] = $goodsData['activity_type'];
//                            controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'repurchase');
                            $order_info['act'] = 'repurchase';
                            $order_info['orderToGd'] = (isset($order->orderToGd) && $order->orderToGd === '1') ? '1' : '0';
                            $this->calcOrderAction($order_info);
                        }
//                        $goodpv = $goods['number'] * 100;
//                        if($goods['number'] >= 1) $uplv2 = 2;
//                        if($goods['number'] >= 5) $uplv2 = 3;
//                        if($goods['number'] >= 10) $uplv2 = 4;
//                        if($user['vip_level'] < $uplv2) {
//                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price'] * $goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
////                            $fwsc = true;
//                            if($user['vip_level'] < 2) $activate = true;
//                            $this->intoNet2($user['id'], $order['order_no'],$goodpv,1,$uplv2,$user['vip_level'] < 2?1:2);
////                        开通店铺
////                            $this->shopApply($user['id']);
//                        } else {
//                            $this->intoNet2($user['id'], $order['order_no'],$goodpv/2,0,$uplv2,3);
//                        }
                    }
//                    if ($uplv > 0 || $uplv2 > 0){
//                        $loops = 0;
//                        $userinfo = $user;
//                        $team20 = true;
//                        while ($userinfo) {
//                            $userarr = array();
//                            if ($loops == 0) {
//                                if ($userinfo['vip_status'] == '0') {
//                                    $userarr['vip_status'] = '1';
//                                }
//                                if($uplv == 2){
//                                    $userarr['vipv_level'] = $uplv;
//                                    $userarr['self_am0'] = $goods['price'] * $goods['number'];
//                                    model('app\admin\model\UserUpgradeLog')->create([
//                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
//                                        'pay_user_id' => $userinfo['id'],
//                                        'rec_user_id' => $userinfo['inviter_id'],
//                                        'lv_old' => 1,
//                                        'lv_new' => $uplv,
//                                        'state' => 1,
//                                        'amount' => $goods['price'] * $goods['number']
//                                    ]);
//                                }
//                                if($uplv2 > 1){
//                                    if ($activate){
//                                        $userarr['monthly_fg'] = 1;
//                                        $userarr['monthly_fgn'] = 1;
//                                    }else{
//                                        if($user['monthly_fg'] == 0)  $userarr['monthly_fg'] = 1;
//                                        else $userarr['monthly_fgn'] = 1;
//                                    }
//                                    if($userinfo['vip_level'] < $uplv2){
//                                        $userarr['vip_level'] = $uplv2;
//                                        $userarr['uptime'] = time();
//                                        $userarr['self_amd0'] = $goods['number'] * 100;
//                                        model('app\admin\model\UserUpgradeLog')->create([
//                                            'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
//                                            'pay_user_id' => $userinfo['id'],
//                                            'rec_user_id' => $userinfo['inviter_id'],
//                                            'lv_old' => $userinfo['vip_level'],
//                                            'lv_new' => $uplv2,
//                                            'state' => 1,
//                                            'amount' => $goods['number'] * 100
//                                        ]);
//                                    }
//                                }
////                                $userarr['self_amt'] = $userinfo['self_amt'] + $goods['price'] * $goods['number'];
//                                $year = date('Y');
//                                $month = date('m');
//                                $day = date('d');
//                                model('app\common\model\Settletime')
//                                    ->where('year', $year)
//                                    ->where('month', $month)
//                                    ->where('day', $day)->setInc('revenue3',$goods['price'] * $goods['number']);
//                            }
//                            if ($loops > 0) {
//                                if ($loops == 1) {
//                                    if ($activate) {
//                                        if ($uplv == 2) {
//                                            $userarr['invite_nums'] = $userinfo['invite_nums'] + 1;
//                                            if ($userarr['invite_nums'] == 3){
//                                                $am3 = $userinfo['self_am0'];
//                                                $user3 = model('app\common\model\User')
//                                                    ->where('inviter_id', $userinfo['id'])
//                                                    ->where('self_am0', '>', 0)
//                                                    ->field('self_am0')
//                                                    ->order('self_am0', 'asc')
//                                                    ->find();
//                                                if ($user3['self_am0'] < $am3) $am3 = $user3['self_am0'];
//                                                if ($am3 > 0) controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am3, $userinfo['id'], '购物返', 'subsidy', $order['order_no'], $this->currency, '0');
//                                            }
//                                            if ($userarr['invite_nums'] > 3){
//                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20*$goods['number'], $userinfo['id'], '推V购物', 'subsidy', $order['order_no'], $this->currency, '0');
//                                            }
//                                            if($userinfo['vipv_level'] == 2 && $userarr['invite_nums'] >= 9 && $userinfo['team_nums'] >= 99) $userarr['vip_level'] = 3;
//                                        }
//                                        if ($uplv2 > 0) {
//                                            $userarr['invites_nums'] = $userinfo['invites_nums'] + 1;
//                                        }
//                                    }
//                                }
//                                if ($activate) {
//                                    if ($uplv == 2) {
//                                        $userarr['team_nums'] = $userinfo['team_nums'] + 1;
////                                        if ($userarr['team_nums'] >= 99 && $userinfo['vipv_level'] == 2 && $userinfo['invite_nums'] >= 9){
////                                            $userarr['vip_level'] = 3;
////                                            error_log(date('Y-m-d H:i:s').json_encode($userarr['vip_level'] == 3,1)."\r\n",3,'qifu.log');
////                                            $userarr['uptime'] = time();
////                                            model('app\admin\model\UserUpgradeLog')->create([
////                                                'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
////                                                'pay_user_id' => $userinfo['id'],
////                                                'rec_user_id' => $userinfo['inviter_id'],
////                                                'lv_old' => $userinfo['vip_level'],
////                                                'lv_new' => 3,
////                                                'state' => 1,
////                                                'amount' => 0
////                                            ]);
//////                                            $this->intonet($userinfo['id'], 'Auto', false);
////                                        }
//                                        if($team20){
//                                            if($userinfo['vip_level'] >= 3) {
//                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $userinfo['id'], '团队奖励', 'subsidy', $order['order_no'], $this->currency, '0');
//                                                $team20 = false;
//                                            }
//                                        }
//                                    }
//                                    if ($uplv2 > 1) $userarr['teams_nums'] = $userinfo['teams_nums'] + 1;
//                                }
//                            }
//                            model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
//                            $loops++;
//                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
//                        }
//                    }
//                    if($fwsc){
//                        $userinfo = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
//                        while ($userinfo && $fwsc) {
//                            if($userinfo['city_server'] != 0 && $order['flq'] == 2){
//                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price']*$goods['number']*0.03, $userinfo['id'], '服务商补贴', 'subsidy', $order['order_no'], $this->currency, '0');
//                                $fwsc = false;
//                            }
//                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
//                        }
//                    }
                }else if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_ws'] && $goods['shop_id'] > 29) {
                    $ws_am = $amount + $goods['price'] * $goods['number'];
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else if($goodsData['activity_type'] == 'ninestar'){
                    $activity = model('app\admin\model\wanlshop\Ninestar')->where('id', $goodsData['activity_id'])->find();
                    if($activity){
                        $ns_am += $activity['price'] * $goods['number'];
                    }
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else{
                    $amount = $amount + $goods['price'] * $goods['number'];
                }
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_nfr', '0', $goods['goods_id']);
            }

            // 帮卖订单支付后-佣金待结算
            $this->helpSellPayOrder($order_id);
        }
        if ($amount > 0){
            model('app\api\model\wanlshop\Order')->where('id',$order_id)->update(['statusb' => '1']);
        }
        if ($ns_am > 0){
            $this->CalcOrderNinestar($order_id, $ns_am, $amount);
        }
        if($ws_am > 0 && $user['svip_level'] == 1){
            model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->setInc('ws_am', $ws_am);
            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->find();
            if($uulinfo){
                if($uulinfo['ws_am'] > 2900) {
                    model('app\common\model\User')
                        ->where('id', $user['id'])
                        ->update(['svip_level' => 2]);
                }
            }
        }
        //测试秒结
//        $this->autoCalcDaliy(false);
    }

    public function calcOrderAction($order, $isOrderNet=false, $currency = null){
//        if($currency != null) $this->currency = $currency;
        if (!empty($order['orderToGd']) && $order['orderToGd'] == '1') {
            $this->feeRate = 0.1;
            $this->istoGd = true;
        }
        $log = array();
        if($order['act'] == 'newpurchase' || $order['act'] == 'repurchase' || $order['act'] == 'emptyorder') {
            if($order['act'] == 'newpurchase' || $order['act'] == 'repurchase'){
                $chekorder = model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $order['order_no'])
                    ->where('type', 'subsidy')
                    ->where('status', '<>', '2')
                    ->order('id', 'desc')
                    ->find();
                if($chekorder) return true;
            }
            $fwsc = false;
            $drq = true;
            $drqJt = false;
            $drqJtRate = 0;
            if($drq) {
                $drqRate = 0.05;
                if (isset($order['cny_rate'])) $drqRate = $order['cny_rate'];
            }
            $zjc = true;
            $zjnlv = 2;
            $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
            if ($user['saas_id'] > 0) {
                if ($order['category_id'] == $this->calcData[0]['category_id_lv2'] || $order['activity_type'] == 'shopgoods') {
                    $ott = false;
                    $orderGoodsPv = 0;
                    $uplv = 0;
                    $activate = false;
                    $goodpv = $order['number'] * 100;
                    $flqPv = 0;
                    $gdrPv = 0;
                    if($user['plan'] == 'A' || $user['plan'] == 'C'){
                        if($order['price'] == 1300) {
                            $flqPv = $order['number'] * 300;
                            $goodpv = $order['number'] * 800;
                            if($this->istoGd) $gdrPv = $order['number'] * 100;
                        }elseif($order['price'] == 1133) {
                            $flqPv = $order['number'] * 250;
                            $goodpv = $order['number'] / 3 * 2000;
                            if($this->istoGd) $gdrPv = $order['number']/3 * 100;
                        }else {
                            $flqPv = $order['number'] * 200;
                            $goodpv = $order['number'] * 700;
                            if($this->istoGd) $gdrPv =  $order['number'] * 100;
                        }
                    }
                    if ($order['flq'] == 1) $goodpv = $flqPv;
                    $goodpv = $goodpv - $gdrPv;
                    if ($order['number'] >= 1) $uplv = 2;
                    if ($order['number'] >= 3) $uplv = 3;
                    if ($order['number'] >= 5) $uplv = 4;
                    if ($order['number'] >= 10) $uplv = 5;
                    if ($uplv == 5 && $user['vip_level'] == 1 && $order['act'] != 'emptyorder') $ott = true;
                    if ($user['vip_level'] < $uplv) {
                        $orderGoodsPv = $order['act'] == 'emptyorder'?0:$goodpv;
                        $gdrPv = $order['act'] == 'emptyorder'?0:$gdrPv;
                        $log['act'] = 'newpurchase';
                        if ($user['vip_level'] < 2) {
                            $fwsc = true;
                            $activate = true;
                        }
                        if (($user['plan'] == 'A' || $user['plan'] == 'C') && !$fwsc) $fwsc = true;
                        $log['net'] = $this->intoNet2($user['id'], $order['order_no'], $orderGoodsPv, $user['vip_level'] < 2?1:0, $uplv, $user['vip_level'] < 2?1:2, $user['pnet_dz'], $isOrderNet, $gdrPv);
                    } else {
                        $orderGoodsPv = $order['act'] == 'emptyorder'?0:$goodpv / 2;
                        $gdrPv = $order['act'] == 'emptyorder'?0:$gdrPv/2;
                        $log['act'] = 'repurchase';
//                        $fwsc = true;
                        $log['net'] = $this->intoNet2($user['id'], $order['order_no'], $orderGoodsPv, 0, $uplv, 3, $user['pnet_dz'], $isOrderNet, $gdrPv);
                    }
                    if ($uplv > 0) {
                        $loops = 0;
                        $userinfo = $user;
                        while ($userinfo) {
                            $userarr = array();
                            if ($loops == 0) {
                                if ($userinfo['vip_status'] == '0') {
                                    $userarr['vip_status'] = '1';
                                    $userarr['activetime'] = time();
                                }
                                if ($uplv > 1) {
                                    if ($activate) {
                                        $userarr['monthly_fg'] = 1;
                                        $userarr['monthly_fgn'] = 1;
                                    } else {
                                        if ($user['monthly_fg'] == 0) $userarr['monthly_fg'] = 1;
                                        else $userarr['monthly_fgn'] = 1;
                                    }
                                    if ($userinfo['vip_level'] < $uplv) {
                                        $userarr['vip_level'] = $uplv;
                                        $userarr['uptime'] = time();
//                                        if($user['plan'] == 'A' || $user['plan'] == 'C'){
//                                            $userarr['self_amd0'] = $order['act'] == 'emptyorder' ? 0:($order['price'] == 1300?800*$order['number']:700*$order['number']);
//                                        }else{
//                                            $userarr['self_amd0'] = $order['act'] == 'emptyorder' ? 0:$order['number'] * 100;
//                                        }
                                        $userarr['self_amd0'] = $orderGoodsPv + $gdrPv;
                                        model('app\admin\model\UserUpgradeLog')->create([
                                            'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                            'pay_user_id' => $userinfo['id'],
                                            'rec_user_id' => $userinfo['inviter_id'],
                                            'lv_old' => $userinfo['vip_level'],
                                            'lv_new' => $uplv,
                                            'state' => 1,
                                            'amount' => $orderGoodsPv
                                        ]);
                                    }
                                }
//                                $userarr['self_amt'] = $userinfo['self_amt'] + $goods['price'] * $goods['number'];
                                if($order['act'] != 'emptyorder') {
                                    $year = date('Y');
                                    $month = date('m');
                                    $day = date('d');
                                    model('app\common\model\Settletime')
                                        ->where('year', $year)
                                        ->where('month', $month)
                                        ->where('day', $day)->setInc('revenue3', $order['price'] * $order['number']);
                                }
                            }
                            if ($loops > 0) {
                                if ($loops == 1) {
                                    if ($activate) {
                                        if ($uplv > 0) {
                                            $userarr['invites_nums'] = $userinfo['invites_nums'] + 1;
                                            $this->Net2LU($userinfo['id']);
                                        }
                                    }
                                }
                                if ($activate) {
                                    if ($uplv > 1) $userarr['teams_nums'] = $userinfo['teams_nums'] + 1;
                                }
//                                flq 1 福利券 2正常 3达人福利券 4？折扣券
                                if($zjc && $order['flq'] == 2 && $user['plan'] == 'C' && $userinfo['svip_levels']>$zjnlv && $order['act'] != 'emptyorder'){
                                    $zjam = $orderGoodsPv * ($this->slvsinfo[$userinfo['svip_levels']]['rate']-$this->slvsinfo[$zjnlv]['rate']);
//                                    if ($user['vip_level'] >= $uplv) $zjam = $zjam / 2;
                                    $zjnlv = $userinfo['svip_levels'];
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($zjam, $userinfo['id'], '总监奖励', 'subsidy', $order['order_no'], 'currency_bd', '0');
                                }
                                if ($fwsc && $userinfo['vip_levels'] > 1 && $order['act'] != 'emptyorder') {
                                    $this->bonusAct($userinfo, $orderGoodsPv * 0.05, $order['order_no'], '商务中心补贴');
                                    $fwsc = false;
                                }
                                if($drq && $order['flq'] == 3 && $this->dkqInfo[$userinfo['svip_lv_dkq']] > $drqRate){
                                    $this->bonusAct($userinfo, $orderGoodsPv * ($this->dkqInfo[$userinfo['svip_lv_dkq']] - $drqRate), $order['order_no'], '达人补贴');
                                    $drqRate = $this->dkqInfo[$userinfo['svip_lv_dkq']];
                                }
                                if($drqJt && $order['flq'] == 2 && $user['plan'] == 'C' && $userinfo['svip_lv_dkq'] > 1 && $this->dkqInfo[$userinfo['svip_lv_dkq']] > $drqJtRate && $order['act'] != 'emptyorder'){
                                    $this->bonusAct($userinfo, $orderGoodsPv * ($this->dkqInfo[$userinfo['svip_lv_dkq']] - $drqJtRate), $order['order_no'], '达人津贴');
                                    $drqJtRate = $this->dkqInfo[$userinfo['svip_lv_dkq']];
                                }
                            }
                            if (!empty($userarr)) {
                                $log = array_merge($log,$userarr);
                                model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                            }
                            $loops++;
                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                        }
                        if($orderGoodsPv > 0) {
                            model('app\api\model\wanlshop\OrderSync')->where('order_no', $order['order_no'])->update(['goodspv' => $orderGoodsPv]);
                            model('app\api\model\wanlshop\Order')->where('order_no', $order['order_no'])->update(['goodspv' => $orderGoodsPv]);
                        }
                    }
                    if($ott) $this->oneToThree($user['id']);
                    return $log;
                }
            }
        }
        if($order['act'] == 'refund' || $order['act'] == 'cancel'){
            $this->calcOrderRefundByNo($order['order_no']);
        }
        if($order['act'] == 'confirm'){
            $this->calcOrderConfirmByNo($order['order_no']);
        }
        return $log;
    }

    public function oneToThree($uid){
        $userModel = model('app\common\model\User');
        $userinfo = $userModel->where('id',$uid)->find();
        $userNew = $userinfo->toArray();
        $userNew['inviter_id'] = $userinfo->id;
        $userNew['parent_code'] = $userinfo->parent_code.",".$userinfo->id;
        $userArr['ns_status'] = '0';
        $userNew['vip_status'] = '0';
        $userNew['vip_level'] = 1;
        $userNew['vip_level'] = 1;
        $userNew['vip_levels'] = 1;
        $userNew['self_amd0'] = 0;
        $userNew['viplc_level'] = 1;
        $userNew['svip_level'] = 1;
        $userNew['manager_id'] = 0;
        $userNew['pid'] = 0;
        $userNew['pnet'] = 0;
        $userNew['pnet_dz'] = 0;
        $userNew['pnet_iv'] = 0;
        $userNew['pnums1'] = 0;
        $userNew['pnums2'] = 0;
        $userNew['pam1'] = 0;
        $userNew['pam2'] = 0;
        $userNew['pam1n'] = 0;
        $userNew['pam2n'] = 0;
        $userNew['pam1c'] = 0;
        $userNew['pam2c'] = 0;
        $userNew['invites_nums'] = 0;
        $userNew['teams_nums'] = 0;
        $userNew['successions'] = 0;
        $userNew['maxsuccessions'] = 0;
        if(strlen($userNew['avatar']) > 255){
            unset($userNew['avatar']);
        }
        unset($userNew['id']);
        unset($userNew['money']);
        unset($userNew['currency_rmb']);
        unset($userNew['currency_xnb']);
        unset($userNew['currency_cny']);
        unset($userNew['currency_bd']);
        unset($userNew['currency_ns']);
        unset($userNew['currency_gp']);
        unset($userNew['currency_tz']);
        unset($userNew['currency_nfr']);
        unset($userNew['currency_adv']);
        unset($userNew['currency_pu']);
        unset($userNew['vipv_level']);
        unset($userNew['form_app']);
        unset($userNew['form_id']);
        unset($userNew['username_gd']);
        unset($userNew['id_gd']);
        unset($userNew['username_fh']);
        unset($userNew['id_fh']);
        unset($userNew['activetime']);

        $userNew['pnet_dz'] = 1;
        $username = '';
        $flag = true;
        while($flag){
            $username = 'YFL'.Random::numeric(8);
            $flag = (bool)$userModel->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
        }
        $userNew['username'] = $username;
        $userNew['nickname'] = $username;
        $id = $userModel->allowField(true)->create($userNew,true)->id;
        if($id > 0){
            $this->invitationCode($id);
            $order_info = array();
            $order_info['user_id'] = $id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $username;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $this->calcOrderAction($order_info);
        }

        $userNew['pnet_dz'] = 2;
        $username = '';
        $flag = true;
        while($flag){
            $username = 'YFL'.Random::numeric(8);
            $flag = (bool)$userModel->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
        }
        $userNew['username'] = $username;
        $userNew['nickname'] = $username;
        $id = $userModel->allowField(true)->create($userNew,true)->id;
        if($id > 0){
            $this->invitationCode($id);
            $order_info = array();
            $order_info['user_id'] = $id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $username;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $this->calcOrderAction($order_info);
        }
    }

    public function calcOrderConfirmByNo($order_no){
        $mtype_arr = array(
            0 => 'currency_ns',
            1 => 'currency_rmb',
            2 => 'currency_gp',
            3 => 'currency_nsd',
        );
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order_no)
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['days' => 1]);
//                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
//                $cl_arr = array();
//                $cl_arr['status'] = '1';
//                $cl_arr['before'] = $user[$mtype];
//                $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                $cl_arr['updatetime'] = strtotime('now');
//                model("app\common\model\Currency{$mtypelog}Log")
//                    ->where('id', $cl['id'])
//                    ->update($cl_arr);
//
//                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }
    }

    public function calcOrderRefundByNo($order_no){
        $mtype_arr = array(
            0 => 'currency_ns',
            1 => 'currency_rmb',
            2 => 'currency_gp',
            3 => 'currency_nsd',
        );

        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order_no)
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
            }
        }
    }

    public function autoSync(){
//        while(true) {
        if($this->setting['is_sync_runing'] == 1){
            echo "未开启或有运行中的进程\n";
            return false;
        }
        model('app\common\model\Config')->where('name','is_sync_runing')->update(['value' => 1]);
//        autoSync User
        $autoUserLv = true;
        if($autoUserLv){
            foreach(model('app\admin\model\UserUpgradeLog')
                        ->where('is_sync_gd', '0')
                        ->order('id desc')
                        ->limit('2')
                        ->select() as $uul) {
                echo $uul->id."===>".$uul->lv_old."===>".$uul->lv_new."\n";
                $user = model('app\common\model\User')->where('id', $uul->pay_user_id)->find();
                if($user && $uul->lv_new > 1){
                    $pData = array(
                        'to' => 'gd',
                        'id' => $uul->id,
                        'user_id' => $user->id_gd,
                        'lv' => $uul->lv_new
                    );
                    $res = controller('app\api\controller\exten\UserOrderReq')->autoSyncLv($pData);
                    var_dump($res);
                }
            }
            foreach(model('app\admin\model\UserUpgradeLog')
                        ->where('is_sync', '0')
                        ->order('id asc')
                        ->limit('2')
                        ->select() as $uul) {
                echo $uul->id."===>".$uul->lv_old."===>".$uul->lv_new."\n";
                $user = model('app\common\model\User')->where('id', $uul->pay_user_id)->where('form_app', 'FHXQ')->find();
                if($user && $uul->lv_new > 1){
                    $pData = array(
                        'to' => 'fh',
                        'id' => $uul->id,
                        'user_id' => $uul->pay_user_id,
                        'lv' => $uul->lv_new
                    );
                    $res = controller('app\api\controller\exten\UserOrderReq')->autoSyncLv($pData);
                    var_dump($res);
                }else{
                    $syncInfo = array('is_sync'=>'1');
                    model('app\admin\model\UserUpgradeLog')->where('id',$uul->id)->update($syncInfo);
                }
            }
        }
        $autoUser = true;
        $timeline = strtotime("-1 minute");
        $user_nums_max = 10;
        if($autoUser){
            $loop = 1;
            $users = array();
            $sync_modify = true;
            echo "From userId:".$this->setting['sync_user_id']."\n";
            foreach(model('app\common\model\User')
//                        ->where('form_app', NULL)
                        ->where('createtime', '<', $timeline)
                        ->where('id', '>=', $this->setting['sync_user_id'])
//                        ->where('id', '<', 40)
                        ->order('id', 'asc')
                        ->select() as $user){
                if($user->id_gd == 0) {
                    $users[$user->id] = $user->id;
                    echo $user->id."\n";
                    if($sync_modify) {
                        $sync_modify = false;
                        if($this->setting['sync_user_id'] < $user->id)
                            model('app\common\model\Config')->where('name','sync_user_id')->update(['value' => $user->id]);
                    }
                    $loop++;
                    if($loop > $user_nums_max) break;
                }
            }
            var_dump(count($users));
//            var_dump($users);
            if(!empty($users)) {
                $res = controller('app\api\controller\exten\UserOrderReq')->registerBatch($users);
                var_dump($res);
            }
        }


        $autoUser = true;
        $timeline = strtotime("-1 minute");
        $user_nums_max = 10;
        if($autoUser){
            $loop = 1;
            $users = array();
            $sync_modify = true;
            echo "From userId:".$this->setting['sync_user_id']."\n";
            foreach(model('app\common\model\User')
                        ->where('form_app', NULL)
//                            ->where('form_app', 'GD')
                        ->where('createtime', '<', $timeline)
//                        ->where('id', '>=', $this->setting['sync_user_id'])
//                        ->where('id', '<', 40)
                        ->where('id', '>', 30000)
                        ->where('id_fh', 0)
                        ->order('id', 'asc')
                        ->select() as $user){
                $users[$user->id] = $user->id;
                echo $user->id."\n";
                $loop++;
                if($loop > $user_nums_max) break;
            }
            var_dump(count($users));
//            var_dump($users);
            if(!empty($users)) {
                $res = controller('app\api\controller\exten\UserOrderReq')->registerBatchFh($users);
                var_dump($res);
            }
        }

        $OTG = true;
        if($OTG){
            foreach(model('app\api\model\wanlshop\OrderSync')
//                    ->where('id', 6587)
                        ->where('act', 'in', ['newpurchase', 'repurchase'])
                        ->where('status', '<>', 0)
                        ->where('status', '<>', 3)
                        ->where('orderToGd', '1')
                        ->where('goodspv', '>', 0)
                        ->order('id', 'asc')
                        ->select() as $order) {
                $user = model('app\common\model\User')->where('id', $order->user_id)->find();
                if($user){
                    $orderInfo = array(
                        'order_type' => 'OrderSync',
                        'order_id' => $order->id,
                        'order_no' => $order->order_no,
                        'user_id' => $order->user_id,
                        'user_id_gd' => $user->id_gd,
                        'order_price' => $order->price * $order->number,
                        'order_pv' => $order->goodspv,
                        'orderToGd' => $order->orderToGd,
                        'order_time' => $order->createtime,
                    );
                    $res = controller('app\api\controller\exten\UserOrderReq')->addOrderGd($orderInfo);
                    var_dump($res);
                }
            }
//        exit;
//        18014
//        18183
            foreach(model('app\api\model\wanlshop\Order')
                        ->where('status', 'normal')
                        ->where('deletetime', null)
                        ->where('state', '>', 1)
                        ->where('state', '<', 7)
                        ->where('statusb', '1')
                        ->where('orderToGd', '1')
                        ->where('goodspv', '>', 0)
                        ->order('id asc')->select() as $order) {
                $user = model('app\common\model\User')->where('id', $order->user_id)->find();
                $pay = model('app\api\model\wanlshop\Pay')->where('user_id',$order['user_id'])->where('order_id',$order->id)->find();
                if($user && $pay) {
                    if(strpos($pay->notice, 'balancermb') === false) {
                        $orderInfo = array(
                            'order_type' => 'Order',
                            'order_id' => $order->id,
                            'order_no' => $order->order_no,
                            'user_id' => $order->user_id,
                            'user_id_gd' => $user->id_gd,
                            'order_price' => $pay->order_price,
                            'order_pv' => $order->goodspv,
                            'orderToGd' => $order->orderToGd,
                            'order_time' => $order->paymenttime,
                        );
                        $res = controller('app\api\controller\exten\UserOrderReq')->addOrderGd($orderInfo);
                        var_dump($res);
                    }
                }
            }
        }

        model('app\common\model\Config')->where('name','is_sync_runing')->update(['value' => 0]);
//        }
    }

    public function autoOrderSync(){
        //校验进程是否在执行
        $process_key = 'autoOrderSync';
        $process_time = Cache::store('redis')->get($process_key);
        if($process_time){
            echo $process_time."的进程还在运行中，跳过本次执行任务！\n";
            exit;
        }
        $time = date('Y-m-d H:i:s');
        //避免出错，锁一直不释放，最长可执行时间为30分钟
        Cache::store('redis')->set($process_key,$time,1800);
        $check = array();
        $check['value'] = 'running';
//        while($check['value'] == 'running'){
            $check = model('app\common\model\Config')->where('name', 'orderCalc')->field('value')->find();
            if($check['value'] == 'running'){
                $order = model('app\api\model\wanlshop\OrderSync')
                    ->where('act', 'in', ['newpurchase', 'repurchase', 'emptyorder'])
                    ->where('status', '0')
                    ->select();
                foreach($order as $os){
                    $usero = model('app\common\model\User')->where('id', $os['user_id'])->find();
                    model('app\api\model\wanlshop\OrderSync')->where('id', $os['id'])->update(['status' => '1']);
                    echo "==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
                    $this->calcOrderAction($os, $os['act']=='emptyorder'?true:false);
                    $user = model('app\common\model\User')->where('id', $os['user_id'])->find();
                    if(($user['vip_level'] > $usero['vip_level']) &&  $user['vip_level'] == 3){
                        model('app\common\model\User')->where('id', $user['id'])->setInc('vip_level', 1);
                        model('app\admin\model\UserUpgradeLog')->create([
                            'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
                            'pay_user_id' => $user['id'],
                            'rec_user_id' => $user['inviter_id'],
                            'lv_old' => $user['vip_level'],
                            'lv_new' => $user['vip_level'] + 1,
                            'state' => 1,
                            'amount' => 0
                        ]);
                    }
                    echo "==ID:$os->id,结束\n";
                }
            }
//        }
        //执行完毕，1s后释放锁
        Cache::store('redis')->set($process_key,$time,1);
    }
    /**
     * 确认货
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderConfirm($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_gp',
            6 => 'currency_nsd',
            7 => 'currency_ans', // 爱多米诺-永福莱豆
            8 => 'currency_gfz', // 爱多米诺-永福莱豆
        );
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['days' => 1]);
//                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
//                $cl_arr = array();
//                $cl_arr['status'] = '1';
//                $cl_arr['before'] = $user[$mtype];
//                $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                $cl_arr['updatetime'] = strtotime('now');
//                model("app\common\model\Currency{$mtypelog}Log")
//                    ->where('id', $cl['id'])
//                    ->update($cl_arr);
//
//                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }
    }

    // 帮卖-订单支付后操作
    private function helpSellPayOrder($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();

        $goods = null;
        $orderGoods = OrderGoods::where('order_id', $order_id)->find();
        if ($orderGoods) {
            $goods = Goods::withTrashed()->where('id', $orderGoods->goods_id)->find();
        }
        //帮卖活动
        if ($goods && $goods->activity == 1 && $goods->activity_type == 'dragon') {

            $activity = Ninestar::get($goods->activity_id);

            if ($activity && $activity->type == 'dragon') {

                Log::info("helpSell 帮卖订单支付 order_id=$order_id");

                //先查询对应订单的分享记录
                $pay_user = GoodsShare::where(['user_id' => $order['user_id'], 'order_id' => $order['id']])->find();
                if ($pay_user) {
                    //分享记录查询帮卖店铺
                    $help = \app\api\model\wanlshop\HelpSell::where(['shop_id' => $goods->shop_id, 'good_id' => $goods->id, 'user_id' => $pay_user->shop_id])->find();
                    //存在帮卖店铺记录
                    if ($help) {
                        //查询有效支付记录
                        $pay = model('app\api\model\wanlshop\Pay')->where(['pay_state' => 1, 'order_id' => $order_id, 'type' => 'goods'])->find();
                        //反钱给商品店铺的本金-让利 goods->shop_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')
                            ->money(+($pay['price'] * (1 - $activity->pv_value)), $help->shop_id, '买家确认收货', 'pay', $order['order_no']);
                        //给帮卖店上级分额
                        $helpUser = \app\common\model\User::get($help->user_id);
                        $inviter = \app\common\model\User::get($helpUser->inviter_id);
                        if ($inviter) {

                            $helpUid = $help->user_id;
                            $inviterUid = $helpUser->inviter_id;

                            $sub = 0;
                            //普通
                            if ($inviter->vip_level == VipEnum::ORDINARY_STORE) {
                                $sub = 0.3;
                            }
                            // 旗舰店长
                            if ($inviter->self_amd0 == 4995) {
                                $sub = 0.5;
                            }
                            if ($sub) {
                                $commissions = bcmul($activity->commission, $sub, 2);
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($commissions), $helpUser->inviter_id, '帮卖共富奖', 'subsidy', $order['order_no'], $this->currency, '0');
                                if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($commissions), $helpUser->inviter_id, '帮卖共富奖', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                                Log::info("helpSell 帮卖订单支付 sub=$sub, helpUid=$helpUid, inviterUid=$inviterUid commissions=$commissions");
                            }
                        }
                        //给帮卖店反佣金$help->user_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($activity->commission), $help->user_id, '帮卖佣金', 'subsidy', $order['order_no'], $this->currency, '0');
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($activity->commission), $help->user_id, '帮卖佣金', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                    }
                }
            }
        }
    }

    // 帮卖结算 - 确认后的7天发生结算操作
    private function helpSellSettlement($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('order_no', $order_id)->find();

        $goods = null;
        $orderGoods = OrderGoods::where('order_id', $order['id'])->find();
        if ($orderGoods) {
            $goods = Goods::withTrashed()->where('id', $orderGoods->goods_id)->find();
        }
        //帮卖活动
        if ($goods && $goods->activity == 1 && $goods->activity_type == 'dragon') {
            $activity = Ninestar::get($goods->activity_id);
            if ($activity && $activity->type == 'dragon') {

                //先查询对应订单的分享记录
                $pay_user = GoodsShare::where(['user_id' => $order['user_id'], 'order_id' => $order['id']])->find();
                if ($pay_user) {
                    $backRule = $activity->back_rule;    //帮卖返本规则
                    //先判断分享人是否购买
                    $info = GoodsShare::where([
                        'good_id' => $goods->id,
                        'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                        'user_id' => $pay_user['share_user'], //查询当前订单来源的分享用户
                        'state' => 1,
                        'is_back' => 0,
                    ])->find();
                    if ($info) {
                        //查询分享人分享出去的是否买了，是否满足条件
                        $shares_pay_count = GoodsShare::where([
                            'good_id' => $goods->id,
                            'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                            'share_user' => $pay_user['share_user'], //查询当前订单来源的分享用户
                            'state' => 1,
                            'is_back' => 0,
                        ])->count();
                        if ($shares_pay_count >= $backRule) {
                            $update = ['is_back' => 1];
                            $memo = '帮卖推' . $backRule . '返本';
                            $pay_log = model('\app\api\model\wanlshop\Pay')->where('order_id',$info['order_id'])->where('user_id',$pay_user['share_user'])->where('pay_state',1)->find();
                            $remark = '';
                            if($pay_log){
                                //实际金额大于单价（存在买多个商品）是返一个的价钱
                                $pay_log['actual_payment'] > $goods->price && $pay_log['actual_payment'] = $goods->price;
                                if($pay_log['pay_type'] == 1){//0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝,
                                    $out_refund_no = rand(10,99).date('ymdHis').rand(10000,99999);
                                    $amount = $pay_log['actual_payment']*100;
                                    $result = Factory::payment(controller('addons\wanlshop\library\WanlPay\WanlPay')->weixinConfig($order['saas_id']))->refund->byTransactionId($pay_log['trade_no'], $out_refund_no, $pay_log['price']*100, $amount, [
                                        'refund_desc' => $memo,
                                        'notify_url'    => 'http://'.$_SERVER['HTTP_HOST'].'/index/wanlshop/refund/notify',
                                    ]);
                                    $remark = '微信支付，未退款成功，交易号：'.$pay_log['trade_no'];
                                    if ($result['return_code'] != 'SUCCESS' || $result['result_code'] != 'SUCCESS'){
                                        $remark = '微信支付，返本金额已原路退回';
                                        if($result['err_code_des'] != '订单已全额退款'){//兼容已退款订单状态变更
                                            $remark = '微信退款，发生异常，交易号：'.$pay_log['trade_no'].'=>'.$result['err_code_des'];
                                        }
                                    }
                                }else{
                                    $remark = '返本金额到余额';
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($pay_log['actual_payment']),  $pay_user['share_user'], $memo, 'subsidy', $order['order_no'], 'currency_bd');
                                    // controller('addons\wanlshop\library\WanlPay\WanlPay')
                                    //     ->money(+($pay_log['actual_payment']), $pay_user['share_user'], $memo, 'subsidy', $order['order_no']);
                                }
                            }
                            $remark && $update['remark'] = $remark;
                            GoodsShare::where('id', $info['id'])->update($update);
                        }
                    }
                }
            }
        }
    }

    public function calcOrderConfirmDaily($dayDP = true){
//        if($dayDP) {
//            $user_arr = array(
//                'pam1d' => 0,
//                'pam2d' => 0
//            );
//            model('app\common\model\User')
//                ->where('id', '>', 0)
//                ->update($user_arr);
//        }

        foreach(model('app\common\model\User')
                    ->where('svip_lv_dkq', '>', 1)
                    ->select() as $user){
            echo $user->mobile."===>".$user->days_dkq_lv."===>".$user->svip_lv_dkq;
//            if($user->days_dkq_lv >= 89) {
//                model('app\common\model\User')
//                    ->where('id', $user->id)
//                    ->update(['svip_lv_dkq' => $user->svip_lv_dkq - 1, 'days_dkq_lv' => 0]);
//            }else{
                model('app\common\model\User')
                    ->where('id', $user->id)
                    ->setInc('days_dkq_lv', 1);
//            }
        }

        $mtype_arr = array(
            0 => 'currency_ns',
            1 => 'currency_rmb',
            2 => 'currency_gp',
            3 => 'currency_nsd'
        );
        foreach(model('app\api\model\wanlshop\OrderSync')
//                    ->where('id', 6587)
                    ->where('status', '<>', 0)
                    ->where('status', '<>', 3)
                    ->where('days', '<', 8)
                    ->order('id', 'asc')
                    ->select() as $os){
            echo "FH==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            model('app\api\model\wanlshop\OrderSync')->where('id', $os['id'])->setInc('days', 1);
//            foreach($mtype_arr as $mtype){
//                $mtypeex = explode("_", $mtype);
//                $mtypelog = ucfirst($mtypeex[1]);
//                foreach (model("app\common\model\Currency{$mtypelog}Log")
//                             ->where('service_ids', $os->order_no)
//                             ->where('type', 'subsidy')
//                             ->where('status','0')
////                             ->where('days', '>', 0)
//                             ->where('days', '<', 8)
//                             ->order('id', 'asc')
//                             ->select() as $cl){
//                    $user = model('app\common\model\User')->where('id', $cl['user_id'])->field("$mtype,vip_status")->find();
//                    $cl_arr = array();
//                    $cl_arr['days'] = $cl['days'] + 1;
//                    if($cl_arr['days'] >= 8) {
//                        if($user['vip_status'] == '0') {
//                            $cl_arr['status'] = '2';
//                        }else{
//                            $cl['money'] = floor($cl['money'] * 100) / 100;
//                            $cl_arr['status'] = '1';
//                            $cl_arr['before'] = $user[$mtype];
//                            $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                            $cl_arr['updatetime'] = strtotime('now');
//                            if ($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
//                            if ($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
//                        }
//                    }
//                    model("app\common\model\Currency{$mtypelog}Log")
//                        ->where('id', $cl['id'])
//                        ->update($cl_arr);
//                }
//            }
            echo "==ID:$os->id,结束\n";
        }

        foreach(model('app\api\model\wanlshop\Order')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('days', '<', 8)
                    ->where('statusb', '1')
                    ->order('id asc')->select() as $os) {
            echo "YFL==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            model('app\api\model\wanlshop\Order')->where('id', $os['id'])->setInc('days', 1);
//            foreach ($mtype_arr as $mtype) {
//                $mtypeex = explode("_", $mtype);
//                $mtypelog = ucfirst($mtypeex[1]);
//                foreach (model("app\common\model\Currency{$mtypelog}Log")
//                             ->where('service_ids', $os->order_no)
//                             ->where('type', 'subsidy')
//                             ->where('status', '0')
////                             ->where('days', '>', 0)
//                             ->where('days', '<', 8)
//                             ->order('id', 'asc')
//                             ->select() as $cl) {
//                    $user = model('app\common\model\User')->where('id', $cl['user_id'])->field("$mtype,vip_status")->find();
//                    $cl_arr = array();
//                    $cl_arr['days'] = $cl['days'] + 1;
//                    if ($cl_arr['days'] >= 8) {
//                        if($user['vip_status'] == '0') {
//                            $cl_arr['status'] = '2';
//                        }else{
//                            $cl['money'] = floor($cl['money'] * 100) / 100;
//                            $cl_arr['status'] = '1';
//                            $cl_arr['before'] = $user[$mtype];
//                            $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                            $cl_arr['updatetime'] = strtotime('now');
//                            if ($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
//                            if ($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
//                        }
//                    }
//                    model("app\common\model\Currency{$mtypelog}Log")
//                        ->where('id', $cl['id'])
//                        ->update($cl_arr);
//                }
//            }
            echo "==ID:$os->id,结束\n";
        }

        foreach ($mtype_arr as $mtype) {
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
//                         ->where('service_ids', $os->order_no)
                         ->where('type', 'subsidy')
                         ->where('status', '0')
//                             ->where('days', '>', 0)
                         ->where('days', '<', 8)
                         ->order('id', 'asc')
                         ->select() as $cl) {
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->field("$mtype,vip_status")->find();
                if($user) {
                    $cl_arr = array();
                    $cl_arr['days'] = $cl['days'] + 1;
                    if ($cl_arr['days'] >= 8) {
                        if ($user['vip_status'] == '0') {
                            $cl_arr['status'] = '2';
                        } else {
                            $cl['money'] = floor($cl['money'] * 100) / 100;
                            $cl_arr['status'] = '1';
                            $cl_arr['before'] = $user[$mtype];
                            $cl_arr['after'] = $user[$mtype] + $cl['money'];
                            $cl_arr['updatetime'] = strtotime('now');
                            if ($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
                            if ($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
                        }
                    }
                    model("app\common\model\Currency{$mtypelog}Log")
                        ->where('id', $cl['id'])
                        ->update($cl_arr);
                }
            }
        }
    }

    public function calcOrderConfirmSupplement($modify = true){
        $mtype_arr = array(
            0 => 'currency_ns',
            1 => 'currency_rmb',
            2 => 'currency_gp',
            3 => 'currency_nsd'
        );
        if($modify) {
            foreach (model('app\api\model\wanlshop\Order')
                         ->where('status', 'normal')
                         ->where('deletetime', null)
                         ->where('state', '>', 1)
                         ->where('state', '<', 7)
                         ->where('days', 8)
                         ->where('statusb', '1')
                         ->order('id asc')->select() as $os) {

                $count = model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $os->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '0')
//                             ->where('days', '>', 0)
                    ->where('days', '<', 8)
                    ->count();
                if ($count > 0) {
                    echo "YFL==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
                    foreach ($mtype_arr as $mtype) {
                        $mtypeex = explode("_", $mtype);
                        $mtypelog = ucfirst($mtypeex[1]);
                        foreach (model("app\common\model\Currency{$mtypelog}Log")
                                     ->where('service_ids', $os->order_no)
                                     ->where('type', 'subsidy')
                                     ->where('status', '0')
//                             ->where('days', '>', 0)
                                     ->where('days', '<', 8)
                                     ->order('id', 'asc')
                                     ->select() as $cl) {
                            $user = model('app\common\model\User')->where('id', $cl['user_id'])->field("$mtype,vip_status")->find();
                            $cl_arr = array();
                            $cl_arr['days'] = 8;
                            if ($cl_arr['days'] >= 8) {
                                if ($user['vip_status'] == '0') {
                                    $cl_arr['status'] = '2';
                                } else {
                                    $cl['money'] = floor($cl['money'] * 100) / 100;
                                    $cl_arr['status'] = '1';
                                    $cl_arr['before'] = $user[$mtype];
                                    $cl_arr['after'] = $user[$mtype] + $cl['money'];
                                    $cl_arr['updatetime'] = strtotime('now');
                                    if ($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
                                    if ($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
                                }
                            }
                            model("app\common\model\Currency{$mtypelog}Log")
                                ->where('id', $cl['id'])
                                ->update($cl_arr);
                        }
                    }
                }
            }
        }else{
            foreach (model('app\api\model\wanlshop\Order')
                         ->where('status', 'normal')
                         ->where('deletetime', null)
                         ->where('order_no', 'in', ['2853879483239866', '2896146943241357'])
                         ->where('state', '>', 1)
                         ->where('state', '<', 7)
                         ->where('days', '<', 8)
                         ->where('statusb', '1')
                         ->order('id asc')->select() as $os) {
                echo "YFL==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
                model('app\api\model\wanlshop\Order')->where('id', $os['id'])->setInc('days', 1);
                foreach ($mtype_arr as $mtype) {
                    $mtypeex = explode("_", $mtype);
                    $mtypelog = ucfirst($mtypeex[1]);
                    foreach (model("app\common\model\Currency{$mtypelog}Log")
                                 ->where('service_ids', $os->order_no)
                                 ->where('type', 'subsidy')
                                 ->where('status', '0')
//                             ->where('days', '>', 0)
                                 ->where('days', '<', 8)
                                 ->order('id', 'asc')
                                 ->select() as $cl) {
                        $user = model('app\common\model\User')->where('id', $cl['user_id'])->field("$mtype,vip_status")->find();
                        $cl_arr = array();
                        $cl_arr['days'] = $cl['days'] + 1;
                        if ($cl_arr['days'] >= 8) {
                            if ($user['vip_status'] == '0') {
                                $cl_arr['status'] = '2';
                            } else {
                                $cl['money'] = floor($cl['money'] * 100) / 100;
                                $cl_arr['status'] = '1';
                                $cl_arr['before'] = $user[$mtype];
                                $cl_arr['after'] = $user[$mtype] + $cl['money'];
                                $cl_arr['updatetime'] = strtotime('now');
                                if ($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
                                if ($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
                            }
                        }
                        model("app\common\model\Currency{$mtypelog}Log")
                            ->where('id', $cl['id'])
                            ->update($cl_arr);
                    }
                }
                echo "==ID:$os->id,结束\n";
            }
        }
    }

    public function OrderDailyCount(){
//        $mtype_arr = array(
//            0 => 'currency_nfr',
//            1 => 'currency_xnb',
//            2 => 'currency_cny',
//            3 => 'currency_rmb',
//            4 => 'currency_ns',
//            5 => 'currency_ans', // 爱多米诺-永福莱豆
//            6 => 'currency_gfz', // 共富值
//        );
        $mtype_arr = array(
            0 => 'currency_ns',
            1 => 'currency_rmb',
            2 => 'currency_gp',
            3 => 'currency_nsd'
        );
        foreach(model('app\api\model\wanlshop\OrderSync')
//                    ->where('id', 6587)
                    ->where('status', '<>', 0)
                    ->where('status', '<>', 3)
                    ->where('days', '<', 8)
                    ->order('id', 'asc')
                    ->select() as $os){
            echo "==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            $days = (time() - $os->createtime) / 86400; // 计算天数

            echo "距离{".date('Ymd His', $os->createtime)."}已有：" . round($days) . "天";
            if($days > 0) {
                model('app\api\model\wanlshop\OrderSync')->where('id', $os['id'])->update(['days' => $days]);
                foreach ($mtype_arr as $mtype) {
                    $mtypeex = explode("_", $mtype);
                    $mtypelog = ucfirst($mtypeex[1]);
                    foreach (model("app\common\model\Currency{$mtypelog}Log")
                                 ->where('service_ids', $os->order_no)
                                 ->where('type', 'subsidy')
                                 ->where('status', '0')
//                             ->where('days', '>', 0)
                                 ->where('days', '<', 8)
                                 ->order('id', 'asc')
                                 ->select() as $cl) {
                        $user = model('app\common\model\User')->where('id', $cl['user_id'])->field($mtype)->find();
                        $cl_arr = array();
                        $cl_arr['days'] = $cl['days'] + $days;
                        if ($cl_arr['days'] >= 8) {
                            $cl['money'] = floor($cl['money'] * 100) / 100;
                            $cl_arr['status'] = '1';
                            $cl_arr['before'] = $user[$mtype];
                            $cl_arr['after'] = $user[$mtype] + $cl['money'];
                            $cl_arr['updatetime'] = strtotime('now');
                            if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
                            if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
                        }
                        model("app\common\model\Currency{$mtypelog}Log")
                            ->where('id', $cl['id'])
                            ->update($cl_arr);
                    }
                }
            }
            echo "==ID:$os->id,结束\n";
        }
        exit;
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_gp',
            6 => 'currency_nsd',
            7 => 'currency_ans', // 爱多米诺-永福莱豆
            8 => 'currency_gfz', // 共富值
        );
        foreach(model('app\api\model\wanlshop\OrderSync')
                    ->where('order_no', 'like', '%HT%')
                    ->where('days', '<', 8)
                    ->select() as $os){
            echo "==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            model('app\api\model\wanlshop\OrderSync')->where('id', $os['id'])->setInc('days', 1);
            foreach($mtype_arr as $mtype){
                $mtypeex = explode("_", $mtype);
                $mtypelog = ucfirst($mtypeex[1]);
                foreach (model("app\common\model\Currency{$mtypelog}Log")
                             ->where('service_ids', $os->order_no)
                             ->where('type', 'subsidy')
                             ->where('status','0')
//                             ->where('days', '>', 0)
                             ->where('days', '<', 8)
                             ->order('id', 'desc')
                             ->select() as $cl){
                    $user = model('app\common\model\User')->where('id', $cl['user_id'])->field($mtype)->find();
                    $cl_arr = array();
                    $cl_arr['days'] = $cl['days'] + 1;
                    if($cl_arr['days'] >= 8) {
                        $cl_arr['status'] = '1';
                        $cl_arr['before'] = $user[$mtype];
                        $cl_arr['after'] = $user[$mtype] + $cl['money'];
                        $cl_arr['updatetime'] = strtotime('now');
                        if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
                        if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mtype, abs($cl['money']));
                    }
                    model("app\common\model\Currency{$mtypelog}Log")
                        ->where('id', $cl['id'])
                        ->update($cl_arr);
                }
            }
            echo "==ID:$os->id,结束\n";
        }
    }

    public function calcCancelOrder($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();

        Log::info("cancelOrder: oid=$order_id");
        //取消订单可能退回的券-积分等
        $mtype_arr = array(
            'currency_rmb',
            'currency_flq',
            'currency_cny',
            'currency_pu',
        );
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            $cl = model("app\common\model\Currency{$mtypelog}Log")
                ->where('service_ids', $order['order_no'])
                ->where('type', 'pay')
                ->find();
            if($cl){
                // 归还提货券,福利券等
                $money = abs($cl['money']);
                Log::info("cancelOrder: oid=$order_id,mtype=$mtype, money=$money");
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($money), $cl['user_id'], '取消支付归还', 'refund', $order['order_no'], $mtype, '1');
            }
        }
    }

    public function calcOrderRefund($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_gp',
            6 => 'currency_nsd',
            7 => 'currency_ans',
            8 => 'currency_flq',
            9 => 'currency_pu',
        );

        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
            }
            //支付券退回
            $cl = model("app\common\model\Currency{$mtypelog}Log")
                ->where('service_ids', $order['order_no'])
                ->where('type', 'pay')
                ->find();
            if($cl){
                // 归还提货券,福利券等
                $money = abs($cl['money']);
                Log::info("orderRefund: oid=$order_id,mtype=$mtype, money=$money");
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($money), $cl['user_id'], '退款后归还', 'refund', $order['order_no'], $mtype, '1');
            }
        }
    }

    public function handBonusRefund(){
//        return true;
        foreach (model("app\common\model\CurrencyNsLog")
                     ->where('calc', '0')
                     ->where('status', '1')
                     ->where('memo', '管理费')
                     ->where('type', 'subsidy')
                     ->where('id', '<=', 26106)
                     ->order('id', 'asc')
                     ->select() as $cl) {
            $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
            echo "ID:".$cl['id'].":".$user['id']."===>".$user['username']."===>".$user['mobile']."===>".$user[$this->currency]."===>".$cl['money'];
            $cl['money'] = floor($cl['money'] * 100) / 100;
//                if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($this->currency, $cl['money']);
            if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($this->currency, abs($cl['money']));
            model("app\common\model\CurrencyNsLog")
                ->where('id', $cl['id'])
                ->update(['calc' => '1']);
            $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
            echo "===>".$user[$this->currency];
            echo "\n";
//            exit;
        }
        exit;
//        31278=>1 -未加
//        $mType = $this->currency;
//        $idB = 23485;
//        $idE = 23503;
//        $order_no = 'FH2666050203232207';
//        31278=>2 -未加
//        $mType = $this->currency;
//        $idB = 23765;
//        $idE = 23783;
//        $order_no = 'FH2672470713233427';
//        31278=>3
//        $mType = $this->currency;
//        $idB = 24511;
//        $idE = 24529;
//        $order_no = 'FH2673403453232799';
//        31278=>4
//        $mType = $this->currency;
//        $idB = 26109;
//        $idE = 26127;
//        $order_no = 'FH2689119923232798';
//        31278=>5
//        $mType = $this->currency;
//        $idB = 26277;
//        $idE = 26295;
//        $order_no = 'FH2689186693232798';
//        31278=>6
//        $mType = $this->currency;
//        $idB = 30024;
//        $idE = 30042;
//        $order_no = 'FH2722368153231620';



//        31262=>1 -未加
//        $mType = $this->currency;
//        $idB = 23503;
//        $idE = 23521;
//        $order_no = 'FH2666050203232207';
//        31262=>2 -未加
//        $mType = $this->currency;
//        $idB = 23783;
//        $idE = 23801;
//        $order_no = 'FH2672470713233427';
//        31262=>3
//        $mType = $this->currency;
//        $idB = 24529;
//        $idE = 24547;
//        $order_no = 'FH2673403453232799';
//        31262=>4
//        $mType = $this->currency;
//        $idB = 26127;
//        $idE = 26145;
//        $order_no = 'FH2689119923232798';
//        31262=>5
//        $mType = $this->currency;
//        $idB = 26295;
//        $idE = 26313;
//        $order_no = 'FH2689186693232798';
//        31262=>6
//        $mType = $this->currency;
//        $idB = 30042;
//        $idE = 30060;
//        $order_no = 'FH2722368153231620';
//        31262=>7
//        $mType = $this->currency;
//        $idB = 31533;
//        $idE = 31551;
//        $order_no = 'FH2733456553232309';
//        31262=>8
        $mType = $this->currency;
        $idB = 33086;
        $idE = 33104;
        $order_no = 'FH2742898553232827';

        $mTypeEx = explode("_", $mType);
        $mTypeLog = ucfirst($mTypeEx[1]);
        foreach (model("app\common\model\Currency{$mTypeLog}Log")
                     ->where('service_ids', $order_no)
                     ->where('id', '>=', $idB)
                     ->where('id', '<', $idE)
                     ->where('type', 'subsidy')
                     ->order('id', 'desc')
                     ->select() as $cl){
            echo "ID".$cl['id'].":";
            if($cl['status'] == '0' || $cl['status'] == '1') {
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
                echo $user['id']."===>".$user['username']."===>".$user['mobile']."===>".$user[$mType]."===>".$cl['money'];
                if ($cl['status'] == '1') {
                    $cl['money'] = floor($cl['money'] * 100) / 100;
                    if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mType, $cl['money']);
                    if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mType, abs($cl['money']));
                }
                model("app\common\model\Currency{$mTypeLog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
                echo "===>".$user[$mType];
            }
            echo "END\n";
        }
    }

    public function autoBonusCount($ids=[],$act=true){
        if(empty($ids)) {
            $nsLog = model("app\common\model\CurrencyNsLog")
                ->where('count', $act ? '0' : '1')
                ->order('id', 'asc')
                ->find();
        }else{
            $nsLog = model("app\common\model\CurrencyNsLog")
                ->where('count', $act ? '0' : '1')
                ->where('id', 'in', $ids)
                ->order('id', 'asc')
                ->find();
        }
        $loop = 1;
        while($nsLog){
            $bonusKey = '';
            if($nsLog->memo == '绩效奖' || $nsLog->memo == '绩效奖D'){
                $bonusKey = 'bonus_jx';
            }else if($nsLog->memo == '管理奖'){
                $bonusKey = 'bonus_gl';
            }else if($nsLog->memo == '推荐奖'){
                $bonusKey = 'bonus_iv';
            }else if($nsLog->memo == '复购奖'){
                $bonusKey = 'bonus_ivr';
            }else if($nsLog->memo == '总监奖励'){
                $bonusKey = 'bonus_zj';
            }else if($nsLog->memo == '达人补贴'){
                $bonusKey = 'bonus_dr';
            }
            if($bonusKey != ''){
                echo "LOOP:".$loop."ID:".$nsLog->id."Uid:".$nsLog->user_id."Memo:".$nsLog->memo."Money:".$nsLog->money."\n";
                $counttime = strtotime(date('Y-m-d',$nsLog->createtime));
                $userBonusTotal = model('app\common\model\UserBonusTotal')->where('user_id', $nsLog->user_id)->find();
                if($act) $method = 'setInc';
                else $method = 'setDec';
                if($userBonusTotal){
                    model('app\common\model\UserBonusTotal')->where('id', $userBonusTotal->id)->$method($bonusKey, $nsLog->money);
                    model('app\common\model\UserBonusTotal')->where('id', $userBonusTotal->id)->$method('total', $nsLog->money);
                }else{
                    if($act) {
                        $userBonusArray=array();
                        $userBonusArray['user_id'] = $nsLog->user_id;
                        $userBonusArray['saas_id'] = $nsLog->saas_id;
                        $userBonusArray[$bonusKey] = $nsLog->money;
                        $userBonusArray['total'] = $nsLog->money;
                        $userBonusArray['createtime'] = time();
                        $userBonusArray['updatetime'] = time();
                        model('app\common\model\UserBonusTotal')->create($userBonusArray);
                    }
                }
                $userBonusDaily = model('app\common\model\UserBonusDaily')->where('counttime', $counttime)->where('saas_id', $nsLog->saas_id)->find();
                if($userBonusDaily){
                    model('app\common\model\UserBonusDaily')->where('id', $userBonusDaily->id)->$method($bonusKey, $nsLog->money);
                    model('app\common\model\UserBonusDaily')->where('id', $userBonusDaily->id)->$method('total', $nsLog->money);
                }else{
                    if($act) {
                        $userBonusArray=array();
                        $userBonusArray['saas_id'] = $nsLog->saas_id;
                        $userBonusArray[$bonusKey] = $nsLog->money;
                        $userBonusArray['total'] = $nsLog->money;
                        $userBonusArray['counttime'] = $counttime;
                        $userBonusArray['createtime'] = time();
                        $userBonusArray['updatetime'] = time();
                        model('app\common\model\UserBonusDaily')->create($userBonusArray);
                    }
                }
                $userBonusLogs = model('app\common\model\UserBonusLogs')->where('user_id', $nsLog->user_id)->where('counttime', $counttime)->find();
                if($userBonusLogs){
                    model('app\common\model\UserBonusLogs')->where('id', $userBonusLogs->id)->$method($bonusKey, $nsLog->money);
                    model('app\common\model\UserBonusLogs')->where('id', $userBonusLogs->id)->$method('total', $nsLog->money);
                }else{
                    if($act) {
                        $userBonusArray=array();
                        $userBonusArray['user_id'] = $nsLog->user_id;
                        $userBonusArray['saas_id'] = $nsLog->saas_id;
                        $userBonusArray[$bonusKey] = $nsLog->money;
                        $userBonusArray['total'] = $nsLog->money;
                        $userBonusArray['counttime'] = $counttime;
                        $userBonusArray['createtime'] = time();
                        $userBonusArray['updatetime'] = time();
                        model('app\common\model\UserBonusLogs')->create($userBonusArray);
                    }
                }
                $loop++;
//                if($loop>10) break;
            }
            model("app\common\model\CurrencyNsLog")->where('id', $nsLog->id)->update(['count' => $act?'1':'2']);
            if(empty($ids)) {
                $nsLog = model("app\common\model\CurrencyNsLog")
                    ->where('count', $act ? '0' : '1')
                    ->order('id', 'asc')
                    ->find();
            }else{
                $nsLog = model("app\common\model\CurrencyNsLog")
                    ->where('count', $act ? '0' : '1')
                    ->where('id', 'in', $ids)
                    ->order('id', 'asc')
                    ->find();
            }
        }
    }

    public function CalcOrderNinestar($order_id, $ns_amt, $amount){
        //首月不考核
        $ns_am = $ns_amt * $this->ns_rate;
        if ($ns_am > 0){
            $order = model('app\api\model\wanlshop\Order')->where('id',$order_id)->find();
            if ($order){
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星订单C', 'subsidy', $order['order_no'], $this->currency, '0');
                if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星订单C', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                $loops = 0;
                $nsactivate = false;
                $userr = model('app\common\model\User')->where('id', $order['user_id'])->find();
                if ($userr['saas_id'] > 0){
                    $monthly_ns = $userr['monthly_nsn'] + 1;
                    while($monthly_ns > 9) {
                        $monthly_ns = $monthly_ns - 10;
                    }
                    $nsdo = true;
                    $nsiv = false;
                    while($userr){
                        if ($loops == 0){
                            model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_nsn', 1);
                            $ns_ac = array();
                            if($userr['ns_status'] == '0') {
                                $ns_ac['ns_status'] = 1;
                                $ns_ac['monthly_ns'] = 9;
                                $nsactivate = true;
                            }
                            $ns_ac['ns_pay'] = $userr['ns_pay'] + $amount;
                            if($ns_ac['ns_pay'] >= 500){
                                $fhq = floor($ns_ac['ns_pay']/500);
                                $ns_ac['ns_pay'] = $ns_ac['ns_pay'] - $fhq * 500;
                                $bs = 1;
                                if($userr['vip_level'] == 2) {
                                    $bs = 2;
                                    if($userr['invite_nums'] >= 99) $bs = 5;//超级会员
                                }
                                if($bs < 5) {
                                    if ($userr['vip_level'] > 2) $bs = 5;
                                }
                                if($bs < 5){
                                    if ($userr['monthly_ns'] >= 9 && (($userr['ns_status'] == '1' && $userr['invite_numsns'] >= 9) || ($userr['vip_level'] >= 2 && $userr['invite_nums'] >= 3))) $bs = 3;
                                }
                                $ns_ac['ns_fhq'] = $userr['ns_fhq'] + $fhq*$bs;
                            }
                            model('app\common\model\User')->where('id', $userr['id'])->update($ns_ac);
                            if($userr['monthly_nsn'] > 9 && $monthly_ns == 0){
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], $this->currency, '0');
                                if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                            }
                        }else if ($loops > 0){
                            if($nsactivate) {
                                if ($loops == 1) {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('invite_numsns', 1);
                                    $userr['invite_numsns'] += 1;
                                } else {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('team_numsns', 1);
                                }
                            }
                            if($nsiv){
                                if(($userr['vip_level'] > 2 && $userr['invite_nums'] >= 9) || ($userr['vip_level']==2 && $userr['invite_nums'] >= 9 && $userr['team_nums'] >= 99)) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, $userr['id'], '九星邀请', 'subsidy', $order['order_no'], $this->currency, '0');
                                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, $userr['id'], '九星邀请', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am * 0.5);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am * 0.5);
                                }
                                $nsdo = false;
                                $nsiv = false;
                            }
                            if ( $loops == $monthly_ns && $userr['monthly_ns'] >= 9 && (($userr['ns_status'] == '1' && $userr['invite_numsns'] >= 9) || ($userr['vip_level']>=2 && $userr['invite_nums'] >= 3)) ){
                                $ft = true;
                                if($userr['vip_level'] >= 3) $ft = false;
                                if($userr['team_nums'] >= 99) {
                                    $lastdata = model('addons\signin\model\Signin')->where('user_id', $userr['id'])->order('createtime', 'desc')->find();
                                    $successions = $lastdata && $lastdata['createtime'] > Date::unixtime('day', -1) ? $lastdata['successions'] : 0;
                                    if($successions >= 15) $ft = false;
                                }
                                if($ft) {
                                    if ($userr['monthly_ns_am'] < $this->ns_top_std) {
                                        if (($userr['monthly_ns_am'] + $ns_am) >= $this->ns_top_std)
                                            $ns_am = $this->ns_top_std - $userr['monthly_ns_am'];
                                    }
                                }
                                if($ns_am > 0) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, $userr['id'], '九星奖励', 'subsidy', $order['order_no'], $this->currency, '0');
                                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, $userr['id'], '九星奖励', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am);
                                    $nsdo = false;
                                    $nsiv = true;
                                }
                            }
                        }
                        $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                        $loops++;
                    }
                    if ($nsdo){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], $this->currency, '0');
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                        $nsiv = true;
                    }
                    if($nsiv){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星邀请C', 'subsidy', $order['order_no'], $this->currency, '0');
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星邀请C', 'subsidy', $order['order_no'], 'currency_nsd', '0');
                    }
                }
            }
            model('app\api\model\wanlshop\Pay')->where('order_id', $order_id)->update(['commission'=>$ns_amt]);
        }
        //
    }

    # manage up order_no = HT
    # am = 0 empty or backfill order_no
    # iv = 1 team number increased
    # lv this menber's level
    # calc 1 empty or backfill 1 new member 2 repurchase
    public function intoNet2($uid,$order_no='',$am=0,$iv=0,$lv=2,$calc=1,$pnet=0,$isOrderNet=false,$gdrPv=0){
//        判断是否已经入网
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user && $user['pid'] == 0 && $calc > 0){
            $useriv = $user;
            $findManager = true;
            while($findManager){
                $useriv = model('app\common\model\User')->where('id', $useriv['inviter_id'])->find();
                if($useriv && $useriv['vip_level'] > 1 && $useriv['vip_status'] > '0') {
                    $findManager = false;
                    $user['manager_id'] = $useriv['id'];
                    model('app\common\model\User')->where('id', $user['id'])->update(['manager_id' => $useriv['id']]);
                }
            }
            if ($useriv && !$findManager) {
                $in2 = true;
                $loop = 1;
                $userP = $useriv;
                if($user['ypid'] > 0) {
                    $userP = model('app\common\model\User')->where('id', $user['ypid'])->find();
                    $userUp = $userP;
                    while($userUp){
                        if($useriv['id'] == $userUp['pid']){
                            model('app\common\model\User')->where('id', $user['id'])->update(['pnet_iv' => $userUp['pnet']]);
                            $userUp = false;
                        }
                        $userUp = model('app\common\model\User')->where('id', $userUp['pid'])->find();
                    }
                }
                if ($user['plan'] == 'A' || $user['plan'] == 'C') {
//                    排网 1左 2右 左滑落
//                    左右 小大
                    $toPN = 1;
                    if($isOrderNet && $pnet > 0) $toPN = $pnet;
                }else{
//                    右左左左
                    $toPN = 2;
                }
//                    down
                while($userP && $in2){
                    if($loop == 1){
                        if ($user['plan'] == 'A' || $user['plan'] == 'C') {
                            $ivnums = model('app\common\model\User')->where('manager_id', $userP['id'])->where('vip_level', '>=', 2)->count();
                            if($ivnums > 0) {
                                if($isOrderNet){
                                    if ($pnet > 0) $toPN = $pnet;
                                    else $toPN = 2;
                                }else{
                                    if ($pnet > 0) $toPN = $pnet;
                                    else if ($userP['pam1n'] > $userP['pam2n']) $toPN = 2;
                                }
                                if($userP['plan'] == 'C' && $userP['vip_level'] > 5){
//                                5=>3;6=>4;7=>5;8=>6;
                                    $pnetMax = $userP['vip_level'] - 3;
                                    if($userP['pnet_team'] > 0) $toPN = $userP['pnet_team'];
                                    if($toPN > $pnetMax) $toPN = $pnetMax;
                                    if($toPN > 3){
                                        for($i=3;$i<$toPN;$i++){
                                            $checkP = model('app\common\model\User')->where('pid', $userP['id'])->where('pnet', $i)->find();
                                            if(!$checkP) {
                                                $toPN = $i;
                                                break;
                                            }
                                        }
                                    }
                                }else{
                                    if($toPN > 2) $toPN = 2;
                                }
                            }
                            if($user['ypid'] == 0) {
                                model('app\common\model\User')->where('id', $user['id'])->update(['pnet_iv' => $toPN]);
                            }
                        }
                        //推荐奖励
                        if($am >= 100){
                            if ($user['plan'] == 'A'){
                                $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$useriv['vip_level']];
                                $this->bonusAct($useriv, $bonus, $order_no, '推荐奖');
                            }else if ($user['plan'] == 'B'){
                                $amss = 100;
                                $ams = 100;
                                $amb = 500;
                                if ($am >= 500) {
                                    $amss = 500;
                                    $ams = 500;
                                    $amb = 1000;
                                }
                                if ($am >= 1000) {
                                    $amss = 1000;
                                    $ams = 1000;
                                    $amb = 10000000;
                                }
                                $count = model('app\common\model\User')
                                    ->where('manager_id', $useriv['id'])
                                    ->where('self_amd0', '>=', $ams)
                                    ->where('self_amd0', '<', $amb)
                                    ->count();
                                $ys = fmod($count, 3);
//                                $amss = $am;
//                                if($useriv['self_amd0'] < $this->lvinfo[$useriv['vip_level']]['cost']/10) $useriv['self_amd0'] = $this->lvinfo[$useriv['vip_level']]['cost']/10;
//                                if ($amss > $useriv['self_amd0']) $amss = $useriv['self_amd0'];
                                $bonus = $amss * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$ys];
                                $findNsLogOne = model("app\common\model\CurrencyNsLog")
                                    ->where('service_ids', $order_no)
                                    ->where('type', 'pay')
                                    ->find();
                                if($findNsLogOne) {
                                    if($ys == 2){
                                        model("app\common\model\CurrencyNsLog")
                                            ->where('id', $findNsLogOne->id)
                                            ->update(['type' => 'sys', 'memo' => 'K3'.$findNsLogOne->memo]);
                                    }else{
//                                        model("app\common\model\CurrencyNsLog")
//                                            ->where('id', $findNsLogOne->id)
//                                            ->update(['type' => 'sys', 'memo' => 'KNA'.$findNsLogOne->memo]);
                                    }
                                }
                                $this->bonusAct($useriv, $bonus, $order_no, '推荐奖');
                                if ($ys == 2) $am = 0;
                            }else if ($user['plan'] == 'C'){
                                $count = model('app\common\model\User')
                                    ->where('manager_id', $useriv['id'])
                                    ->where('self_amd0', $am+$gdrPv)
                                    ->count();
                                $ys = fmod($count, 3);
                                $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$useriv['vip_level']];
                                if ($ys == 2) {
                                    $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * 1;
                                    $am = 0;
                                }
                                $findNsLogOne = model("app\common\model\CurrencyNsLog")
                                    ->where('service_ids', $order_no)
                                    ->where('type', 'pay')
                                    ->find();
                                if($findNsLogOne) {
                                    if($ys == 2){
                                        model("app\common\model\CurrencyNsLog")
                                            ->where('id', $findNsLogOne->id)
                                            ->update(['type' => 'sys', 'memo' => 'K3'.$findNsLogOne->memo]);
                                    }else{
//                                        model("app\common\model\CurrencyNsLog")
//                                            ->where('id', $findNsLogOne->id)
//                                            ->update(['type' => 'sys', 'memo' => 'KNA'.$findNsLogOne->memo]);
                                    }
                                }
                                $this->bonusAct($useriv, $bonus, $order_no, '推荐奖');
                            }
                        }
                    }else{
                        if ($user['plan'] == 'A') {
                            if($isOrderNet){
                                $toPN = 1;
                            }else{
                                if ($userP['pam1n'] < $userP['pam2n']) $toPN = 2;
                                else $toPN = 1;
                            }
                        }else{
                            $toPN = 1;
                        }
                    }
                    $checkP = model('app\common\model\User')->where('pid', $userP['id'])->where('pnet', $toPN)->find();
                    if(!$checkP){
                        $in2 = false;
                        model('app\common\model\User')->where('id', $user['id'])->update(['pid'=>$userP['id'], 'pnet'=>$toPN]);
                    }else{
                        $userP = model('app\common\model\User')->where('pid', $userP['id'])->where('pnet', $toPN)->find();
                    }
                    $loop++;
                }
//                    up
                if(($am > 0 || $iv > 0) && !$in2) {
                    return $this->Net2DP($uid,$order_no,$am,$iv,$lv,$calc);
                }
            } else {
                return '推荐人不存在';
            }
        }else if($user && $user['pid'] > 0 && $calc == 2) {
            if($am > 0) {
                $userP = model('app\common\model\User')->where('id', $user['manager_id'])->find();
                if ($user['plan'] == 'A') {
                    $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$userP['vip_level']];
                    $this->bonusAct($userP, $bonus, $order_no, '推荐奖');
                } else if ($user['plan'] == 'C') {
                    $count = model('app\common\model\User')
                        ->where('manager_id', $userP['id'])
                        ->where('self_amd0', $am)
                        ->count();
                    $ys = fmod($count, 3);
                    $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$userP['vip_level']];
                    if ($ys == 2) {
                        $bonus = $am * $this->planrate[$user['plan']]['ratePv'] * 1;
                        $am = 0;
                    }
                    $this->bonusAct($userP, $bonus, $order_no, '推荐奖');
                }
            }
            if($am > 0 || $iv > 0) {
                return $this->Net2DP($uid,$order_no,$am,$iv,$lv,$calc);
            }
        } else if($user && $user['pid'] > 0 && $calc == 3 && $am > 0) {
            $amss = $am * 2;
            $userm = model('app\common\model\User')->where('id', $user['manager_id'])->find();
            $bs = $this->planrate[$user['plan']]['rateBs'];
            if($userm['self_amd0'] > 0 && ($userm['plan'] == 'A' || $userm['plan'] == 'C')){
                if ($userm['self_amd0'] % 1300 === 0) $bs = $this->planrate[$user['plan']]['rateBs2'];
                if ($userm['self_amd0'] % 1133 === 0) $bs = $this->planrate[$user['plan']]['rateBs2'];
            }
            $lvfd = $this->lvinfo[$userm['vip_level']]['cost']/10*$bs;
            if($userm['self_amd0'] < $lvfd) $userm['self_amd0'] = $lvfd;
            if ($amss > $userm['self_amd0']) $amss = $userm['self_amd0'];
            if ($user['plan'] == 'A' || $userm['plan'] == 'C') {
                $bonus = $amss * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][$userm['vip_level']];
            } else $bonus = $amss * $this->planrate[$user['plan']]['ratePv'] * $this->planrate[$user['plan']]['tj'][3];
            $this->bonusAct($userm, $bonus, $order_no, '复购奖');
            if($am > 0 || $iv > 0) {
                return $this->Net2DP($uid,$order_no,$am,$iv,$lv,$calc);
//                return $this->Net2DP($uid,$order_no,$user['plan'] == 'A'?$am*2:$am,$iv,$lv,$calc);
            }
        } else return '会员不存在';

        return false;
    }

    public function Net2DP($uid,$order_no,$am=0,$iv=0,$lvmax,$calc=1){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user) {
            $doDP = true;
            $nowNet = $user['pnet'];
            $userP = $user;
            $loop = 0;
            $loopDp = 1;
            $amDpl = 0;
            $usersDp = array();
            while ($doDP && $userP && $user['plan'] == $userP['plan']) {
                if($loop > 0) {
                    if($userP['plan'] == 'C' && $nowNet > 2){
//                        $doDP = false;
                        $bonus = $am * $this->planrate[$user['plan']]['zy'][$userP['vip_level']];
                        if($bonus > 0) $this->bonusAct($userP, $bonus, $order_no, '卓越奖');
                    }else{
                        $isDp = false;
                        $otherNet = $nowNet == 1 ? 2:1;
                        $update_array = array();
                        if ($am > 0) {
                            $update_array['pam' . $nowNet] = $userP['pam' . $nowNet] + $am;
                            $update_array['pam' . $nowNet . 'd'] = $userP['pam' . $nowNet . 'd'] + $am;
                            $update_array['pam' . $nowNet . 'm'] = $userP['pam' . $nowNet . 'm'] + $am;
                            if($userP['pam' . $nowNet . 'n'] >= $userP['pam' . $otherNet . 'n']) {
                                $update_array['pam' . $nowNet . 'n'] = $userP['pam' . $nowNet . 'n'] + $am;
                                $userP['pam' . $nowNet . 'n'] = $update_array['pam' . $nowNet . 'n'];
                            }else{
                                $isDp = true;
                            }
                        }
                        if ($iv > 0) {
                            $update_array['pnums' . $nowNet] = $userP['pnums' . $nowNet] + $iv;
                        }
                        if($calc > 0 && $userP['vip_level'] > 1 && $am > 0) {
                            $amSy = 0;
                            $amDp2 = 0;
                            if($isDp && $userP['pam' . $otherNet . 'n'] > 0) {
                                $amountDp = $am;
                                if ($amountDp > $userP['pam'.$otherNet.'n']) {
                                    $amountDp = $userP['pam' . $otherNet . 'n'];
                                    $amSy = $am - $amountDp;
                                }
                                $update_array['pam'.$otherNet.'n'] = $userP['pam'.$otherNet.'n'] - $amountDp;
                                if($loopDp < $this->planrate[$user['plan']]['dpMax'] && $amSy > 0) $update_array['pam'.$nowNet.'n'] = $userP['pam'.$nowNet.'n'] + $amSy;
                                $amDp = $amountDp * $this->planrate[$user['plan']]['dp'][$userP['vip_level']] * $this->planrate[$user['plan']]['ratePv'];
                                if ($loopDp >= $this->planrate[$user['plan']]['dpMax']) {
                                    $usersDp[$userP['id']]['am'] = $amountDp;
                                    $usersDp[$userP['id']]['dp'] = $amDp;
                                    $usersDp[$userP['id']]['pnet'] = $otherNet;
                                    if($loopDp == $this->planrate[$user['plan']]['dpMax']) $amDpl = $amDp;
                                    $amDp = 0;
                                }
                                if ($user['plan'] == 'B') $amDp2 = $amDp = $amDp / 2;
                                if ($userP['monthly_fg'] == 0 && $this->fgDP) $amDp = 0;
                                if (($amDp + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
                                    $amDp = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
                                }
                                if ($amDp > 0) {
                                    $update_array['weekfd'] = $userP['weekfd'] + $amDp;
                                    $bonus = $amDp;
                                    $this->bonusAct($userP, $bonus, $order_no, '绩效奖');
//                                if ($lv > $lvmax) $lvmax = $lv;
//                                管理
                                    $this->Net2Gl($userP['id'], $amDp, $order_no);
//                                辅导
                                    if ($user['plan'] == 'B') $this->Net2Fd($userP['id'], $amDp, $order_no);
                                }
                                if ($amDp2 > 0) {
                                    $userm = model('app\common\model\User')->where('id', $userP['manager_id'])->find();
                                    $bonus = $amDp2;
                                    $this->bonusAct($userm, $bonus, $order_no, '合赢奖');
                                }
                                $loopDp++;
                            }
                        }
                        //levelm base on inviter_id
//                    if($lvmax > $userP['vip_levelm']) $update_array['vip_levelm'] = $lvmax;
//                    else if($lvmax < $userP['vip_levelm']) $lvmax = $userP['vip_levelm'];
//                    if($lvmax < $userP['vip_level']) $lvmax = $userP['vip_level'];
                        if (!empty($update_array) && $userP['vip_status'] >= '0')
                            model('app\common\model\User')->where('id', $userP['id'])->update($update_array);
                    }
                }
                $nowNet = $userP['pnet'];
                $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                $loop++;
            }
//            4碰后调整
            if($amDpl > 0) {
                $count = count($usersDp);
                if($count > 0){
                    foreach($usersDp as $uid=>$vdp){
                        $am = $vdp['am'];
                        $amDp = $vdp['dp'];
                        $pNet = $vdp['pnet'];
                        $userP = model('app\common\model\User')->where('id', $uid)->find();
                        if($amDp > 0 && $userP) {
                            $amDpv = $amDp / $count;
                            $amV = $am * (1 - 1 / $count);
                            if ($userP['monthly_fg'] == 0 && $this->fgDP) $amDpv = 0;
                            if (($amDpv + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
                                $amDpv = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
                            }
                            if ($amDpv > 0) {
                                model('app\common\model\User')->where('id', $uid)
                                    ->update(['weekfd' => Db::raw('weekfd+'.$amDpv), 'pam'.$pNet.'n' => Db::raw('pam'.$pNet.'n+'.$amV)]);
                                $bonus = $amDpv;
                                $this->bonusAct($userP, $bonus, $order_no, '绩效奖D');
                                if ($user['plan'] == 'B') $this->Net2Fd($userP['id'], $amDpv, $order_no);
                            }
                        }
                    }
                }
            }
        }
        return true;
    }
    public function Net2DPOld($uid,$order_no,$am=0,$iv=0,$lvmax,$calc=1){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user) {
            $doDP = true;
            $nowNet = $user['pnet'];
            $userP = $user;
            $loop = 0;
            $loopDp = 1;
            $amDpl = 0;
            $usersDp = array();
            while ($doDP && $userP && $user['plan'] == $userP['plan']) {
                if($loop > 0) {
                    if($userP['plan'] == 'C' && $nowNet > 2){
//                        $doDP = false;
                        $bonus = $am * $this->planrate[$user['plan']]['zy'][$userP['vip_level']];
                        if($bonus > 0) $this->bonusAct($userP, $bonus, $order_no, '卓越奖');
                    }else{
                        $update_array = array();
                        if ($am > 0) {
                            $update_array['pam' . $nowNet] = $userP['pam' . $nowNet] + $am;
                            $update_array['pam' . $nowNet . 'n'] = $userP['pam' . $nowNet . 'n'] + $am;
                            $update_array['pam' . $nowNet . 'd'] = $userP['pam' . $nowNet . 'd'] + $am;
                            $update_array['pam' . $nowNet . 'm'] = $userP['pam' . $nowNet . 'm'] + $am;
                            $userP['pam' . $nowNet . 'n'] = $update_array['pam' . $nowNet . 'n'];
                        }
                        if ($iv > 0) {
                            $update_array['pnums' . $nowNet] = $userP['pnums' . $nowNet] + $iv;
                        }
                        if($calc > 0 && $userP['vip_level'] > 1 && $am > 0) {
                            $amDp = 0;
                            $amDp2 = 0;
                            if($userP['pam1n'] > 0 && $userP['pam2n'] > 0) {
                                if ($loopDp <= $this->planrate[$user['plan']]['dpMax']) {
                                    $amountdp = $am;
                                    if ($amountdp > $userP['pam1n']) $amountdp = $userP['pam1n'];
                                    if ($amountdp > $userP['pam2n']) $amountdp = $userP['pam2n'];
//                                $amountdp = $userP['pam1n']<=$userP['pam2n']?$userP['pam1n']:$userP['pam2n'];
                                    if($loopDp < $this->planrate[$user['plan']]['dpMax']) {
                                        $update_array['pam1n'] = $userP['pam1n'] - $amountdp;
                                        $update_array['pam2n'] = $userP['pam2n'] - $amountdp;
                                    }
                                    $amDp = $amountdp * $this->planrate[$user['plan']]['dp'][$userP['vip_level']] * $this->planrate[$user['plan']]['ratePv'];
                                }
                                if ($loopDp == $this->planrate[$user['plan']]['dpMax']) {
                                    $amDpl = $amDp;
                                    $amDp = 0;
                                    $usersDp[] = $userP['id'];
                                }else if ($loopDp > $this->planrate[$user['plan']]['dpMax']) {
                                    $usersDp[] = $userP['id'];
                                }
                                if ($user['plan'] == 'B') $amDp2 = $amDp = $amDp / 2;
                                if ($userP['monthly_fg'] == 0 && $this->fgDP) $amDp = 0;
                                if (($amDp + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
                                    $amDp = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
                                }
                                if ($amDp > 0) {
                                    $update_array['weekfd'] = $userP['weekfd'] + $amDp;
                                    $bonus = $amDp;
                                    $this->bonusAct($userP, $bonus, $order_no, '绩效奖');
//                                if ($lv > $lvmax) $lvmax = $lv;
//                                管理
                                    $this->Net2Gl($userP['id'], $amDp, $order_no);
//                                辅导
                                    if ($user['plan'] == 'B') $this->Net2Fd($userP['id'], $amDp, $order_no);
                                }
                                if ($amDp2 > 0) {
                                    $userm = model('app\common\model\User')->where('id', $userP['manager_id'])->find();
                                    $bonus = $amDp2;
                                    $this->bonusAct($userm, $bonus, $order_no, '合赢奖');
                                }
                                $loopDp++;
                            }
                        }
                        //levelm base on inviter_id
//                    if($lvmax > $userP['vip_levelm']) $update_array['vip_levelm'] = $lvmax;
//                    else if($lvmax < $userP['vip_levelm']) $lvmax = $userP['vip_levelm'];
//                    if($lvmax < $userP['vip_level']) $lvmax = $userP['vip_level'];
                        if (!empty($update_array) && $userP['vip_status'] >= '0')
                            model('app\common\model\User')->where('id', $userP['id'])->update($update_array);
                    }
                }
                $nowNet = $userP['pnet'];
                $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                $loop++;
            }
//            4碰后调整
            if($amDpl > 0) {
                $count = count($usersDp);
                if($count > 0){
                    $amDpv = $amDpl / $count;
                    $amv = $am / $count;
                    foreach($usersDp as $uid){
                        $userP = model('app\common\model\User')->where('id', $uid)->find();
                        if ($userP['monthly_fg'] == 0 && $this->fgDP) $amDp = 0;

                        if (($amDpv + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
                            $amDpv = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
                        }
                        if ($amDpv > 0) {
                            $amDec = $amv;
                            if($amDec > $userP['pam1n']) $amDec = $userP['pam1n'];
                            if($amDec > $userP['pam2n']) $amDec = $userP['pam2n'];
                            model('app\common\model\User')
                                ->where('id', $uid)
                                ->update(['weekfd' => $userP['weekfd'] + $amDpv, 'pam1n' => $userP['pam1n'] - $amDec, 'pam2n' => $userP['pam2n'] - $amDec]);
                            $bonus = $amDpv * $amDec / $amv;
                            $this->bonusAct($userP, $bonus, $order_no, '绩效奖D');
                            if ($user['plan'] == 'B') $this->Net2Fd($userP['id'], $amDp, $order_no);
                        }
                    }
                }
            }
        }
        return true;
    }

    public function Net2DPOne($uid,$order_no,$am=0,$iv=0,$lvmax,$calc=1){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user) {
            $doDP = true;
            $nowNet = $user['pnet'];
            $userP = $user;
            if ($doDP && $userP && $user['plan'] == $userP['plan']) {
                if($userP['plan'] == 'C' && $nowNet > 2){
                    $bonus = $am * $this->planrate[$user['plan']]['zy'][$userP['vip_level']];
                    if($bonus > 0) $this->bonusAct($userP, $bonus, $order_no, '卓越奖');
                }else{
                    if($calc > 0 && $userP['vip_level'] > 1 && $am > 0) {
                        $update_array = array();
                        $amDp = $am * $this->planrate[$user['plan']]['dp'][$userP['vip_level']] * $this->planrate[$user['plan']]['ratePv'];
                        $update_array['weekfd'] = $userP['weekfd'] + $amDp;
                        $bonus = $amDp;
                        $this->bonusAct($userP, $bonus, $order_no, '绩效奖');
                        $this->Net2Gl($userP['id'], $amDp, $order_no);
                        if ($user['plan'] == 'B') $this->Net2Fd($userP['id'], $amDp, $order_no);
                        if (!empty($update_array) && $userP['vip_status'] >= '0')
                            model('app\common\model\User')->where('id', $userP['id'])->update($update_array);
                    }
                }
            }
        }
        return true;
    }


    public function Net2Modify($uid,$order_no,$am=0,$iv=0,$calc=1, $teamAct=0, $orderGL=false, $userR=false){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user && $teamAct > 0) {
            $nowNet = $user['pnet'];
            $userP = $user;
            $loop = 0;
            $loopDp = 1;
            while ($userP && $user['plan'] == $userP['plan']) {
                $userO = array(
                    'pam1' => $userP['pam1'],
                    'pam1n' => $userP['pam1n'],
                    'pam2' => $userP['pam2'],
                    'pam2n' => $userP['pam2n']
                );
                $idDP = false;
                if($loop > 0) {
                    $update_array = array();
                    if ($am != 0) {
                        $update_array['pam' . $nowNet] = $userP['pam' . $nowNet] - $am;
                        $update_array['pam' . $nowNet . 'n'] = $userP['pam' . $nowNet . 'n'] - $am;
                        $userP['pam' . $nowNet . 'n'] = $update_array['pam' . $nowNet . 'n'];
                    }
                    if ($iv != 0) {
                        $update_array['pnums' . $nowNet] = $userP['pnums' . $nowNet] - $iv;
                    }
                    if($calc > 0 && $userP['vip_level'] > 1 && $am > 0) {
                        $amDp = 0;
                        $usersDp = array();
//                        判断是否对碰
                        $chekorder = model("app\common\model\CurrencyNsLog")
                            ->where('user_id', $userP['id'])
                            ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                            ->where('service_ids', $order_no)
                            ->where('type', 'subsidy')
                            ->order('id', 'desc')
                            ->find();
                        if($chekorder) {
                            $idDP = true;
                            if ($loopDp <= $this->planrate[$user['plan']]['dpMax']) {
                                $amDp = $chekorder['money'];
                                $amountdp = $amDp / $this->planrate[$user['plan']]['ratePv'] / $this->planrate[$user['plan']]['dp'][$userP['vip_level']];
//                                $amountdp = 800;
                                if($uid == 43475) $amountdp = 4000;
                                if($uid == 40959) $amountdp = 150;
                                if(abs($am-$amountdp) < 10) $amountdp = $am;
                                $update_array['pam1n'] = $userP['pam1n'] + $amountdp;
                                $update_array['pam2n'] = $userP['pam2n'] + $amountdp;
                            }
//                            if (($amDp + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
//                                $amDp = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
//                            }

                            $loopDp++;
                        }
                    }
                    if (!empty($update_array)) {
                        if($teamAct == 1) {
                            echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                            echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                        }
                        if($teamAct == 2) {
                            if($idDP) {
                                echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                            }else{
                                if(array_key_exists('pam1n',$update_array)){
                                    if($update_array['pam1n'] < 0){
                                        echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                        echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                                    }
                                }
                                if(array_key_exists('pam2n',$update_array)){
                                    if($update_array['pam2n'] < 0){
                                        echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                        echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                                    }
                                }
                            }
                        }
//                        var_dump($update_array);
//                        if($userP['pam' . $nowNet . 'n'] < 0) echo $userP->id.":".$userP['pam' . $nowNet]."===>".($userP['pam' . $nowNet . 'n']+$am)."===>-".$am."===>".$update_array['pam' . $nowNet]."===>".$update_array['pam' . $nowNet . 'n']."\n";
                        if($teamAct == 3) model('app\common\model\User')->where('id', $userP['id'])->update($update_array);
                    }
                }
                $nowNet = $userP['pnet'];
                $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                $loop++;
            }
        }
        if($orderGL) {
            if($userR) {
                model('app\common\model\User')
                    ->where('id', $uid)
                    ->update(['vip_status' => '0', 'vip_level' => 1, 'self_amd0' => 0, 'manager_id' => 0, 'pid' => 0, 'pnet' => 0]);
            }
            model('app\api\model\wanlshop\OrderSync')
                ->where('order_no', $order_no)
                ->update(['status' => '3']);
            $mtype_arr = array(
                0 => 'currency_ns',
                1 => 'currency_rmb',
                2 => 'currency_gp',
                3 => 'currency_nsd'
            );
            foreach ($mtype_arr as $mtype) {
                $mtypeex = explode("_", $mtype);
                $mtypelog = ucfirst($mtypeex[1]);
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('service_ids', $order_no)
                    ->update(['status' => '2']);
            }
        }
        return true;
    }


    public function dpSupplement(){
        $sid = 977;
        $doDp = true;
        $max = 11;
        $loop = 1;
        foreach(model('app\api\model\wanlshop\Order')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('id', '>=', $sid)
                    ->where('id', '<>', 938)
                    ->where('id', '<', 978)
                    ->where('statusb', '1')
                    ->order('id asc')->select() as $os) {
            echo $loop."YFL==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            $clogB = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'pay')
                ->where('status', '1')
                ->order('id', 'asc')
                ->find();
            echo 'Amount:'.$clogB->money*-1;
            $clogIv = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['推荐奖','复购奖'])
                ->order('id', 'asc')
                ->find();
            echo 'IvAm:'.$clogIv->memo.$clogIv->money;
            $userIv = model('app\common\model\User')->where('id', $clogIv['user_id'])->find();
            $user = model('app\common\model\User')->where('id', $os['user_id'])->find();
            echo 'SelfAm:'.$user->self_amd0."\n";
            $am = $clogIv->money / $this->planrate[$userIv['plan']]['tj'][$userIv['vip_level']];
            if($clogIv->memo == '复购奖') echo 'PV:XXXX';
            else if ((($clogB->money * -1) / 1300 * 800) == $am && $am == $user->self_amd0) echo 'PV:'.$am;
            else echo 'PV:XXXX';
            if ($user->id == 38869) $am = 2000;
            if ($user->id == 39331) $am = 2000;
            if ($user->id == 43543) $am = 0;
            if ($user->id == 37876) $am = 0;
            echo "\n";
            if($am > 0) {
                $userXL = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 1)->find();
                $userXR = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 2)->find();
                if ($user) echo $user->self_amd0 . 'MY:' . $user->pam1 . "===>" . $user->pam1n . "R:" . $user->pam2 . "===>" . $user->pam2n . "\n";
                if ($userXL) echo $userXL->self_amd0 . 'Pam L:' . $userXL->pam1 . "===>" . $userXL->pam1n . "R:" . $userXL->pam2 . "===>" . $userXL->pam2n . "\n";
                if ($userXR) echo $userXR->self_amd0 . 'Pam R:' . $userXR->pam1 . "===>" . $userXR->pam1n . "R:" . $userXR->pam2 . "===>" . $userXR->pam2n . "\n";
                $nowNet = $user['pnet'];
                $userP = $user;
                $loopDp = 0;
                while ($userP) {
                    if ($loopDp > 0) {
                        if ($loopDp == 1) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pnums1'] . "  右:" . $userP['pnums2'] . "\n";
                            echo '=============>' . "\n";
                        }
                        if ($userP->monthly_fg == 0 && $userP->pam1 > 0 && $userP->pam2 > 0 && $userP['pam' . $nowNet . 'n'] < $am) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";

                            $clogDp = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $os->order_no)
                                ->where('type', 'subsidy')
                                ->where('user_id', $userP->id)
                                ->where('status', '0')
                                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                ->order('id', 'asc')
                                ->find();
                            if (!$clogDp && $doDp) {
                                if ($user->id == 43529 && $loopDp == 3) $am = $am/2;
                                $this->Net2DPOne($userP->id, $os->order_no, $am, 0, 0, 1);
                            }
                        }
                    }
                    $nowNet = $userP['pnet'];
                    $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                    $loopDp++;
                }
            }
            echo "ID:$os->id,结束\n";
            $loop++;
            if ($loop == $max) exit;
        }
    }

    public function dpSupplementFH(){
        $sid = 12436;
        $doDp = false;
        $max = 11;
        $loop = 1;
        foreach(model('app\api\model\wanlshop\OrderSync')
//                    ->where('id', 6587)
                    ->where('status', '1')
                    ->where('createtime', '>', 1730390400)
                    ->where('createtime', '<', 1730455200)
                    ->where('id', '>=', $sid)
                    ->where('id', '<', 12441)
                    ->order('id', 'asc')
                    ->select() as $os){
            echo $loop."FH==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            if($os->price==1300) $goodsPV = $os->number*800*($os->flq==2?1:3/8);
            if($os->price==999) $goodsPV = $os->number*700*($os->flq==2?1:3/8);
            echo 'GoodsPV:'.$goodsPV;
            $clogIv = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['推荐奖','复购奖'])
                ->order('id', 'asc')
                ->find();
            echo 'IvAm:'.$clogIv->memo.$clogIv->money;
            $userIv = model('app\common\model\User')->where('id', $clogIv['user_id'])->find();
            $user = model('app\common\model\User')->where('id', $os['user_id'])->find();
            echo 'SelfAm:'.$user->self_amd0."\n";
            $am = $clogIv->money / $this->planrate[$userIv['plan']]['tj'][$userIv['vip_level']];
            if($clogIv->memo == '复购奖') echo 'PV:XXXX';
            else if ($goodsPV == $am && $am == $user->self_amd0) echo 'PV:'.$am;
            else echo 'PV:XXXX';
            if ($os->id == 12436) $am = 150;
            if ($os->id == 12438) $am = 300;
            if ($os->id == 12439) $am = 300;
            if($doDp && $am == 300 && $clogIv->memo == '推荐奖' && $user->self_amd0 == 800){
                model('app\common\model\User')->where('id', $os['user_id'])->update(['self_amd0'=>300]);
            }
            echo "\n";
            if($am > 0) {
                $userXL = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 1)->find();
                $userXR = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 2)->find();
                if ($user) echo $user->self_amd0 . 'MY:' . $user->pam1 . "===>" . $user->pam1n . "R:" . $user->pam2 . "===>" . $user->pam2n . "\n";
                if ($userXL) echo $userXL->self_amd0 . 'Pam L:' . $userXL->pam1 . "===>" . $userXL->pam1n . "R:" . $userXL->pam2 . "===>" . $userXL->pam2n . "\n";
                if ($userXR) echo $userXR->self_amd0 . 'Pam R:' . $userXR->pam1 . "===>" . $userXR->pam1n . "R:" . $userXR->pam2 . "===>" . $userXR->pam2n . "\n";
                $nowNet = $user['pnet'];
                $userP = $user;
                $loopDp = 0;
                while ($userP) {
                    if ($loopDp > 0) {
                        if ($loopDp == 1) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pnums1'] . "  右:" . $userP['pnums2'] . "\n";
                            echo '=============>' . "\n";
                        }
                        if ($userP->monthly_fg == 0 && $userP->pam1 > 0 && $userP->pam2 > 0 && $userP['pam' . $nowNet . 'n'] < $am) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";

                            $clogDp = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $os->order_no)
                                ->where('type', 'subsidy')
                                ->where('user_id', $userP->id)
                                ->where('status', '0')
                                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                ->order('id', 'asc')
                                ->find();
                            if (!$clogDp && $doDp) {
                                $amPv = $am;
                                if ($os->id == 12418 && $userP->id == 32683) $amPv = $am/2;
//                                if ($userP->id == 43529 && $loopDp == 3) $am = $am/2;
                                $this->Net2DPOne($userP->id, $os->order_no, $amPv, 0, 0, 1);
                            }
                        }
                    }
                    $nowNet = $userP['pnet'];
                    $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                    $loopDp++;
                }
                model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $os->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '0')
                    ->update(['days' => 1]);
                model("app\common\model\CurrencyRmbLog")
                    ->where('service_ids', $os->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '0')
                    ->update(['days' => 1]);
            }
            echo "ID:$os->id,结束\n";
            $loop++;
            if ($loop == $max) exit;
        }
    }

    public function Net2NumsCounts(){
        foreach(model('app\common\model\User')
                    ->where('vip_status', '1')
                    ->select() as $user){
            $count = model('app\common\model\User')->where('pid', $user->id)->count();
            $pnums1 = model('app\common\model\User')->where('pid', $user->id)->sum('pnums1');
            $pnums2 = model('app\common\model\User')->where('pid', $user->id)->sum('pnums2');
            $pnums = $user->pnums1+$user->pnums2 - $count-$pnums1-$pnums2;
            model('app\common\model\User')->where('id', $user->id)->update(['pnums'=>$pnums]);
        }
    }

    public function Net2Show($uid, $direction, $stopId){
        $ids = array();
        $fields = 'id,username,mobile,truename,self_amd0,plan,vip_status,vip_level,inviter_id,manager_id,id,pid,pnet,pam1,pam2,pam1n,pam2n,pnums1,pnums2,invites_nums,teams_nums,pam1s,pam2s';
        $fields_arr = explode(",", $fields);
        $user = model('app\common\model\User')->where('id', $uid)->field($fields)->find();
        if($user){
            if ($direction == 'up') {
                $loop = 0;
                $userP = $user;
//                $nowNet = $userP['pnet'];
                while ($userP && $user['plan'] == $userP['plan']) {
                    $ids[] = $userP->id;
//                    echo "L:".$loop;
//                    foreach($fields_arr as $val){
//                        echo $val.":".$userP[$val]."==>";
//                    }
//                    echo "\n";
//                    $nowNet = $userP['pnet'];
                    if($userP->id == $stopId) break;
                    $userP = model('app\common\model\User')->where('id', $userP['pid'])->field($fields)->find();
                    $loop++;
                }
            }
            if ($direction == 'down' || $direction == 'downL') {
                $loop = 0;
                $userP = $user;
//                $nowNet = $userP['pnet'];
                while ($userP && $user['plan'] == $userP['plan']) {
                    $ids[] = $userP->id;
//                    echo "L:".$loop;
//                    foreach($fields_arr as $val){
//                        echo $val.":".$userP[$val]."==>";
//                    }
//                    echo "\n";
//                    $nowNet = $userP['pnet'];
                    if($userP->id == $stopId) break;
                    if($direction == 'downL'){
                        $userP = model('app\common\model\User')->where('pid', $userP['id'])->field($fields)->order('pnet asc')->find();
                    }else {
                        if ($loop == 0) $userP = model('app\common\model\User')->where('pid', $userP['id'])->field($fields)->order('pam1 asc')->find();
                        else $userP = model('app\common\model\User')->where('pid', $userP['id'])->field($fields)->order('pam1 desc')->find();
                    }
                    $loop++;
                }
            }
            foreach($ids as $v){
                echo ' or id='.$v;
            }
            echo "\n";
        }
    }

    public function NetIvFind(){
//        $idsNot = [30913, 30922, 31493, 31495, 34034, 34035, 34036, 34037, 34038, 34039];
        $idsNot = array(30913, 30922, 31493, 31495, 34034, 34035, 34036, 34037, 34038, 34039, 34040, 34274, 34276, 34287, 34309, 34310, 34486,36233,34489,37453,37500,37625,37689,38031,38032,38151,38241,38313,38373,38436,38660,38718,39996,41545,41584);

//        36233
        $loop = 1;
        $count = 0;
        $uid = 0;
        $userinfo = model('app\common\model\User')
            ->where('pid', '>', 0)
            ->where('pnet_iv', 0)
            ->where('id', 'not in', $idsNot)
            ->order('id', 'asc')
            ->find();
        echo $userinfo->id."\n";
//        exit;
        while($userinfo){
            $pnetIv=0;
            if($uid == $userinfo->id) {
                $count++;
            } else {
                $uid = $userinfo->id;
                $count = 0;
            }
            $userUp = $userinfo;
            while($userUp){
                echo $userUp['id']."|".$userUp['pnet']."--->";
                if($userinfo['manager_id'] == $userUp['pid']){
                    $pnetIv = $userUp['pnet'];
                    model('app\common\model\User')->where('id', $userinfo['id'])->update(['pnet_iv' => $userUp['pnet']]);
                    $userUp = false;
                }
                $userUp = model('app\common\model\User')->where('id', $userUp['pid'])->find();
            }
            echo "\n".$userinfo->id."===>".$uid."===>"."Pnet:".$pnetIv."===>".$count;
            if($count > 0) {
                echo "Error\n";
//                break;
                $idsNot[] = $userinfo->id;
//                var_dump($idsNot);
                $loop++;
            }else{
                echo "Done\n";
            }
            $userinfo = model('app\common\model\User')
                ->where('pid', '>', 0)
                ->where('pnet_iv', 0)
                ->where('id', 'not in', $idsNot)
                ->order('id', 'asc')
                ->find();

            if($loop > 0) {
                var_dump($idsNot);
                break;
            }
        }
    }

    public function Net2Gl($uid, $am, $order_no){
        $loopM = 7;
        $loop = 0;
        $userm = model('app\common\model\User')->where('id', $uid)->find();
        while($userm && $loop < $loopM){
            if($loop > 0) {
                $userm['plan']=in_array($userm['plan'],['A','B','C']) ? $userm['plan'] : 'C';
                if($this->planrate[$userm['plan']]['glstd'][$userm['vip_level']] >= $loop && $this->planrate[$userm['plan']]['glrate'][$loop] > 0)
                    $this->bonusAct($userm, $am*$this->planrate[$userm['plan']]['glrate'][$loop], $order_no, '管理奖');
            }
            $userm = model('app\common\model\User')->where('id', $userm['manager_id'])->find();
            $loop++;
        }

    }

    public function Net2Fd($uid, $am, $order_no){
        //count the number of users
        $userlist = [];
        $userlist = $this->Net2FdCount($uid, 1, $userlist);
        $count = count($userlist);
        foreach ($userlist as $user_id) {
            $user = model('app\common\model\User')->where('id', $user_id)->find();
            $hb = $am*0.2/$count;
            $bj = $user['self_amd0'] * 10;
            if(($user['plan' == 'A'] || $user['plan' == 'C']) && $user['self_amd0'] > 0){
                if($user['self_amd0'] % 700 == 0) $bj = $user['self_amd0']/700*1000;
                if($user['self_amd0'] % 800 == 0) $bj = $user['self_amd0']/800*1300;
            }
            if($bj < $this->lvinfo[$user['vip_level']]['cost']) $bj = $this->lvinfo[$user['vip_level']]['cost'];
            if (($hb + $user['bonus_tt']) > $bj) $hb = $bj - $user['bonus_tt'];
            if($hb > 0) {
                $this->bonusAct($user, $hb, $order_no, '回本奖');
            }
        }

    }

    public function bonusAct($user, $bonus, $order_no, $note){
        $bonus = floor($bonus * 100) / 100;
        if($bonus > 0) {
            if ($user['vip_status'] == '0') return 0;
            if ($user['vip_level'] < 2) return 0;
            if ($user['monthly_fg'] == 0 && $note != '推荐奖' && $this->fgDP) return 0;
            model('app\common\model\User')->where('id', $user['id'])->setInc('bonus_tt', $bonus);
            model('app\common\model\User')->where('id', $user['id'])->setInc('bonus_wk', $bonus);
            if ($user['is_backfill'] == '1') {
                $bs = $this->planrate[$user['plan']]['rateBs'];
                if ($user['self_amd0'] > 0 && ($user['plan'] == 'A' || $user['plan'] == 'C')) {
                    if ($user['self_amd0'] % 1300 === 0) $bs = $this->planrate[$user['plan']]['rateBs2'];
                    if ($user['self_amd0'] % 1133 === 0) $bs = $this->planrate[$user['plan']]['rateBs2'];
                }
                $lvfd = $this->lvinfo[$user['vip_level']]['cost'] / 10 * $bs;
                if ($user['self_amd0'] < $lvfd) $user['self_amd0'] = $lvfd;
//            if($user['self_amd0'] < $this->lvinfo[$user['vip_level']]['cost'] / 10) $user['self_amd0'] = $this->lvinfo[$user['vip_level']]['cost'] / 10;
                if ($user['bonus_tt'] < $user['self_amd0'] / $bs * 10) {
                    if (($bonus / 2 + $user['bonus_tt']) >= $user['self_amd0'] / $bs * 10) {
                        $bonusf = $user['self_amd0'] / $bs * 10 - $user['bonus_tt'];
                        if ($bonusf > 0) $bonus = $bonus - $bonusf;
                        model('app\common\model\User')->where('id', $user['id'])->update(['is_backfill' => '0']);
                    } else {
                        $bonus = $bonus / 2;
                    }
                } else {
                    model('app\common\model\User')->where('id', $user['id'])->update(['is_backfill' => '0']);
                }
            }
            if ($bonus > 0) {
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($bonus, $user['id'], $note, 'subsidy', $order_no, $this->currency, '0');
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$bonus * $this->feeRate, $user['id'], '管理费', 'subsidy', $order_no, $this->currency, '0');
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($bonus * $this->feeRate, $user['id'], $note, 'subsidy', $order_no, 'currency_rmb', '0');
                if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($bonus*(1-$this->feeRate), $user['id'], $note, 'subsidy', $order_no, 'currency_nsd', '0');
            }
            if ($user['vip_level'] >= 4) return $this->Net2LU($user['id']);
        }
        return 0;
    }

    public function Net2FdCount($uid, $loop, $userlist){
        if($loop < 6) {
            foreach(model('app\common\model\User')
                         ->where('manager_id', $uid)
                         ->select() as $user) {
                $userlist[] = $user['id'];
                $userlist = $this->Net2FdCount($user['id'], $loop+1, $userlist);
            }
        }
        return $userlist;
    }

    public function Net2LU($uid){
        $lvup = 0;
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user['plan'] == 'A') return 0;
        if($user['vip_level'] == 5 && $user['bonus_wk'] >= 30000 && $user['bonus_wkn'] >= 1){
            $count = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_level', '>', 1)->count();
            if($count > 4) {
                model('app\admin\model\UserUpgradeLog')->create([
                    'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
                    'pay_user_id' => $user['id'],
                    'rec_user_id' => $user['inviter_id'],
                    'lv_old' => $user['vip_level'],
                    'lv_new' => 6,
                    'state' => 1,
                    'amount' => 0
                ]);
                model('app\common\model\User')->where('id', $uid)->update(['vip_level' => 6,'bonus_wkn' => 0]);
                $lvup = 6;
            }
        }else if($user['vip_level'] == 6 && $user['bonus_wk'] >= 100000 && $user['bonus_wkn'] >= 1){
            $count = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_level', '>', 1)->count();
            if($count > 9) {
                $count2 = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_levelm', '>=', 6)->count();
                if($count2 > 1) {
                    model('app\admin\model\UserUpgradeLog')->create([
                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
                        'pay_user_id' => $user['id'],
                        'rec_user_id' => $user['inviter_id'],
                        'lv_old' => $user['vip_level'],
                        'lv_new' => 7,
                        'state' => 1,
                        'amount' => 0
                    ]);
                    model('app\common\model\User')->where('id', $uid)->update(['vip_level' => 7,'bonus_wkn' => 0]);
                    $lvup = 7;
                }
            }
        }else if($user['vip_level'] == 7 && $user['bonus_wk'] >= 200000 && $user['bonus_wkn'] >= 2){
            $count = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_level', '>', 1)->count();
            if($count > 9) {
                $count2 = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_levelm', '>=', 7)->count();
                if($count2 > 1) {
                    model('app\admin\model\UserUpgradeLog')->create([
                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
                        'pay_user_id' => $user['id'],
                        'rec_user_id' => $user['inviter_id'],
                        'lv_old' => $user['vip_level'],
                        'lv_new' => 8,
                        'state' => 1,
                        'amount' => 0
                    ]);
                    model('app\common\model\User')->where('id', $uid)->update(['vip_level' => 8,'bonus_wkn' => 0]);
                    $lvup = 8;
                }
            }
        }else if($user['vip_level'] == 8 && $user['bonus_wk'] >= 300000 && $user['bonus_wk'] >= 3){
            $count = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_level', '>', 1)->count();
            if($count > 9) {
                $count2 = model('app\common\model\User')->where('inviter_id', $uid)->where('vip_levelm', '>=', 8)->count();
                if($count2 > 1) {
                    model('app\admin\model\UserUpgradeLog')->create([
                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999),
                        'pay_user_id' => $user['id'],
                        'rec_user_id' => $user['inviter_id'],
                        'lv_old' => $user['vip_level'],
                        'lv_new' => 9,
                        'state' => 1,
                        'amount' => 0
                    ]);
                    model('app\common\model\User')->where('id', $uid)->update(['vip_level' => 9,'bonus_wkn' => 0]);
                    $lvup = 9;
                }
            }
        }
        if($lvup > 0) {
            $pup = true;
            $loop = 0;
            $userm = $user;
            while($userm && $pup){
                if($loop > 0) {
                    if($userm['vip_levelm'] < $lvup) {
                        model('app\common\model\User')->where('id', $userm['pid'])->update(['vip_levelm' => $lvup]);
                    }else{
                        $pup = false;
                    }
                }
                $loop++;
                $userm = model('app\common\model\User')->where('id', $userm['inviter_id'])->find();
                if($pup && $loop>0 && $userm['vip_level'] == $lvup) $this->Net2LU($userm['id']);
            }
        }
        return 0;
    }

    public function intonet($uid,$order_no='', $calc=true, $nums = 1){
        $cx_check = false;
//        判断是否已经入网
        $uinfo = model('app\common\model\User')->where('id', $uid)->find();
        $u2info = model('app\admin\model\UserTrident2n')
            ->where('saas_id', $uinfo['saas_id'])
            ->where('state', '1')
            ->where('status', '0')
            ->where('user_id', $uinfo['id'])
            ->order('id', 'asc')->find();
        if(!$u2info) {
//        寻找上级
            $frc = true;
            $rid = $uinfo['inviter_id'];
            $parent_code = '';
            while ($frc && $rid > 1) {
                $rinfo = model('app\common\model\User')->where('id', $rid)->find();
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('user_id', $rinfo['id'])
                    ->order('id', 'asc')->find();
                if ($r2info) {
                    $frc = false;
                    $parent_code = $r2info['parent_code'] . "$rid,";
                }else{
                    $rid = $rinfo['inviter_id'];
                }
            }
            $count = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('rid', $rid)
                ->count();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $market = $count + 1;
            $narr = array();
            $narr['saas_id'] = $uinfo['saas_id'];
            $narr['user_id'] = $uid;
            $narr['rid'] = $rid;
            $narr['market'] = $market;
            $narr['market_lw'] = $market;
            $narr['market_kh'] = $market;
            $narr['parent_code'] = $parent_code;
            $narr['is_calc'] = $calc?1:0;
//            $createtime = strtotime($year.'-'.$month.'-'.$day);
            $createtime = strtotime('now');
            $narr['createtime'] = $createtime;
            model('app\admin\model\UserTrident2n')->insert($narr);

            if (!$calc) {
                model('app\admin\model\User')->where('id', $uid)->update(['monthly_fg' => 1, 'monthly_fgn' => 1]);
//                return 0;
            }
            $r2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $rid)
                ->order('id', 'asc')->find();
            $loops = 1;
            $tmarket = $market;
            $f2n = true;
            $d2n = false;
            $zy2n = true;
            while($r2info){
                if ($loops == 1){
                    if($tmarket < 3){
                        $mt = 200*$nums;
                        $f2n = true;
                    }else{
                        $mt = 400*$nums;
                        $f2n = false;
                    }
                    model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('invite_nums', 1);
                    if($r2info['status'] == '0' && $calc) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, $this->currency, '0');
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_nsd', '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_xnb', '0');
                    }
                    if($tmarket == 3 && $calc) {
                        if($cx_check) {
                            if(strtotime('now') - $r2info['createtime'] < 172800) {
                                $r2u = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
                                $cx_am = 999;
                                if($r2u['self_amd0'] >= 4995 && $nums >= 9) {
                                    $cx_am = 4995;
                                    foreach(model('app\admin\model\UserTrident2n')
                                                ->where('saas_id', $r2info['saas_id'])
                                                ->where('state', '1')
                                                ->where('status', '0')
                                                ->where('user_id', $r2info['user_id'])
                                                ->where('market_kh', '<', 3)
                                                ->order('id', 'asc') as $r2t){
                                        $r2tu = model('app\common\model\User')->where('id', $r2t['user_id'])->find();
                                        if($r2tu['self_amd0'] < 4995) $cx_am = 999;
                                    }
                                }
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($cx_am, $r2info['user_id'], '健康大礼包', 'subsidy', $order_no, 'currency_rmb', '0');
                            }
                        }
                    }
                }else{
                    if ($f2n){
                        if ($tmarket > 2) {
                            $f2n = false;
                            $d2n = true;
                        }
                    }
                }
                if($tmarket < 3){
                    $r2_arr = array();
                    if($r2info['team_add'] == 0) $r2_arr['team_add'] = $tmarket;
                    else if($r2info['team_add'] != $tmarket) $r2_arr['team_add'] = 3;
                    if (array_key_exists('team_add', $r2_arr)) {
                        $r2info['team_add'] = $r2_arr['team_add'];
                        model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->update(['team_add' => $r2_arr['team_add']]);
                    }
                }
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('team_nums', 1);
//                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('month_nums', 1);
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('week_nums', 1);
                if($d2n && $calc){
                    $mp = 200*$nums;
                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['invites_numsw'] > 1) $mp += 100;
                    if($rinfo['is_distributor'] == 1) $mp += 200*$nums;
                    elseif($rinfo['svip_level'] > 1) $mp += 200*$nums;
                    elseif($rinfo['self_amd0'] == 4995) $mp += 200*$nums;
//                    if($r2info['month'] > 1 && $r2info['month_kh'] == '0') $mp -= 50;
                    if($r2info['status'] == '0') {
                        $rid = $r2info['rid'];
                        $userId = $r2info['user_id'];
                        $mpPrice = $mp * 0.1;
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $userId, '首购共富奖', 'subsidy', $order_no, $this->currency, '0');
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $userId, '首购共富奖', 'subsidy', $order_no, 'currency_nsd', '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $userId, '首购共富奖', 'subsidy', $order_no, 'currency_xnb', '0');
//                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp*0.1, $userId, '伯乐奖', 'subsidy', $order_no, $this->currency, '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mpPrice, $rid, '共富值', 'subsidy', $order_no, 'currency_gfz', '0');
                        Log::info("wanlPayCurrency 添加-共富值 userId=$userId rid=$rid order_no=$order_no ".$mpPrice);
                    }
//                    if($rinfo['invites_nums'] >= 6 && $rinfo['team_nums'] >= 99 && $rinfo['svip_level'] > 1)
                    $d2n = false;
                }
//                if($zy2n){
//                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['vip_level'] == 5) {
//                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $r2info['user_id'], '店长卓越', 'subsidy', $order_no, 'currency_rmb', '0');
//                        $zy2n = false;
//                    }
//                }
//                if($tmarket == 2) model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('week_nums2', 1);
                $tmarket = $r2info['market_kh'];
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('saas_id', $uinfo['saas_id'])
                    ->where('state', '1')
//                    ->where('status', '0')
                    ->where('user_id', $r2info['rid'])
                    ->order('id', 'asc')->find();
                $loops++;
            }
            if ($calc) controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100*$nums, 4, '云店分红', 'subsidy', $order_no, $this->currency, 0);
            if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100*$nums, 4, '云店分红', 'subsidy', $order_no, 'currency_nsd', 0);

            return true;
        }

        return false;
    }

    public function CalcDistributor($user_id, $amount = 3900){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        if ($user['is_distributor'] != 1){
            $loops = 0;
            $userinfo = $user;
            $toplv = 0;
            while ($userinfo) {
                $userarr = array();
                if ($loops == 0) {
                    $userarr['is_distributor'] = 1;
                    $userarr['vipd_level'] = 1;
                    $userarr['top_lvd'] = 1;
                    $toplv = 1;
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($amount, $userinfo['id'], '分销商赠送', 'subsidy', 'FXS', 'currency_rmb');
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, 3, '分销商分红', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, 3, '分销商分红', 'subsidy', 'FXS', 'currency_nsd');
                }
                if ($loops > 0) {
                    if ($loops == 1) {
                        $userarr['invited_nums'] = $userinfo['invited_nums'] + 1;
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $userinfo['id'], '推荐分销商', 'subsidy', 'FXS', $this->currency);
                        if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $userinfo['id'], '推荐分销商', 'subsidy', 'FXS', 'currency_nsd');
                    }
                    $userarr['teamd_nums'] = $userinfo['teamd_nums'] + 1;

                    if($userinfo['vipd_level'] == 1){
                        if($userarr['teamd_nums'] > 99) {
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userinfo['id'])
                                ->where('teams_nums', '>', 101)
                                ->count();
                            if ($count > 2) {
                                $userarr['vipd_level'] = 2;
                                if($toplv == 1) $toplv = 2;
                            }
                        }
                    }
                    foreach($this->fxs_rate as $k=>$v){
                        if($k > 2) {
                            if ($userinfo['vipd_level'] == $k - 1) {
                                $count = model('app\common\model\User')
                                    ->where('inviter_id', $userinfo['id'])
                                    ->where('top_lvd', '>=', $k - 1)
                                    ->count();
                                if ($count > 2) {
                                    $userarr['vipd_level'] = $k;
                                    if ($toplv == $k-1) $toplv = $k;
                                }
                            }
                        }
                    }
                    if($toplv > $userinfo['top_lvd']) $userarr['top_lvd'] = $toplv;
                }
                model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                $loops++;
                $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
            }
            $dist_address = explode("/", $user['dist_address']);

            $is_area = $dist_address[2];
            $is_city = $dist_address[1];

            if($is_area != ''){
                $userservice = model('app\common\model\User')->where('city_server', '1')->where('city_address', 'like','%/'.$is_area)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(200, $userservice['id'], '县分销商', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(200, $userservice['id'], '县分销商', 'subsidy', 'FXS', 'currency_nsd');
                    $is_area = '';
                }
            }
            if($is_city != ''){

                $userservice = model('app\common\model\User')->where('city_server', '1')->where('city_address', 'like', '%/'.$is_city)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100, $userservice['id'], '市分销商', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100, $userservice['id'], '市分销商', 'subsidy', 'FXS', 'currency_nsd');
                    $is_city = '';
                }
            }
        }
        return true;
    }

    //云店代理商
    public function CalcDistributorYDFXS($user_id, $order_id, $activate, $nums){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        if ($user['is_distributor'] != 1){
            $loops = 0;
            $userinfo = $user;
            $toplv = 0;
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, 3, '分销商分红', 'subsidy', 'HHR', $this->currency);
            if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, 3, '分销商分红', 'subsidy', 'HHR', 'currency_nsd');
            if($activate == 1) {
                while ($userinfo) {
                    $userarr = array();
                    if ($loops == 0) {
                        $userarr['is_distributor'] = 1;
                        $userarr['vipd_level'] = 1;
                        $userarr['top_lvd'] = 1;
                        $toplv = 1;
                    }
                    if ($loops > 0) {
                        if ($loops == 1) {
                            $userarr['invited_nums'] = $userinfo['invited_nums'] + 1;
                        }
                        $userarr['teamd_nums'] = $userinfo['teamd_nums'] + 1;

                        if ($userinfo['vipd_level'] == 1) {
                            if ($userarr['teamd_nums'] > 99) {
                                $count = model('app\common\model\User')
                                    ->where('inviter_id', $userinfo['id'])
                                    ->where('teams_nums', '>', 99)
                                    ->count();
                                if ($count > 2) {
                                    $userarr['vipd_level'] = 2;
                                    if ($toplv == 1) $toplv = 2;
                                }
                            }
                        }
                        foreach ($this->fxs_rate as $k => $v) {
                            if ($k > 2) {
                                if ($userinfo['vipd_level'] == $k - 1) {
                                    $count = model('app\common\model\User')
                                        ->where('inviter_id', $userinfo['id'])
                                        ->where('top_lvd', '>=', $k - 1)
                                        ->count();
                                    if ($count > 2) {
                                        $userarr['vipd_level'] = $k;
                                        if ($toplv == $k - 1) $toplv = $k;
                                    }
                                }
                            }
                        }
                        if ($toplv > $userinfo['top_lvd']) $userarr['top_lvd'] = $toplv;
                    }
                    model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                    $loops++;
                    $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                }
            }

            $orderAddr = model('app\index\model\wanlshop\OrderAddress')->get(['order_id' => $order_id]);
            $dist_address = explode("/", $orderAddr['address']);

            $is_area = $dist_address[2];
            $is_city = $dist_address[1];
            $is_province = $dist_address[0];

            if($is_area != ''){
                $userservice = model('app\common\model\User')->where('city_server', '2')->where('city_address', 'like','%/'.$is_area)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, $userservice['id'], '县分销商', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, $userservice['id'], '县分销商', 'subsidy', 'FXS', 'currency_nsd');
                    $is_area = '';
                }
            }
            if($is_city != ''){
                $userservice = model('app\common\model\User')->where('city_server', '3')->where('city_address', 'like', '%/'.$is_city)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(10*$nums, $userservice['id'], '市分销商', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(10*$nums, $userservice['id'], '市分销商', 'subsidy', 'FXS', 'currency_nsd');
                    $is_city = '';
                }
            }
            if($is_province != ''){
                $userservice = model('app\common\model\User')->where('city_server', '4')->where('city_address', 'like', '%/'.$is_province)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(5*$nums, $userservice['id'], '省分销商', 'subsidy', 'FXS', $this->currency);
                    if($this->currency == 'currency_ns') controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(5*$nums, $userservice['id'], '省分销商', 'subsidy', 'FXS', 'currency_nsd');
                    $is_province = '';
                }
            }
        }
        return true;
    }

    public function autoCalcDaliy()
    {
        $this->calcOrderConfirmDaily(true);
        $this->autoBonusCount();
    }

    public function autoWeekly(){
        if(date("w") == 1){
            $user_arr = array(
                'pam1d' => 0,
                'pam2d' => 0
            );
            model('app\common\model\User')
                ->where('id', '>', 0)
                ->update($user_arr);

            $user_arr = array(
                'weekfd' => 0,
                'bonus_wk' => 0
            );
            foreach(model('app\common\model\User')
                        ->where('id', '>', 0)
                        ->select() as $user){
                if($user['vip_level'] == 5 && $user['bonus_wk'] >= 30000) $user_arr['bonus_wkn'] = $user['bonus_wkn'] + 1;
                if($user['vip_level'] == 6 && $user['bonus_wk'] >= 100000) $user_arr['bonus_wkn'] = $user['bonus_wkn'] + 1;
                if($user['vip_level'] == 7 && $user['bonus_wk'] >= 200000) $user_arr['bonus_wkn'] = $user['bonus_wkn'] + 1;
                if($user['vip_level'] == 8 && $user['bonus_wk'] >= 300000) $user_arr['bonus_wkn'] = $user['bonus_wkn'] + 1;
//                if($user_arr['bonus_wkn'] == 0)
                    model('app\common\model\User')
                        ->where('id', $user['id'])
                        ->update($user_arr);
            }
        }

    }

    public function autoMonthly(){
        $user_arr = array(
            'monthly_fgn' => 0,
        );
        if(date("d") == 1){
            foreach(model('app\common\model\User')
                        ->where('id', '>', 0)
                        ->select() as $user){
                $user_arr['monthly_fg'] = $user['monthly_fgn'];
                model('app\common\model\User')
                    ->where('id', $user['id'])
                    ->update($user_arr);
            }
        }
//        foreach(model('app\admin\model\UserTrident2n')
//            ->where('state', '1')
//            ->where('status', '0')
//            ->order('id', 'asc')->select() as $ut2n){
//            $ut_arr = array();
//            $ut_arr['month'] = $ut2n['month'] + 1;
//            $ut_arr['team_add'] = 0;
//            $ut_arr['month_nums'] = 0;
//            if ($ut2n['team_add'] == 3){
//                $ut_arr['month_kh'] = '1';
//            }else{
//                $ut_arr['month_kh'] = '0';
//            }
//            model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->update($ut_arr);
//        }
    }

    public function invitationCode($user_id){
        $ivcode = Random::alnum(8);
        $checkhv = true;
        while($checkhv){
            $count = model('app\common\model\User')->where('ivcode', $ivcode)->count();
            if ($count > 0 ){
                $ivcode = Random::alnum(8);
            }else{
                $checkhv = false;
            }
        }
        model('app\common\model\User')->where('id', $user_id)->update(['ivcode' => $ivcode]);
    }


    public function doconfirm($params){
        $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
            ->where('id', $params['id'])
            ->find();
        if ($remitinfo) {
            if($remitinfo['uplogid'] > 0){
                if($params['action'] == 'received'){
                    $count = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('status', '<>', 'confirm')
                        ->where('uplogid', $remitinfo['uplogid'])
                        ->count();
                    if($count == 0){
                        model('app\admin\model\UserUpgradeLog')
                            ->where('id', $remitinfo['uplogid'])
                            ->update(['state' => '1']);
                        if($remitinfo['rtype'] > 0){
                            $uul = model('app\admin\model\UserUpgradeLog')
                                ->where('id', $remitinfo['uplogid'])
                                ->find();
                            if ($uul['lv_new'] > 2) {
                                $this->wslvUp($uul['pay_user_id'], $uul['lv_new']);
                            }
                        }
                    }
                }
                if($params['action'] == 'cancel'){
                    model('app\admin\model\UserUpgradeLog')
                        ->where('id', $remitinfo['uplogid'])
                        ->update(['state' => '2']);
                }
            }
            if($remitinfo['rtype'] < 2){
                if($params['action'] == 'received'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['user_id'], '进货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                    if($remitinfo['rtype'] == 0){
                        $user = model('app\common\model\User')->where('id', $remitinfo['user_id'])->find();
                        $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                        if($userr['svip_level'] >= $user['svip_level']){
                            $pjrate = 0.05;
                            if($userr['svip_level'] == 4) $pjrate = 0.03;
                            if($userr['svip_level'] > 4) $pjrate = 0.02;
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount']*$remitinfo['discount']*$pjrate, $userr['id'], '平级奖励', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                        }
                        if($user['svip_level'] < 11) $this->wslvUp($user['id'], $user['svip_level'], false);
                    }
                }
                if($params['action'] == 'cancel'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['rec_user_id'], '批发商退货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                }
            }
        }
    }

    public function wslvUp($user_id, $uplv, $updo=true){
        $loops = 0;
        if($updo) {
            model('app\common\model\User')
                ->where('id', $user_id)
                ->update(['svip_level' => $uplv]);
            if($uplv == 3 || $uplv == 4){
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
                while($userr){
                    if($loops>0){
                        model('app\common\model\User')->where('id', $userr['id'])->setInc('wslv'.$uplv, 1);
                        if($userr['svip_level'] == 3){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv3', '>', 0)
                                ->sum('wslv3');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv3', '>', 0)->order('wslv3', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv3'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 20) $this->wsUpOrder($userr['id'], 4);
                        }else if($userr['svip_level'] == 4){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv4', '>', 0)
                                ->sum('wslv4');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv4', '>', 0)->order('wslv4', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv4'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 10) $this->wsUpOrder($userr['id'], 5);
                        }
                    }
                    $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                    $loops++;
                }
            }
        }
        if($uplv >= 5){
            if($updo){
                $user = model('app\common\model\User')->where('id', $user_id)->find();
                $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
            }else{
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
            }
            if($userr['svip_level'] == $uplv) {
                $count = model('app\common\model\User')
                    ->where('inviter_id', $userr['id'])
                    ->where('svip_level', '>=', $userr['svip_level'])
                    ->count();
                if ($count >= 3) {
                    $year = date('Y');
                    $month = date('m');
                    $createtime = strtotime($year . '-' . $month . '-' . '01');
                    $thismontham = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('user_id', $userr['id'])
                        ->where('createtime', '>=', $createtime)
                        ->where('rtype', '0')
                        ->where('status', 'confirm')
                        ->sum('amount');
                    if ($thismontham >= 300000 && $userr['svip_level'] < 11) wslvUp($userr['id'], $userr['svip_level'] + 1);
                }
            }
        }
    }

    public function wsUpOrder($user_id, $uplv){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        $uulinfo = model('app\admin\model\UserUpgradeLog')
            ->where('pay_user_id', $user['id'])
            ->where('lv_old', $user['svip_level'])
            ->where('lv_new', $uplv)
            ->where('item', '1')
            ->find();
        if (!$uulinfo){
            $margin = $this->lvsinfo[$uplv]['margin'] - $this->lvsinfo[$user['svip_level']]['margin'];
            $orderam = $this->lvsinfo[$uplv]['orderam'];
            $orderam = 0;

            $cid = 0;
            if ($margin > 0) $cid = 1;
            $pid = 1;
            $rid = $user['inviter_id'];
            $fsj = true;
            while($fsj) {
                $puser = model('app\common\model\User')
                    ->where('id', $rid)
                    ->find();
                if($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $orderam/$this->lvsinfo[$uplv]['discount']){
                    $pid = $puser['id'];
                    $fsj = false;
                }
                $rid = $puser['inviter_id'];
                if($puser['id'] == 1) $fsj = false;
            }
            $uul_arr = array();
            $uul_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $uul_arr['pay_user_id'] = $user['id'];
            $uul_arr['rec_user_id'] = $pid;
            $uul_arr['cpn_user_id'] = $cid;
            $uul_arr['item'] = '1';
            $uul_arr['lv_old'] = $user['svip_level'];
            $uul_arr['lv_new'] = $uplv;
            $uul_arr['orderam'] = $orderam;
            $uul_arr['margin'] = $margin;
            $uul_arr['amount'] = $orderam + $margin;
            $uul_arr['createtime'] = time();
            if($margin > 0) $uul_arr['item'] = '1';
            $uulid = model('app\admin\model\UserUpgradeLog')->insertGetId($uul_arr);
            if($uulid) {
                if ($margin > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $cid;
                    $remit_arr['amount'] = $margin;
                    $remit_arr['payamount'] = $margin;
                    $remit_arr['discount'] = 1;
                    $remit_arr['rtype'] = '2';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                }
                if ($orderam > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $pid;
                    $remit_arr['amount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['payamount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['discount'] = 1;//$this->lvsinfo[$uplv]['discount'];
                    $remit_arr['rtype'] = '1';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                    if ($pid > 5){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$orderam/$this->lvsinfo[$uplv]['discount'], $pid, '批发商进货', 'subsidy', $remit_arr['pay_sn'], 'currency_rmb');
                    }
                }
            }
        }
    }

    public static function sendRequest($url, $params = [], $method = 'POST', $options = [], $Sign=null)
    {
        $method = strtoupper($method);
        $protocol = substr($url, 0, 5);
        $query_string = is_array($params) ? http_build_query($params) : $params;

        $ch = curl_init();
        $defaults = [];
        if ('GET' == $method) {
            $geturl = $query_string ? $url . (stripos($url, "?") !== false ? "&" : "?") . $query_string : $url;
            $defaults[CURLOPT_URL] = $geturl;
        } else {
            $defaults[CURLOPT_URL] = $url;
            if ($method == 'POST') {
                $defaults[CURLOPT_POST] = 1;
            } else {
                $defaults[CURLOPT_CUSTOMREQUEST] = $method;
            }
            $defaults[CURLOPT_POSTFIELDS] = $params;
        }

        $defaults[CURLOPT_HEADER] = false;
        $defaults[CURLOPT_USERAGENT] = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.98 Safari/537.36";
        $defaults[CURLOPT_FOLLOWLOCATION] = true;
        $defaults[CURLOPT_RETURNTRANSFER] = true;
        $defaults[CURLOPT_CONNECTTIMEOUT] = 3;
        $defaults[CURLOPT_TIMEOUT] = 3;

        // disable 100-continue
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        if($Sign != null) {
//            $defaults[CURLOPT_HEADER] = true;
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Sign:$Sign"));
        }

        if ('https' == $protocol) {
            $defaults[CURLOPT_SSL_VERIFYPEER] = false;
            $defaults[CURLOPT_SSL_VERIFYHOST] = false;
        }

        curl_setopt_array($ch, (array)$options + $defaults);

        $ret = curl_exec($ch);
        $err = curl_error($ch);

        if (false === $ret || !empty($err)) {
            $errno = curl_errno($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);
            return [
                'ret'   => false,
                'errno' => $errno,
                'msg'   => $err,
                'info'  => $info,
            ];
        }
        curl_close($ch);
        return [
            'ret' => true,
            'msg' => $ret,
        ];
    }

    public function checkMerchant(){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/merchant/info", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkBalance($gateway = ''){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $url = 'https://pay.s100mi.com/api/v1/merchant/balance';
        if($gateway == 'yeepay') $url = $url.'/yeePay';
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($gateway == 'yeepay') $balance = $result['msg']['data']['totalAccountBalance'];
            else $balance = $result['msg']['data']['balance'];
            if($balance == 0) {
                return ['code' => 10005 ,'msg' => '余额不足'];
            }else{
                return $result['msg'];
            }
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function OutTransfer($id, $gateway='sandpay'){
        $url = "https://pay.s100mi.com/api/v1/out-order/transfer";
        if($gateway == 'yeepay') $url = $url.'Yee';
        $order = model('app\admin\model\wanlshop\Withdraw')
            ->where('status', 'created')
            ->where('id',$id)->find();
        if($order){
            if($gateway == 'yeepay'){
                if ($order['type_text'] == 'CMB') $order['type_text'] = 'CMBCHINA';
                if ($order['type_text'] == 'COMM') $order['type_text'] = 'BOCO';
                if ($order['type_text'] == 'PAB') $order['type_text'] = 'SDB';
                if ($order['type_text'] == 'CITIC') $order['type_text'] = 'ECITIC';
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'amount' => $order['money'],//
                    'bankAccountType' => 'DEBIT_CARD',//
                    'bankNo' => $order['type_text'],
                    'feeChargeSide' => 'PAYEE',
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '提现'
                ];
                if($order['mtype'] == 'currency_tz'){
                    $data['amount'] = $order['money']*60;
                    $data['remark'] = '提现';
                }
                if($order['mtype'] == 'currency_cny'){
                    $data['amount'] = $order['money'];
                    $data['remark'] = '异业提现';
                }
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
            }else{
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'accType' => 4,
                    'amount' => $order['money']*60,//
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '往来',
                    'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
                ];
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
            }
            $sign = strtoupper(md5($signbf));
            $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
                return $result['msg'];
            }else{
                return ['code' => 10005 ,'msg' => '接口异常'];
            }
        }
        return ['code' => 10005 ,'msg' => '订单异常'];

        if($gateway == 'yeepay'){
            $data = [
                'accName' => '季正新',
                'accNo' => '6214186666006839513',//账号
                'amount' => 4,//
                'bankAccountType' => 'DEBIT_CARD',//
                'bankNo' => 'NBCB',
                'feeChargeSide' => 'PAYEE',//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '提现'
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
        }else{
            $data = [
                'accName' => '季正新',
                'accNo' => '6214186666006839513',//账号
                'accType' => 4,
                'amount' => 4,//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '往来',
                'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
        }
        $sign = strtoupper(md5($signbf));
        var_dump($data);
        var_dump($signbf);
        var_dump($sign);
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            var_dump($result['msg']);
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkOrderIn($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'orderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&orderId='.$data['orderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/in-order/inquire-order", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
//                if( $result['msg']['data']['orderStatus'] == 'SUCCESS') return true;
//                else return false;
                var_dump($result['msg']);
            }
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }

    public function checkOrderOut($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'merchantOrderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/out-order/transfer/query", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
                if( $result['msg']['data']['status'] == 'SUCCESS') return true;
                else return false;
//                var_dump($result['msg']);
            }
            return false;//$result['msg'];
        }else{
            return false;//['code' => 10005 ,'msg' => '接口异常'];
        }
    }

    // 自动发货
    protected function autoDelivery($order_id) {
        Log::info("自动发货-虚拟发货 -- autoDelivery order_id=$order_id");
        $pay = model('app\index\model\wanlshop\Pay')->where(['order_id'=>$order_id])->find();
        Log::info("自动发货-虚拟发货 -- autoDelivery trade_no=${pay['trade_no']}");
        if (empty($pay['trade_no'])) {
            Log::info("自动发货-虚拟发货 -- false 1");
            return false;
        }

        // 微信支付才需要
        if ($pay['pay_type'] != 1) {
            Log::info("自动发货-虚拟发货 -微信支付才需要 false");
            // return false;
        }else{
            Log::info("自动发货-虚拟发货 2");
            $order = Order::get($order_id);
            list($appid, $appsecret) = $this->getAppInfoBySaasId($order['saas_id']);
            if (empty($appid) || empty($appsecret)) {
                Log::info("自动发货-虚拟发货 配置不对");
                return false;
            }
            Log::info("自动发货-虚拟发货 2.5");
            $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret);
            if (!$access_token)
                $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret, true);
            if (!$access_token) {
                Log::info("自动发货-虚拟发货 access_token=false");
                return false;
            }
            Log::info("自动发货-虚拟发货 3");
            $order_key = [
                'order_number_type' => 2,
                'transaction_id' => $pay['trade_no'],
            ];
            $shipping_list = [
                [
                    'item_desc' => '购买线上云店',
                    'contact' => ['consignor_contact'=>''],
                ]
            ];
            Log::info("自动发货-虚拟发货 4");
            // 物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提
            $logistics_type = 3;
            //查询支付记录中的通知是否含有openid
            $pay['notice'] = json_decode($pay['notice'],true);
            $openid = array_get($pay['notice'], 'openid');
            if(!$openid){
                $third_info = model('app\index\model\wanlshop\Third')->where(['user_id'=>$order['user_id']])->find();
                if (empty($third_info['openid'])) {
                    return false;
                }
                $openid = $third_info['openid'];
            }
            $payer = ['openid'=>$openid];
            date_default_timezone_set('UTC');
            $upload_time = gmdate("Y-m-d\TH:i:s.120+08:00", time());
            $req = [
                'order_key' => $order_key,
                'logistics_type' => $logistics_type,
                'delivery_mode' => 1,
                'shipping_list' => $shipping_list,
                'upload_time' => $upload_time, // "2024-04-11T08:09:35.120+08:00", // 上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`
                'payer' => $payer
            ];
            Log::info("自动发货-虚拟发货 5");
            Log::info("自动发货-虚拟发货 ".json_encode($req, JSON_UNESCAPED_UNICODE));
            $url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token={$access_token}";
            sleep(2);
            $result = Http::sendRequest($url, json_encode($req, JSON_UNESCAPED_UNICODE));
            Log::info("自动发货-虚拟发货 ".json_encode($result, JSON_UNESCAPED_UNICODE));
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                return false;
            }
        }
        // 更新成已发货
        Order::update(['state'=> 6, 'express_name'=>'xunifahuo', 'express_no'=>'1001'], ['id'=>$order_id], ['state','express_name','express_no']);
        $this->calcOrderConfirm($order_id);
        return true;
    }

    private function getAppInfoBySaasId($saas_id)
    {
        $configFile = APP_PATH . "../addons/wanlshop/config.php";
        if($saas_id > 0){
            $configFile = APP_PATH . "../addons/wanlshop/config{$saas_id}.php";
        }

        $config = [];
        if (is_file($configFile)) {
            $configArr = include $configFile;
            if (is_array($configArr)) {
                foreach ($configArr as $key => $value) {
                    $config[$value['name']] = $value['value'];
                }
                unset($configArr);
            }
        }
        Log::info("自动发货-虚拟发货 configArr=".json_encode($config,JSON_UNESCAPED_UNICODE));
        return [$config["mp_weixin"]["appid"], $config["mp_weixin"]["appsecret"]];
    }


    public function marketAct($mid){
        foreach(model('app\common\model\User')
                    ->where('id', '>=', $mid)
                    ->where('m_id', 0)
                    ->order('id', 'desc')->select() as $user) {
            $findmid = true;
            $iv = $user;
            while($iv && $findmid){
                if($iv['id'] == $mid){
                    model('app\common\model\User')->where('id', $user['id'])->update(['m_id'=>$mid]);
                    $findmid = false;
                }else if($iv['id'] < $mid){
                    $findmid = false;
                }
                $iv = model('app\common\model\User')->where('id', $iv['inviter_id'])->find();
            }
        }
    }
    public function newAccount(){
        $mobile = ***********;
        $user = model('app\common\model\User');
        $mUserId = 31928;
        $userinfo = $user->where('id', '>=', $mUserId)
            ->where('m_id', $mUserId)
            ->where('is_pw', 1)
            ->order('pw_no', 'asc')
            ->find();
        while($userinfo) {
            // create Account
            $userNew = $userinfo->toArray();
            if($userinfo['id'] == $mUserId) $userNew['inviter_id'] = 1050;
            else{
                $userIvo = $user->where('id', $userinfo['pw_iv'])->field('mobile2')->find();
                $userIvn = $user->where('mobile', $userIvo['mobile2'])->field('id')->find();
                $userNew['inviter_id'] = $userIvn['id'];
                $userNew['pw_iv'] = $userIvn['id'];
            }
            $userNew['vip_status'] = '0';
            $userNew['vip_level'] = 1;
            $userNew['manager_id'] = 0;
            $userNew['pid'] = 0;
            $userNew['pnet'] = 0;
            $userNew['pnums1'] = 0;
            $userNew['pnums2'] = 0;
            $userNew['pam1'] = 0;
            $userNew['pam2'] = 0;
            $userNew['pam1n'] = 0;
            $userNew['pam2n'] = 0;
            $userNew['invites_nums'] = 0;
            $userNew['teams_nums'] = 0;
            if(strlen($userNew['avatar']) > 255){
                unset($userNew['avatar']);
            }
            unset($userNew['id']);
            unset($userNew['m_id']);
            $username = '';
            $hasUser = true;
            while($hasUser){
                $username = 'YFL'.Random::numeric(8);
                // 检测用户名、昵称、邮箱、手机号是否存在
                $userCheck = $user->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
                if(!$userCheck) $hasUser = false;
            }
            $userNew['username'] = $username;
            $id = $user->allowField(true)->create($userNew,true)->id;
            if($id > 0){
                $this->invitationCode($id);
                $user->where('id', $userinfo->id)->update(['mobile' =>  $mobile, 'is_pw' => 4]);
                $mobile++;
            }
            // update account
            echo $id."===>".$userNew['nickname']."===>".$mobile."\n";
//            var_dump($id);
//            exit;
            $userinfo = $user->where('id', '>=', $mUserId)
                ->where('m_id', $mUserId)
                ->where('is_pw', 1)
                ->order('pw_no', 'asc')
                ->find();
        }
    }
    public function actAccount($idF, $idE){
        $userinfo = model('app\common\model\User')
            ->where('is_pw', 1)
            ->where('id', '>=', $idF)
            ->where('id', '<', $idE)
            ->order('pw_no,id', 'asc')
            ->find();
//        echo $userinfo->id;exit;
        while($userinfo) {
            $order_info = array();
            $order_info['user_id'] = $userinfo->id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $userinfo->username;
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
//            $order_info['act'] = 'newpurchase';
            $this->calcOrderAction($order_info, true);
            model('app\common\model\User')->where(['id' => $userinfo->id])->update(['is_pw'=>3]);
            echo $userinfo['id']."===>".$userinfo['mobile']."===>".$userinfo->inviter_id."\n";
//            sleep(5);
//            exit;
            $userinfo = model('app\common\model\User')
                ->where('is_pw', 1)
                ->where('id', '>', $idF)
                ->where('id', '<', $idE)
                ->order('pw_no,id', 'asc')
                ->find();
        }

    }

    public function userOrder(){
        $count = 0;
        foreach(model('app\api\model\wanlshop\OrderSync')
                    ->where('user_id', '>', 0)
                    ->select() as $order) {
            $user = model('app\common\model\User')->where('id', $order->user_id)->find();
            if($user->self_amd0 == 0) {
                $count++;
                echo $user->id . "=>" . $user->self_amd0 . "==>";
                echo $order->price . "==>" . $order->number;
                echo "\n";
            }
        }
        echo $count;
        exit;
        $count = 0;
        foreach(model('app\common\model\User')
                    ->where('id', '>',29999)
                    ->where('self_amd0', '>', 0)
                    ->select() as $user){
            echo $user->id."=>".$user->self_amd0."==>";
            $order = model('app\api\model\wanlshop\OrderSync')
                ->where('user_id', $user->id)
                ->find();
            if($order) {
                $count++;
                echo $order->price . "==>" . $order->number;
            }else{
                echo 'empty';
                model('app\common\model\User')->where('id', $user->id)->update(['self_amd0' => 0]);
            }
            echo "\n";
        }
        echo $count;
    }

    public function cUsers() {
//        $password = 'MKap2LtjHOBg3_9yVR1gaNY4sy3DxLf1dr5';
//        $salt = 'CagZjx';
//        $paypwd = 'Yco8IefQyaiVvELD2wehdNwMvlM';
//        $psalt = 'WEK9cB';
        $password = 'o9FknF1N9w2ksEuA4Wkf5qvyc_tdKgy';
        $salt = 'rWK40U';
        $paypwd = 'tFTh-I48QnEJOmC1QxgtVDk-IMg';
        $psalt = '7rH2w6';
        $user_key = ['id', 'saas_id', 'm_id', 'plan', 'group_id', 'username', 'nickname', 'truename', 'idcard', 'alipay', 'password', 'salt', 'paypwd', 'psalt', 'ivcode', 'email', 'mobile', 'avatar', 'level', 'vip_status', 'ns_status', 'trident11', 'trident21', 'level_id', 'vip_level', 'vipv_level', 'vip_levels', 'vip_levelm', 'svip_level', 'svip_levels', 'vip_statusu', 'vip_levelu', 'svip_levelu', 'svip_levelum', 'is_balance', 'is_remit', 'is_withdraw', 'is_certified', 'gender', 'birthday', 'bio', 'money', 'currency_old', 'currency_cny', 'currency_rmb', 'currency_bd', 'currency_xnb', 'currency_nfr', 'currency_ns', 'currency_ans', 'currency_usdt', 'currency_fil', 'currency_lmt', 'currency_points', 'currency_gq', 'currency_tz', 'currency_pu', 'currency_tzu', 'currency_adv', 'currency_love', 'currency_pow', 'currency_spow', 'currency_ric', 'riches', 'currency_flq', 'currency_fhq', 'currency_gfz', 'score', 'successions', 'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'loginfailure', 'joinip', 'jointime', 'createtime', 'updatetime', 'uptime', 'token', 'status', 'verification', 'inviter_id', 'pid', 'pnet', 'pam1', 'pam2', 'pam1c', 'pam2c', 'pam1n', 'pam2n', 'pnets', 'self_am0', 'self_amt', 'self_times', 'self_amd0', 'self_amdt', 'invite_nums', 'invite_numsns', 'invites_loop', 'invites_nums', 'invite_numsw', 'invites_numsw', 'team_nums', 'team_numsns', 'teams_nums', 'invite_am', 'team_am', 'team_amm', 'invite_numsu', 'invite_amu', 'team_numsu', 'team_amu', 'team_amu_new', 'invite_numsu_new', 'team_numsu_new', 'static_m', 'static_bonus', 'share_bonus', 'share2_bonus', 'dynamic_bonus', 'dynamic_am', 'svip_bonus', 'autopay', 'purelease', 'pu_static', 'pu_share', 'pu_dynamic', 'pu_todaytt', 'wslv3', 'wslv4', 'monthly_ns', 'monthly_nsn', 'monthly_ns_am', 'ns_pay', 'ns_dynamic', 'ns_static', 'ns_fhq', 'adv_nums', 'adv_num_time', 'parent_code', 'is_securities_firms', 'is_distributor', 'vipd_level', 'invited_nums', 'teamd_nums', 'top_lvd', 'dist_address', 'city_server', 'city_address', 'city_info', 'monthly_fg', 'monthly_fgn', 'viplc_level', 'lc_lam', 'up_state'];
        $user_val = [6586, 4, 0, 'C', 0, '13000000030', '130****0030', NULL, NULL, NULL, $password, $salt, $paypwd, $psalt, 'FHXQyA01', '', '13000000030', '', 1, '1', '0', '0', '0', 0, 3, 1, 1, 1, 1, 1, '0', 1, 1, 1, '1', '1', '1', '1', 0, NULL, '', '0.00', '0.00000', '0.00', '0.00', '0.00', '0', '0', '0.00', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', 0, 1, 1, 1725926400, 1712742196, '***************', 0, '***************', 1725926400, 1725926400, 1712742196, NULL, '', 'normal', '', 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', 0, '0.00000', '0', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', 0, 0, 0, 0, '0.00', '0.00000', '0.00000', '0.00000', 0, 0, NULL, '0,1,150,152,', 0, 0, 0, 0, 0, 0, NULL, '0', NULL, NULL, 0, 0, 1, 0, 0];
        $count = count($user_key);

        $u13410001131t1136 = true;
        if($u13410001131t1136) {
            $no1 = 13410001131;
            $sid = 8259;
            $ivid = 8181;
            $ivc = 1131;
            $isIv = true;
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k > 0){
                    $user_array['pnet_dz'] = 2;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13400000961t094 = false;
        if($u13400000961t094) {
            $no1 = 13400000961;
            $sid = 8255;
            $ivid = 6313;
            $ivc = 961;
            $isIv = true;
            for($k = 0; $k < 4;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }elseif($k == 1){
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 1;
                }elseif($k == 2){
                    $user_array['inviter_id'] = $sid+1;
                    $user_array['pnet_dz'] = 1;
                }else{
                    $user_array['inviter_id'] = $sid+1;
                    $user_array['pnet_dz'] = 2;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1340001076t1089 = false;
        if($u1340001076t1089) {
            $no1 = 13400001076;
            $sid = 8241;
            $ivid = 8211;
            $ivc = 1076;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }elseif($k < 6){
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 1;
                }else{
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 2;
                }
                if($k == 13) {
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                    $user_array['pnet_dz'] = 1;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1340001063t1074 = false;
        if($u1340001063t1074) {
            $no1 = 13400001063;
            $sid = 8229;
            $ivid = 8175;
            $ivc = 1063;
            $isIv = true;
            for($k = 0; $k < 12;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k < 4){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = 8207;
                }else{
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }
                if($k == 10) $user_array['inviter_id'] = $user_array['id'] - 1;
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1340001041t1061 = false;
        if($u1340001041t1061) {
            $no1 = 13400001041;
            $sid = 8208;
            $ivid = 7585;
            $ivc = 1041;
            $isIv = true;
            for($k = 0; $k < 21;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = 8019;
                }elseif($k == 3 || $k == 5 || $k == 7 || $k == 9 || $k == 11 || $k == 13 || $k >= 15){
                    $user_array['inviter_id'] = $sid;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k == 20) $user_array['inviter_id'] = $user_array['id'] - 1;
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1340001001t1037 = false;
        if($u1340001001t1037) {
            $no1 = 13400001001;
            $sid = 8171;
            $ivid = 8136;
            $ivc = 1001;
            $isIv = true;
            for($k = 0; $k < 37;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0 || $k == 2 || $k == 4 || $k == 6 || $k == 7 || $k == 8 || $k == 12 || $k == 14 || $k == 16 || $k == 18 || $k == 20 || $k == 22 || $k == 24 || $k == 26 || $k == 28 || $k >= 30){
                    $user_array['inviter_id'] = $ivid;
                }elseif($k == 9 || $k == 10){
                    $user_array['inviter_id'] = 8177;
                }elseif($k == 11){
                    $user_array['inviter_id'] = $user_array['id'] - 2;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1340000951t0985 = false;
        if($u1340000951t0985) {
            $no1 = 13400000951;
            $sid = 8137;
            $ivid = 8135;
            $ivc = 951;
            $isIv = true;
            for($k = 0; $k < 34;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0 || $k == 2 || $k == 4 || $k == 6 || $k == 19 || $k == 20 || $k == 21 || $k == 22 || $k == 23 || $k == 26 || $k == 28 || $k >= 30){
                    $user_array['inviter_id'] = $ivid;
                }elseif($k == 9 || $k == 10 || $k == 11){
                    $user_array['inviter_id'] = $user_array['id'] - 3;
                }elseif($k == 13 || $k == 14){
                    $user_array['inviter_id'] = 8146;
                }elseif($k == 24 || $k == 25){
                    $user_array['inviter_id'] = 8150;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u1341 = false;
        if($u1341) {
            $noarr = array(
                0 => 13410000871,
                1 => 13410000911,
                2 => 13410000914,
                3 => 13410000919,
                4 => 13410000941,
                5 => 13410000931,
            );
            $iv = array(
                0 => 7842,
                1 => 7831,
                2 => 8132,
                3 => 8133,
                4 => 8134,
                5 => 8134,
            );
            $no1 = 13410000831;
            $sid = 8131;
            $ivid = 7842;
            $ivc = 2440;
            $isIv = true;
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $noarr[$k];
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        $search = "1341000";
                        $replace = "";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7906;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7906;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13400002416t2439 = false;
        if($u13400002416t2439) {
            $no1 = 13400002416;
            $ivid = 8107;
            $isIv = true;
            for($k = 0; $k < 24;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8107 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 2416+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                $user_array['inviter_id'] = 7393;
                $user_array['pnet_dz'] = 2;
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1340000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13200000651t0665 = false;
        if($u13200000651t0665) {
            $no1 = 13200000651;
            $ivid = 8092;
            $isIv = true;
            for($k = 0; $k < 15;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8092 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 651+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQ2Cn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1320000";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['inviter_id'] = 917;
                    $user_array['ypid'] = 917;
                    $user_array['pnet_dz'] = 2;
                }else if($k==1) {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = 8092;
                    $user_array['pnet_dz'] = 1;
                }else if($k==2) {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = 8092;
                    $user_array['pnet_dz'] = 2;
                }else {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = $user_array['id'] - 2;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $search = "1320000";
                $replace = "YFLL000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000831t0844 = false;
        if($u13410000831t0844) {
            $no1 = 13410000831;
            $ivid = 7906;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8078 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 831+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7906;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7906;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000850t0869 = false;
        if($u13410000850t0869) {
            $no1 = 13410000850;
            $ivid = 7904;
            $isIv = true;
            for($k = 0; $k < 20;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8058 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 850+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7904;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7904;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }

        $u13410000801t0810 = false;
        if($u13410000801t0810) {
            $no1 = 13410000801;
            $ivid = 7854;
            $isIv = true;
            for($k = 0; $k < 10;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8032 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 801+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7854;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7854;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000811t0824 = false;
        if($u13410000811t0824) {
            $no1 = 13410000811;
            $ivid = 7856;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8042 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 811+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7856;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7856;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000825t0826 = false;
        if($u13410000825t0826) {
            $no1 = 13410000825;
            $ivid = 7854;
            $isIv = true;
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8056 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 825+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 8041;
//                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7854;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000751t0791 = false;
        if($u13410000751t0791) {
            $no1 = 13410000751;
            $ivid = 7832;
            $isIv = true;
            for($k = 0; $k < 41;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7991 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 751+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7832;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==15) {
                    $user_array['ypid'] = 7997;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==29) {
                    $user_array['ypid'] = 8007;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k==0) {
                        $user_array['inviter_id'] = 7832;
                    }elseif($k>=15 && $k<29) {
                        $user_array['inviter_id'] = 7997;
                    }elseif($k>=29) {
                        $user_array['inviter_id'] = 8007;
                    }else $user_array['inviter_id'] = 7991;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000642t0643 = false;
        if($u13410000642t0643) {
            $no1 = 13410000642;
            $ivid = 6795;
            $isIv = true;
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7989 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 642+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
//                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                $user_array['ypid'] = 6795;
                $user_array['pnet_dz'] = 2;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000631t0641 = false;
        if($u13410000631t0641) {
            $no1 = 13410000631;
            $ivid = 6315;
            $isIv = true;
            for($k = 0; $k < 11;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7978 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 631+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6315;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==1) {
                    $user_array['inviter_id'] = $user_array['ypid'] = $user_array['id'] - 1;
                    $user_array['pnet_dz'] = 1;
                }else {
                    if($k==2) $user_array['pnet_dz'] = 2;
                    $user_array['inviter_id'] = $user_array['ypid'] = $user_array['id'] - 2;
                    $user_array['pnet_dz'] = 1;
                }
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000601t0630 = false;
        if($u13410000601t0630) {
            $no1 = 13410000601;
            $ivid = 7569;
            $isIv = true;
            for($k = 0; $k < 30;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7948 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 601+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7569;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==15) {
                    $user_array['ypid'] = 7952;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=15) {
                        $user_array['inviter_id'] = 7952;
                    }else $user_array['inviter_id'] = 7569;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 12 && $k != 13 && $k != 13 && $k != 29)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000585t0598 = false;
        if($u13410000585t0598) {
            $no1 = 13410000585;
            $ivid = 7891;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7934 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 585+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7891;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7891;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000571t0584 = false;
        if($u13410000571t0584) {
            $no1 = 13410000571;
            $ivid = 7890;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7920 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 571+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7890;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7890;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000551t0566 = false;
        if($u13410000551t0566) {
            $no1 = 13410000551;
            $ivid = 7618;
            $isIv = true;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7904 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 551+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7618;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7618;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000535t0550 = false;
        if($u13410000535t0550) {
            $no1 = 13410000535;
            $ivid = 7608;
            $isIv = true;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7888 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 535+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                $user_array['is_pw'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7608;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7608;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 3 && $k != 14)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000516t0534 = false;
        if($u13410000516t0534) {
            $no1 = 13410000516;
            $ivid = 7604;
            $isIv = true;
            for($k = 0; $k < 19;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7869 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 516+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7604;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7604;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 17)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000501t0515 = false;
        if($u13410000501t0515) {
            $no1 = 13410000501;
            $ivid = 7590;
            $isIv = true;
            for($k = 0; $k < 15;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7854 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 501+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7590;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7590;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 11)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000384t0394 = false;
        if($u13410000384t0394) {
            $no1 = 13410000384;
            $ivid = 7588;
            $isIv = true;
            for($k = 0; $k < 11;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7843 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 384+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7588;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7588;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 1)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000371t0383 = false;
        if($u13410000371t0383) {
            $no1 = 13410000371;
            $ivid = 7586;
            $isIv = true;
            for($k = 0; $k < 13;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7830 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 371+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 7586;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7586;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 11)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000329t0368 = false;
        if($u13410000329t0368) {
            $no1 = 13410000329;
            $ivid = 6613;
            $isIv = true;
            for($k = 0; $k < 40;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7790 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 329+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6613;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==23) {
                    $user_array['ypid'] = 7794;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=23) {
                        $user_array['inviter_id'] = 7794;
                    }else $user_array['inviter_id'] = 6613;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 6 && $k != 38)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $u13410000289t0328 = false;
        if($u13410000289t0328) {
            $no1 = 13410000289;
            $ivid = 6606;
            $isIv = true;
            for($k = 0; $k < 40;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7750 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 289+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6606;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==24) {
                    $user_array['ypid'] = 7754;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=24) {
                        $user_array['inviter_id'] = 7754;
                    }else $user_array['inviter_id'] = 6606;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 21 && $k != 29 && $k != 41)
                    $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000247t0289 = false;
        if($u13410000247t0289) {
            $no1 = 13410000247;
            $ivid = 6594;
            $isIv = true;
            for($k = 0; $k < 43;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7707 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 247+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6594;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==23) {
                    $user_array['ypid'] = 7711;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=23) {
                        $user_array['inviter_id'] = 7711;
                    }else $user_array['inviter_id'] = 6594;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 21 && $k != 29 && $k != 41) $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000203t0246 = false;
        if($u13410000203t0246) {
            $no1 = 13410000203;
            $ivid = 6133;
            $isIv = true;
            $loop = 0;
            for($k = 0; $k < 44;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7663 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 203+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6133;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==25) {
                    $user_array['ypid'] = 7667;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=25) {
                        $user_array['inviter_id'] = 7667;
                    }else $user_array['inviter_id'] = 6133;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 23 && $k != 42) $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000161t0202 = false;
        if($u13410000161t0202) {
            $no1 = 13410000161;
            $ivid = 6128;
            $isIv = true;
            $loop = 0;
            for($k = 0; $k < 42;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7621 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 161+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6128;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==21) {
                    $user_array['ypid'] = 7625;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=21) {
                        $user_array['inviter_id'] = 7625;
                    }else $user_array['inviter_id'] = 6128;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 19 && $k != 40) $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000042t0083 = false;
        if($u13410000042t0083) {
            $no1 = 13410000042;
            $ivid = 6114;
            $isIv = true;
            $loop = 0;
            for($k = 0; $k < 42;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7579 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 42+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6114;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==21) {
                    $user_array['ypid'] = 7583;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=21) {
                        $user_array['inviter_id'] = 7583;
                    }else $user_array['inviter_id'] = 6114;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 40) $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000006t0041 = false;
        if($u13410000006t0041) {
            $no1 = 13410000006;
            $ivid = 6111;
            $isIv = true;
            $loop = 0;
            for($k = 0; $k < 36;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7543 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 6+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['ypid'] = 6111;
                }elseif($k==20) {
                    $user_array['ypid'] = 7547;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=20) {
                        $user_array['inviter_id'] = 7547;
                        if ($k==20) $user_array['pnet_dz'] = 2;
                    }else $user_array['inviter_id'] = 6111;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 11) $isIv = !$isIv;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000136t0160 = false;
        if($u13410000136t0160) {
            $no1 = 13410000136;
            $ivid = 6591;
            $loop = 0;
            for($k = 0; $k < 25;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7518 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 136+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) $user_array['ypid'] = 6615;
                else $user_array['ypid'] = $user_array['id'] - 1;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13410000106t0130 = false;
        if($u13410000106t0130) {
            $no1 = 13410000106;
            $ivid = 6111;
            $loop = 0;
            for($k = 0; $k < 25;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7493 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 106+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 1;
                if($k==0) $user_array['ypid'] = 6135;
                else $user_array['ypid'] = $user_array['id'] - 1;
                $search = "1341000";
                $replace = "YFL1000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u13400002000 = false;
        if($u13400002000) {
            $no1 = 13400002000;
            $ivid = 528;
            $loop = 0;
            for($k = 0; $k < 1;$k++){
                for ($j = 0; $j < $count; $j++) {
                    echo $j;
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7077 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    echo $user_key[$j];
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHCLJXQC200'.$k.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
//                $user_array['pnet_dz'] = 2;
                $search = "1340000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13400002001 = false;
        if($u13400002001) {
            $no1 = 13400002001;
            $ivid = 530;
            $loop = 0;
            for($k = 0; $k < 1;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7078 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHCLJXQC200'.($k+1).'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
//                $user_array['pnet_dz'] = 2;
                $search = "1340000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13400002002t15 = false;
        if($u13400002002t15) {
            $no1 = 13400002002;
            $ivid = 7078;
            $loop = 0;
            for($k = 0; $k < 14;$k++){
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7079 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        if($k < 8) $user_array[$user_key[$j]] = 'FHCLJXQC200'.($k+2).'M';
                        else $user_array[$user_key[$j]] = 'FHCLJXQC20'.($k+2).'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
//                $user_array['pnet_dz'] = 2;
                $search = "1340000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }

        }
        $u134000020168x50 = false;
        if($u134000020168x50) {
            $no1 = 13400002015;
            $ivid = 7085;
            for($k = 0; $k < 8;$k++){
                for($i = 0; $i < 50;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 7092 + $k*50 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 50 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid + $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            if(($k * 50 + $i + 16) < 100) $user_array[$user_key[$j]] = 'FHCLJXQC20'.($k * 50 + $i + 16).'M';
                            else $user_array[$user_key[$j]] = 'FHCLJXQC2'.($k * 50 + $i + 16).'M';
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    $user_array['vip_status'] = '0';
                    $user_array['vip_level'] = 1;
//                $user_array['pnet_dz'] = 2;
                    $search = "1340000";
                    $replace = "YFL0000";
                    $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }

        $uone = false;
        if($uone) {
            $no1 = 13288803101;
            $ivid = 560;
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7076 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FH31XQA'.$k.'CM';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1328880";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '1';
                $user_array['vip_level'] = 3;
                $search = "1328880";
                $replace = "YFL8000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1325101 = false;
        if($u1325101) {
            $no1 = 13200005151;
            $ivid = 3150;
            for($k = 0; $k < 10;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7066 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FH50XQ5'.$k.'CM';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1320000";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = 2;
                $search = "1320000";
                $replace = "YFL0000";
                $user_array['username'] = str_replace($search, $replace, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
//        partments 4
        $p4lv5t30 = false;
        $p4line8 = false;
        $p4lv1t3 = false;
        $p4lv4 = false;
        $p4lv4t30 = false;
        if($p4line8) {
            $team_array = array(
                '0' => 13000000040,
                '1' => 13000000041,
                '2' => 13000000042,
                '3' => 13000000043,
                '4' => 13000000044,
                '5' => 13000000045,
                '6' => 13000000046,
                '7' => 13000000047,
                '8' => 13000000048
            );
            $loop = 0;
            foreach ($team_array as $k => $v) {
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6066 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $v;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = $loop == 0 ? 153 : $user_array['id'] - 1;
                        $user_array[$user_key[$j]] = $user_array['id'] - 1;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3A00' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1330000";
                        $replace = "133****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        if($p4lv1t3) {
            $no1 = 13400000001;
            $ivid = 6074;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6075 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid+floor($k/2);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3AL0' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($p4lv4) {
            $no1 = 13400000100;
            $ivid = 6081;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6089 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k * 100;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid+floor($k/2);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3ALF0' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($p4lv4t30) {
            $no1 = 13400000100;
            $ivid = 6089;
            for($k = 0; $k < 16;$k++){
                for($i = 0; $i < 30;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 6105 + $k*30 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 100 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FHXQy3ALF30' . $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }
        if($p4lv5t30) {
            $no1 = 13400000100;
            $ivid = 6089;
            for($k = 0; $k < 16;$k++){
                for($i = 0; $i < 30;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 6585 + $k*30 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 100 + 31 + $i;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FHXQy3ALF60' . $k.$j;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    $user_array['vip_status'] = '0';
                    $user_array['vip_level'] = 1;
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }

        $line9 = false;
        $line108 = false;
        $line5400 = false;
        if($line9) {
            $team_array = array(
                '0' => 13000000030,
                '1' => 13000000031,
                '2' => 13000000032,
                '3' => 13000000033,
                '4' => 13000000034,
                '5' => 13000000035
//            ,
//                '6' => 13300000026,
//                '7' => 13300000027,
//                '8' => 13300000028
            );
            $loop = 0;
            foreach ($team_array as $k => $v) {
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6050 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $v;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $loop == 0 ? 153 : $user_array['id'] - 1;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy1A' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1300000";
                        $replace = "130****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        if($line108) {
            $nums = 10;
            $no1 = 13300000000;
            $ivid = 6055;
            for($k = 0; $k < $nums;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6056 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = $no1 + 100*$k;
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = 'FHXQyC' . $k .'M';
                        $user_array[$user_key[$j]] = 'FHXQy1A' . $k .'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1330000";
                        $replace = "133****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1320001";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($line5400) {
            $no1 = 13200000100;
            $ivid = 530;
            for($k = 0; $k < 108;$k++){
                for($i = 0; $i < 50;$i++){
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 650 + $k*50 + $i;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + 100*$k + $i+1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FH'.($k>9?$k:'0'.$k).'XQ' . ($i>9?$i:'0'.$i) .'CM';
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
                            $search = "1320000";
                            $replace = "132****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1320001";
                            $replace = "132****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }

        }
    }

    public function test($auction_id = 1)
    {
        exit;
        foreach(model('app\common\model\User')
//                    ->where('id', 'in', [528,6066,7077])
                    ->where('id', '>', 7076)
                    ->where('id', '<', 7093)
//                    ->where('id', '<', 7493)
                    ->field('id,username,truename,mobile,inviter_id,pid,pnet,is_pw')
                    ->select() as $user){
            echo "ID:".$user->id."==>".$user->username."==>".$user->truename."==>".$user->mobile;
            echo "==>IV:".$user->inviter_id."==>PID:".$user->pid."==>NET:".$user->pnet;
            echo "==>PW:".$user->is_pw;
            echo "\n";
        }
        exit();
        foreach(model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('px_nums', 2)
                    ->order('id', 'asc')->select() as $ut2n){
            $loopm = 1;
            foreach(model('app\admin\model\UserTrident2n')
                        ->where('state', '1')
                        ->where('status', '0')
                        ->where('rid', $ut2n['user_id'])
                        ->order('team_nums desc,id asc')->select() as $ut2n2){
//                if($loopm == 1)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
//                if($loopm == 3)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
//                if($loopm == 2)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);

                if($loopm == 1)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);
                if($loopm == 2)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
                if($loopm == 3)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
                $loopm++;
            }
        }

        exit();
        foreach (model("app\common\model\CurrencyRmbLog")
                     ->where('type', 'subsidy')
                     ->where('money',999)
                     ->order('id', 'desc')
                     ->select() as $cl) {
            $count = model("app\common\model\CurrencyRmbLog")
                ->where('type', 'subsidy')
                ->where('money',999)
                ->where('service_ids',$cl['service_ids'])
                ->order('id', 'desc')
                ->count();
            if($count > 1){
                echo $cl['id']."===>".$cl['user_id']."===>".$cl['service_ids']."\n";
            }
        }
        exit();
        foreach(model('app\api\model\wanlshop\Order')
                    ->where('id', '>', '622')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('state', '<', 7)
                    ->where('statusb', '0')->select() as $order){
            $user = model('app\common\model\User')
                ->where('id', $order['user_id'])
                ->find();
            $u2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $order['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $order['user_id'])
                ->order('id', 'asc')->find();
            $hvn2 = False;
            if($u2info) $hvn2 = True;
            echo 'ID:'.$user['id'].';'.$user['username'].'入网：'.($hvn2?'Y':'N')."\n";
//            if (!$hvn2) $this->calcOrderPayTest($order['id']);
        }

    }

    public function YTest($act){
        exit();
//        SELECT id,username,saas_id,vip_status,vip_level,vip_levelm,inviter_id,manager_id,self_amd0,pid,pnet,pnums1,pnums2,pam1,pam2,pam1n,pam2n FROM `fa_user` WHERE id>100 and id < 200
        $userlist = [102,103,106];
        if($act == 'Clear') $userlist = [101,102,103,106];
        if($act == 'New') model('app\common\model\User')->where('id', 101)->update(['vip_level' => 2]);
        foreach($userlist as $user_id){
            if($act == 'Clear')
                model('app\common\model\User')
                    ->where('id', $user_id)
                    ->update([
                        'vip_level' => 1,
                        'vip_levelm' => 1,
                        'self_amd0' => 0,
                        'pid' => 0,
                        'pnet' => 0,
                        'pam1' => 0,
                        'pam2' => 0,
                        'pam1n' => 0,
                        'pam2n' => 0,
                        'pnums1' => 0,
                        'pnums2' => 0
                    ]);
            if($act == 'New') {
                $this->intoNet2($user_id, '1000000' . $user_id, 100, 1, 2, 1);
                model('app\common\model\User')
                    ->where('id', $user_id)
                    ->update([
                        'vip_level' => 2,
                        'self_amd0' => 100
                    ]);
            }
        }
    }
}
