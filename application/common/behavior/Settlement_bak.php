<?php

namespace app\common\behavior;

use addons\wanlshop\library\WanlPay\WanlPay;
use addons\wanlshop\library\WeixinSdk\Mp;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\Order;
use app\api\model\wanlshop\OrderGoods;
use app\api\model\wanlshop\Pay;
use app\api\model\wanlshop\Shop;
use app\common\enum\VipEnum;
use app\common\model\CurrencyNsLog;
use app\common\model\CurrencyRmbLog;
use app\common\model\LtJs;
use app\common\library\LtEncry;
use app\common\model\LtOrder;
use app\common\model\LtStock;
use app\common\model\LtStockLog;
use app\common\model\User;
use app\index\model\wanlshop\Ninestar;
use EasyWeChat\Factory;
use fast\Date;
use fast\Http;
use fast\Random;
use MongoDB\Driver\Query;
use think\Cache;
use think\Db;
use think\Exception;
use think\Log;

class Settlement
{
    public function __construct()
    {
        $this->merchantId = 6996282976456978432;
        $this->key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
        $this->nottest = true;
        $this->setting = model('app\common\model\Config')->where('group','settlement')->column('name,value');
        $this->lvname = model('app\admin\model\UserVipLevel')->column('id,name');
        $this->lvinfo = model('app\admin\model\UserVipLevel')->column('id,name,bonus,upgrade,share,share1,share2,compound,excitation,profit');
        $this->lvsinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,orderam,discount,upgrade,bonus,share,compound,excitation,profit');
        $this->union_iv = model('app\admin\model\UserVipLevelu')->column('id,iv_rate');
        $this->union_jc = model('app\admin\model\UserVipLevelu')->column('id,jc_rate');
        $this->saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
        $this->lt_shop = 0;
        $this->fxs_rate = array(
            1 => 0,
            2 => 0.5,
            3 => 0.3,
            4 => 0.1,
            5 => 0.1
        );
//        1：消费
//        2：联创
//        3：合伙人
//        4：卓越
        $this->input = 1111;
        $this->suid = 3;
        $this->bonusrate = 0.005;
        $this->fday = 10;
        $this->eday = 1;
        $this->jckh = array(
            0 => 0,
            1 => 100000,
            2 => 1000000
        );
        $this->jc = array(
            0 => 0,
            1 => 0.5,
            2 => 1
        );
        $this->tjkh = array(
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 6,
            7 => 7
        );
        $this->lvkh = array(
            2 => 799,
            3 => 3999,
            4 => 8999
        );

        $this->lvsq = array(
            1 => array(
                's' => 0,
                'x' => 0
            ),
            2 => array(
                's' => 5,
                'x' => 10
            ),
            3 => array(
                's' => 10,
                'x' => 20
            ),
            4 => array(
                's' => 15,
                'x' => 25
            )
        );
        $this->dgstd = array(
            1 => 0,
            2 => 19,
            3 => 80,
            4 => 160,
            5 => 500,
            6 => 1000
        );
        $this->dgm = array(
            1 => 0,
            2 => 10,
            3 => 20,
            4 => 30,
            5 => 40,
            6 => 50
        );
        $this->ustd = array(
            2 => 99,
            3 => 999,
            4 => 9999,
            5 => 19999
        );
        $this->uprate = array(
            2 => 1,
            3 => 2,
            4 => 2.5,
            5 => 3
        );

        $this->tj = array(
            1 => 0.15,
            2 => 0.18
        );

        $this->calcData = array(
            '0' => array(
                'shop_id' => 1,
                'category_id_lv1' => 107,//会员专享
                'category_id_lv2' => 108,//店长专享
                'category_id_lv3' => 228
            )
        );
        $this->ns_rate = 0.28;
        $this->ns_top_std = 2000;
    }


    public function run(&$params)
    {
//        if($params['action'] == 'test'){
//            $this->calcAmountB(129,3999,3618596952981129);
//        }
//        if($params['action'] == 'autoone'){
//
//        }
//        if($params['action'] == 'auto'){
//
//        }
        if(
//            $params['action'] == 'payed' ||
            $params['action'] == 'received'
        ){
            $this->doconfirm($params);
        }
        if($params['action'] == 'certified'){
            $this->certified($params['user_id']);
        }

        if($params['action'] == 'orderPay'){
            $this->calcOrderPay($params['order_id']);
        }

        if($params['action'] == 'orderConfirm'){
            $this->calcOrderConfirm($params['order_id']);
        }

        if($params['action'] == 'orderRefund'){
            $this->calcOrderRefund($params['order_id']);
        }

        if($params['action'] == 'cancelOrder'){
            $this->calcCancelOrder($params['order_id']);
        }

        if($params['action'] == 'ivc'){
            $this->invitationCode($params['user_id']);
        }

        if($params['action'] == 'distributor'){
            $this->CalcDistributor($params['user_id'], $params['amount']);
        }

        if($params['action'] == 'intoNet'){
            $this->intonet($params['user_id'], 'HT', false);
        }
        if($params['action'] == 'wslvUp'){
            $this->wslvUp($params['user_id'], $params['uplv']);
        }

        if($params['action'] == 'autoSync'){
            $this->autoSync();
        }

        if($params['action'] == 'ltOrder'){
            $this->findLtOrder();
        }

        if($params['action'] == 'ltPush'){
            $this->findLtPush();
        }

        if($params['action'] == 'autoSyncIn'){
//            $this->autoSyncIn(8680, 10000);
            $this->autoSyncIn(44036, 44044);
//            $this->autoSyncIn(6066, 6067);
//            $this->autoSyncIn(36158, 36159);
//            $this->autoSyncIn(36607, 36917);
//            $this->autoSyncIn(36917, 37198);
//            $this->autoSyncIn(38431, 38502);
//            $this->autoSyncIn(39311, 39516);
//            $this->autoSyncIn(39516, 39517);
//            幸福家族13567850678
//            $this->autoSyncIn(39685, 40152);
//            阳光家族13857463771
//            $this->autoSyncIn(40152, 40523);
//            爱心家族13175156513
//            $this->autoSyncIn(40523, 40812);
//            黄金家族13968313965
//            $this->autoSyncIn(41019, 41142);
        }

        if($params['action'] == 'userRearrange'){
            $this->userRearrange();
        }

        if($params['action'] == 'daily'){
            // $this->autoCalcDaliy();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $hour = date('H');
            $settletime = model('app\common\model\Settletime')->where('year', $year)->where('month', $month)->where('day', $day)->find();
            if (!$settletime) {
                model('app\common\model\Settletime')->create([
                    'year' => $year,
                    'month' => $month,
                    'day' => $day, // 备注
                    'hour' => $hour
                ]);
                $this->autoCalcDaliy();
//                $this->autoCalcDaliyYY();
            }
        }
        if($params['action'] == 'weekly'){
            $this->autoWeekly();
        }
        if($params['action'] == 'monthly'){
            $this->autoMonthly();
        }
        if($params['action'] == 'sandpay_out'){
//            var_dump($this->checkMerchant());
            $bl = $this->checkBalance($params['gateway']?$params['gateway']:'sandpay');
            if($bl['code'] == 200){
                return $this->OutTransfer($params['ids'], $params['gateway']?$params['gateway']:'sandpay');
            }else{
//                var_dump($bl);
                return $bl;
            }
        }
        if($params['action'] == 'sandpay_bl'){
//            var_dump($this->checkMerchant());
            $gateway = 'yeepay';
            $bl = $this->checkBalance();
//            var_dump($bl);
        }
        if($params['action'] == 'cUsers'){
            $this->cUsers();
        }
        if($params['action'] == 'Test'){
//            $this->calcOrderConfirmDaily();
//            $this->autoSyncTest();
//            $this->test();
//            $userlist = [680,
//681,
//682,
//683,
//684,
//685,
//686,
//687,
//688,
//689,
//690,
//691];
//            var_dump(controller('app\api\controller\exten\UserOrderReq')->registerBatch($userlist));
//            foreach($userlist as $user_id){
//                var_dump(controller('app\api\controller\exten\UserOrderReq')->register($user_id));
//            }
//            $order_info = array();
//            $order_info['user_id'] = 31;
//            $order_info['order_id'] = 0;
//            $order_info['order_no'] = 'HT32';
//            $order_info['price'] = 999;
//            $order_info['number'] = 1;
//            $order_info['category_id'] = 108;
//            $order_info['activity_type'] = 'shopgoods';
//            $user = model('app\common\model\User')->where('id', $order_info['user_id'])->field('saas_id')->find();
//            define('SAAS', $user['saas_id']);
//            $res = controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'confirm');
            exit();
//            var_dump($res);
//            $userlist = [1311];
//            foreach($userlist as $user_id){
//                $user = model('app\common\model\User')->where('id', $user_id)->field('saas_id')->find();
//                define('SAAS', $user['saas_id']);
////                if(!defined(SAAS)){
////
////                }
//                var_dump(controller('app\api\controller\exten\UserOrderReq')->register($user_id));
//            }


//            $this->test();
//            $this->calcOrderPay(3910);
//            $this->wslvUp(139, 3);
//            $this->intonet(168,$order_no='1206242000531168', $calc=true);
//            $this->calcOrderPay(16);
//            $this->shopApply(106);
//            $this->calcOrderConfirm(63);
//            $this->intonet(1,1);
//            $bl = $this->checkBalance('yeepay');
//            var_dump($bl);
//            $msg = $this->OutTransfer(186974, 'yeepay');
//            $msg = $this->checkOrderOut(106078);
//            $msg = $this->checkOrderIn('202301282316531255493153100519');
//            $this->autoCalcDaliyYY();
//            $this->calcOrder(25359);
//             var_dump($msg);
        }
    }

    public function certified($user_id){
        return true;
        //赠送1张优惠券
//        $coupon_id = 4;
        $userinfo = model('app\common\model\User')->where('id', $user_id)->field('saas_id')->find();
//        $coupon_id = $userinfo['saas_id'] + 28;
        $coupon_id = $this->saas_info[$userinfo['saas_id']]['coupon_id'];
        $coupon = model('app\api\model\wanlshop\Coupon')->get($coupon_id);
        if($coupon){
            // 领取优惠券并保留备份
            $result = model('app\api\model\wanlshop\CouponReceive');
            $result->state = 1;
            $result->coupon_id = $coupon_id;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $user_id;
            $result->shop_id = $coupon['shop_id'];
            $result->type = $coupon['type'];
            $result->name = $coupon['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $coupon['price'];
            $result->discount = $coupon['discount'];
            $result->limit = $coupon['limit'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $coupon['validity'];
            $result->startdate = $coupon['startdate'];
            $result->enddate = $coupon['enddate'];
            $result->save();
            if($result){
                if($coupon['grant'] != '-1'){
                    // 剩余数量
                    $data['surplus'] = $coupon['surplus'] - 1;
                    // 即将过期，强制失效
                    if($coupon['surplus'] == 1){
                        $data['invalid'] = 1;
                    }
                }
                $data['alreadygrant'] = $coupon['alreadygrant'] + 1;
                // 更新优惠券领取+1
                $coupon->allowField(true)->save($data);
            }
        }
    }

    public function shopApply($user_id){
        $userinfo = model('app\common\model\User')->where('id', $user_id)->find();
        $shopauth = model('app\api\model\wanlshop\Auth')->where('user_id', $user_id)->find();
        if(!$shopauth){
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['verify'] = '3';
            $shoparr['status'] = 'normal';
            $shoparr['bio'] = $userinfo['username'];

            model('app\api\model\wanlshop\Auth')->insert($shoparr);
        }
        $shopinfo = model('app\api\model\wanlshop\Shop')->where('user_id', $user_id)->find();
        if (!$shopinfo) {
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['shopname'] = $userinfo['username'];
            $shoparr['avatar'] = '';
            $shoparr['bio'] = $userinfo['username'];
            $shoparr['description'] = $userinfo['username'];
            $shoparr['pickup_address'] = '';
            $shoparr['business_hours'] = '';
            $shoparr['city'] = '';
            $shoparr['verify'] = '3';

            model('app\api\model\wanlshop\Shop')->insert($shoparr);
        }
    }

    /**
     * 订单支付
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderPay($order_id)
    {
        $goods_id = 1;
        $goodsnums = 0;
        $amount = 0;
        $points = 0;
        $ns_am = 0;
        $ws_am = 0;
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
//            ->where('shop_id', 1)
            ->where('state', '>', 1)
            ->where('state', '<', 7)
            ->where('statusb', '0')
            ->where('id',$order_id)->find();
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user['saas_id'] > 0){
            $uplv = 0;
            //查询支付记录，是否含快递费用20
            $pay = Pay::where(['order_id'=>$order_id,'pay_state'=>1])->find();
            $freight_price = 20;
            if($pay && $pay['freight_price'] == $freight_price && $pay['price'] == $freight_price){
                // 20快递费
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $user['id'], '购买云店赠送', 'subsidy', $order['order_no'], 'currency_rmb', '0');
            }
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $fwsc = false;
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] || $goodsData['activity_type'] == 'vipgoods' || $goodsData['activity_type'] == 'shopgoods') {
//                if (($goods['shop_id'] - 28) == $user['saas_id'] && $goods['shop_id'] > 29) {
//                if ($goods['shop_id'] == $this->calcData[0]['shop_id']) {
                    $amount = $amount + $goods['price'] * $goods['number'];
                    $goodsnums = $goodsnums + $goods['number'];
                    $activate = false;
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv1'] || $goodsData['activity_type'] == 'vipgoods'){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price']*$goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                        if ($user['vipv_level'] < 2) {
                            $uplv = 2;
                            $activate = true;
                        } else {
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20*$goods['number'], $user['inviter_id'], '推V复购', 'subsidy', $order['order_no'], 'currency_ns', '0');
                        }
                    }
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods'){
                        if($goodsData['gift_type'] == 'rmb') { //  买云店赠送提货券
                            try {
                                // 送两份提货券
                                $nnNum = 2; // 送几倍提货券
                                $giftRmb = $goods['price'] * $goods['number'] * $nnNum;
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($giftRmb, $user['id'], '购买云店赠送', 'subsidy', $order['order_no'], 'currency_rmb', '1');
                                // 自动发货-虚拟发货
                                $this->autoDelivery($order_id);
                            } catch (Exception $e) {
                                $message = $e->getMessage();
                                Log::error("自动发货-虚拟发货 -- error=".$message);
                            }
                        }
                        Log::info("calcOrderPay 订音类型 flq=" . $order['flq']);
                        $goodam = $goods['price'] * $goods['number'];
                        $price = 999;
                        if($goodam % 999 == 0){
                            $nums = floor($goodam / 999);
                        }else if($goodam % 1300 == 0){
                            $price = 1300;
                            $nums = floor($goodam / 1300);
                        }

                        if($user['vip_level'] <= 2) {
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price'] * $goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                            $uplv = 3;
//                            $fwsc = true;
                            $activate = true;
                            //注册用户
//                            $res = controller('app\api\controller\exten\UserOrderReq')->register($user['id']);
                            $order_info = array();
                            $order_info['user_id'] = $user['id'];
                            $order_info['order_id'] = $order['id'];
                            $order_info['order_no'] = $order['order_no'];
                            $order_info['price'] = $price;
                            $order_info['number'] = $nums;
                            $order_info['flq'] = $order['flq'];
                            $order_info['category_id'] = $goodsData['category_id'];
                            $order_info['activity_type'] = $goodsData['activity_type'];
                            controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'newpurchase');
//                        开通店铺
                            $this->shopApply($user['id']);
                        } else {
                            $order_info = array();
                            $order_info['user_id'] = $user['id'];
                            $order_info['order_id'] = $order['id'];
                            $order_info['order_no'] = $order['order_no'];
                            $order_info['price'] = $price;
                            $order_info['number'] = $nums;
                            $order_info['flq'] = $order['flq'];
                            $order_info['category_id'] = $goodsData['category_id'];
                            $order_info['activity_type'] = $goodsData['activity_type'];
                            controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'repurchase');
                        }
                    }
                    if ($uplv > 0){
                        $loops = 0;
                        $userinfo = $user;
                        $team20 = true;
                        while ($userinfo) {
                            $userarr = array();
                            if ($loops == 0) {
//                        $rate = $this->setting['jion_basic_rate'];
//                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($this->lvinfo[$uplv]['bonus'] * $goods['number'], $userinfo['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb');
                                if ($userinfo['vip_status'] == '0') {
                                    $userarr['vip_status'] = '1';
                                }
                                if($uplv == 2){
                                    $userarr['vipv_level'] = $uplv;
                                    $userarr['self_am0'] = $goods['price'] * $goods['number'];
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => 1,
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
                                if($uplv == 3){
//                                    $userarr['monthly_fg'] = 1;
//                                    $userarr['monthly_fgn'] = 1;
                                    $userarr['vip_level'] = $uplv;
                                    $userarr['uptime'] = time();
//                                    $userarr['self_amd0'] = $goods['price'] * $goods['number'];
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => 1,
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
//                                $userarr['self_amt'] = $userinfo['self_amt'] + $goods['price'] * $goods['number'];
                                $year = date('Y');
                                $month = date('m');
                                $day = date('d');
                                model('app\common\model\Settletime')
                                    ->where('year', $year)
                                    ->where('month', $month)
                                    ->where('day', $day)->setInc('revenue'.$uplv,$goods['price'] * $goods['number']);
                            }
                            if ($loops > 0) {
                                if ($loops == 1) {
                                    if ($activate) {
                                        if ($uplv == 2) {
                                            $userarr['invite_nums'] = $userinfo['invite_nums'] + 1;
                                            if ($userarr['invite_nums'] == 3){
                                                $am3 = $userinfo['self_am0'];
                                                $user3 = model('app\common\model\User')
                                                    ->where('inviter_id', $userinfo['id'])
                                                    ->where('self_am0', '>', 0)
                                                    ->field('self_am0')
                                                    ->order('self_am0', 'asc')
                                                    ->find();
                                                if ($user3['self_am0'] < $am3) $am3 = $user3['self_am0'];
                                                if ($am3 > 0) controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am3, $userinfo['id'], '购物返', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                            }
                                            if ($userarr['invite_nums'] > 3){
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20*$goods['number'], $userinfo['id'], '推V购物', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                            }
                                            if($userinfo['vipv_level'] == 2 && $userarr['invite_nums'] >= 9 && $userinfo['team_nums'] >= 99) $userarr['vip_level'] = 3;
                                        }
                                        if ($uplv == 3) {
//                                            $userarr['invites_nums'] = $userinfo['invites_nums'] + 1;
//                                            $userarr['invites_numsw'] = $userinfo['invites_numsw'] + 1;
//                                            if($userinfo['vip_level'] < 5) {
//                                                if ($userinfo['vip_level'] >= 2 && $userarr['invites_nums'] >= 6 && $userinfo['team_nums'] >= 99) {
//                                                    $userarr['vip_level'] == 5;
//                                                    if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
//                                                }
//                                            }
                                        }
                                    }
                                }
                                error_log(date('Y-m-d H:i:s').json_encode($activate,1)."\r\n",3,'qifu.log');
                                if ($activate) {
                                    if ($uplv == 2) {
                                        error_log(date('Y-m-d H:i:s').json_encode($userinfo,1)."\r\n",3,'qifu.log');
                                        $userarr['team_nums'] = $userinfo['team_nums'] + 1;
//                                        if ($userarr['team_nums'] >= 99 && $userinfo['vipv_level'] == 2 && $userinfo['invite_nums'] >= 9){
//                                            $userarr['vip_level'] = 3;
//                                            error_log(date('Y-m-d H:i:s').json_encode($userarr['vip_level'] == 3,1)."\r\n",3,'qifu.log');
//                                            $userarr['uptime'] = time();
//                                            model('app\admin\model\UserUpgradeLog')->create([
//                                                'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
//                                                'pay_user_id' => $userinfo['id'],
//                                                'rec_user_id' => $userinfo['inviter_id'],
//                                                'lv_old' => $userinfo['vip_level'],
//                                                'lv_new' => 3,
//                                                'state' => 1,
//                                                'amount' => 0
//                                            ]);
//                                            $this->intonet($userinfo['id'], 'Auto', false);
//                                        }
//                                        if ($userarr['team_nums'] >= 99 && $userinfo['invite_nums'] >= 6 && $userinfo['vip_level'] == 3){
//                                            $userarr['vip_level'] == 4;
//                                        }
//                                        if($userinfo['vip_level'] < 5) {
//                                            if ($userinfo['vip_level'] >= 2 && $userinfo['invites_nums'] >= 6 && $userarr['team_nums'] >= 99) {
//                                                $userarr['vip_level'] == 5;
//                                                if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
//                                            }
//                                        }
                                        if($team20){
                                            if($userinfo['vip_level'] >= 3) {
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $userinfo['id'], '团队奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                                $team20 = false;
                                            }
                                        }
                                    }
//                                    if ($uplv == 3) $userarr['teams_nums'] = $userinfo['teams_nums'] + 1;
                                }
                            }
                            model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                            $loops++;
                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                        }
                    }
                    if($fwsc){
                        $userinfo = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                        while ($userinfo && $fwsc) {
                            if($userinfo['city_server'] != 0 && $order['flq'] == 2){
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price']*$goods['number']*0.03, $userinfo['id'], '服务商补贴', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                $fwsc = false;
                            }
                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                        }
                    }
                }else if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_ws'] && $goods['shop_id'] > 29) {
                    $ws_am = $amount + $goods['price'] * $goods['number'];
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else if($goodsData['activity_type'] == 'ninestar'){
                    $activity = model('app\admin\model\wanlshop\Ninestar')->where('id', $goodsData['activity_id'])->find();
                    if($activity){
                        $ns_am += $activity['price'] * $goods['number'];
                    }
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else{
                    $amount = $amount + $goods['price'] * $goods['number'];
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_nfr', '0', $goods['goods_id']);
            }

            // 帮卖订单支付后-佣金待结算
            $this->helpSellPayOrder($order_id);
        }
        if ($amount > 0){
            model('app\api\model\wanlshop\Order')->where('id',$order_id)->update(['statusb' => '1']);
        }
        if ($ns_am > 0){
            $this->CalcOrderNinestar($order_id, $ns_am, $amount);
        }
        if($ws_am > 0 && $user['svip_level'] == 1){
            model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->setInc('ws_am', $ws_am);
            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->find();
            if($uulinfo){
                if($uulinfo['ws_am'] > 2900) {
                    model('app\common\model\User')
                        ->where('id', $user['id'])
                        ->update(['svip_level' => 2]);
                }
            }
        }
        //lt模块兼容,查询商品是否为指定店铺
        if($order['shop_id'] && $order['shop_id'] == $this->lt_shop){
            if(!model('app\common\model\LtOrder')->where('order_no',$order['order_no'])->find()){
                $o_goods = model('app\api\model\wanlshop\OrderGoods')->where('order_id', $order_id)->find();
                //写入lt订单，进行后续操作
                model('app\common\model\LtOrder')->insert([
                    'saas_id'=> $order['saas_id'],
                    'user_id'=> $order['user_id'],
                    'order_id'=> $order['id'],
                    'order_no'=> $order['order_no'],
                    'goods_id'=> $o_goods['goods_id'],
                    'sku_id'=> $o_goods['goods_sku_id'],
                    'num'=> $o_goods['number'],
                    'price'=> $o_goods['price'],
                    'total_price'=>round($o_goods['number'] * $o_goods['price'],2)
                ]);
            }
           
            //将订单完成-不发货
            model('app\api\model\wanlshop\Order')->where('id',$order_id)->update(['state' => 6,'remarks'=>'LT订单']);
        }
        //测试秒结
//        $this->autoCalcDaliy(false);
    }


    //查询lt未处理订单
    public function findLtOrder(){
        //进程锁
        $process_key = 'findLtOrder';
        $cache = Cache::store('redis');
        $process_value = $cache->get($process_key);
        if($process_value){
            echo '进程未结束-'.$process_value;die();
        }
        $cache->set($process_key,date('Y-m-d H:i:s'),180);
        $order = LtOrder::where('status',1)->order('id asc')->limit(20)->select();
        Db::startTrans();
        try{
            $find = true;
            $sell_info = [];
            foreach ($order as $k=>$v){
                $sell_info = [];
                $need_num = $v['num'];
                $js_num = 0;
                //查询对应商品是否存在寄售-排除自己的寄售产品
                while($find){
                    //加入乐观锁-同时查询的无法修改
                    $js = model('app\common\model\LtJs')->where(['status'=>['=',2],'user_id'=>['<>',$v['user_id']]])->order('id asc')->lock('lock in share mode')->find();
                    if($js){
                        $surplus_num = $js['valid_num'];
                        if($surplus_num > 0){
                            if($need_num >= $surplus_num){
                                //变更为已售罄
                                $js->sell_num += $js['num'];
                                $js->valid_num -= $js['num'];
                                $js->status = 3;

                                $need_num -= $surplus_num;
                                $num = $surplus_num;
                            }else{
                                $js->sell_num += $need_num;
                                $js->valid_num -= $need_num;

                                $need_num = 0;
                                $num = $need_num;
                            }

                            $sell_info[] = [
                                'js_no'=>$js['js_no'],
                                'user_id'=>$js['user_id'],
                                'stock_id'=>$js['stock_id'],
                                'num'=>$num,
                            ];
                            $js_num += $num;
                        }else{
                            //变更为已售罄
                            $js->valid_num = 0;
                            $js->status = 3;
                        }
                        $js->save();
                        //已找齐，不再查询
                        if($need_num < 1){
                            break;
                        }
                    }else{
                        //后续不再查找
                        $find = false;
                    }
                }
                //存在寄售库存-变更甲乙双方库存信息，并记录
                $arr = ['user_id'=>$v['user_id'],'goods_id'=>$v['goods_id'],'sku_id'=>$v['sku_id'],'price'=>$v['price'],'saas_id'=>'saas_id'];
                //写入对应的用户lt库存数据
                $lt_stock = LtStock::where($arr)->find();
                if(!$lt_stock){
                    $arr['createtime'] = time();
                    $lt_stock['id'] = LtStock::insert($arr,false,true);
                }
                //产生入账记录等
                foreach($sell_info as $vs){
                    LtStock::where('id',$vs['stock_id'])->setInc('sell_num',$num);
                    $log1 = LtStockLog::record(-$vs['num'],$vs['stock_id'],'寄售产品卖出','sell',$v['user_id'],$v['order_no']);
                    if($log1['code'] != 0){
                        throw new \Exception($log1['msg']);
                    }
                    $log2 = LtStockLog::record($vs['num'],$lt_stock['id'],'购入用户寄售商品','pay',$vs['user_id'],$v['order_no']);
                    if($log2['code'] != 0){
                        throw new \Exception($log2['msg']);
                    }
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->money($num*$v['price'], $vs['user_id'], '寄售产品卖出*'.$num, 'pay', $v['order_no']);
                }
                //无寄售，买入系统库存
                if($need_num > 0){
                    $log3 = LtStockLog::record($need_num,$lt_stock['id'],'购入商品','pay',0,$v['order_no']);
                    if($log3['code'] != 0){
                        throw new \Exception($log3['msg']);
                    }
                }
                $v->js_num = $js_num;
                $v->status = 2;//已处理
                $v->save();
            }
            Db::commit();
        }catch(\Exception $e){
            Db::rollback();
            $cache->set($process_key,1,1);
            echo '进程异常-'.$e->getMessage();die();
        }
        //给予1秒缓冲时间
        $cache->set($process_key,1,1);
        echo '处理完成！';die();
    }


    /**
     * 查询未推送数据
     */
    public function findLtPush(){
        //进程锁
        $process_key = 'findLtPush';
        $cache = Cache::store('redis');
        $process_value = $cache->get($process_key);
        if($process_value){
            echo '进程未结束-'.$process_value;die();
        }
        $cache->set($process_key,date('Y-m-d H:i:s'),180);
        $order = LtOrder::where('push_status','<>',2)->order('push_status asc,id asc')->limit(20)->select();
        foreach ($order as $k=>$v){
            $res = LtEncry::fetch([
                'order_sn'=>$v['order_no'],
                'mobile'=>User::where('id',$v['user_id'])->value('mobile'),
                'price'=>round($v['num']*$v['price'],2),
            ]);
            $v->push_status = 3;
            if($res['code'] == 1){
                $v->push_status = 2;//已推送成功
            }
            $v->push_time = date('Y-m-d H:i:s');
            $v->save();
        }
        //给予1秒缓冲时间
        $cache->set($process_key,1,1);
        echo '处理完成！';die();
    }



    /**
     * 确认货
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderConfirm($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans', // 爱多米诺-绿色积分
            6 => 'currency_gfz', // 爱多米诺-绿色积分
        );
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user['saas_id'] > 0) {
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods') {
                    $order_info = array();
                    $order_info['user_id'] = $user['id'];
                    $order_info['order_id'] = $order['id'];
                    $order_info['order_no'] = $order['order_no'];
                    controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'confirm');
                }
            }
        }
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['days' => 1]);
//                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
//                $cl_arr = array();
//                $cl_arr['status'] = '1';
//                $cl_arr['before'] = $user[$mtype];
//                $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                $cl_arr['updatetime'] = strtotime('now');
//                model("app\common\model\Currency{$mtypelog}Log")
//                    ->where('id', $cl['id'])
//                    ->update($cl_arr);
//
//                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }
    }

    // 帮卖-订单支付后操作
    private function helpSellPayOrder($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();

        $goods = null;
        $orderGoods = OrderGoods::where('order_id', $order_id)->find();
        if ($orderGoods) {
            $goods = Goods::withTrashed()->where('id', $orderGoods->goods_id)->find();
        }
        //帮卖活动
        if ($goods && $goods->activity == 1 && $goods->activity_type == 'dragon') {

            $activity = Ninestar::get($goods->activity_id);

            if ($activity && $activity->type == 'dragon') {

                Log::info("helpSell 帮卖订单支付 order_id=$order_id");

                //先查询对应订单的分享记录
                $pay_user = GoodsShare::where(['user_id' => $order['user_id'], 'order_id' => $order['id']])->find();
                if ($pay_user) {
                    //分享记录查询帮卖店铺
                    $help = \app\api\model\wanlshop\HelpSell::where(['shop_id' => $goods->shop_id, 'good_id' => $goods->id, 'user_id' => $pay_user->shop_id])->find();
                    //存在帮卖店铺记录
                    if ($help) {
                        //查询有效支付记录
                        $pay = model('app\api\model\wanlshop\Pay')->where(['pay_state' => 1, 'order_id' => $order_id, 'type' => 'goods'])->find();
                        //反钱给商品店铺的本金-让利 goods->shop_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')
                            ->money(+($pay['price'] * (1 - $activity->pv_value)), $help->shop_id, '买家确认收货', 'pay', $order['order_no']);
                        //给帮卖店上级分额
                        $helpUser = \app\common\model\User::get($help->user_id);
                        $inviter = \app\common\model\User::get($helpUser->inviter_id);
                        if ($inviter) {

                            $helpUid = $help->user_id;
                            $inviterUid = $helpUser->inviter_id;

                            $sub = 0;
                            //普通
                            if ($inviter->vip_level == VipEnum::ORDINARY_STORE) {
                                $sub = 0.3;
                            }
                            // 旗舰店长
                            if ($inviter->self_amd0 == 4995) {
                                $sub = 0.5;
                            }
                            if ($sub) {

                                $commissions = bcmul($activity->commission, $sub, 2);
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($commissions), $helpUser->inviter_id, '帮卖共富奖', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                Log::info("helpSell 帮卖订单支付 sub=$sub, helpUid=$helpUid, inviterUid=$inviterUid commissions=$commissions");
                            }
                        }
                        //给帮卖店反佣金$help->user_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($activity->commission), $help->user_id, '帮卖佣金', 'subsidy', $order['order_no'], 'currency_ns', '0');
                    }
                }
            }
        }
    }

    // 帮卖结算 - 确认后的7天发生结算操作
    private function helpSellSettlement($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('order_no', $order_id)->find();

        $goods = null;
        $orderGoods = OrderGoods::where('order_id', $order['id'])->find();
        if ($orderGoods) {
            $goods = Goods::withTrashed()->where('id', $orderGoods->goods_id)->find();
        }
        //帮卖活动
        if ($goods && $goods->activity == 1 && $goods->activity_type == 'dragon') {
            $activity = Ninestar::get($goods->activity_id);
            if ($activity && $activity->type == 'dragon') {

                //先查询对应订单的分享记录
                $pay_user = GoodsShare::where(['user_id' => $order['user_id'], 'order_id' => $order['id']])->find();
                if ($pay_user) {
                    $backRule = $activity->back_rule;    //帮卖返本规则
                    //先判断分享人是否购买
                    $info = GoodsShare::where([
                        'good_id' => $goods->id,
                        'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                        'user_id' => $pay_user['share_user'], //查询当前订单来源的分享用户
                        'state' => 1,
                        'is_back' => 0,
                    ])->find();
                    if ($info) {
                        //查询分享人分享出去的是否买了，是否满足条件
                        $shares_pay_count = GoodsShare::where([
                            'good_id' => $goods->id,
                            'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                            'share_user' => $pay_user['share_user'], //查询当前订单来源的分享用户
                            'state' => 1,
                            'is_back' => 0,
                        ])->count();
                        if ($shares_pay_count >= $backRule) {
                            $update = ['is_back' => 1];
                            $memo = '帮卖推' . $backRule . '返本';
                            $pay_log = model('\app\api\model\wanlshop\Pay')->where('order_id',$info['order_id'])->where('user_id',$pay_user['share_user'])->where('pay_state',1)->find();
                            $remark = '';
                            if($pay_log){
                                //实际金额大于单价（存在买多个商品）是返一个的价钱
                                $pay_log['actual_payment'] > $goods->price && $pay_log['actual_payment'] = $goods->price;
                                if(in_array($pay_log['pay_type'],[1,2])){//0=余额支付,1=微信支付,2=支付宝支付,3=杉德支付,4=易宝支付,5=易宝支付宝,
                                    $out_refund_no = rand(10,99).date('ymdHis').rand(10000,99999);
                                    if($pay_log['pay_type'] == 1){
                                        $amount = $pay_log['actual_payment']*100;
                                        $wanlpay = new WanlPay('wechat');
                                        $result = Factory::payment($wanlpay->payConfig($order['saas_id']))->refund->byTransactionId($pay_log['trade_no'], $out_refund_no, $pay_log['price']*100, $amount, [
                                            'refund_desc' => $memo,
                                            'notify_url'    => 'http://'.$_SERVER['HTTP_HOST'].'/index/wanlshop/refund/notify',
                                        ]);
                                        $remark = '微信支付，未退款成功，交易号：'.$pay_log['trade_no'];
                                        if ($result['return_code'] != 'SUCCESS' || $result['result_code'] != 'SUCCESS'){
                                            $remark = '微信支付，返本金额已原路退回';
                                            if($result['err_code_des'] != '订单已全额退款'){//兼容已退款订单状态变更
                                                $remark = '微信退款，发生异常，交易号：'.$pay_log['trade_no'].'=>'.$result['err_code_des'];
                                            }
                                        }
                                    }else{
                                        $wanlpay = new WanlPay('alipay');
                                        //先查询一下交易是否已退款
                                        $find = json_decode(\WanlPay\Yansongda\Pay::alipay($wanlpay->payConfig($order['saas_id']))->find(['trade_no'=>$pay_log['trade_no']]),true);
                                        if(array_get($find,'code') != 10000){
                                            $remark = '返本退款查询异常:'.json_encode($find,JSON_UNESCAPED_UNICODE);
                                        }else{
                                            //交易状态：WAIT_BUYER_PAY（交易创建，等待买家付款）、TRADE_CLOSED（未付款交易超时关闭，或支付完成后全额退款）、TRADE_SUCCESS（交易支付成功）、TRADE_FINISHED（交易结束，不可退款）
                                            //交易成功继续执行退款

                                            if($find['trade_status'] == 'TRADE_SUCCESS'){
                                                $data = [
                                                    'out_request_no'=>$out_refund_no,
                                                    'refund_amount'=>round($pay_log['actual_payment'],2),
                                                    'trade_no'=>$pay_log['trade_no'],
                                                    'refund_reason'=>$memo,
                                                ];
                                                $result = json_decode(Pay::alipay($wanlpay->payConfig($order['saas_id'],$pay_log['merchant_id']))->refund($data),true);
                                                if(array_get($result,'code') != 10000){
                                                    $remark = '返本退款异常:'.json_encode($result,JSON_UNESCAPED_UNICODE);
                                                }
                                            }else if($find['trade_status'] == 'TRADE_CLOSED'){
                                                //已退款，默认执行后续操作
                                                $remark = '原订单已退款，无法重复返本退款';
                                            }else{
                                                $remark = '未查询到有效付款记录无法返本退款:'.json_encode($find,JSON_UNESCAPED_UNICODE);
                                            }
                                        }
                                    }
                                }else{
                                    $remark = '返本金额到余额';
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($pay_log['actual_payment']),  $pay_user['share_user'], $memo, 'subsidy', $order['order_no'], 'currency_bd');
                                    // controller('addons\wanlshop\library\WanlPay\WanlPay')
                                    //     ->money(+($pay_log['actual_payment']), $pay_user['share_user'], $memo, 'subsidy', $order['order_no']);
                                }
                            }
                            $remark && $update['remark'] = $remark;
                            GoodsShare::where('id', $info['id'])->update($update);
                        }
                    }
                }
            }
        }
    }

    public function calcOrderConfirmDaily(){
        $actDo = true;
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans', // 爱多米诺-绿色积分
            6 => 'currency_gfz', // 共富值
        );
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('type', 'subsidy')
                         ->where('status','0')
                         ->where('days', '>', 0)
                         ->where('days', '<', 7)
                         ->order('id', 'desc')
                         ->select() as $cl){
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->field($mtype)->find();
//                echo "ID:$cl->id,UserID:$cl->user_id,OrderNO:$cl->service_ids\n";
                if($actDo && $user) {
                    $cl_arr = array();
                    $cl_arr['days'] = $cl['days'] + 1;
                    if ($cl_arr['days'] >= 7) {
                        $cl_arr['status'] = '1';
                        $cl_arr['before'] = $user[$mtype];
                        $cl_arr['after'] = $user[$mtype] + $cl['money'];
                        $cl_arr['updatetime'] = strtotime('now');
                        model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);

                        // 帮卖结算
                        $order_id = $cl['service_ids'];
                        $this->helpSellSettlement($order_id);
                    }
                    model("app\common\model\Currency{$mtypelog}Log")
                        ->where('id', $cl['id'])
                        ->update($cl_arr);
                }
            }
        }
    }

    public function calcCancelOrder($order_id) {
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();
        //取消订单可能退回的券-积分等
        $mtype_arr = array(
            'currency_rmb',
            'currency_flq',
            'currency_ns',
            'currency_cny',
        );
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            $cl = model("app\common\model\Currency{$mtypelog}Log")
                ->where('service_ids', $order['order_no'])
                ->where('type', 'pay')
                ->find();
            if($cl){
                // 归还提货券,福利券等
                $money = abs($cl['money']);
                Log::info("cancelOrder: oid=$order_id,mtype=$mtype, money=$money");
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($money), $cl['user_id'], '取消支付归还', 'refund', $order['order_no'], $mtype, '1');
            }
        }
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user['saas_id'] > 0) {
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods') {
                    $order_info = array();
                    $order_info['user_id'] = $user['id'];
                    $order_info['order_id'] = $order['id'];
                    $order_info['order_no'] = $order['order_no'];
                    controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'cancel');
                }
            }
        }
        Log::info("cancelOrder: oid=$order_id");
    }

    public function calcOrderRefund($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans',
            6 => 'currency_flq',
        );

        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
            }
            //支付券退回
            $cl = model("app\common\model\Currency{$mtypelog}Log")
                ->where('service_ids', $order['order_no'])
                ->where('type', 'pay')
                ->find();
            if($cl){
                // 归还提货券,福利券等
                $money = abs($cl['money']);
                Log::info("orderRefund: oid=$order_id,mtype=$mtype, money=$money");
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($money), $cl['user_id'], '退款后归还', 'refund', $order['order_no'], $mtype, '1');
            }
        }
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
            if ($user['saas_id'] > 0) {
                foreach (model('app\api\model\wanlshop\OrderGoods')
                             ->where('order_id', $order_id)
                             ->select() as $goods) {
                    $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods') {
                        $order_info = array();
                        $order_info['user_id'] = $user['id'];
                        $order_info['order_id'] = $order['id'];
                        $order_info['order_no'] = $order['order_no'];
                        controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'refund');
                    }
                }
            }
    }

    public function CalcOrderNinestar($order_id, $ns_amt, $amount){
        //首月不考核
        $ns_am = $ns_amt * $this->ns_rate;
        if ($ns_am > 0){
            $order = model('app\api\model\wanlshop\Order')->where('id',$order_id)->find();
            if ($order){
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星订单C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                $loops = 0;
                $nsactivate = false;
                $userr = model('app\common\model\User')->where('id', $order['user_id'])->find();
                if ($userr['saas_id'] > 0){
                    $monthly_ns = $userr['monthly_nsn'] + 1;
                    while($monthly_ns > 9) {
                        $monthly_ns = $monthly_ns - 10;
                    }
                    $nsdo = true;
                    $nsiv = false;
                    while($userr){
                        if ($loops == 0){
                            model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_nsn', 1);
                            $ns_ac = array();
                            if($userr['ns_status'] == '0') {
                                $ns_ac['ns_status'] = 1;
                                $ns_ac['monthly_ns'] = 9;
                                $nsactivate = true;
                            }
                            $ns_ac['ns_pay'] = $userr['ns_pay'] + $amount;
                            if($ns_ac['ns_pay'] >= 500){
                                $fhq = floor($ns_ac['ns_pay']/500);
                                $ns_ac['ns_pay'] = $ns_ac['ns_pay'] - $fhq * 500;
                                $bs = 1;
                                if($userr['vip_level'] == 2) {
                                    $bs = 2;
                                    if($userr['invite_nums'] >= 99) $bs = 5;//超级会员
                                }
                                if($bs < 5) {
                                    if ($userr['vip_level'] > 2) $bs = 5;
                                }
                                if($bs < 5){
                                    if ($userr['monthly_ns'] >= 9 && (($userr['ns_status'] == '1' && $userr['invite_numsns'] >= 9) || ($userr['vip_level'] >= 2 && $userr['invite_nums'] >= 3))) $bs = 3;
                                }
                                $ns_ac['ns_fhq'] = $userr['ns_fhq'] + $fhq*$bs;
                            }
                            model('app\common\model\User')->where('id', $userr['id'])->update($ns_ac);
                            if($userr['monthly_nsn'] > 9 && $monthly_ns == 0){
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                            }
                        }else if ($loops > 0){
                            if($nsactivate) {
                                if ($loops == 1) {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('invite_numsns', 1);
                                    $userr['invite_numsns'] += 1;
                                } else {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('team_numsns', 1);
                                }
                            }
                            if($nsiv){
                                if(($userr['vip_level'] > 2 && $userr['invite_nums'] >= 9) || ($userr['vip_level']==2 && $userr['invite_nums'] >= 9 && $userr['team_nums'] >= 99)) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, $userr['id'], '九星邀请', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am * 0.5);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am * 0.5);
                                }
                                $nsdo = false;
                                $nsiv = false;
                            }
                            if ( $loops == $monthly_ns && $userr['monthly_ns'] >= 9 && (($userr['ns_status'] == '1' && $userr['invite_numsns'] >= 9) || ($userr['vip_level']>=2 && $userr['invite_nums'] >= 3)) ){
                                $ft = true;
                                if($userr['vip_level'] >= 3) $ft = false;
                                if($userr['team_nums'] >= 99) {
                                    $lastdata = model('addons\signin\model\Signin')->where('user_id', $userr['id'])->order('createtime', 'desc')->find();
                                    $successions = $lastdata && $lastdata['createtime'] > Date::unixtime('day', -1) ? $lastdata['successions'] : 0;
                                    if($successions >= 15) $ft = false;
                                }
                                if($ft) {
                                    if ($userr['monthly_ns_am'] < $this->ns_top_std) {
                                        if (($userr['monthly_ns_am'] + $ns_am) >= $this->ns_top_std)
                                            $ns_am = $this->ns_top_std - $userr['monthly_ns_am'];
                                    }
                                }
                                if($ns_am > 0) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, $userr['id'], '九星奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am);
                                    $nsdo = false;
                                    $nsiv = true;
                                }
                            }
                        }
                        $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                        $loops++;
                    }
                    if ($nsdo){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                        $nsiv = true;
                    }
                    if($nsiv){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星邀请C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                    }
                }
            }
            model('app\api\model\wanlshop\Pay')->where('order_id', $order_id)->update(['commission'=>$ns_amt]);
        }
        //
    }

    public function intonet($uid,$order_no='', $calc=true, $nums = 1){
        $cx_check = false;
//        判断是否已经入网
        $uinfo = model('app\common\model\User')->where('id', $uid)->find();
        $u2info = model('app\admin\model\UserTrident2n')
            ->where('saas_id', $uinfo['saas_id'])
            ->where('state', '1')
            ->where('status', '0')
            ->where('user_id', $uinfo['id'])
            ->order('id', 'asc')->find();
        if(!$u2info) {
//        寻找上级
            $frc = true;
            $rid = $uinfo['inviter_id'];
            $parent_code = '';
            while ($frc && $rid > 1) {
                $rinfo = model('app\common\model\User')->where('id', $rid)->find();
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('user_id', $rinfo['id'])
                    ->order('id', 'asc')->find();
                if ($r2info) {
                    $frc = false;
                    $parent_code = $r2info['parent_code'] . "$rid,";
                }else{
                    $rid = $rinfo['inviter_id'];
                }
            }
            $count = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('rid', $rid)
                ->count();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $market = $count + 1;
            $narr = array();
            $narr['saas_id'] = $uinfo['saas_id'];
            $narr['user_id'] = $uid;
            $narr['rid'] = $rid;
            $narr['market'] = $market;
            $narr['market_lw'] = $market;
            $narr['market_kh'] = $market;
            $narr['parent_code'] = $parent_code;
            $narr['is_calc'] = $calc?1:0;
//            $createtime = strtotime($year.'-'.$month.'-'.$day);
            $createtime = strtotime('now');
            $narr['createtime'] = $createtime;
            model('app\admin\model\UserTrident2n')->insert($narr);

            if (!$calc) {
                model('app\admin\model\User')->where('id', $uid)->update(['monthly_fg' => 1, 'monthly_fgn' => 1]);
//                return 0;
            }
            $r2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $rid)
                ->order('id', 'asc')->find();
            $loops = 1;
            $tmarket = $market;
            $f2n = true;
            $d2n = false;
            $zy2n = true;
            while($r2info){
                if ($loops == 1){
                    if($tmarket < 3){
                        $mt = 200*$nums;
                        $f2n = true;
                    }else{
                        $mt = 400*$nums;
                        $f2n = false;
                    }
                    model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('invite_nums', 1);
                    if($r2info['status'] == '0' && $calc) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_ns', '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_xnb', '0');
                    }
                    if($tmarket == 3 && $calc) {
                        if($cx_check) {
                            if(strtotime('now') - $r2info['createtime'] < 172800) {
                                $r2u = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
                                $cx_am = 999;
                                if($r2u['self_amd0'] >= 4995 && $nums >= 9) {
                                    $cx_am = 4995;
                                    foreach(model('app\admin\model\UserTrident2n')
                                                ->where('saas_id', $r2info['saas_id'])
                                                ->where('state', '1')
                                                ->where('status', '0')
                                                ->where('user_id', $r2info['user_id'])
                                                ->where('market_kh', '<', 3)
                                                ->order('id', 'asc') as $r2t){
                                        $r2tu = model('app\common\model\User')->where('id', $r2t['user_id'])->find();
                                        if($r2tu['self_amd0'] < 4995) $cx_am = 999;
                                    }
                                }
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($cx_am, $r2info['user_id'], '健康大礼包', 'subsidy', $order_no, 'currency_rmb', '0');
                            }
                        }
                    }
                }else{
                    if ($f2n){
                        if ($tmarket > 2) {
                            $f2n = false;
                            $d2n = true;
                        }
                    }
                }
                if($tmarket < 3){
                    $r2_arr = array();
                    if($r2info['team_add'] == 0) $r2_arr['team_add'] = $tmarket;
                    else if($r2info['team_add'] != $tmarket) $r2_arr['team_add'] = 3;
                    if (array_key_exists('team_add', $r2_arr)) {
                        $r2info['team_add'] = $r2_arr['team_add'];
                        model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->update(['team_add' => $r2_arr['team_add']]);
                    }
                }
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('team_nums', 1);
//                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('month_nums', 1);
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('week_nums', 1);
                if($d2n && $calc){
                    $mp = 200*$nums;
                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['invites_numsw'] > 1) $mp += 100;
                    if($rinfo['is_distributor'] == 1) $mp += 200*$nums;
                    elseif($rinfo['svip_level'] > 1) $mp += 200*$nums;
                    elseif($rinfo['self_amd0'] == 4995) $mp += 200*$nums;
//                    if($r2info['month'] > 1 && $r2info['month_kh'] == '0') $mp -= 50;
                    if($r2info['status'] == '0') {
                        $rid = $r2info['rid'];
                        $userId = $r2info['user_id'];
                        $mpPrice = $mp * 0.1;
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $userId, '首购共富奖', 'subsidy', $order_no, 'currency_ns', '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $userId, '首购共富奖', 'subsidy', $order_no, 'currency_xnb', '0');
//                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp*0.1, $userId, '伯乐奖', 'subsidy', $order_no, 'currency_ns', '0');
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mpPrice, $rid, '共富值', 'subsidy', $order_no, 'currency_gfz', '0');
                        Log::info("wanlPayCurrency 添加-共富值 userId=$userId rid=$rid order_no=$order_no ".$mpPrice);
                    }
//                    if($rinfo['invites_nums'] >= 6 && $rinfo['team_nums'] >= 99 && $rinfo['svip_level'] > 1)
                    $d2n = false;
                }
//                if($zy2n){
//                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['vip_level'] == 5) {
//                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $r2info['user_id'], '店长卓越', 'subsidy', $order_no, 'currency_rmb', '0');
//                        $zy2n = false;
//                    }
//                }
//                if($tmarket == 2) model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('week_nums2', 1);
                $tmarket = $r2info['market_kh'];
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('saas_id', $uinfo['saas_id'])
                    ->where('state', '1')
//                    ->where('status', '0')
                    ->where('user_id', $r2info['rid'])
                    ->order('id', 'asc')->find();
                $loops++;
            }
            if ($calc) controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100*$nums, 4, '云店分红', 'subsidy', $order_no, 'currency_ns', 0);

            return true;
        }

        return false;
    }

    public function CalcDistributor($user_id, $amount = 3900){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        if ($user['is_distributor'] != 1){
            $loops = 0;
            $userinfo = $user;
            $toplv = 0;
            while ($userinfo) {
                $userarr = array();
                if ($loops == 0) {
                    $userarr['is_distributor'] = 1;
                    $userarr['vipd_level'] = 1;
                    $userarr['top_lvd'] = 1;
                    $toplv = 1;
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($amount, $userinfo['id'], '分销商赠送', 'subsidy', 'FXS', 'currency_rmb');
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, 3, '分销商分红', 'subsidy', 'FXS', 'currency_ns');
                }
                if ($loops > 0) {
                    if ($loops == 1) {
                        $userarr['invited_nums'] = $userinfo['invited_nums'] + 1;
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $userinfo['id'], '推荐分销商', 'subsidy', 'FXS', 'currency_ns');
                    }
                    $userarr['teamd_nums'] = $userinfo['teamd_nums'] + 1;

                    if($userinfo['vipd_level'] == 1){
                        if($userarr['teamd_nums'] > 99) {
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userinfo['id'])
                                ->where('teams_nums', '>', 101)
                                ->count();
                            if ($count > 2) {
                                $userarr['vipd_level'] = 2;
                                if($toplv == 1) $toplv = 2;
                            }
                        }
                    }
                    foreach($this->fxs_rate as $k=>$v){
                        if($k > 2) {
                            if ($userinfo['vipd_level'] == $k - 1) {
                                $count = model('app\common\model\User')
                                    ->where('inviter_id', $userinfo['id'])
                                    ->where('top_lvd', '>=', $k - 1)
                                    ->count();
                                if ($count > 2) {
                                    $userarr['vipd_level'] = $k;
                                    if ($toplv == $k-1) $toplv = $k;
                                }
                            }
                        }
                    }
                    if($toplv > $userinfo['top_lvd']) $userarr['top_lvd'] = $toplv;
                }
                model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                $loops++;
                $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
            }
            $dist_address = explode("/", $user['dist_address']);

            $is_area = $dist_address[2];
            $is_city = $dist_address[1];

            if($is_area != ''){
                $userservice = model('app\common\model\User')->where('city_server', '1')->where('city_address', 'like','%/'.$is_area)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(200, $userservice['id'], '县分销商', 'subsidy', 'FXS', 'currency_ns');
                    $is_area = '';
                }
            }
            if($is_city != ''){

                $userservice = model('app\common\model\User')->where('city_server', '1')->where('city_address', 'like', '%/'.$is_city)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(100, $userservice['id'], '市分销商', 'subsidy', 'FXS', 'currency_ns');
                    $is_city = '';
                }
            }
        }
        return true;
    }

    //云店代理商
    public function CalcDistributorYDFXS($user_id, $order_id, $activate, $nums){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        if ($user['is_distributor'] != 1){
            $loops = 0;
            $userinfo = $user;
            $toplv = 0;
            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, 3, '分销商分红', 'subsidy', 'HHR', 'currency_ns');
            if($activate == 1) {
                while ($userinfo) {
                    $userarr = array();
                    if ($loops == 0) {
                        $userarr['is_distributor'] = 1;
                        $userarr['vipd_level'] = 1;
                        $userarr['top_lvd'] = 1;
                        $toplv = 1;
                    }
                    if ($loops > 0) {
                        if ($loops == 1) {
                            $userarr['invited_nums'] = $userinfo['invited_nums'] + 1;
                        }
                        $userarr['teamd_nums'] = $userinfo['teamd_nums'] + 1;

                        if ($userinfo['vipd_level'] == 1) {
                            if ($userarr['teamd_nums'] > 99) {
                                $count = model('app\common\model\User')
                                    ->where('inviter_id', $userinfo['id'])
                                    ->where('teams_nums', '>', 99)
                                    ->count();
                                if ($count > 2) {
                                    $userarr['vipd_level'] = 2;
                                    if ($toplv == 1) $toplv = 2;
                                }
                            }
                        }
                        foreach ($this->fxs_rate as $k => $v) {
                            if ($k > 2) {
                                if ($userinfo['vipd_level'] == $k - 1) {
                                    $count = model('app\common\model\User')
                                        ->where('inviter_id', $userinfo['id'])
                                        ->where('top_lvd', '>=', $k - 1)
                                        ->count();
                                    if ($count > 2) {
                                        $userarr['vipd_level'] = $k;
                                        if ($toplv == $k - 1) $toplv = $k;
                                    }
                                }
                            }
                        }
                        if ($toplv > $userinfo['top_lvd']) $userarr['top_lvd'] = $toplv;
                    }
                    model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                    $loops++;
                    $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                }
            }

            $orderAddr = model('app\index\model\wanlshop\OrderAddress')->get(['order_id' => $order_id]);
            $dist_address = explode("/", $orderAddr['address']);

            $is_area = $dist_address[2];
            $is_city = $dist_address[1];
            $is_province = $dist_address[0];

            if($is_area != ''){
                $userservice = model('app\common\model\User')->where('city_server', '2')->where('city_address', 'like','%/'.$is_area)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(30*$nums, $userservice['id'], '县分销商', 'subsidy', 'FXS', 'currency_ns');
                    $is_area = '';
                }
            }
            if($is_city != ''){
                $userservice = model('app\common\model\User')->where('city_server', '3')->where('city_address', 'like', '%/'.$is_city)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(10*$nums, $userservice['id'], '市分销商', 'subsidy', 'FXS', 'currency_ns');
                    $is_city = '';
                }
            }
            if($is_province != ''){
                $userservice = model('app\common\model\User')->where('city_server', '4')->where('city_address', 'like', '%/'.$is_province)->find();
                if($userservice){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(5*$nums, $userservice['id'], '省分销商', 'subsidy', 'FXS', 'currency_ns');
                    $is_province = '';
                }
            }
        }
        return true;
    }

    public function autoSync(){
        if($this->setting['is_sync_runing'] == 1){
            echo "未开启或有运行中的进程\n";
            return false;
        }
        model('app\common\model\Config')->where('name','is_sync_runing')->update(['value' => 1]);
//        autoSync User
        $autoUser = true;
        $autoOrder = true;
        $timeline = strtotime("-1 minute");
        $user_nums_max = 20;
        if($autoUser){
            $loop = 1;
            $users = array();
            $sync_modify = true;
            echo "From userId:".$this->setting['sync_user_id']."\n";
            foreach(model('app\common\model\User')
                        ->where('form_app', NULL)
                        ->where('createtime', '<', $timeline)
                        ->where('id', '>=', $this->setting['sync_user_id'])
//                        ->where('id', '<', 40)
                        ->order('id', 'asc')
                        ->select() as $user){
                if($user->id_yct == 0) {
                    $users[$user->id] = $user->id;
                    if($sync_modify) {
                        $sync_modify = false;
                        if($this->setting['sync_user_id'] < $user->id)
                            model('app\common\model\Config')->where('name','sync_user_id')->update(['value' => $user->id]);
                    }
                    $loop++;
                    if($loop > $user_nums_max) break;
                }
            }
            var_dump(count($users));
//            var_dump($users);
            if(!empty($users)) {
                $res = controller('app\api\controller\exten\UserOrderReq')->registerBatch($users);
                var_dump($res);
            }
        }

        if($autoOrder) {
            $loop = 0;
            $sync_order_id = $this->setting['sync_order_id'];
            $timelineend = strtotime("-2 day");
            $sync_modify = true;
            echo "From orderId:".$this->setting['sync_order_id']."\n";
            foreach (model('app\api\model\wanlshop\Order')
                         ->where('status', 'normal')
                         ->where('deletetime', null)
//                         ->where('paymenttime', '<>', null)
//                         ->whereTime('paymenttime', 'today')
                         ->where('paymenttime', '>', 0)
                         ->where('paymenttime', '<', $timeline)
                         ->where('submit', 'not in', ['newpurchase_done', 'repurchase_done', 'confirm_done', 'cancel_done', 'refund_done', 'noneed'])
                         ->where('state', '>', 1)
                         ->where('id', '>', $this->setting['sync_order_id'])
                         ->where('statusb', '1')
                         ->order('id asc')->select() as $order) {
                $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
                if ($user['saas_id'] > 0){
                    foreach (model('app\api\model\wanlshop\OrderGoods')
                                 ->where('order_id', $order['id'])
                                 ->select() as $goods) {
                        $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                        if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] || $goodsData['activity_type'] == 'vipgoods' || $goodsData['activity_type'] == 'shopgoods') {
                            if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods'){
                                $goodam = $goods['price'] * $goods['number'];
                                $price = 999;
                                if($goodam % 999 == 0){
                                    $nums = floor($goodam / 999);
                                }else if($goodam % 1300 == 0){
                                    $price = 1300;
                                    $nums = floor($goodam / 1300);
                                }
                                $order_info = array();
                                $order_info['user_id'] = $user['id'];
                                $order_info['order_id'] = $order['id'];
                                $order_info['order_no'] = $order['order_no'];
                                $order_info['price'] = $price;
                                $order_info['number'] = $nums;
                                $order_info['flq'] = $order['flq'];
                                $order_info['category_id'] = $goodsData['category_id'];
                                $order_info['activity_type'] = $goodsData['activity_type'];
                                $res = controller('app\api\controller\exten\UserOrderReq')->order($order_info, $user['vip_level'] < 3?'newpurchase':'repurchase');
                                $sync_modify = false;
                                sleep(5);
                                $res['order_id'] = $order_info['order_id'];
                                var_dump($res);
//                                if($loop == 0)
//                                    if($res['code'] == 200)
//                                        if($res['data'] && $res['data']['order'] && $res['data']['order']['order'] && $res['data']['order']['order']['order_id'] && $res['data']['order']['order']['order_id'] > 0 )
//                                            $sync_order_id = $order['id']-1;
                                $loop++;
                            }else{
                                Order::where('id', $order['id'])->update(['submit' => 'noneed']);
                                if($sync_modify){
                                    if($order['paymenttime'] > $timelineend) $sync_modify = false;
                                    else $sync_order_id = $order['id'];
                                }
                            }
                        }
                    }
                }
            }
            echo $loop."\n";
            if($sync_order_id > $this->setting['sync_order_id']) model('app\common\model\Config')->where('name','sync_order_id')->update(['value' => $sync_order_id]);
        }
        model('app\common\model\Config')->where('name','is_sync_runing')->update(['value' => 0]);
    }



    public function autoSyncTest(){

        $autoUser = true;
        $timeline = strtotime("-1 minute");
        $user_nums_max = 20;
        if($autoUser){
            $loop = 1;
            $users = array();
            $sync_modify = true;
            echo "From userId:".$this->setting['sync_user_id']."\n";
            foreach(model('app\common\model\User')
                        ->where('form_app', NULL)
                        ->where('createtime', '<', $timeline)
                        ->where('id', '>=', $this->setting['sync_user_id'])
//                        ->where('id', '<', 40)
                        ->order('id', 'asc')
                        ->select() as $user){
                if($user->id_yct == 0) {
                    $users[$user->id] = $user->id;
                    if($sync_modify) {
                        $sync_modify = false;
                        if($this->setting['sync_user_id'] < $user->id)
                            model('app\common\model\Config')->where('name','sync_user_id')->update(['value' => $user->id]);
                    }
                    $loop++;
                    if($loop > $user_nums_max) break;
                }
            }
            var_dump(count($users));
            var_dump($users);
//            if(!empty($users)) {
//                $res = controller('app\api\controller\exten\UserOrderReq')->registerBatch($users);
//                var_dump($res);
//            }
        }
    }

    public function autoSyncIn($idF, $idE){
        $do = true;
        while($do) {
            $do = false;
            foreach (model('app\common\model\User')
//                         ->where('form_app', 'FHOLD')
//                         ->where('id_yct', 0)
                         ->where('id', '>=', $idF)
                         ->where('id', '<', $idE)
                         ->where('vip_level', '>',0)
                         ->order('id', 'asc')
                         ->select() as $user) {
                $do = true;
//            $users = array();
//            if($user->id_yct == 0) {
//                $users[$user->id] = $user->id;
//                if($sync_modify) {
//                    $sync_modify = false;
//                    if($this->setting['sync_user_id'] < $user->id)
//                        model('app\common\model\Config')->where('name','sync_user_id')->update(['value' => $user->id]);
//                }
//            }
//            if(!empty($users)) $res = controller('app\api\controller\exten\UserOrderReq')->registerBatch($users);
//
//            var_dump($res);
//            if($res['code'] != 200) exit();

                echo $user->id . "===>" . $user->pnet_dz . "===>";
                $order_info = array();
                $order_info['user_id'] = $user->id;
                $order_info['userinfo'] = $user->toArray();
                $order_info['order_id'] = 0;
                $order_info['order_no'] = 'HT' . $user->username;
                $order_info['price'] = 1300;
//                $order_info['number'] = floor($user->self_amd0 / 999);
                $order_info['number'] = 10;
                $order_info['category_id'] = 108;
                $order_info['activity_type'] = 'shopgoods';
                $res = controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'emptyorder');
                var_dump($res);
                sleep(2);
                if ($res['code'] == 200) echo 'success';
                else {
                    echo 'error';
                    break;
                }
//                sleep(5);
//                exit;
            }
        }
    }

    public function userRearrange(){
        $user = model('app\common\model\User')
            ->where('is_pw', '>', 0)
            ->where('is_pw', '<', 3)
            ->order('id', 'asc')
            ->find();
        $loop = 1;
        while($user){
            model('app\common\model\User')->where('id',$user->id)->update(['is_pw'=>2]);
            var_dump(controller('app\api\controller\exten\UserOrderReq')->syncUserInfoBatch(array('0'=>$user->id)));
            echo $user->pw_no.'===>'.$user->id."===>";
            echo "$loop\n";
//            exit;
            $loop++;
            $user = model('app\common\model\User')
                ->where('is_pw', '>', 0)
                ->where('is_pw', '<', 3)
                ->order('id', 'asc')
                ->find();
        }
    }

    public function autoCalcDaliy($daily = true)
    {
        $this->calcOrderConfirmDaily();
    }

    public function autoWeekly(){
        $weekly_ns = true;
        $weekly_4995 = true;
        if(date("w") == 1){
            foreach(model('app\admin\model\UserTrident2n')
                        ->where('state', '1')
                        ->where('status', '0')
                        ->order('id', 'asc')->select() as $ut2n){
                $loopm = 1;
                $week_nums2 = 0;
                foreach(model('app\admin\model\UserTrident2n')
                            ->where('state', '1')
                            ->where('status', '0')
                            ->where('rid', $ut2n['user_id'])
                            ->order('team_nums desc,id asc')->select() as $ut2n2){
                    $ut2_arr = array();
                    $ut2_arr['week_nums'] = 0;
                    $ut2_arr['weekl_nums'] = $ut2n2['week_nums'];
                    $ut2_arr['market_lw'] = $ut2n2['market_kh'];
                    $ut2_arr['market_kh'] = $loopm;
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update($ut2_arr);
                    if ($loopm > 1 && $ut2n2['week_nums'] > $week_nums2) $week_nums2 = $ut2n2['week_nums'];
                    $loopm++;
                }
                model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->update(['week_nums2' => $week_nums2]);
                if($week_nums2 > 2){
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->setInc('px_nums', 1);
                    if($ut2n['px_nums'] >= 0){
                        $ys = ($ut2n['px_nums']+1) % 3;
                        if($ys == 1){
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 1)->update(['market_kh' => 0]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 3)->update(['market_kh' => 1]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 2)->update(['market_kh' => 3]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 0)->update(['market_kh' => 2]);
                        }
                        if($ys == 2){
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 1)->update(['market_kh' => 0]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 2)->update(['market_kh' => 1]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 3)->update(['market_kh' => 2]);
                            model('app\admin\model\UserTrident2n')->where('rid', $ut2n['user_id'])->where('market_kh', 0)->update(['market_kh' => 3]);
                        }
                    }
                }else{
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->update(['px_nums' => 0]);
                }
            }
            if($weekly_ns){
                $user1 = model('app\common\model\User')->where('id', 1)->field('currency_ns')->find();
                $amount = $user1['currency_ns'];
                $fhqt = 0;
                foreach(model('app\common\model\User')
                            ->where('id', '>', 0)
                            ->where('ns_fhq', '>', 0)
                            ->where('adv_nums', 7)
                            ->select() as $user){
                    if($user['ns_static'] >= 5000) {
                        $user_arr = array();
                        $fhqd = floor($user['ns_static'] / 5000);
                        $user_arr['ns_static'] = $user['ns_static'] - $fhqd * 5000;
                        $user_arr['ns_fhq'] = $user['ns_fhq'] - $fhqd;
                        $user['ns_fhq'] = $user_arr['ns_fhq'];
                        model('app\common\model\User')->where('id', $user['id'])->update($user_arr);
                    }
                    $fhqt += $user['ns_fhq'];
                }
                if($fhqt > 0){
                    $am = $amount / $fhqt;
                    foreach(model('app\common\model\User')
                                ->where('id', '>', 0)
                                ->where('ns_fhq', '>', 0)
                                ->select() as $user){
                        model('app\common\model\User')->where('id', $user['id'])->setInc('ns_static',$am*$user['ns_fhq']);
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am*$user['ns_fhq'], $user['id'], '消费分红', 'subsidy', 'XF', 'currency_xnb');
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, 1, '消费分红发放', 'subsidy', '', 'currency_ns');
                model('app\common\model\User')->where('adv_nums', '>', 0)->update(['adv_nums' => 0]);
            }
            if($weekly_4995){
                $user4 = model('app\common\model\User')->where('id', 4)->field('currency_ns')->find();
                $amount = $user4['currency_ns'];
                $count = model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('week_nums2', '>', 2)
                    ->count();
                if($count > 0) {
                    $am = $amount * 0.5 / $count;
                    foreach (model('app\admin\model\UserTrident2n')
                                 ->where('state', '1')
                                 ->where('status', '0')
                                 ->where('week_nums2', '>', 2)
                                 ->order('week_nums2 desc,id asc')->select() as $ut2n2) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am, $ut2n2['user_id'], '获得本周卓越分红奖', 'subsidy', 'ZY', 'currency_ns');
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, 4, '获得本周卓越分红奖', 'subsidy', '', 'currency_ns');
                model('app\admin\model\UserTrident2n')->where('week_nums2', '>', 0)->update(['week_nums2' => 0]);
            }
        }

    }

    public function autoMonthly(){
        $weekly_ns2 = true;
        $weekly_ns2_rate = 0.5;
        $user_arr = array(
            'monthly_nsn' => 0,
            'monthly_ns_am' => 0,
            'monthly_fgn' => 0,
        );
        foreach(model('app\common\model\User')
                    ->where('id', '>', 0)
                    ->select() as $user){
            $user_arr['monthly_ns'] = $user['monthly_nsn'];
            $user_arr['monthly_fg'] = $user['monthly_fgn'];
            model('app\common\model\User')
                ->where('id', $user['id'])
                ->update($user_arr);
        }
        if(date("d") == 1){
            if($weekly_ns2){
                $user2 = model('app\common\model\User')->where('id', 2)->field('currency_ns')->find();
                $amount = $user2['currency_ns'] * $weekly_ns2_rate;
                $count = model('app\common\model\User')
                    ->whereOr('city_server', '>', 0)
                    ->whereOr('is_securities_firms', 1)
                    ->count();
                if ($count > 0 && false) {
                    $am = $amount / $count;
                    foreach (model('app\common\model\User')
                                 ->whereOr('city_server', '>', 0)
                                 ->whereOr('is_securities_firms', 1)
                                 ->select() as $user) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am, $user['id'], '城市服务商分红', 'subsidy', 'FWS', 'currency_ns');
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, 2, '城市服务商分红发放', 'subsidy', 'FWS', 'currency_ns');

                $amount = $user2['currency_ns'] * $weekly_ns2_rate;
                $count = model('app\common\model\User')
                    ->where('currency_fhq', '>', 0)
                    ->sum('currency_fhq');
                if ($count > 0) {
                    $am = $amount / $count;
                    foreach (model('app\common\model\User')
                                 ->whereOr('currency_fhq', '>', 0)
                                 ->select() as $user) {
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am*$user['currency_fhq'], $user['id'], '联创分红', 'subsidy', 'LC', 'currency_ns');
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, 2, '联创分红发放', 'subsidy', 'LC', 'currency_ns');

                $user3 = model('app\common\model\User')->where('id', 3)->field('currency_ns')->find();
                $amount = $user3['currency_ns'];
                foreach($this->fxs_rate as $k=>$v){
                    if($k > 1) {
                        $am = $amount * $weekly_ns2_rate * $v;
                        $count = model('app\common\model\User')
                            ->where('vipd_level', $k)
                            ->count();
                        if ($count > 0) {
                            foreach(model('app\common\model\User')
                                        ->where('vipd_level', $k)
                                        ->select() as $userf){
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am/$count, $userf['id'], '合伙人分红', 'subsidy', 'HHR', 'currency_ns');
                            }
                        }
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount, 3, '合伙人分红发放', 'subsidy', '', 'currency_ns');
            }
        }
//        foreach(model('app\admin\model\UserTrident2n')
//            ->where('state', '1')
//            ->where('status', '0')
//            ->order('id', 'asc')->select() as $ut2n){
//            $ut_arr = array();
//            $ut_arr['month'] = $ut2n['month'] + 1;
//            $ut_arr['team_add'] = 0;
//            $ut_arr['month_nums'] = 0;
//            if ($ut2n['team_add'] == 3){
//                $ut_arr['month_kh'] = '1';
//            }else{
//                $ut_arr['month_kh'] = '0';
//            }
//            model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->update($ut_arr);
//        }
    }

    public function invitationCode($user_id){
        $ivcode = Random::alnum(8);
        $checkhv = true;
        while($checkhv){
            $count = model('app\common\model\User')->where('ivcode', $ivcode)->count();
            if ($count > 0 ){
                $ivcode = Random::alnum(8);
            }else{
                $checkhv = false;
            }
        }
        model('app\common\model\User')->where('id', $user_id)->update(['ivcode' => $ivcode]);
    }


    public function doconfirm($params){
        $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
            ->where('id', $params['id'])
            ->find();
        if ($remitinfo) {
            if($remitinfo['uplogid'] > 0){
                if($params['action'] == 'received'){
                    $count = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('status', '<>', 'confirm')
                        ->where('uplogid', $remitinfo['uplogid'])
                        ->count();
                    if($count == 0){
                        model('app\admin\model\UserUpgradeLog')
                            ->where('id', $remitinfo['uplogid'])
                            ->update(['state' => '1']);
                        if($remitinfo['rtype'] > 0){
                            $uul = model('app\admin\model\UserUpgradeLog')
                                ->where('id', $remitinfo['uplogid'])
                                ->find();
                            if ($uul['lv_new'] > 2) {
                                $this->wslvUp($uul['pay_user_id'], $uul['lv_new']);
                            }
                        }
                    }
                }
                if($params['action'] == 'cancel'){
                    model('app\admin\model\UserUpgradeLog')
                        ->where('id', $remitinfo['uplogid'])
                        ->update(['state' => '2']);
                }
            }
            if($remitinfo['rtype'] < 2){
                if($params['action'] == 'received'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['user_id'], '进货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                    if($remitinfo['rtype'] == 0){
                        $user = model('app\common\model\User')->where('id', $remitinfo['user_id'])->find();
                        $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                        if($userr['svip_level'] >= $user['svip_level']){
                            $pjrate = 0.05;
                            if($userr['svip_level'] == 4) $pjrate = 0.03;
                            if($userr['svip_level'] > 4) $pjrate = 0.02;
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount']*$remitinfo['discount']*$pjrate, $userr['id'], '平级奖励', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                        }
                        if($user['svip_level'] < 11) $this->wslvUp($user['id'], $user['svip_level'], false);
                    }
                }
                if($params['action'] == 'cancel'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['rec_user_id'], '批发商退货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                }
            }
        }
    }

    public function wslvUp($user_id, $uplv, $updo=true){
        $loops = 0;
        if($updo) {
            model('app\common\model\User')
                ->where('id', $user_id)
                ->update(['svip_level' => $uplv]);
            if($uplv == 3 || $uplv == 4){
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
                while($userr){
                    if($loops>0){
                        model('app\common\model\User')->where('id', $userr['id'])->setInc('wslv'.$uplv, 1);
                        if($userr['svip_level'] == 3){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv3', '>', 0)
                                ->sum('wslv3');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv3', '>', 0)->order('wslv3', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv3'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 20) $this->wsUpOrder($userr['id'], 4);
                        }else if($userr['svip_level'] == 4){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv4', '>', 0)
                                ->sum('wslv4');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv4', '>', 0)->order('wslv4', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv4'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 10) $this->wsUpOrder($userr['id'], 5);
                        }
                    }
                    $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                    $loops++;
                }
            }
        }
        if($uplv >= 5){
            if($updo){
                $user = model('app\common\model\User')->where('id', $user_id)->find();
                $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
            }else{
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
            }
            if($userr['svip_level'] == $uplv) {
                $count = model('app\common\model\User')
                    ->where('inviter_id', $userr['id'])
                    ->where('svip_level', '>=', $userr['svip_level'])
                    ->count();
                if ($count >= 3) {
                    $year = date('Y');
                    $month = date('m');
                    $createtime = strtotime($year . '-' . $month . '-' . '01');
                    $thismontham = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('user_id', $userr['id'])
                        ->where('createtime', '>=', $createtime)
                        ->where('rtype', '0')
                        ->where('status', 'confirm')
                        ->sum('amount');
                    if ($thismontham >= 300000 && $userr['svip_level'] < 11) wslvUp($userr['id'], $userr['svip_level'] + 1);
                }
            }
        }
    }

    public function wsUpOrder($user_id, $uplv){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        $uulinfo = model('app\admin\model\UserUpgradeLog')
            ->where('pay_user_id', $user['id'])
            ->where('lv_old', $user['svip_level'])
            ->where('lv_new', $uplv)
            ->where('item', '1')
            ->find();
        if (!$uulinfo){
            $margin = $this->lvsinfo[$uplv]['margin'] - $this->lvsinfo[$user['svip_level']]['margin'];
            $orderam = $this->lvsinfo[$uplv]['orderam'];
            $orderam = 0;

            $cid = 0;
            if ($margin > 0) $cid = 1;
            $pid = 1;
            $rid = $user['inviter_id'];
            $fsj = true;
            while($fsj) {
                $puser = model('app\common\model\User')
                    ->where('id', $rid)
                    ->find();
                if($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $orderam/$this->lvsinfo[$uplv]['discount']){
                    $pid = $puser['id'];
                    $fsj = false;
                }
                $rid = $puser['inviter_id'];
                if($puser['id'] == 1) $fsj = false;
            }
            $uul_arr = array();
            $uul_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $uul_arr['pay_user_id'] = $user['id'];
            $uul_arr['rec_user_id'] = $pid;
            $uul_arr['cpn_user_id'] = $cid;
            $uul_arr['item'] = '1';
            $uul_arr['lv_old'] = $user['svip_level'];
            $uul_arr['lv_new'] = $uplv;
            $uul_arr['orderam'] = $orderam;
            $uul_arr['margin'] = $margin;
            $uul_arr['amount'] = $orderam + $margin;
            $uul_arr['createtime'] = time();
            if($margin > 0) $uul_arr['item'] = '1';
            $uulid = model('app\admin\model\UserUpgradeLog')->insertGetId($uul_arr);
            if($uulid) {
                if ($margin > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $cid;
                    $remit_arr['amount'] = $margin;
                    $remit_arr['payamount'] = $margin;
                    $remit_arr['discount'] = 1;
                    $remit_arr['rtype'] = '2';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                }
                if ($orderam > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $pid;
                    $remit_arr['amount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['payamount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['discount'] = 1;//$this->lvsinfo[$uplv]['discount'];
                    $remit_arr['rtype'] = '1';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                    if ($pid > 5){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$orderam/$this->lvsinfo[$uplv]['discount'], $pid, '批发商进货', 'subsidy', $remit_arr['pay_sn'], 'currency_rmb');
                    }
                }
            }
        }
    }

    public static function sendRequest($url, $params = [], $method = 'POST', $options = [], $Sign=null)
    {
        $method = strtoupper($method);
        $protocol = substr($url, 0, 5);
        $query_string = is_array($params) ? http_build_query($params) : $params;

        $ch = curl_init();
        $defaults = [];
        if ('GET' == $method) {
            $geturl = $query_string ? $url . (stripos($url, "?") !== false ? "&" : "?") . $query_string : $url;
            $defaults[CURLOPT_URL] = $geturl;
        } else {
            $defaults[CURLOPT_URL] = $url;
            if ($method == 'POST') {
                $defaults[CURLOPT_POST] = 1;
            } else {
                $defaults[CURLOPT_CUSTOMREQUEST] = $method;
            }
            $defaults[CURLOPT_POSTFIELDS] = $params;
        }

        $defaults[CURLOPT_HEADER] = false;
        $defaults[CURLOPT_USERAGENT] = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.98 Safari/537.36";
        $defaults[CURLOPT_FOLLOWLOCATION] = true;
        $defaults[CURLOPT_RETURNTRANSFER] = true;
        $defaults[CURLOPT_CONNECTTIMEOUT] = 3;
        $defaults[CURLOPT_TIMEOUT] = 3;

        // disable 100-continue
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        if($Sign != null) {
//            $defaults[CURLOPT_HEADER] = true;
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Sign:$Sign"));
        }

        if ('https' == $protocol) {
            $defaults[CURLOPT_SSL_VERIFYPEER] = false;
            $defaults[CURLOPT_SSL_VERIFYHOST] = false;
        }

        curl_setopt_array($ch, (array)$options + $defaults);

        $ret = curl_exec($ch);
        $err = curl_error($ch);

        if (false === $ret || !empty($err)) {
            $errno = curl_errno($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);
            return [
                'ret'   => false,
                'errno' => $errno,
                'msg'   => $err,
                'info'  => $info,
            ];
        }
        curl_close($ch);
        return [
            'ret' => true,
            'msg' => $ret,
        ];
    }

    public function checkMerchant(){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/merchant/info", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkBalance($gateway = ''){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $url = 'https://pay.s100mi.com/api/v1/merchant/balance';
        if($gateway == 'yeepay') $url = $url.'/yeePay';
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($gateway == 'yeepay') $balance = $result['msg']['data']['totalAccountBalance'];
            else $balance = $result['msg']['data']['balance'];
            if($balance == 0) {
                return ['code' => 10005 ,'msg' => '余额不足'];
            }else{
                return $result['msg'];
            }
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function OutTransfer($id, $gateway='sandpay'){
        $url = "https://pay.s100mi.com/api/v1/out-order/transfer";
        if($gateway == 'yeepay') $url = $url.'Yee';
        $order = model('app\admin\model\wanlshop\Withdraw')
            ->where('status', 'created')
            ->where('id',$id)->find();
        if($order){
            if($gateway == 'yeepay'){
                if ($order['type_text'] == 'CMB') $order['type_text'] = 'CMBCHINA';
                if ($order['type_text'] == 'COMM') $order['type_text'] = 'BOCO';
                if ($order['type_text'] == 'PAB') $order['type_text'] = 'SDB';
                if ($order['type_text'] == 'CITIC') $order['type_text'] = 'ECITIC';
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'amount' => $order['money'],//
                    'bankAccountType' => 'DEBIT_CARD',//
                    'bankNo' => $order['type_text'],
                    'feeChargeSide' => 'PAYEE',
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '提现'
                ];
                if($order['mtype'] == 'currency_tz'){
                    $data['amount'] = $order['money']*60;
                    $data['remark'] = '提现';
                }
                if($order['mtype'] == 'currency_cny'){
                    $data['amount'] = $order['money'];
                    $data['remark'] = '异业提现';
                }
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
            }else{
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'accType' => 4,
                    'amount' => $order['money']*60,//
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '往来',
                    'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
                ];
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
            }
            $sign = strtoupper(md5($signbf));
            $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
                return $result['msg'];
            }else{
                return ['code' => 10005 ,'msg' => '接口异常'];
            }
        }
        return ['code' => 10005 ,'msg' => '订单异常'];

        if($gateway == 'yeepay'){
            $data = [
                'accName' => '季正新',
                'accNo' => '6214186666006839513',//账号
                'amount' => 4,//
                'bankAccountType' => 'DEBIT_CARD',//
                'bankNo' => 'NBCB',
                'feeChargeSide' => 'PAYEE',//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '提现'
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
        }else{
            $data = [
                'accName' => '季正新',
                'accNo' => '6214186666006839513',//账号
                'accType' => 4,
                'amount' => 4,//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '往来',
                'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
        }
        $sign = strtoupper(md5($signbf));
        var_dump($data);
        var_dump($signbf);
        var_dump($sign);
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            var_dump($result['msg']);
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkOrderIn($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'orderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&orderId='.$data['orderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/in-order/inquire-order", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
//                if( $result['msg']['data']['orderStatus'] == 'SUCCESS') return true;
//                else return false;
                var_dump($result['msg']);
            }
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }

    public function checkOrderOut($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'merchantOrderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/out-order/transfer/query", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
                if( $result['msg']['data']['status'] == 'SUCCESS') return true;
                else return false;
//                var_dump($result['msg']);
            }
            return false;//$result['msg'];
        }else{
            return false;//['code' => 10005 ,'msg' => '接口异常'];
        }
    }


    public function calcOrderPayTest($order_id)
    {
        $goods_id = 1;
        $goodsnums = 0;
        $amount = 0;
        $points = 0;
        $ns_am = 0;
        $ws_am = 0;
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
//            ->where('shop_id', 1)
            ->where('state', '>', 1)
            ->where('state', '<', 7)
            ->where('statusb', '0')
            ->where('id',$order_id)->find();
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user['saas_id'] > 0){
            $uplv = 0;
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] && $goods['shop_id'] > 29) {
//                if (($goods['shop_id'] - 28) == $user['saas_id'] && $goods['shop_id'] > 29) {
//                if ($goods['shop_id'] == $this->calcData[0]['shop_id']) {
                    $amount = $amount + $goods['price'] * $goods['number'];
                    $goodsnums = $goodsnums + $goods['number'];
                    $activate = false;

                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2']){
                        $uplv = 3;
                        $activate = true;
//                        入网
                        if($goods['price'] >= 4995) {
                            $nums = 5;
                        }
                        else $nums = 1;
                        $this->CalcDistributorYDFXS($user['id'], $order_id, 1, $nums);
                        $this->intonet($user['id'], $order['order_no'], (bool)($order['flq'] == 2), $nums);
//                        开通店铺
                        $this->shopApply($user['id']);
                    }
                    if ($uplv > 0){
                        $loops = 0;
                        $userinfo = $user;
                        $team20 = true;
                        while ($userinfo) {
                            $userarr = array();
                            if ($loops == 0) {
//                        $rate = $this->setting['jion_basic_rate'];
//                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($this->lvinfo[$uplv]['bonus'] * $goods['number'], $userinfo['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb');
                                if ($userinfo['vip_status'] == '0') {
                                    $userarr['vip_status'] = '1';
                                }
                                if($uplv == 2){
                                    $userarr['vip_level'] = $uplv;
                                    $userarr['self_am0'] = $goods['price'] * $goods['number'];
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => 1,
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
                                if($uplv == 3){
                                    $userarr['monthly_fg'] = 1;
                                    $userarr['monthly_fgn'] = 1;
                                    $userarr['vip_level'] = $uplv;
                                    if($userinfo['self_amd0'] < 4995) $userarr['self_amd0'] = $goods['price'] * $goods['number'];
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => 1,
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
//                                $userarr['self_amt'] = $userinfo['self_amt'] + $goods['price'] * $goods['number'];
                                if($userinfo['vip_level'] < $uplv && $activate) {
                                    $userarr['vip_level'] = $uplv;
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => $userinfo['vip_level'],
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
                                $year = date('Y');
                                $month = date('m');
                                $day = date('d');
                                model('app\common\model\Settletime')
                                    ->where('year', $year)
                                    ->where('month', $month)
                                    ->where('day', $day)->setInc('revenue'.$uplv,$goods['price'] * $goods['number']);
                            }
                            if ($loops > 0) {
                                if ($loops == 1) {
                                    if ($activate) {
                                        if ($uplv == 2) {
                                            $userarr['invite_nums'] = $userinfo['invite_nums'] + 1;
                                            if ($userarr['invite_nums'] == 3){
                                                $am3 = $userinfo['self_am0'];
                                                $user3 = model('app\common\model\User')
                                                    ->where('inviter_id', $userinfo['id'])
                                                    ->where('self_am0', '>', 0)
                                                    ->field('self_am0')
                                                    ->order('self_am0', 'asc')
                                                    ->find();
                                                if ($user3['self_am0'] < $am3) $am3 = $user3['self_am0'];
                                                if ($am3 == 99) $am3 = 199;
                                                $am3 = ($am3+1-100)/100*99;
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am3, $userinfo['id'], '购物返', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                            }
                                        }
                                        if ($uplv == 3) {
                                            $userarr['invites_nums'] = $userinfo['invites_nums'] + 1;
//                                            $userarr['invites_numsw'] = $userinfo['invites_numsw'] + 1;
                                            if($userinfo['vip_level'] < 5) {
                                                if ($userinfo['vip_level'] >= 2 && $userarr['invites_nums'] >= 6 && $userinfo['team_nums'] >= 99) {
                                                    $userarr['vip_level'] == 5;
                                                    if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
                                                }
                                            }
                                        }
                                    }
                                }
                                if ($activate) {
                                    if ($uplv == 2) {
                                        $userarr['team_nums'] = $userinfo['team_nums'] + 1;
                                        if ($userarr['team_nums'] >= 99 && $userinfo['vip_level'] == 2){
                                            $userarr['vip_level'] == 3;
                                            model('app\admin\model\UserUpgradeLog')->create([
                                                'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                                'pay_user_id' => $userinfo['id'],
                                                'rec_user_id' => $userinfo['inviter_id'],
                                                'lv_old' => 2,
                                                'lv_new' => 3,
                                                'state' => 1,
                                                'amount' => 0
                                            ]);
                                            $this->intonet($userinfo['id'], 'Auto', false);
                                        }
                                        if ($userarr['team_nums'] >= 99 && $userinfo['invite_nums'] >= 6 && $userinfo['vip_level'] == 3){
                                            $userarr['vip_level'] = 4;
                                        }
                                        if($userinfo['vip_level'] < 5) {
                                            if ($userinfo['vip_level'] >= 2 && $userinfo['invites_nums'] >= 6 && $userarr['team_nums'] >= 99) {
                                                $userarr['vip_level'] = 5;
                                                if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
                                            }
                                        }
                                        if($team20){
                                            if($userinfo['vip_level'] >= 3 || $userarr['team_nums'] >= 99) {
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $userinfo['id'], '团队奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                                $team20 = false;
                                            }
                                        }
                                    }
                                    if ($uplv == 3) $userarr['teams_nums'] = $userinfo['teams_nums'] + 1;
                                }
                            }
                            model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                            $loops++;
                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                        }
                    }
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_nfr', '0', $goods['goods_id']);
            }
        }
        if ($amount > 0){
            model('app\api\model\wanlshop\Order')->where('id',$order_id)->update(['statusb' => '1']);
        }
        if ($ns_am > 0){
            $this->CalcOrderNinestar($order_id, $ns_am, $amount);
        }
        if($ws_am > 0 && $user['svip_level'] == 1){
            model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->setInc('ws_am', $ws_am);
            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->find();
            if($uulinfo){
                if($uulinfo['ws_am'] > 2900) {
                    model('app\common\model\User')
                        ->where('id', $user['id'])
                        ->update(['svip_level' => 2]);
                }
            }
        }
        //测试秒结
//        $this->autoCalcDaliy(false);
    }

    // 自动发货
    protected function autoDelivery($order_id) {
        Log::info("自动发货-虚拟发货 -- autoDelivery order_id=$order_id");
        $pay = model('app\index\model\wanlshop\Pay')->where(['order_id'=>$order_id])->find();
        Log::info("自动发货-虚拟发货 -- autoDelivery trade_no=${pay['trade_no']}");
        if (empty($pay['trade_no'])) {
            Log::info("自动发货-虚拟发货 -- false 1");
            return false;
        }

        // 微信支付才需要
        if ($pay['pay_type'] != 1) {
            Log::info("自动发货-虚拟发货 -微信支付才需要 false");
            // return false;
        }else{
            Log::info("自动发货-虚拟发货 2");
            $order = Order::get($order_id);
            list($appid, $appsecret) = $this->getAppInfoBySaasId($order['saas_id']);
            if (empty($appid) || empty($appsecret)) {
                Log::info("自动发货-虚拟发货 配置不对");
                return false;
            }
            Log::info("自动发货-虚拟发货 2.5");
            $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret);
            if (!$access_token)
                $access_token = Mp::getAccessTokenWithWeChat($appid, $appsecret, true);
            if (!$access_token) {
                Log::info("自动发货-虚拟发货 access_token=false");
                return false;
            }
            Log::info("自动发货-虚拟发货 3");
            $order_key = [
                'order_number_type' => 2,
                'transaction_id' => $pay['trade_no'],
            ];
            $shipping_list = [
                [
                    'item_desc' => '购买线上云店',
                    'contact' => ['consignor_contact'=>''],
                ]
            ];
            Log::info("自动发货-虚拟发货 4");
            // 物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提
            $logistics_type = 3;
            //查询支付记录中的通知是否含有openid
            $pay['notice'] = json_decode($pay['notice'],true);
            $openid = array_get($pay['notice'], 'openid');
            if(!$openid){
                $third_info = model('app\index\model\wanlshop\Third')->where(['user_id'=>$order['user_id']])->find();
                if (empty($third_info['openid'])) {
                    return false;
                }
                $openid = $third_info['openid'];
            }
            $payer = ['openid'=>$openid];
            date_default_timezone_set('UTC');
            $upload_time = gmdate("Y-m-d\TH:i:s.120+08:00", time());
            $req = [
                'order_key' => $order_key,
                'logistics_type' => $logistics_type,
                'delivery_mode' => 1,
                'shipping_list' => $shipping_list,
                'upload_time' => $upload_time, // "2024-04-11T08:09:35.120+08:00", // 上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`
                'payer' => $payer
            ];
            Log::info("自动发货-虚拟发货 5");
            Log::info("自动发货-虚拟发货 ".json_encode($req, JSON_UNESCAPED_UNICODE));
            $url = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token={$access_token}";
            sleep(2);
            $result = Http::sendRequest($url, json_encode($req, JSON_UNESCAPED_UNICODE));
            Log::info("自动发货-虚拟发货 ".json_encode($result, JSON_UNESCAPED_UNICODE));
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                return false;
            }
        }
        // 更新成已发货
        Order::update(['state'=> 6, 'express_name'=>'xunifahuo', 'express_no'=>'1001'], ['id'=>$order_id], ['state','express_name','express_no']);
        $this->calcOrderConfirm($order_id);
        return true;
    }

    private function getAppInfoBySaasId($saas_id)
    {
        $configFile = APP_PATH . "../addons/wanlshop/config.php";
        if($saas_id > 0){
            $configFile = APP_PATH . "../addons/wanlshop/config{$saas_id}.php";
        }

        $config = [];
        if (is_file($configFile)) {
            $configArr = include $configFile;
            if (is_array($configArr)) {
                foreach ($configArr as $key => $value) {
                    $config[$value['name']] = $value['value'];
                }
                unset($configArr);
            }
        }
        Log::info("自动发货-虚拟发货 configArr=".json_encode($config,JSON_UNESCAPED_UNICODE));
        return [$config["mp_weixin"]["appid"], $config["mp_weixin"]["appsecret"]];
    }

    public function cUsers() {
//        $password = 'MKap2LtjHOBg3_9yVR1gaNY4sy3DxLf1dr5';
//        $salt = 'CagZjx';
//        $paypwd = 'Yco8IefQyaiVvELD2wehdNwMvlM';
//        $psalt = 'WEK9cB';
        $password = 'o9FknF1N9w2ksEuA4Wkf5qvyc_tdKgy';
        $salt = 'rWK40U';
        $paypwd = 'tFTh-I48QnEJOmC1QxgtVDk-IMg';
        $psalt = '7rH2w6';
        $user_key = ['id', 'saas_id', 'plan', 'group_id', 'username', 'username_yct', 'id_yct', 'nickname', 'truename', 'idcard', 'alipay', 'password', 'salt', 'paypwd', 'psalt', 'ivcode', 'email', 'mobile', 'avatar', 'level', 'vip_status', 'ns_status', 'trident11', 'trident21', 'level_id', 'vip_level', 'vipv_level', 'vip_levels', 'vip_levelm', 'svip_level', 'svip_levels', 'vip_statusu', 'vip_levelu', 'svip_levelu', 'svip_levelum', 'is_balance', 'is_remit', 'is_withdraw', 'is_certified', 'gender', 'birthday', 'bio', 'money', 'currency_old', 'currency_cny', 'currency_rmb', 'currency_bd', 'currency_xnb', 'currency_nfr', 'currency_ns', 'currency_ans', 'currency_usdt', 'currency_fil', 'currency_lmt', 'currency_points', 'currency_gq', 'currency_tz', 'currency_pu', 'currency_tzu', 'currency_adv', 'currency_love', 'currency_pow', 'currency_spow', 'currency_ric', 'riches', 'currency_flq', 'currency_fhq', 'currency_gfz', 'score', 'successions', 'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'loginfailure', 'joinip', 'jointime', 'createtime', 'updatetime', 'uptime', 'token', 'status', 'verification', 'inviter_id', 'pid', 'pnet', 'pam1', 'pam2', 'pam1c', 'pam2c', 'pam1n', 'pam2n', 'pnets', 'self_am0', 'self_amt', 'self_times', 'self_amd0', 'self_amdt', 'invite_nums', 'invite_numsns', 'invites_loop', 'invites_nums', 'invite_numsw', 'invites_numsw', 'team_nums', 'team_numsns', 'teams_nums', 'invite_am', 'team_am', 'team_amm', 'invite_numsu', 'invite_amu', 'team_numsu', 'team_amu', 'team_amu_new', 'invite_numsu_new', 'team_numsu_new', 'static_m', 'static_bonus', 'share_bonus', 'share2_bonus', 'dynamic_bonus', 'dynamic_am', 'svip_bonus', 'autopay', 'purelease', 'pu_static', 'pu_share', 'pu_dynamic', 'pu_todaytt', 'wslv3', 'wslv4', 'monthly_ns', 'monthly_nsn', 'monthly_ns_am', 'ns_pay', 'ns_dynamic', 'ns_static', 'ns_fhq', 'adv_nums', 'adv_num_time', 'parent_code', 'is_securities_firms', 'is_distributor', 'vipd_level', 'invited_nums', 'teamd_nums', 'top_lvd', 'dist_address', 'city_server', 'city_address', 'city_info', 'monthly_fg', 'monthly_fgn', 'viplc_level', 'lc_lam', 'up_state', 'auth_state', 'sortB'];
        $user_val = [7493, 4, 'C', 0, '13410000106', '', 0, '134****0106', NULL, NULL, NULL, $password, $salt, $paypwd, $psalt, 'FHXQyA01', '', '13000000030', '', 1, '1', '0', '0', '0', 0, 3, 1, 1, 1, 1, 1, '0', 1, 1, 1, '1', '1', '1', '1', 0, NULL, '', '0.00', '0.00000', '0.00', '0.00', '0.00', '0', '0', '0.00', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', 0, 1, 1, 1725926400, 1712742196, '***************', 0, '***************', 1725926400, 1725926400, 1712742196, NULL, '', 'normal', '', 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', 0, '0.00000', '0', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', 0, 0, 0, 0, '0.00', '0.00000', '0.00000', '0.00000', 0, 0, NULL, '0,1,150,152,', 0, 0, 0, 0, 0, 0, NULL, '0', NULL, NULL, 0, 0, 1, 0, 0, '0', 1000];
        $count = count($user_key);


        $u13410001281t1283 = false;
        if($u13410001281t1283) {
            $no1 = 13410001281;
            $sid = 8782;
            $ivid = 8773;
            $ypid = 8773;
            $ivn = 134;
            $ivc = 1281;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 3;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k == 1 || $k == 2 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 3) {
//                    $user_array['inviter_id'] = $sid + 2;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001285 = false;
        if($u13410001285) {
            $no1 = 13410001285;
            $sid = 8785;
            $ivid = 8773;
            $ypid = 8773;
            $ivn = 134;
            $ivc = 1285;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k == 1 || $k == 2 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 3) {
//                    $user_array['inviter_id'] = $sid + 2;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001205 = false;
        if($u13410001205) {
            $no1 = 13410001205;
            $sid = 8781;
            $ivid = 8773;
            $ypid = 8773;
            $ivn = 134;
            $ivc = 1205;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k == 1 || $k == 2 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 3) {
//                    $user_array['inviter_id'] = $sid + 2;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001194t1203 = false;
        if($u13410001194t1203) {
            $no1 = 13410001194;
            $sid = 8771;
            $ivid = 8683;
            $ypid = 8683;
            $ivn = 134;
            $ivc = 1194;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 10;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0) {
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }
                if($k == 1 || $k == 2 || $k == 3) {
                    $user_array['inviter_id'] = $sid + $k - 1;
                    $user_array['pnet_dz'] = 1;
                }
                if($k > 3) {
                    $user_array['inviter_id'] = $sid + 2;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $YFLMobile1 = false;
        if($YFLMobile1) {
            $noarr = array(
                0 => 17759899973
            );
            $nnarr = array(
                0 => '177****9973'
            );
            $nmarr = array(
                0 => 'YFL59899973'
            );
            $iv = array(
                0 => 8763
            );
            $sid = 8770;
            $ivn = 'CsDZ';
            $ivc = 4;
            $isIv = true;
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $iv[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $nnarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $nmarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
                $user_array['createtime'] = time();
                $user_array['jointime'] = time();

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001256t1261 = false;
        if($u13410001256t1261) {
            $no1 = 13410001256;
            $sid = 8764;
            $ivid = 8763;
            $ypid = 8763;
            $ivn = 134;
            $ivc = 1256;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 6) {
//                    $user_array['inviter_id'] = $sid + 6;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $YFLMobile = false;
        if($YFLMobile) {
            $noarr = array(
                0 => 13328546444,
                1 => 13306093366,
                2 => 18065398865
            );
            $nnarr = array(
                0 => '133****6444',
                1 => '133****3366',
                2 => '180****8865'
            );
            $nmarr = array(
                0 => 'YFL28546444',
                1 => 'YFL06093366',
                2 => 'YFL65398865'
            );
            $iv = array(
                0 => 8760,
                1 => 8761,
                2 => 8761
            );
            $sid = 8761;
            $ivn = 'CsDZ';
            $ivc = 1;
            $isIv = true;
            for($k = 0; $k < 3;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $iv[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $nnarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $nmarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
                if($k==2) {
                    $user_array['pnet_dz'] = 2;
                }
                $user_array['createtime'] = time();
                $user_array['jointime'] = time();

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001236t1241 = false;
        if($u13410001236t1241) {
            $no1 = 13410001236;
            $sid = 8751;
            $ivid = 8151;
            $ypid = 8151;
            $ivn = 134;
            $ivc = 1236;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 1 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 6) {
//                    $user_array['inviter_id'] = $sid + 6;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001251t1253 = false;
        if($u13410001251t1253) {
            $no1 = 13410001251;
            $sid = 8757;
            $ivid = 8151;
            $ypid = 8151;
            $ivn = 134;
            $ivc = 1251;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 3;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 1 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 6) {
//                    $user_array['inviter_id'] = $sid + 6;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001255 = false;
        if($u13410001255) {
            $no1 = 13410001255;
            $sid = 8760;
            $ivid = 8151;
            $ypid = 8151;
            $ivn = 134;
            $ivc = 1255;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 1 || $k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k > 6) {
//                    $user_array['inviter_id'] = $sid + 6;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001206t1219 = false;
        if($u13410001206t1219) {
            $no1 = 13410001206;
            $sid = 8729;
            $ivid = 8185;
            $ypid = 8185;
            $ivn = 134;
            $ivc = 1206;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
                if($k == 1 || $k == 3) {
                    $user_array['inviter_id'] = $sid + $k - 1;
                    $user_array['pnet_dz'] = 1;
                }
                if($k > 6) {
                    $user_array['inviter_id'] = $sid + 6;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001221t1228 = false;
        if($u13410001221t1228) {
            $no1 = 13410001221;
            $sid = 8743;
            $ivid = 8735;
            $ypid = 8185;
            $ivn = 134;
            $ivc = 1221;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 8;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001230t1235 = false;
        if($u13410001230t1235) {
            $no1 = 13410001230;
            $sid = 8720;
            $ivid = 8190;
            $ypid = 8703;
            $ivn = 134;
            $ivc = 1230;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
                if($k > 0) $user_array['pnet_dz'] = 2;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001271t1273 = false;
        if($u13410001271t1273) {
            $no1 = 13410001271;
            $sid = 8726;
            $ivid = 8190;
            $ypid = 8703;
            $ivn = 134;
            $ivc = 1271;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 3;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 10 || $k == 11 || $k == 13) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410000985 = false;
        if($u13410000985) {
            $no1 = 13410000985;
            $sid = 8703;
            $ivid = 8135;
            $ypid = 8170;
            $ivn = 134;
            $ivc = 985;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
                $user_array['ypid'] = $ypid;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001114t1129 = false;
        if($u13410001114t1129) {
            $no1 = 13410001114;
            $sid = 8704;
            $ivid = 8135;
            $ypid = 8703;
            $ivn = 134;
            $ivc = 1114;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 1;
                $user_array['ypid'] = $ypid;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
                if($k == 10 || $k == 11 || $k == 13) {
                    $user_array['inviter_id'] = $sid + $k - 1;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u134100011051t1108 = false;
        if($u134100011051t1108) {
            $no1 = 13410001105;
            $sid = 8692;
            $ivid = 8681;
            $ypid = 8681;
            $ivn = 134;
            $ivc = 1105;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 4;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
                if($k == 3) {
                    $user_array['inviter_id'] = $sid + $k - 1;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u134100011031t1104 = false;
        if($u134100011031t1104) {
            $no1 = 13410001103;
            $sid = 8696;
            $ivid = 8681;
            $ypid = 8681;
            $ivn = 134;
            $ivc = 1103;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0) {
                    $user_array['inviter_id'] = $sid-1;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
                if($k == 1) {
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u134100011091t1113 = false;
        if($u134100011091t1113) {
            $no1 = 13410001109;
            $sid = 8698;
            $ivid = 8681;
            $ypid = 8681;
            $ivn = 134;
            $ivc = 1109;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 5;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                $user_array['inviter_id'] = $ivid;
                $user_array['pnet_dz'] = 2;
//                if($k == 0) {
//                    $user_array['inviter_id'] = $ivid;
//                    $user_array['pnet_dz'] = 2;
//                }
//                if($k > 0) $user_array['pnet_dz'] = 1;
//                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
//                    $user_array['inviter_id'] = $sid;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 2;
//                }
//                if($k == 3) {
//                    $user_array['inviter_id'] = $sid + $k - 1;
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13410001091t1102 = false;
        if($u13410001091t1102) {
            $no1 = 13410001091;
            $sid = 8680;
            $ivid = 8175;
            $ypid = 8240;
            $ivn = 134;
            $ivc = 1091;
            $isIv = true;
            $search = "1341000";
            $replace = "134****";
            $replaceY = "YFL1000";
            for($k = 0; $k < 12;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
//                $isIv = !$isIv;
//                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0) {
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = $ypid;
                }
                if($k > 0) $user_array['pnet_dz'] = 1;
                if($k == 1 || $k == 5 || $k == 7 || $k == 8 || $k == 9 || $k == 10) {
                    $user_array['inviter_id'] = $sid;
                }
                if($k == 3) {
                    $user_array['inviter_id'] = $sid + $k - 2;
                }
                if($k == 2 || $k == 4 || $k == 6 || $k == 11) {
                    $user_array['inviter_id'] = $sid + $k - 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $YFLL0000665 = false;
        if($YFLL0000665) {
            $noarr = array(
                0 => 13197131319,
                1 => 13957067599,
                2 => 13665595678,
                3 => 18966426887,
                4 => 18857630138,
                5 => 13806032133,
                6 => 13989649393,
                7 => 15990467699,
                8 => 13634040916,
                9 => 13575365659,
                10 => 18357623993,
                11 => 13905764849
            );
            $iv = array(
                0 => 8106,
                1 => 44032,
                2 => 44032,
                3 => 44032,
                4 => 44032,
                5 => 44032,
                6 => 44032,
                7 => 44032,
                8 => 44032,
                9 => 44032,
                10 => 44032,
                11 => 44032
            );

            $no1 = 13400002440;
            $sid = 44032;
            $ivid = 8106;
            $ivc = 1;
            $isIv = true;
            for($k = 0; $k < 12;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $noarr[$k];
                        $user_array[$user_key[$j]] = 'FHXQUmCn'.$ic.'M';
                        $search = "1341000";
                        $replace = "";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array[$user_key[$j]]);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['ypid'] = 0;
                $user_array['pnet_dz'] = 1;
                if($k==0) {
                    $user_array['pnet_dz'] = 2;
                }
                if($k>0) {
                    $user_array['inviter_id'] = $sid;
                }
                if($k>5) {
                    $user_array['pnet_dz'] = 2;
                }
                $user_array['createtime'] = time();
                $user_array['jointime'] = time();
                $user_array['username_yct'] = '';
                $user_array['id_yct'] = 0;

                $uinfo = model('app\common\model\User')->create($user_array);
                $this->invitationCode($uinfo->id);
                echo "\n";
                $loop++;
            }
        }
        $u8x50 = false;
        if($u8x50) {
            for($i=0;$i<8;$i++){
                $no1 = 13300000025 + 50 * $i;
                $sid = 8280 + 50 * $i;
                $ivid = 8272 + $i;
                $ivn = 133;
                $ivc = 25 + 50 * $i;
                $isIv = true;
                $search = "1330000";
                $replace = "133****";
                $replaceY = "YFL3000";
                for ($k = 0; $k < 50; $k++) {
                    $loop = 0;
                    $user_array = array();
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id', 'id_yct'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $sid + $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $ic = $ivc + $k;
                            $user_array[$user_key[$j]] = 'FHXQ' . $ivn . 'Cn' . $ic . 'M';
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'username_yct') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
//                    $isIv = !$isIv;
                    $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                    if ($k > 24) {
                        $user_array['pnet_dz'] = 2;
                    }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }

        }
        $u13300000010t0014 = false;
        if($u13300000010t0014) {
            $no1 = 13300000010;
            $sid = 8265;
            $ivid = 7076;
            $ivn = 133;
            $ivc = 0010;
            $isIv = true;
            $search = "1330000";
            $replace = "133****";
            $replaceY = "YFL3000";
            for($k = 0; $k < 15;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQ'.$ivn.'Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $user_array[$user_key[$j]] = str_replace($search, $replaceY, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $isIv = !$isIv;
                $user_array['pnet_dz'] = $isIv?1:2;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k > 0){
                    if($k == 1 || $k == 2) $user_array['inviter_id'] = $sid;
                    if($k == 3 || $k == 4) $user_array['inviter_id'] = $sid+1;
                    if($k == 5 || $k == 6) $user_array['inviter_id'] = $sid+2;
                    if($k == 7 || $k == 8) $user_array['inviter_id'] = $sid+3;
                    if($k == 9 || $k == 10) $user_array['inviter_id'] = $sid+4;
                    if($k == 11 || $k == 12) $user_array['inviter_id'] = $sid+5;
                    if($k == 13 || $k == 14) $user_array['inviter_id'] = $sid+6;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410001131t1136 = false;
        if($u13410001131t1136) {
            $no1 = 13410001131;
            $sid = 8259;
            $ivid = 8181;
            $ivc = 1131;
            $isIv = true;
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k > 0){
                    $user_array['pnet_dz'] = 2;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13400000961t094 = false;
        if($u13400000961t094) {
            $no1 = 13400000961;
            $sid = 8255;
            $ivid = 6313;
            $ivc = 961;
            $isIv = true;
            for($k = 0; $k < 4;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }elseif($k == 1){
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 1;
                }elseif($k == 2){
                    $user_array['inviter_id'] = $sid+1;
                    $user_array['pnet_dz'] = 1;
                }else{
                    $user_array['inviter_id'] = $sid+1;
                    $user_array['pnet_dz'] = 2;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $YFL10000372 = false;
        if($YFL10000372) {
//            $noarr = array(
//                0 => 13651875011,
//                1 => 18956004167,
//                2 => 18876647283,
//                3 => 13790892988,
//                4 => 17547500859
//            );
//            $iv = array(
//                0 => 42345,
//                1 => 42345,
//                2 => 42345,
//                3 => 42345,
//                4 => 42345
//            );
//            $noarr = array(
//                0 => 18979357077,
//                1 => 18970340868,
//                2 => 13117933332,
//                3 => 18966426885,
//                4 => 13857698988,
//                5 => 15068661999
//            );
//            $iv = array(
//                0 => 8092,
//                1 => 8092,
//                2 => 8092,
//                3 => 8092,
//                4 => 8092,
//                5 => 8092
//            );
            $noarr = array(
                0 => 15292881367,
                1 => 18420482268,
                2 => 13399083327,
                3 => 18599186516,
                4 => 15199168680,
                5 => 13999129820,
                6 => 18060976077,
                7 => 18199003989,
                8 => 19018431633,
                9 => 18678651013,
                10 => 13011718504,
                11 => 18948797321
            );
            $iv = array(
                0 => 8092,
                1 => 8092,
                2 => 8092,
                3 => 8092,
                4 => 8092,
                5 => 8092,
                6 => 8092,
                7 => 8092,
                8 => 8092,
                9 => 8092,
                10 => 8092,
                11 => 8092
            );

            $no1 = 13400002440;
            $sid = 42901;
            $ivid = 8092;
            $ivc = 0001;
            $isIv = true;
            for($k = 0; $k < 12;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $noarr[$k];
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        $search = "1341000";
                        $replace = "";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array[$user_key[$j]]);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['ypid'] = 0;
                $user_array['pnet_dz'] = 1;
                if($k==2 || $k==5 || $k==8 || $k==11) $user_array['pnet_dz'] = 2;
                if($k==0) {
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = 8105;
                }elseif($k==4 || $k==5) {
                    $user_array['inviter_id'] = $sid+3;
                }elseif($k==7 || $k==8) {
                    $user_array['inviter_id'] = $sid+6;
                }elseif($k==10 || $k==11) {
                    $user_array['inviter_id'] = $sid+9;
                }else{
                    $user_array['inviter_id'] = $sid;
                }
                $user_array['createtime'] = time();
                $user_array['jointime'] = time();
                $user_array['username_yct'] = '';
                $user_array['id_yct'] = 0;

                $uinfo = model('app\common\model\User')->create($user_array);
                $this->invitationCode($uinfo->id);
                echo "\n";
                $loop++;
            }
        }
        $u1340001076t1089 = false;
        if($u1340001076t1089) {
            $no1 = 13400001076;
            $sid = 8241;
            $ivid = 8211;
            $ivc = 1076;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }elseif($k < 6){
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 1;
                }else{
                    $user_array['inviter_id'] = $sid;
                    $user_array['pnet_dz'] = 2;
                }
                if($k == 13) {
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                    $user_array['pnet_dz'] = 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1340001063t1074 = false;
        if($u1340001063t1074) {
            $no1 = 13400001063;
            $sid = 8229;
            $ivid = 8175;
            $ivc = 1063;
            $isIv = true;
            for($k = 0; $k < 12;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k < 4){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = 8207;
                }else{
                    $user_array['inviter_id'] = $ivid;
                    $user_array['pnet_dz'] = 2;
                }
                if($k == 10) $user_array['inviter_id'] = $user_array['id'] - 1;
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1340001041t1061 = false;
        if($u1340001041t1061) {
            $no1 = 13400001041;
            $sid = 8208;
            $ivid = 7585;
            $ivc = 1041;
            $isIv = true;
            for($k = 0; $k < 21;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0){
                    $user_array['inviter_id'] = $ivid;
                    $user_array['ypid'] = 8019;
                }elseif($k == 3 || $k == 5 || $k == 7 || $k == 9 || $k == 11 || $k == 13 || $k >= 15){
                    $user_array['inviter_id'] = $sid;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k == 20) $user_array['inviter_id'] = $user_array['id'] - 1;
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1340001001t1037 = false;
        if($u1340001001t1037) {
            $no1 = 13400001001;
            $sid = 8171;
            $ivid = 8136;
            $ivc = 1001;
            $isIv = true;
            for($k = 0; $k < 37;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0 || $k == 2 || $k == 4 || $k == 6 || $k == 7 || $k == 8 || $k == 12 || $k == 14 || $k == 16 || $k == 18 || $k == 20 || $k == 22 || $k == 24 || $k == 26 || $k == 28 || $k >= 30){
                    $user_array['inviter_id'] = $ivid;
                }elseif($k == 9 || $k == 10){
                    $user_array['inviter_id'] = 8177;
                }elseif($k == 11){
                    $user_array['inviter_id'] = $user_array['id'] - 2;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1340000951t0985 = false;
        if($u1340000951t0985) {
            $no1 = 13400000951;
            $sid = 8137;
            $ivid = 8135;
            $ivc = 951;
            $isIv = true;
            for($k = 0; $k < 34;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['pnet_dz'] = 1;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
                if($k == 0 || $k == 2 || $k == 4 || $k == 6 || $k == 19 || $k == 20 || $k == 21 || $k == 22 || $k == 23 || $k == 26 || $k == 28 || $k >= 30){
                    $user_array['inviter_id'] = $ivid;
                }elseif($k == 9 || $k == 10 || $k == 11){
                    $user_array['inviter_id'] = $user_array['id'] - 3;
                }elseif($k == 13 || $k == 14){
                    $user_array['inviter_id'] = 8146;
                }elseif($k == 24 || $k == 25){
                    $user_array['inviter_id'] = 8150;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 0 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $YFL10000372 = false;
        if($YFL10000372) {
            $noarr = array(
                0 => 13410000871,
                1 => 13410000911,
                2 => 13410000914,
                3 => 13410000919,
                4 => 13410000941,
                5 => 13410000931,
            );
            $iv = array(
                0 => 7842,
                1 => 7831,
                2 => -1,
                3 => -1,
                4 => -1,
                5 => -2,
            );
            $no1 = 13400002440;
            $sid = 8131;
            $ivid = 7842;
            $ivc = 2440;
            $isIv = true;
            for($k = 0; $k < 6;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $noarr[$k];
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $noarr[$k];
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        $search = "1341000";
                        $replace = "";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array[$user_key[$j]]);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['inviter_id'] = 7393;
                $user_array['pnet_dz'] = 2;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $line10 = false;
        if($line10) {
            $team_array = array(
                '0' => 18247198628,
                '1' => 15848399098,
                '2' => 18548116042,
                '3' => 18805681115,
                '4' => 15034944281,
                '5' => 18647149134,
                '6' => 15754896858,
                '7' => 15047720037,
                '8' => 15148788818,
                '9' => 15771344725
            );
            $loop = 0;
            foreach ($team_array as $k => $v) {
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 42130 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $v;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $loop == 0 ? 30899 : $user_array['id'] - 1;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQTJJM' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
//                    if ($user_key[$j] == 'nickname') {
//                        print($user_key[$j]);
//                        echo ':';
//                        $search = "1300000";
//                        $replace = "130****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        print($user_array[$user_key[$j]]);
//                        echo "===>";
//                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        $u13400002416t2439 = false;
        if($u13400002416t2439) {
            $no1 = 13400002416;
            $ivid = 8107;
            $isIv = true;
            for($k = 0; $k < 24;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8107 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 2416+$k;
                        $user_array[$user_key[$j]] = 'FHXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['inviter_id'] = 7393;
                $user_array['pnet_dz'] = 2;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13200000651t0665 = false;
        if($u13200000651t0665) {
            $no1 = 13200000651;
            $ivid = 8092;
            $isIv = true;
            for($k = 0; $k < 15;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8092 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 651+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQ2Cn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1320000";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1320000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['inviter_id'] = 917;
                    $user_array['ypid'] = 917;
                    $user_array['pnet_dz'] = 2;
                }else if($k==1) {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = 8092;
                    $user_array['pnet_dz'] = 1;
                }else if($k==2) {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = 8092;
                    $user_array['pnet_dz'] = 2;
                }else {
                    $user_array['inviter_id'] = 8092;
                    $user_array['ypid'] = $user_array['id'] - 2;
                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000831t0844 = false;
        if($u13410000831t0844) {
            $no1 = 13410000831;
            $ivid = 7906;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8078 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 831+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7906;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7906;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000850t0869 = false;
        if($u13410000850t0869) {
            $no1 = 13410000850;
            $ivid = 7904;
            $isIv = true;
            for($k = 0; $k < 20;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8058 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 850+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7904;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7904;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000825t0826 = false;
        if($u13410000825t0826) {
            $no1 = 13410000825;
            $ivid = 7854;
            $isIv = true;
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8056 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 825+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 8041;
//                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7854;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000825t0826 = false;
        if($u13410000825t0826) {
            $no1 = 13410000825;
            $ivid = 7854;
            $isIv = true;
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8056 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 825+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 8041;
//                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7854;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000811t0824 = false;
        if($u13410000811t0824) {
            $no1 = 13410000811;
            $ivid = 7856;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8042 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 811+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7856;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7856;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000801t0810 = false;
        if($u13410000801t0810) {
            $no1 = 13410000801;
            $ivid = 7854;
            $isIv = true;
            for($k = 0; $k < 10;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 8032 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 801+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7854;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7854;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000751t0791 = false;
        if($u13410000751t0791) {
            $no1 = 13410000751;
            $ivid = 7832;
            $isIv = true;
            for($k = 0; $k < 41;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7991 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 751+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7832;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==15) {
                    $user_array['ypid'] = 7997;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==29) {
                    $user_array['ypid'] = 8007;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k==0) {
                        $user_array['inviter_id'] = 7832;
                    }elseif($k>=15 && $k<29) {
                        $user_array['inviter_id'] = 7997;
                    }elseif($k>=29) {
                        $user_array['inviter_id'] = 8007;
                    }else $user_array['inviter_id'] = 7991;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000642t0643 = false;
        if($u13410000642t0643) {
            $no1 = 13410000642;
            $ivid = 6795;
            for($k = 0; $k < 2;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7989 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 642+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                $user_array['ypid'] = 6795;
                $user_array['pnet_dz'] = 2;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000631t0641 = false;
        if($u13410000631t0641) {
            $no1 = 13410000631;
            $ivid = 6315;
            for($k = 0; $k < 11;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7978 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 631+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6315;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==1) {
                    $user_array['inviter_id'] =  $user_array['ypid'] = $user_array['id'] - 1;
                    $user_array['pnet_dz'] = 1;
                }else {
                    if($k==2) $user_array['pnet_dz'] = 2;
                    $user_array['ypid'] = $user_array['id'] - 2;
                    $user_array['pnet_dz'] = 1;
                }

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000601t0630 = false;
        if($u13410000601t0630) {
            $no1 = 13410000601;
            $ivid = 7569;
            $isIv = true;
            for($k = 0; $k < 30;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7948 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 601+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7569;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==15) {
                    $user_array['ypid'] = 7952;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=15) {
                        $user_array['inviter_id'] = 7952;
                    }else $user_array['inviter_id'] = 7569;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 12 && $k != 13 && $k != 13 && $k != 29)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000585t0598 = false;
        if($u13410000585t0598) {
            $no1 = 13410000585;
            $ivid = 7891;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7934 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 585+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7891;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7891;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000571t0584 = false;
        if($u13410000571t0584) {
            $no1 = 13410000571;
            $ivid = 7890;
            $isIv = true;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7920 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 571+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7890;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7890;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000551t0566 = false;
        if($u13410000551t0566) {
            $no1 = 13410000551;
            $ivid = 7618;
            $isIv = true;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7904 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 551+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7618;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7618;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 3 && $k != 14)
                $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000535t0550 = false;
        if($u13410000535t0550) {
            $no1 = 13410000535;
            $ivid = 7608;
            $isIv = true;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7888 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 535+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7608;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7608;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 3 && $k != 14)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000516t0534 = false;
        if($u13410000516t0534) {
            $no1 = 13410000516;
            $ivid = 7604;
            $isIv = true;
            for($k = 0; $k < 19;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7869 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 516+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7604;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7604;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 17)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000501t0515 = false;
        if($u13410000501t0515) {
            $no1 = 13410000501;
            $ivid = 7590;
            $isIv = true;
            for($k = 0; $k < 15;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7854 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 501+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7590;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7590;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 11)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000384t0394 = false;
        if($u13410000384t0394) {
            $no1 = 13410000384;
            $ivid = 7588;
            $isIv = true;
            for($k = 0; $k < 11;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7843 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 384+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7588;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7588;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 1)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000371t0383 = false;
        if($u13410000371t0383) {
            $no1 = 13410000371;
            $ivid = 7586;
            $isIv = true;
            for($k = 0; $k < 13;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7830 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 371+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 7586;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    $user_array['inviter_id'] = 7586;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 11)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000329t0368 = false;
        if($u13410000329t0368) {
            $no1 = 13410000329;
            $ivid = 6613;
            $isIv = true;
            for($k = 0; $k < 40;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7790 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 329+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6613;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==23) {
                    $user_array['ypid'] = 7794;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=23) {
                        $user_array['inviter_id'] = 7794;
                    }else $user_array['inviter_id'] = 6613;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 6 && $k != 38)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000289t0328 = false;
        if($u13410000289t0328) {
            $no1 = 13410000289;
            $ivid = 6606;
            $isIv = true;
            for($k = 0; $k < 40;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7750 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 289+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6606;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==24) {
                    $user_array['ypid'] = 7754;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=24) {
                        $user_array['inviter_id'] = 7754;
                    }else $user_array['inviter_id'] = 6606;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
//                if($k != 21 && $k != 29 && $k != 41)
                    $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000247t0289 = false;//369
        if($u13410000247t0289) {
            $no1 = 13410000247;
            $ivid = 6594;
            $isIv = true;
            for($k = 0; $k < 43;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7707 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 247+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6594;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==23) {
                    $user_array['ypid'] = 7711;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=23) {
                        $user_array['inviter_id'] = 7711;
                    }else $user_array['inviter_id'] = 6594;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 21 && $k != 29 && $k != 41) $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000203t0246 = false;
        if($u13410000203t0246) {
            $no1 = 13410000203;
            $ivid = 6133;
            $isIv = true;
            for($k = 0; $k < 44;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7663 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 203+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6133;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==25) {
                    $user_array['ypid'] = 7667;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=25) {
                        $user_array['inviter_id'] = 7667;
                    }else $user_array['inviter_id'] = 6133;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 23 && $k != 42) $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000161t0202 = false;
        if($u13410000161t0202) {
            $no1 = 13410000161;
            $ivid = 6128;
            $isIv = true;
            for($k = 0; $k < 42;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7621 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 161+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6128;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==21) {
                    $user_array['ypid'] = 7625;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=21) {
                        $user_array['inviter_id'] = 7625;
                    }else $user_array['inviter_id'] = 6128;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 19 && $k != 40) $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000042t0083 = false;
        if($u13410000042t0083) {
            $no1 = 13410000042;
            $ivid = 6114;
            $isIv = true;
            for($k = 0; $k < 42;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7579 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 42+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6114;
                    $user_array['pnet_dz'] = 2;
                }elseif($k==21) {
                    $user_array['ypid'] = 7583;
                    $user_array['pnet_dz'] = 2;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=21) {
                        $user_array['inviter_id'] = 7583;
                    }else $user_array['inviter_id'] = 6114;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 40) $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000006t0041 = false;
        if($u13410000006t0041) {
            $no1 = 13410000006;
            $ivid = 6111;
            $isIv = true;
            for($k = 0; $k < 36;$k++){
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7543 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 6+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) {
                    $user_array['ypid'] = 6111;
                }elseif($k==20) {
                    $user_array['ypid'] = 7547;
                }
                else $user_array['ypid'] = $user_array['id'] - 1;
                if($isIv){
                    if($k>=20) {
                        $user_array['inviter_id'] = 7547;
                        if ($k==20) $user_array['pnet_dz'] = 2;
                    }else $user_array['inviter_id'] = 6111;
                }else{
                    $user_array['inviter_id'] = $user_array['id'] - 1;
                }
                if($k != 5 && $k != 11) $isIv = !$isIv;

                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000136t0160 = false;
        if($u13410000136t0160) {
            $no1 = 13410000136;
            $ivid = 6591;
            for($k = 0; $k < 25;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7518 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 136+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) $user_array['ypid'] = 6615;
                else $user_array['ypid'] = $user_array['id'] - 1;
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13410000106t0130 = false;
        if($u13410000106t0130) {
            $no1 = 13410000106;
            $ivid = 6111;
            for($k = 0; $k < 25;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7493 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = 106+$k;
                        $user_array[$user_key[$j]] = 'FHCLJXQCn'.$ic.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1341000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1341000";
                        $replace = "YFL1000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                if($k==0) $user_array['ypid'] = 6135;
                else $user_array['ypid'] = $user_array['id'] - 1;
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13400002000 = false;
        if($u13400002000) {
            $no1 = 13400002000;
            $ivid = 528;
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7077 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHCLJXQC200'.$k.'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13400002001 = false;
        if($u13400002001) {
            $no1 = 13400002001;
            $ivid = 530;
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7078 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHCLJXQC200'.($k+1).'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u13400002002t15 = false;
        if($u13400002002t15) {
            $no1 = 13400002002;
            $ivid = 7078;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7079 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        if($k < 8) $user_array[$user_key[$j]] = 'FHCLJXQC200'.($k+2).'M';
                        else $user_array[$user_key[$j]] = 'FHCLJXQC20'.($k+2).'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
//        7085
//        7092
        $u134000020168x50 = false;
        if($u134000020168x50) {
            $no1 = 13400002015;
            $ivid = 7085;
            for($k = 0; $k < 8;$k++){
                for($i = 0; $i < 50;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id', 'id_yct'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 7092 + $k*50 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 50 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid + $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            if(($k * 50 + $i + 16) < 100) $user_array[$user_key[$j]] = 'FHCLJXQC20'.($k * 50 + $i + 16).'M';
                            else $user_array[$user_key[$j]] = 'FHCLJXQC2'.($k * 50 + $i + 16).'M';
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'username_yct') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "YFL0000";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }
        $uone1 = false;
        if($uone1) {
            $no1 = 13288803101;
            $ivid = 560;
            for($k = 0; $k < 1;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7076 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FH31XQA'.$k.'CM';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1328880";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1328880";
                        $replace = "YFL8000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        $u1325101 = false;
        if($u1325101) {
            $no1 = 13200005151;
            $ivid = 3150;
            for($k = 0; $k < 10;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id', 'id_yct'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 7066 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FH50XQ5'.$k.'CM';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1320000";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'username_yct') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1320000";
                        $replace = "YFL0000";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
//        partments 4
        $p4lv5t30 = false;
        $p4line8 = false;
        $p4lv1t3 = false;
        $p4lv4 = false;
        $p4lv4t30 = false;
        if($p4line8) {
            $team_array = array(
                '0' => 13000000040,
                '1' => 13000000041,
                '2' => 13000000042,
                '3' => 13000000043,
                '4' => 13000000044,
                '5' => 13000000045,
                '6' => 13000000046,
                '7' => 13000000047,
                '8' => 13000000048
            );
            $loop = 0;
            foreach ($team_array as $k => $v) {
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6066 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $v;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = $loop == 0 ? 153 : $user_array['id'] - 1;
                        $user_array[$user_key[$j]] = $user_array['id'] - 1;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3A00' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1330000";
                        $replace = "133****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        if($p4lv1t3) {
            $no1 = 13400000001;
            $ivid = 6074;
            for($k = 0; $k < 14;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6075 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid+floor($k/2);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3AL0' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($p4lv4) {
            $no1 = 13400000100;
            $ivid = 6081;
            for($k = 0; $k < 16;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6089 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k * 100;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid+floor($k/2);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy3ALF0' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1340000";
                        $replace = "134****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($p4lv4t30) {
            $no1 = 13400000100;
            $ivid = 6089;
            for($k = 0; $k < 16;$k++){
                for($i = 0; $i < 30;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 6105 + $k*30 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 100 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FHXQy3ALF30' . $k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }
        if($p4lv5t30) {
            $no1 = 13400000100;
            $ivid = 6089;
            for($k = 0; $k < 16;$k++){
                for($i = 0; $i < 30;$i++) {
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id', 'id_yct'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 6585 + $k*30 + $i + 1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + $k * 100 + 31 + $i;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FHXQy3ALF60' . $k.$j;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "134****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'username_yct') {
                            print($user_key[$j]);
                            echo ':';
//                        $search = "1320000";
//                        $replace = "132****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1340000";
                            $replace = "YFL0000";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }
        }

        $line9 = false;
        $line108 = false;
        $line5400 = false;
        if($line9) {
            $team_array = array(
                '0' => 13000000030,
                '1' => 13000000031,
                '2' => 13000000032,
                '3' => 13000000033,
                '4' => 13000000034,
                '5' => 13000000035
//            ,
//                '6' => 13300000026,
//                '7' => 13300000027,
//                '8' => 13300000028
            );
            $loop = 0;
            foreach ($team_array as $k => $v) {
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6050 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $v;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $loop == 0 ? 153 : $user_array['id'] - 1;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 'FHXQy1A' . $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1300000";
                        $replace = "130****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }
        }
        if($line108) {
            $nums = 10;
            $no1 = 13300000000;
            $ivid = 6055;
            for($k = 0; $k < $nums;$k++){
                $loop = 0;
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = 6056 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = $no1 + 100*$k;
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
//                        $user_array[$user_key[$j]] = 'FHXQyC' . $k .'M';
                        $user_array[$user_key[$j]] = 'FHXQy1A' . $k .'M';
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $search = "1330000";
                        $replace = "133****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        $search = "1320001";
                        $replace = "132****";
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }
                model('app\common\model\User')->create($user_array);
                echo "\n";
                $loop++;
            }

        }
        if($line5400) {
            $no1 = 13200000100;
            $ivid = 530;
            for($k = 0; $k < 108;$k++){
                for($i = 0; $i < 50;$i++){
                    $loop = 0;
                    for ($j = 0; $j < $count; $j++) {
                        $user_array[$user_key[$j]] = $user_val[$j];
                        if (in_array($user_key[$j], array('id'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 650 + $k*50 + $i;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if (in_array($user_key[$j], array('username', 'mobile'))) {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $no1 + 100*$k + $i+1;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'inviter_id') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = $ivid+$k;
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'ivcode') {
                            print($user_key[$j]);
                            echo ':';
                            $user_array[$user_key[$j]] = 'FH'.($k>9?$k:'0'.$k).'XQ' . ($i>9?$i:'0'.$i) .'CM';
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                        if ($user_key[$j] == 'nickname') {
                            print($user_key[$j]);
                            echo ':';
                            $search = "1320000";
                            $replace = "132****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            $search = "1320001";
                            $replace = "132****";
                            $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                            print($user_array[$user_key[$j]]);
                            echo "===>";
                        }
                    }
                    model('app\common\model\User')->create($user_array);
                    echo "\n";
                    $loop++;
                }
            }

        }
    }
    public function test($auction_id = 1)
    {
        exit;
        $order_info = array();
        $order_info['user_id'] = 1;
        $order_info['order_id'] = 0;
        $order_info['order_no'] = 'HT13011111111';
        $order_info['price'] = 999;
        $order_info['number'] = 1;
        $order_info['category_id'] = 108;
        $order_info['activity_type'] = 'shopgoods';
        $res = controller('app\api\controller\exten\UserOrderReq')->order($order_info, 'emptyorder');
        var_dump($res);
        exit();

        foreach(model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('px_nums', 2)
                    ->order('id', 'asc')->select() as $ut2n){
            $loopm = 1;
            foreach(model('app\admin\model\UserTrident2n')
                        ->where('state', '1')
                        ->where('status', '0')
                        ->where('rid', $ut2n['user_id'])
                        ->order('team_nums desc,id asc')->select() as $ut2n2){
//                if($loopm == 1)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
//                if($loopm == 3)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
//                if($loopm == 2)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);

                if($loopm == 1)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);
                if($loopm == 2)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
                if($loopm == 3)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
                $loopm++;
            }
        }

        exit();
        foreach (model("app\common\model\CurrencyRmbLog")
                     ->where('type', 'subsidy')
                     ->where('money',999)
                     ->order('id', 'desc')
                     ->select() as $cl) {
            $count = model("app\common\model\CurrencyRmbLog")
                ->where('type', 'subsidy')
                ->where('money',999)
                ->where('service_ids',$cl['service_ids'])
                ->order('id', 'desc')
                ->count();
            if($count > 1){
                echo $cl['id']."===>".$cl['user_id']."===>".$cl['service_ids']."\n";
            }
        }
        exit();
        foreach(model('app\api\model\wanlshop\Order')
                    ->where('id', '>', '622')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('state', '<', 7)
                    ->where('statusb', '0')->select() as $order){
            $user = model('app\common\model\User')
                ->where('id', $order['user_id'])
                ->find();
            $u2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $order['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $order['user_id'])
                ->order('id', 'asc')->find();
            $hvn2 = False;
            if($u2info) $hvn2 = True;
            echo 'ID:'.$user['id'].';'.$user['username'].'入网：'.($hvn2?'Y':'N')."\n";
//            if (!$hvn2) $this->calcOrderPayTest($order['id']);
        }

    }
}
