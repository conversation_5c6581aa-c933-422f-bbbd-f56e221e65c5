<?php

namespace app\common\behavior;

use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\OrderGoods;
use app\api\model\wanlshop\Shop;
use app\common\enum\VipEnum;
use app\index\model\wanlshop\Ninestar;
use fast\Date;
use fast\Random;

class Settlement
{
    public function __construct()
    {
        $this->merchantId = 6996282976456978432;
        $this->key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
        $this->nottest = true;
        $this->setting = model('app\common\model\Config')->where('group','settlement')->column('name,value');
        $this->lvname = model('app\admin\model\UserVipLevel')->column('id,name');
        $this->lvinfo = model('app\admin\model\UserVipLevel')->column('id,name,bonus,upgrade,share,share1,share2,compound,excitation,profit');
        $this->lvsinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,orderam,discount,upgrade,bonus,share,compound,excitation,profit');
        $this->union_iv = model('app\admin\model\UserVipLevelu')->column('id,iv_rate');
        $this->union_jc = model('app\admin\model\UserVipLevelu')->column('id,jc_rate');
        $this->saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');


        $this->input = 1111;
        $this->suid = 3;
        $this->bonusrate = 0.005;
        $this->fday = 10;
        $this->eday = 1;
        $this->jckh = array(
            0 => 0,
            1 => 100000,
            2 => 1000000
        );
        $this->jc = array(
            0 => 0,
            1 => 0.5,
            2 => 1
        );
        $this->tjkh = array(
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 6,
            7 => 7
        );
        $this->lvkh = array(
            2 => 799,
            3 => 3999,
            4 => 8999
        );

        $this->lvsq = array(
            1 => array(
                's' => 0,
                'x' => 0
            ),
            2 => array(
                's' => 5,
                'x' => 10
            ),
            3 => array(
                's' => 10,
                'x' => 20
            ),
            4 => array(
                's' => 15,
                'x' => 25
            )
        );
        $this->dgstd = array(
            1 => 0,
            2 => 19,
            3 => 80,
            4 => 160,
            5 => 500,
            6 => 1000
        );
        $this->dgm = array(
            1 => 0,
            2 => 10,
            3 => 20,
            4 => 30,
            5 => 40,
            6 => 50
        );
        $this->ustd = array(
            2 => 99,
            3 => 999,
            4 => 9999,
            5 => 19999
        );
        $this->uprate = array(
            2 => 1,
            3 => 2,
            4 => 2.5,
            5 => 3
        );

        $this->tj = array(
            1 => 0.15,
            2 => 0.18
        );

        $this->calcData = array(
            '0' => array(
                'shop_id' => 1,
                'category_id_lv1' => 107,
                'category_id_lv2' => 108,
                'category_id_lv3' => 228
            )
        );
        $this->ns_rate = 0.28;
        $this->ns_top_std = 2000;
    }


    public function run(&$params)
    {
//        if($params['action'] == 'test'){
//            $this->calcAmountB(129,3999,3618596952981129);
//        }
//        if($params['action'] == 'autoone'){
//
//        }
//        if($params['action'] == 'auto'){
//
//        }
        if(
//            $params['action'] == 'payed' ||
            $params['action'] == 'received'
        ){
            $this->doconfirm($params);
        }
        if($params['action'] == 'certified'){
            $this->certified($params['user_id']);
        }

        if($params['action'] == 'orderPay'){
            $this->calcOrderPay($params['order_id']);
        }

        if($params['action'] == 'orderConfirm'){
            $this->calcOrderConfirm($params['order_id']);
        }

        if($params['action'] == 'orderRefund'){
            $this->calcOrderRefund($params['order_id']);
        }

        if($params['action'] == 'ivc'){
            $this->invitationCode($params['user_id']);
        }

        if($params['action'] == 'intoNet'){
            $this->intonet($params['user_id'], 'HT', false);
        }
        if($params['action'] == 'wslvUp'){
            $this->wslvUp($params['user_id'], $params['uplv']);
        }

        if($params['action'] == 'daily'){
            $this->autoCalcDaliy();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $hour = date('H');
            $settletime = model('app\common\model\Settletime')->where('year', $year)->where('month', $month)->where('day', $day)->find();
            if (!$settletime) {
                model('app\common\model\Settletime')->create([
                    'year' => $year,
                    'month' => $month,
                    'day' => $day, // 备注
                    'hour' => $hour
                ]);
                $this->autoCalcDaliy();
//                $this->autoCalcDaliyYY();
            }
        }
        if($params['action'] == 'weekly'){
            $this->autoWeekly();
        }
        if($params['action'] == 'monthly'){
            $this->autoMonthly();
        }
        if($params['action'] == 'sandpay_out'){
//            var_dump($this->checkMerchant());
            $bl = $this->checkBalance($params['gateway']?$params['gateway']:'sandpay');
            if($bl['code'] == 200){
                return $this->OutTransfer($params['ids'], $params['gateway']?$params['gateway']:'sandpay');
            }else{
//                var_dump($bl);
                return $bl;
            }
        }
        if($params['action'] == 'sandpay_bl'){
//            var_dump($this->checkMerchant());
            $gateway = 'yeepay';
            $bl = $this->checkBalance();
//            var_dump($bl);
        }
        if($params['action'] == 'Test'){
//            $this->calcOrderPay(326);
//            $this->wslvUp(139, 3);
//            $this->intonet(168,$order_no='1206242000531168', $calc=true);
//            $this->calcOrderPay(16);
//            $this->shopApply(106);
//            $this->calcOrderConfirm(63);
//            $this->intonet(1,1);
//            $bl = $this->checkBalance('yeepay');
//            var_dump($bl);
//            $msg = $this->OutTransfer(186974, 'yeepay');
//            $msg = $this->checkOrderOut(106078);
//            $msg = $this->checkOrderIn('202301282316531255493153100519');
//            $this->autoCalcDaliyYY();
//            $this->calcOrder(25359);
//             var_dump($msg);
        }
    }

    public function certified($user_id){
        //赠送1张优惠券
//        $coupon_id = 4;
        $userinfo = model('app\common\model\User')->where('id', $user_id)->field('saas_id')->find();
//        $coupon_id = $userinfo['saas_id'] + 28;
        $coupon_id = $this->saas_info[$userinfo['saas_id']]['coupon_id'];
        $coupon = model('app\api\model\wanlshop\Coupon')->get($coupon_id);
        if($coupon){
            // 领取优惠券并保留备份
            $result = model('app\api\model\wanlshop\CouponReceive');
            $result->state = 1;
            $result->coupon_id = $coupon_id;
            $result->coupon_no = Random::alnum(16);
            $result->user_id = $user_id;
            $result->shop_id = $coupon['shop_id'];
            $result->type = $coupon['type'];
            $result->name = $coupon['name'];
            $result->userlevel = $coupon['userlevel'];
            $result->usertype = $coupon['usertype'];
            $result->price = $coupon['price'];
            $result->discount = $coupon['discount'];
            $result->limit = $coupon['limit'];
            $result->rangetype = $coupon['rangetype'];
            $result->range = $coupon['range'];
            $result->pretype = $coupon['pretype'];
            $result->validity = $coupon['validity'];
            $result->startdate = $coupon['startdate'];
            $result->enddate = $coupon['enddate'];
            $result->save();
            if($result){
                if($coupon['grant'] != '-1'){
                    // 剩余数量
                    $data['surplus'] = $coupon['surplus'] - 1;
                    // 即将过期，强制失效
                    if($coupon['surplus'] == 1){
                        $data['invalid'] = 1;
                    }
                }
                $data['alreadygrant'] = $coupon['alreadygrant'] + 1;
                // 更新优惠券领取+1
                $coupon->allowField(true)->save($data);
            }
        }
    }

    public function shopApply($user_id){
        $userinfo = model('app\common\model\User')->where('id', $user_id)->find();
        $shopauth = model('app\api\model\wanlshop\Auth')->where('user_id', $user_id)->find();
        if(!$shopauth){
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['verify'] = '3';
            $shoparr['status'] = 'normal';
            $shoparr['bio'] = $userinfo['username'];

            model('app\api\model\wanlshop\Auth')->insert($shoparr);
        }
        $shopinfo = model('app\api\model\wanlshop\Shop')->where('user_id', $user_id)->find();
        if (!$shopinfo) {
            $shoparr = array();
            $shoparr['user_id'] = $user_id;
            $shoparr['state'] = '0';
            $shoparr['shopname'] = $userinfo['username'];
            $shoparr['avatar'] = '';
            $shoparr['bio'] = $userinfo['username'];
            $shoparr['description'] = $userinfo['username'];
            $shoparr['city'] = '';
            $shoparr['verify'] = '3';

            model('app\api\model\wanlshop\Shop')->insert($shoparr);
        }
    }

    /**
     * 订单支付
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderPay($order_id)
    {
        $goods_id = 1;
        $goodsnums = 0;
        $amount = 0;
        $points = 0;
        $ns_am = 0;
        $ws_am = 0;
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
//            ->where('shop_id', 1)
            ->where('state', '>', 1)
            ->where('state', '<', 7)
            ->where('statusb', '0')
            ->where('id',$order_id)->find();
        $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
        if ($user['saas_id'] > 0){
            $uplv = 0;
            foreach (model('app\api\model\wanlshop\OrderGoods')
                         ->where('order_id', $order_id)
                         ->select() as $goods) {
                $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] && $goods['shop_id'] > 29) {
//                if (($goods['shop_id'] - 28) == $user['saas_id'] && $goods['shop_id'] > 29) {
//                if ($goods['shop_id'] == $this->calcData[0]['shop_id']) {
                    $amount = $amount + $goods['price'] * $goods['number'];
                    $goodsnums = $goodsnums + $goods['number'];
                    $activate = false;
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv1'] && $user['vip_level'] == 1){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(($goods['price']>=199?$goods['price']-100:$goods['price']) * $goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                        $uplv = 2;
                        $activate = true;
                    }
                    if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] && $user['vip_level'] == 2){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['price'] * $goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb', '0');
                        $uplv = 3;
                        $activate = true;
//                        入网
                        $this->intonet($user['id'], $order['order_no']);
//                        开通店铺
                        $this->shopApply($user['id']);
                    }
                    if ($uplv > 0){
                        $loops = 0;
                        $userinfo = $user;
                        $team20 = true;
                        while ($userinfo) {
                            $userarr = array();
                            if ($loops == 0) {
//                        $rate = $this->setting['jion_basic_rate'];
//                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($this->lvinfo[$uplv]['bonus'] * $goods['number'], $userinfo['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_xnb');
                                if ($userinfo['vip_status'] == '0') {
                                    $userarr['vip_status'] = '1';
                                    $userarr['vip_level'] = $uplv;
                                    $userarr['self_am0'] = $goods['price'] * $goods['number'];
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => 1,
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
//                                $userarr['self_amt'] = $userinfo['self_amt'] + $goods['price'] * $goods['number'];
                                if($userinfo['vip_level'] < $uplv && $activate) {
                                    $userarr['vip_level'] = $uplv;
                                    model('app\admin\model\UserUpgradeLog')->create([
                                        'pay_sn' => date("Ymdhis") . sprintf("%08d", $userinfo['id']) . mt_rand(1000, 9999),
                                        'pay_user_id' => $userinfo['id'],
                                        'rec_user_id' => $userinfo['inviter_id'],
                                        'lv_old' => $userinfo['vip_level'],
                                        'lv_new' => $uplv,
                                        'state' => 1,
                                        'amount' => $goods['price'] * $goods['number']
                                    ]);
                                }
                                $year = date('Y');
                                $month = date('m');
                                $day = date('d');
                                model('app\common\model\Settletime')
                                    ->where('year', $year)
                                    ->where('month', $month)
                                    ->where('day', $day)->setInc('revenue'.$uplv,$goods['price'] * $goods['number']);
                            }
                            if ($loops > 0) {
                                if ($loops == 1) {
                                    if ($activate) {
                                        if ($uplv == 2) {
                                            $userarr['invite_nums'] = $userinfo['invite_nums'] + 1;
                                            if ($userarr['invite_nums'] == 3){
                                                $am3 = $userinfo['self_am0'];
                                                $user3 = model('app\common\model\User')
                                                    ->where('inviter_id', $userinfo['id'])
                                                    ->where('self_am0', '>', 0)
                                                    ->field('self_am0')
                                                    ->order('self_am0', 'asc')
                                                    ->find();
                                                if ($user3['self_am0'] < $am3) $am3 = $user3['self_am0'];
                                                if ($am3 == 99) $am3 = 199;
                                                $am3 = ($am3+1-100)/100*99;
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am3, $userinfo['id'], '购物返', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                            }
                                        }
                                        if ($uplv == 3) {
                                            $userarr['invites_nums'] = $userinfo['invites_nums'] + 1;
//                                            $userarr['invites_numsw'] = $userinfo['invites_numsw'] + 1;
                                            if($userinfo['vip_level'] < 5) {
                                                if ($userinfo['vip_level'] >= 2 && $userarr['invites_nums'] >= 6 && $userinfo['team_nums'] >= 99) {
                                                    $userarr['vip_level'] == 5;
                                                    if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
                                                }
                                            }
                                        }
                                    }
                                }
                                if ($activate) {
                                    if ($uplv == 2) {
                                        $userarr['team_nums'] = $userinfo['team_nums'] + 1;
                                        if ($userarr['team_nums'] >= 99 && $userinfo['invite_nums'] >= 6 && $userinfo['vip_level'] == 3){
                                            $userarr['vip_level'] == 4;
                                        }
                                        if($userinfo['vip_level'] < 5) {
                                            if ($userinfo['vip_level'] >= 2 && $userinfo['invites_nums'] >= 6 && $userarr['team_nums'] >= 99) {
                                                $userarr['vip_level'] == 5;
                                                if ($userinfo['vip_level'] < 3) $this->intonet($userinfo['id'], 'GJDZ', false);
                                            }
                                        }
                                        if($team20){
                                            if($userinfo['vip_level'] >= 3 || $userarr['team_nums'] >= 99) {
                                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(20, $userinfo['id'], '团队奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                                $team20 = false;
                                            }
                                        }
                                    }
                                    if ($uplv == 3) $userarr['teams_nums'] = $userinfo['teams_nums'] + 1;
                                }
                            }
                            model('app\common\model\User')->where('id', $userinfo['id'])->update($userarr);
                            $loops++;
                            $userinfo = model('app\common\model\User')->where('id', $userinfo['inviter_id'])->find();
                        }
                    }
                }else if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_ws'] && $goods['shop_id'] > 29) {
                    $ws_am = $amount + $goods['price'] * $goods['number'];
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else if($goodsData['activity_type'] == 'ninestar'){
                    $activity = model('app\admin\model\wanlshop\Ninestar')->where('id', $goodsData['activity_id'])->find();
                    if($activity){
                        $ns_am += $activity['price'] * $goods['number'];
                    }
                    $amount = $amount + $goods['price'] * $goods['number'];
                }else{
                    $amount = $amount + $goods['price'] * $goods['number'];
                }
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($goods['number'], $user['id'], '购买赠送', 'subsidy', $order['order_no'], 'currency_nfr', '0', $goods['goods_id']);
            }
        }
        if ($amount > 0){
            model('app\api\model\wanlshop\Order')->where('id',$order_id)->update(['statusb' => '1']);
        }
        if ($ns_am > 0){
            $this->CalcOrderNinestar($order_id, $ns_am, $amount);
        }
        if($ws_am > 0 && $user['svip_level'] == 1){
            model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->setInc('ws_am', $ws_am);
            $uulinfo = model('app\admin\model\UserUpgradeLog')
                ->where('pay_user_id', $user['id'])
                ->where('lv_old', 1)
                ->where('lv_new', 2)
                ->where('item', '1')
                ->find();
            if($uulinfo){
                if($uulinfo['ws_am'] > 2900) {
                    model('app\common\model\User')
                        ->where('id', $user['id'])
                        ->update(['svip_level' => 2]);
                }
            }
        }
        //测试秒结
//        $this->autoCalcDaliy(false);
    }

    /**
     * 确认货
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderConfirm($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns'
        );
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
                $cl_arr = array();
                $cl_arr['status'] = '1';
                $cl_arr['before'] = $user[$mtype];
                $cl_arr['after'] = $user[$mtype] + $cl['money'];
                $cl_arr['updatetime'] = strtotime('now');
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update($cl_arr);

                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }

        $goods = null;
        $orderGoods = OrderGoods::where('order_id', $order_id)->find();
        if ($orderGoods) {
            $goods = Goods::get($orderGoods->goods_id);
        }
        $activity = Ninestar::get($goods->activity_id);
        //帮卖活动
        if ($goods && $goods->activity == 1 && $goods->activity_type == 'dragon') {
            if ($activity && $activity->type == 'dragon') {
                //先查询对应订单的分享记录
                $pay_user = GoodsShare::where(['user_id'=>$order['user_id'],'order_id'=>$order['id']])->find();
                if($pay_user){
                    //分享记录查询帮卖店铺
                    $help = \app\api\model\wanlshop\HelpSell::where(['shop_id' => $goods->shop_id, 'good_id' => $goods->id,'user_id'=>$pay_user->shop_id])->find();
                    //存在帮卖店铺记录
                    if ($help) {
                        //查询有效支付记录
                        $pay = model('app\api\model\wanlshop\Pay')->where(['pay_state'=>1,'order_id' => $order_id, 'type' => 'goods'])->find();
                        //反钱给商品店铺的本金-让利 goods->shop_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')
                            ->money(+($pay['price'] * (1-$activity->pv_value)), $help->shop_id, '买家确认收货', 'pay', $order['order_no']);
                        //给帮卖店上级分额
                        $helpUser = \app\common\model\User::get($help->user_id);
                        $inviter = \app\common\model\User::get($helpUser->inviter_id);
                        if ($inviter) {
                            $sub = 0;
                            //普通
                            if ($inviter->vip_level == VipEnum::ORDINARY_STORE) {
                                $sub = 0.3;
                            }
                            //资深
                            if ($inviter->vip_level == VipEnum::SENIOR_STORE) {
                                $sub = 0.4;
                            }
                            //高级
                            if ($inviter->vip_level == VipEnum::SUPER_STORE) {
                                $sub = 0.5;
                            }
                            if ($sub) {
                                $commissions =  bcmul($activity->commission, $sub, 2);
                                controller('addons\wanlshop\library\WanlPay\WanlPay')
                                    ->money(+($commissions), $helpUser->inviter_id, '买家确认收货，给帮卖店上级分佣', 'subsidy', $order['order_no']);
                            }
                        }
                        //给帮卖店反佣金$help->user_id
                        controller('addons\wanlshop\library\WanlPay\WanlPay')
                            ->money(+($activity->commission), $help->user_id, '买家确认收货，给店铺返佣', 'subsidy', $order['order_no']);
                        //推几反本
                        $backRule = $activity->back_rule;    //帮卖返本规则
                        //先判断分享人是否购买
                        $info = GoodsShare::where([
                            'good_id' => $goods->id,
                            'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                            'user_id' => $pay_user['share_user'], //查询当前订单来源的分享用户
                            'state' => 1,
                            'is_back' => 0,
                        ])->find();
                        if($info){
                            //查询分享人分享出去的是否买了，是否满足条件
                            $shares_pay_count = GoodsShare::where([
                                'good_id' => $goods->id,
                                'shop_id' => $pay_user->shop_id,//对应帮卖店铺
                                'share_user' => $pay_user['share_user'], //查询当前订单来源的分享用户
                                'state' => 1,
                                'is_back' => 0,
                            ])->count();
                            if ($shares_pay_count >= $backRule) {
                                controller('addons\wanlshop\library\WanlPay\WanlPay')
                                    ->money(+($goods->price), $pay_user['share_user'], '帮卖推'.$backRule.'返本', 'subsidy', $order['order_no']);
                                GoodsShare::where('id',$info['id'])->update(['is_back' => 1]);
                            }
                        }
                    }
                }
            }
        }

    }

    public function calcOrderRefund($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns'
        );

        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
            }
        }
    }

    public function CalcOrderNinestar($order_id, $ns_amt, $amount){
        //首月不考核
        $ns_am = $ns_amt * $this->ns_rate;
        if ($ns_am > 0){
            $order = model('app\api\model\wanlshop\Order')->where('id',$order_id)->find();
            if ($order){
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星订单C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                $loops = 0;
                $nsactivate = false;
                $userr = model('app\common\model\User')->where('id', $order['user_id'])->find();
                if ($userr['saas_id'] > 0){
                    $monthly_ns = $userr['monthly_nsn'] + 1;
                    while($monthly_ns > 9) {
                        $monthly_ns = $monthly_ns - 10;
                    }
                    $nsdo = true;
                    $nsiv = false;
                    while($userr){
                        if ($loops == 0){
                            model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_nsn', 1);
                            $ns_ac = array();
                            if($userr['ns_status'] == '0') {
                                $ns_ac['ns_status'] = 1;
                                $ns_ac['monthly_ns'] = 9;
                                $nsactivate = true;
                            }
                            $ns_ac['ns_pay'] = $userr['ns_pay'] + $amount;
                            model('app\common\model\User')->where('id', $userr['id'])->update($ns_ac);
                            if($userr['monthly_nsn'] > 9 && $monthly_ns == 0){
                                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                            }
                        }else if ($loops > 0){
                            if($nsactivate) {
                                if ($loops == 1) {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('invite_numsns', 1);
                                    $userr['invite_numsns'] += 1;
                                } else {
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('team_numsns', 1);
                                }
                            }
                            if($nsiv){
                                if(($userr['vip_level'] > 2 && $userr['invite_nums'] >= 9) || ($userr['vip_level']==2 && $userr['invite_nums'] >= 9 && $userr['team_nums'] >= 99)) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, $userr['id'], '九星邀请', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am * 0.5);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am * 0.5);
                                }
                                $nsdo = false;
                                $nsiv = false;
                            }
                            if ( $loops == $monthly_ns && $userr['monthly_ns'] >= 9 && (($userr['ns_status'] == '1' && $userr['invite_numsns'] >= 9) || ($userr['vip_level']>=2 && $userr['invite_nums'] >= 9)) ){
                                $ft = true;
                                if($userr['vip_level'] >= 3) $ft = false;
                                if($userr['team_nums'] >= 99) {
                                    $lastdata = model('addons\signin\model\Signin')->where('user_id', $userr['id'])->order('createtime', 'desc')->find();
                                    $successions = $lastdata && $lastdata['createtime'] > Date::unixtime('day', -1) ? $lastdata['successions'] : 0;
                                    if($successions >= 15) $ft = false;
                                }
                                if($ft) {
                                    if ($userr['monthly_ns_am'] < $this->ns_top_std) {
                                        if (($userr['monthly_ns_am'] + $ns_am) >= $this->ns_top_std)
                                            $ns_am = $this->ns_top_std - $userr['monthly_ns_am'];
                                    }
                                }
                                if($ns_am > 0) {
                                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, $userr['id'], '九星奖励', 'subsidy', $order['order_no'], 'currency_ns', '0');
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('monthly_ns_am', $ns_am);
                                    model('app\common\model\User')->where('id', $userr['id'])->setInc('ns_dynamic', $ns_am);
                                    $nsdo = false;
                                    $nsiv = true;
                                }
                            }
                        }
                        $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                        $loops++;
                    }
                    if ($nsdo){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am, 1, '九星奖励C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                        $nsiv = true;
                    }
                    if($nsiv){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($ns_am * 0.5, 1, '九星邀请C', 'subsidy', $order['order_no'], 'currency_ns', '0');
                    }
                }
            }
            model('app\api\model\wanlshop\Pay')->where('order_id', $order_id)->update(['commission'=>$ns_amt]);
        }
        //
    }

    public function intonet($uid,$order_no='', $calc=true){
//        判断是否已经入网
        $uinfo = model('app\common\model\User')->where('id', $uid)->find();
        $u2info = model('app\admin\model\UserTrident2n')
            ->where('saas_id', $uinfo['saas_id'])
            ->where('state', '1')
            ->where('status', '0')
            ->where('user_id', $uinfo['id'])
            ->order('id', 'asc')->find();
        if(!$u2info) {
//        寻找上级
            $frc = true;
            $rid = $uinfo['inviter_id'];
            while ($frc && $rid > 1) {
                $rinfo = model('app\common\model\User')->where('id', $rid)->find();
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('user_id', $rinfo['id'])
                    ->order('id', 'asc')->find();
                if ($r2info) {
                    $frc = false;
                }else{
                    $rid = $rinfo['inviter_id'];
                }
            }
            $count = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('rid', $rid)
                ->count();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $market = $count + 1;
            $narr = array();
            $narr['saas_id'] = $uinfo['saas_id'];
            $narr['user_id'] = $uid;
            $narr['rid'] = $rid;
            $narr['market'] = $market;
            $narr['market_kh'] = $market;
            $narr['is_calc'] = $calc?1:0;
            $createtime = strtotime($year.'-'.$month.'-'.$day);
            $narr['createtime'] = $createtime;
            model('app\admin\model\UserTrident2n')->insert($narr);

            if (!$calc) return 0;
            $r2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $uinfo['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $rid)
                ->order('id', 'asc')->find();
            $loops = 1;
            $tmarket = $market;
            $f2n = true;
            $d2n = false;
            $zy2n = true;
            while($r2info){
                if ($loops == 1){
                    if($tmarket < 3){
                        $mt = 200;
                        $f2n = true;
                    }else{
                        $mt = 400;
                        $f2n = false;
                    }
                    model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('invite_nums', 1);
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_ns', '0');
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mt, $r2info['user_id'], '店长推荐', 'subsidy', $order_no, 'currency_xnb', '0');
                }else{
                    if ($f2n){
                        if ($tmarket > 2) {
                            $f2n = false;
                            $d2n = true;
                        }
                    }
                }
                if($tmarket < 3){
                    $r2_arr = array();
                    if($r2info['team_add'] == 0) $r2_arr['team_add'] = $tmarket;
                    else if($r2info['team_add'] != $tmarket) $r2_arr['team_add'] = 3;
                    if (array_key_exists('team_add', $r2_arr)) {
                        $r2info['team_add'] = $r2_arr['team_add'];
                        model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->update(['team_add' => $r2_arr['team_add']]);
                    }
                }
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('team_nums', 1);
//                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('month_nums', 1);
                model('app\admin\model\UserTrident2n')->where('id', $r2info['id'])->setInc('week_nums', 1);
                if($d2n){
                    $mp = 200;
                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['invites_numsw'] > 1) $mp += 100;
                    if($rinfo['is_distributor'] == 1) $mp += 300;
                    elseif($rinfo['svip_level'] > 1) $mp += 300;
//                    if($r2info['month'] > 1 && $r2info['month_kh'] == '0') $mp -= 50;
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $r2info['user_id'], '店长共富', 'subsidy', $order_no, 'currency_ns', '0');
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($mp, $r2info['user_id'], '店长共富', 'subsidy', $order_no, 'currency_xnb', '0');
//                    if($rinfo['invites_nums'] >= 6 && $rinfo['team_nums'] >= 99 && $rinfo['svip_level'] > 1)
                    $d2n = false;
                }
//                if($zy2n){
//                    $rinfo = model('app\common\model\User')->where('id', $r2info['user_id'])->find();
//                    if($rinfo['vip_level'] == 5) {
//                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(300, $r2info['user_id'], '店长卓越', 'subsidy', $order_no, 'currency_rmb', '0');
//                        $zy2n = false;
//                    }
//                }
                $tmarket = $r2info['market_kh'];
                $r2info = model('app\admin\model\UserTrident2n')
                    ->where('saas_id', $uinfo['saas_id'])
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('user_id', $r2info['rid'])
                    ->order('id', 'asc')->find();
                $loops++;
            }

            return true;
        }

        return false;
    }

    public function autoCalcDaliy($daily = true)
    {


    }

    public function autoWeekly(){
        $weekly_ns = true;
        if(date("w") == 1){
            foreach(model('app\admin\model\UserTrident2n')
                        ->where('state', '1')
                        ->where('status', '0')
                        ->order('id', 'asc')->select() as $ut2n){
                $loopm = 1;
                foreach(model('app\admin\model\UserTrident2n')
                            ->where('state', '1')
                            ->where('status', '0')
                            ->where('rid', $ut2n['user_id'])
                            ->order('week_nums desc,id asc')->select() as $ut2n2){
                    $ut2_arr = array();
                    $ut2_arr['week_nums'] = 0;
                    $ut2_arr['market_kh'] = $loopm;
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update($ut2_arr);
                    $loopm++;
                }
            }
            if($weekly_ns){
                model('app\common\model\User')->update(['ns_fhq' => 0]);
                $user1 = model('app\common\model\User')->where('id', 1)->field('currency_ns')->find();
                $amount = $user1['currency_ns'] * 0.3;
                $fhqt = 0;
                foreach(model('app\common\model\User')
                            ->where('id', '>', 0)
                            ->where('adv_nums', 7)
                            ->select() as $user){
                    $xf = $user['ns_pay'] - $user['ns_dynamic'] - $user['ns_static'];
                    if($xf > 500) {
                        $fhq = floor($xf/500);
                        $bs = 1;
                        if($user['vip_level'] == 2) $bs = 2;
                        if($user['vip_level'] > 2) $bs = 5;
                        if($bs < 5){
                            if ($user['monthly_ns'] >= 9 && $user['ns_status'] == '1' && $user['invite_numsns'] >= 9) $bs = 3;
                        }
                        $fhq = $fhq * $bs;
                        $fhqt = $fhqt + $fhq;
                        model('app\common\model\User')->where('id', $user['id'])->update(['ns_fhq' => $fhq]);
                    }
                }
                if($fhqt > 0){
                    $am = $amount / $fhqt;
                    foreach(model('app\common\model\User')
                                ->where('id', '>', 0)
                                ->where('ns_fhq', '>', 0)
                                ->select() as $user){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($am*$user['ns_fhq'], $user['id'], '周薪', 'subsidy', '', 'currency_ns');
                    }
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$amount*0.3, 1, '分红发放', 'subsidy', '', 'currency_ns');
                }
                model('app\common\model\User')->update(['adv_nums' => 0]);
            }
        }

    }

    public function autoMonthly(){
        $user_arr = array(
            'monthly_nsn' => 0,
            'monthly_ns_am' => 0,
        );
        foreach(model('app\common\model\User')
                    ->where('id', '>', 0)
                    ->select() as $user){
            $user_arr['monthly_ns'] = $user['monthly_nsn'];
            model('app\common\model\User')
                ->where('id', $user['id'])
                ->update($user_arr);
        }
//        foreach(model('app\admin\model\UserTrident2n')
//            ->where('state', '1')
//            ->where('status', '0')
//            ->order('id', 'asc')->select() as $ut2n){
//            $ut_arr = array();
//            $ut_arr['month'] = $ut2n['month'] + 1;
//            $ut_arr['team_add'] = 0;
//            $ut_arr['month_nums'] = 0;
//            if ($ut2n['team_add'] == 3){
//                $ut_arr['month_kh'] = '1';
//            }else{
//                $ut_arr['month_kh'] = '0';
//            }
//            model('app\admin\model\UserTrident2n')->where('id', $ut2n['id'])->update($ut_arr);
//        }
    }

    public function invitationCode($user_id){
        $ivcode = Random::alnum(8);
        $checkhv = true;
        while($checkhv){
            $count = model('app\common\model\User')->where('ivcode', $ivcode)->count();
            if ($count > 0 ){
                $ivcode = Random::alnum(8);
            }else{
                $checkhv = false;
            }
        }
        model('app\common\model\User')->where('id', $user_id)->update(['ivcode' => $ivcode]);
    }


    public function doconfirm($params){
        $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
            ->where('id', $params['id'])
            ->find();
        if ($remitinfo) {
            if($remitinfo['uplogid'] > 0){
                if($params['action'] == 'received'){
                    $count = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('status', '<>', 'confirm')
                        ->where('uplogid', $remitinfo['uplogid'])
                        ->count();
                    if($count == 0){
                        model('app\admin\model\UserUpgradeLog')
                            ->where('id', $remitinfo['uplogid'])
                            ->update(['state' => '1']);
                        if($remitinfo['rtype'] > 0){
                            $uul = model('app\admin\model\UserUpgradeLog')
                                ->where('id', $remitinfo['uplogid'])
                                ->find();
                            if ($uul['lv_new'] > 2) {
                                $this->wslvUp($uul['pay_user_id'], $uul['lv_new']);
                            }
                        }
                    }
                }
                if($params['action'] == 'cancel'){
                    model('app\admin\model\UserUpgradeLog')
                        ->where('id', $remitinfo['uplogid'])
                        ->update(['state' => '2']);
                }
            }
            if($remitinfo['rtype'] < 2){
                if($params['action'] == 'received'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['user_id'], '进货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                    if($remitinfo['rtype'] == 0){
                        $user = model('app\common\model\User')->where('id', $remitinfo['user_id'])->find();
                        $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                        if($userr['svip_level'] >= $user['svip_level']){
                            $pjrate = 0.05;
                            if($userr['svip_level'] == 4) $pjrate = 0.03;
                            if($userr['svip_level'] > 4) $pjrate = 0.02;
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount']*$remitinfo['discount']*$pjrate, $userr['id'], '平级奖励', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                        }
                        if($user['svip_level'] < 11) $this->wslvUp($user['id'], $user['svip_level'], false);
                    }
                }
                if($params['action'] == 'cancel'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['rec_user_id'], '批发商退货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                }
            }
        }
    }

    public function wslvUp($user_id, $uplv, $updo=true){
        $loops = 0;
        if($updo) {
            model('app\common\model\User')
                ->where('id', $user_id)
                ->update(['svip_level' => $uplv]);
            if($uplv == 3 || $uplv == 4){
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
                while($userr){
                    if($loops>0){
                        model('app\common\model\User')->where('id', $userr['id'])->setInc('wslv'.$uplv, 1);
                        if($userr['svip_level'] == 3){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv3', '>', 0)
                                ->sum('wslv3');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv3', '>', 0)->order('wslv3', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv3'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 20) $this->wsUpOrder($userr['id'], 4);
                        }else if($userr['svip_level'] == 4){
                            $count = model('app\common\model\User')
                                ->where('inviter_id', $userr['id'])
                                ->where('wslv4', '>', 0)
                                ->sum('wslv4');
                            $lp = 1;
                            foreach(model('app\common\model\User')
                                        ->where('inviter_id', $userr['id'])
                                        ->where('wslv4', '>', 0)->order('wslv4', 'desc')->select() as $xj){
                                if ($lp < 3) $count = $count - $xj['wslv4'];
                                else{
                                    break;
                                }
                                $lp++;
                            }
                            if($count >= 10) $this->wsUpOrder($userr['id'], 5);
                        }
                    }
                    $userr = model('app\common\model\User')->where('id', $userr['inviter_id'])->find();
                    $loops++;
                }
            }
        }
        if($uplv >= 5){
            if($updo){
                $user = model('app\common\model\User')->where('id', $user_id)->find();
                $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
            }else{
                $userr = model('app\common\model\User')->where('id', $user_id)->find();
            }
            if($userr['svip_level'] == $uplv) {
                $count = model('app\common\model\User')
                    ->where('inviter_id', $userr['id'])
                    ->where('svip_level', '>=', $userr['svip_level'])
                    ->count();
                if ($count >= 3) {
                    $year = date('Y');
                    $month = date('m');
                    $createtime = strtotime($year . '-' . $month . '-' . '01');
                    $thismontham = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('user_id', $userr['id'])
                        ->where('createtime', '>=', $createtime)
                        ->where('rtype', '0')
                        ->where('status', 'confirm')
                        ->sum('amount');
                    if ($thismontham >= 300000 && $userr['svip_level'] < 11) wslvUp($userr['id'], $userr['svip_level'] + 1);
                }
            }
        }
    }

    public function wsUpOrder($user_id, $uplv){
        $user = model('app\common\model\User')->where('id', $user_id)->find();
        $uulinfo = model('app\admin\model\UserUpgradeLog')
            ->where('pay_user_id', $user['id'])
            ->where('lv_old', $user['svip_level'])
            ->where('lv_new', $uplv)
            ->where('item', '1')
            ->find();
        if (!$uulinfo){
            $margin = $this->lvsinfo[$uplv]['margin'] - $this->lvsinfo[$user['svip_level']]['margin'];
            $orderam = $this->lvsinfo[$uplv]['orderam'];
            $orderam = 0;

            $cid = 0;
            if ($margin > 0) $cid = 1;
            $pid = 1;
            $rid = $user['inviter_id'];
            $fsj = true;
            while($fsj) {
                $puser = model('app\common\model\User')
                    ->where('id', $rid)
                    ->find();
                if($puser['svip_level'] >= $uplv && $puser['currency_rmb'] >= $orderam/$this->lvsinfo[$uplv]['discount']){
                    $pid = $puser['id'];
                    $fsj = false;
                }
                $rid = $puser['inviter_id'];
                if($puser['id'] == 1) $fsj = false;
            }
            $uul_arr = array();
            $uul_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
            $uul_arr['pay_user_id'] = $user['id'];
            $uul_arr['rec_user_id'] = $pid;
            $uul_arr['cpn_user_id'] = $cid;
            $uul_arr['item'] = '1';
            $uul_arr['lv_old'] = $user['svip_level'];
            $uul_arr['lv_new'] = $uplv;
            $uul_arr['orderam'] = $orderam;
            $uul_arr['margin'] = $margin;
            $uul_arr['amount'] = $orderam + $margin;
            $uul_arr['createtime'] = time();
            if($margin > 0) $uul_arr['item'] = '1';
            $uulid = model('app\admin\model\UserUpgradeLog')->insertGetId($uul_arr);
            if($uulid) {
                if ($margin > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $cid;
                    $remit_arr['amount'] = $margin;
                    $remit_arr['payamount'] = $margin;
                    $remit_arr['discount'] = 1;
                    $remit_arr['rtype'] = '2';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                }
                if ($orderam > 0) {
                    $remit_arr = array();
                    $remit_arr['pay_sn'] = date("Ymdhis") . sprintf("%08d", $user['id']) . mt_rand(1000, 9999);
                    $remit_arr['user_id'] = $user['id'];
                    $remit_arr['rec_user_id'] = $pid;
                    $remit_arr['amount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['payamount'] = $orderam / $this->lvsinfo[$uplv]['discount'];
                    $remit_arr['discount'] = 1;//$this->lvsinfo[$uplv]['discount'];
                    $remit_arr['rtype'] = '1';
                    $remit_arr['uplogid'] = $uulid;
                    $remit_arr['createtime'] = time();
                    $remit_arr['status'] = 'pending';
                    model('app\admin\model\wanlshop\RemitOrder')->insert($remit_arr);
                    if ($pid > 5){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$orderam/$this->lvsinfo[$uplv]['discount'], $pid, '批发商进货', 'subsidy', $remit_arr['pay_sn'], 'currency_rmb');
                    }
                }
            }
        }
    }

    public static function sendRequest($url, $params = [], $method = 'POST', $options = [], $Sign=null)
    {
        $method = strtoupper($method);
        $protocol = substr($url, 0, 5);
        $query_string = is_array($params) ? http_build_query($params) : $params;

        $ch = curl_init();
        $defaults = [];
        if ('GET' == $method) {
            $geturl = $query_string ? $url . (stripos($url, "?") !== false ? "&" : "?") . $query_string : $url;
            $defaults[CURLOPT_URL] = $geturl;
        } else {
            $defaults[CURLOPT_URL] = $url;
            if ($method == 'POST') {
                $defaults[CURLOPT_POST] = 1;
            } else {
                $defaults[CURLOPT_CUSTOMREQUEST] = $method;
            }
            $defaults[CURLOPT_POSTFIELDS] = $params;
        }

        $defaults[CURLOPT_HEADER] = false;
        $defaults[CURLOPT_USERAGENT] = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/45.0.2454.98 Safari/537.36";
        $defaults[CURLOPT_FOLLOWLOCATION] = true;
        $defaults[CURLOPT_RETURNTRANSFER] = true;
        $defaults[CURLOPT_CONNECTTIMEOUT] = 3;
        $defaults[CURLOPT_TIMEOUT] = 3;

        // disable 100-continue
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Expect:'));
        if($Sign != null) {
//            $defaults[CURLOPT_HEADER] = true;
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Sign:$Sign"));
        }

        if ('https' == $protocol) {
            $defaults[CURLOPT_SSL_VERIFYPEER] = false;
            $defaults[CURLOPT_SSL_VERIFYHOST] = false;
        }

        curl_setopt_array($ch, (array)$options + $defaults);

        $ret = curl_exec($ch);
        $err = curl_error($ch);

        if (false === $ret || !empty($err)) {
            $errno = curl_errno($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);
            return [
                'ret'   => false,
                'errno' => $errno,
                'msg'   => $err,
                'info'  => $info,
            ];
        }
        curl_close($ch);
        return [
            'ret' => true,
            'msg' => $ret,
        ];
    }

    public function checkMerchant(){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/merchant/info", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkBalance($gateway = ''){
        $signbf = 'merchantId='.$this->merchantId.'&key='.$this->key;
        $url = 'https://pay.s100mi.com/api/v1/merchant/balance';
        if($gateway == 'yeepay') $url = $url.'/yeePay';
        $sign = strtoupper(md5($signbf));
        $data = [
            'merchantId' => $this->merchantId,
        ];
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($gateway == 'yeepay') $balance = $result['msg']['data']['totalAccountBalance'];
            else $balance = $result['msg']['data']['balance'];
            if($balance == 0) {
                return ['code' => 10005 ,'msg' => '余额不足'];
            }else{
                return $result['msg'];
            }
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function OutTransfer($id, $gateway='sandpay'){
        $url = "https://pay.s100mi.com/api/v1/out-order/transfer";
        if($gateway == 'yeepay') $url = $url.'Yee';
        $order = model('app\admin\model\wanlshop\Withdraw')
            ->where('status', 'created')
            ->where('id',$id)->find();
        if($order){
            if($gateway == 'yeepay'){
                if ($order['type_text'] == 'CMB') $order['type_text'] = 'CMBCHINA';
                if ($order['type_text'] == 'COMM') $order['type_text'] = 'BOCO';
                if ($order['type_text'] == 'PAB') $order['type_text'] = 'SDB';
                if ($order['type_text'] == 'CITIC') $order['type_text'] = 'ECITIC';
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'amount' => $order['money'],//
                    'bankAccountType' => 'DEBIT_CARD',//
                    'bankNo' => $order['type_text'],
                    'feeChargeSide' => 'PAYEE',
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '提现'
                ];
                if($order['mtype'] == 'currency_tz'){
                    $data['amount'] = $order['money']*60;
                    $data['remark'] = '提现';
                }
                if($order['mtype'] == 'currency_cny'){
                    $data['amount'] = $order['money'];
                    $data['remark'] = '异业提现';
                }
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
            }else{
                $data = [
                    'accName' => $order['truename'],
                    'accNo' => $order['account'],//账号
                    'accType' => 4,
                    'amount' => $order['money']*60,//
                    'merchantId' => $this->merchantId,
                    'merchantOrderId' => $order['id'],//订单号
                    'remark' => '往来',
                    'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
                ];
                $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
            }
            $sign = strtoupper(md5($signbf));
            $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
            if ($result['ret']) {
                $result['msg'] = (array)json_decode($result['msg'], true);
//                var_dump($result['msg']);
                return $result['msg'];
            }else{
                return ['code' => 10005 ,'msg' => '接口异常'];
            }
        }
        return ['code' => 10005 ,'msg' => '订单异常'];

        if($gateway == 'yeepay'){
            $data = [
                'accName' => '季正新',
                'accNo' => '**********006839513',//账号
                'amount' => 4,//
                'bankAccountType' => 'DEBIT_CARD',//
                'bankNo' => 'NBCB',
                'feeChargeSide' => 'PAYEE',//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '提现'
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&amount='.$data['amount'].'&bankAccountType='.$data['bankAccountType'].'&bankNo='.$data['bankNo'].'&feeChargeSide='.$data['feeChargeSide'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&key='.$this->key;
        }else{
            $data = [
                'accName' => '季正新',
                'accNo' => '**********006839513',//账号
                'accType' => 4,
                'amount' => 4,//
                'merchantId' => $this->merchantId,
                'merchantOrderId' => '********',//订单号
                'remark' => '往来',
                'type' => 1,//0公1私
//            'bankName' => '宁波银行',
//            'bankType' => '',
            ];
            $signbf = 'accName='.$data['accName'].'&accNo='.$data['accNo'].'&accType='.$data['accType'].'&amount='.$data['amount'].'&merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&remark='.$data['remark'].'&type='.$data['type'].'&key='.$this->key;
        }
        $sign = strtoupper(md5($signbf));
        var_dump($data);
        var_dump($signbf);
        var_dump($sign);
        $result = $this->sendRequest($url, $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            var_dump($result['msg']);
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function checkOrderIn($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'orderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&orderId='.$data['orderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/in-order/inquire-order", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
//                if( $result['msg']['data']['orderStatus'] == 'SUCCESS') return true;
//                else return false;
                var_dump($result['msg']);
            }
            return $result['msg'];
        }else{
            return ['code' => 10005 ,'msg' => '接口异常'];
        }
    }

    public function checkOrderOut($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'merchantOrderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/out-order/transfer/query", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
                if( $result['msg']['data']['status'] == 'SUCCESS') return true;
                else return false;
//                var_dump($result['msg']);
            }
            return false;//$result['msg'];
        }else{
            return false;//['code' => 10005 ,'msg' => '接口异常'];
        }
    }
    public function test($auction_id = 1)
    {
    }
}
