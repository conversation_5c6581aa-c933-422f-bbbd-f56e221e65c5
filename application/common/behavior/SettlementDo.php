<?php

namespace app\common\behavior;

use addons\wanlshop\library\WeixinSdk\Mp;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\Order;
use app\api\model\wanlshop\OrderGoods;
use app\api\model\wanlshop\Pay;
use app\api\model\wanlshop\Shop;
use app\common\enum\VipEnum;
use app\common\model\CurrencyNsLog;
use app\common\model\CurrencyRmbLog;
use app\common\model\User;
use app\index\model\wanlshop\Ninestar;
use EasyWeChat\Factory;
use fast\Date;
use fast\Http;
use fast\Random;
use MongoDB\Driver\Query;
use think\Cache;
use think\Db;
use think\Exception;
use think\Hook;
use think\Log;

class SettlementDo
{
    public function __construct()
    {
        $this->merchantId = 6996282976456978432;
        $this->key = 'C-xvQua8UeXsUB2y/Aakhpb3FKOW0APEMgJ22Rdyfkb+FIEok2zva89fqZLTM7iHLINM3uH8yM8FR9mjf3Ua+FK8qbKYMAXE+YMBD0HbCRelr0bqIn50qKpqSohrD+MRLWbjhNyhMyxixuz+UKFTiw==';
        $this->nottest = true;
        $this->setting = model('app\common\model\Config')->where('group','settlement')->column('name,value');
        $this->lvname = model('app\admin\model\UserVipLevel')->column('id,name');
        $this->lvinfo = model('app\admin\model\UserVipLevel')->column('id,name,cost,bonus,upgrade,share,share1,share2,compound,excitation,profit');
        $this->lvsinfo = model('app\admin\model\UserSvipLevel')->column('id,name,margin,orderam,discount,upgrade,bonus,share,compound,excitation,profit');
        $this->slvsinfo = model('app\admin\model\UserSvipLevels')->column('id,name,amount,rate');
        $this->union_iv = model('app\admin\model\UserVipLevelu')->column('id,iv_rate');
        $this->union_jc = model('app\admin\model\UserVipLevelu')->column('id,jc_rate');
        $this->saas_info = model('app\admin\model\Saas')->column('id,shop_id_vip,shop_id_ws,coupon_id');
        $this->dkqInfo = model('\app\admin\model\UserSvipLvDkq')->column('id,rate');
        $this->fxs_rate = array(
            1 => 0,
            2 => 0.5,
            3 => 0.3,
            4 => 0.1,
            5 => 0.1
        );
//        1：消费
//        2：联创
//        3：合伙人
//        4：卓越
        $this->input = 1111;
        $this->suid = 3;
        $this->bonusrate = 0.005;
        $this->fday = 10;
        $this->eday = 1;
        $this->jckh = array(
            0 => 0,
            1 => 100000,
            2 => 1000000
        );
        $this->jc = array(
            0 => 0,
            1 => 0.5,
            2 => 1
        );
        $this->planA = [2];
        $this->planrate = array(
            'A' => array(
                'ratePv' => 1,
                'rateBs' => 7,
                'rateBs2' => 8,
                'dpMax' => 5,
                'tj' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0.15,
                    3 => 0.2,
                    4 => 0.3,
                    5 => 0.3,
                    6 => 0.3,
                    7 => 0.3,
                    8 => 0.3
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.1,
                    3 => 0.12,
                    4 => 0.14,
                    5 => 0.15,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 300000,
                    4 => 700000,
                    5 => 1000000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0
                ),
                'glrate' => array(
                    1 => 0.1,
                    2 => 0.1,
                    3 => 0.1,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 4,
                    4 => 8,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8
                )
            ),
            'B' => array(
                'ratePv' => 8,
                'rateBs' => 1,
                'dpMax' => 4,
                'tj' => array(
                    0 => 0.1,
                    1 => 0.1,
                    2 => 1,
                    3 => 0.2
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.08,
                    3 => 0.1,
                    4 => 0.12,
                    5 => 0.14,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 300000,
                    4 => 700000,
                    5 => 1000000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0
                ),
                'glrate' => array(
                    1 => 0.0,
                    2 => 0.4,
                    3 => 0.2,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 4,
                    4 => 8,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8
                )
            ),
            'C' => array(
                'ratePv' => 1,
                'rateBs' => 7,
                'rateBs2' => 8,
                'dpMax' => 5,
                'tj' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0.15,
                    3 => 0.2,
                    4 => 0.25,
                    5 => 0.3,
                    6 => 0.3,
                    7 => 0.3,
                    8 => 0.3,
                    9 => 0.3
                ),
                'zy' => array(
                    0 => 0,
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0.16,
                    7 => 0.18,
                    8 => 0.19,
                    9 => 0.2
                ),
                'dp' => array(
                    1 => 0,
                    2 => 0.1,
                    3 => 0.11,
                    4 => 0.12,
                    5 => 0.14,
                    6 => 0.15,
                    7 => 0.16,
                    8 => 0.18,
                    9 => 0.2
                ),
                'fd' => array(
                    1 => 0,
                    2 => 100000,
                    3 => 200000,
                    4 => 300000,
                    5 => 700000,
                    6 => 1000000,
                    7 => 1000000,
                    8 => 1000000,
                    9 => 1000000
                ),
                'gl' => array(
                    1 => 0,
                    2 => 0,
                    3 => 0,
                    4 => 0,
                    5 => 0,
                    6 => 0,
                    7 => 0,
                    8 => 0,
                    9 => 0
                ),
                'glrate' => array(
                    1 => 0.1,
                    2 => 0.1,
                    3 => 0.1,
                    4 => 0.1,
                    5 => 0.05,
                    6 => 0.05,
                    7 => 0.05,
                    8 => 0.05
                ),
                'glstd' => array(
                    1 => 0,
                    2 => 2,
                    3 => 3,
                    4 => 4,
                    5 => 8,
                    6 => 8,
                    7 => 8,
                    8 => 8,
                    9 => 8
                )
            )
        );

        $this->calcData = array(
            '0' => array(
                'shop_id' => 1,
                'category_id_lv1' => 107,//会员专享
                'category_id_lv2' => 108,//店长专享
                'category_id_lv3' => 228
            )
        );
        $this->ns_rate = 0.28;
        $this->ns_top_std = 2000;
        $this->fgDP = false;
    }


    public function run(&$params)
    {

        if($params['action'] == 'orderRefund'){
            $this->calcOrderRefund($params['order_id']);
        }

        if($params['action'] == 'cancelOrder'){
            $this->calcCancelOrder($params['order_id']);
        }

        if($params['action'] == 'ivc'){
            $this->invitationCode($params['user_id']);
        }

        if($params['action'] == 'net2'){
            $this->intoNet2($params['user_id'], 'HT',0,0,false);
        }

        if($params['action'] == 'distributor'){
            $this->CalcDistributor($params['user_id'], $params['amount']);
        }

        if($params['action'] == 'intoNet'){
            $this->intonet($params['user_id'], 'HT', false);
        }
        if($params['action'] == 'wslvUp'){
            $this->wslvUp($params['user_id'], $params['uplv']);
        }

        if($params['action'] == 'autoSync'){
            $this->autoSync();
//            $this->autoSyncFh();
        }

        if($params['action'] == 'autoOrderSync'){
            $this->autoOrderSync();
        }

        if($params['action'] == 'marketAct'){
            $this->marketAct(1050);
        }
        if($params['action'] == 'pMarketAct'){
            $this->pMarketAct();
        }
        if($params['action'] == 'pMarketAm'){
            $this->pMarketAm(30027);
//            $this->pMarketAm(30004);
//            $this->pMarketAm(6071);
        }

        if($params['action'] == 'newAccount'){
            $this->newAccount();
        }

        if($params['action'] == 'actAccount'){
            $this->actAccount(11571, 11970);
//            $this->actAccount(10251, 30000);
//            $mobileArr = array(***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********,***********);
//            foreach($mobileArr as $k=>$v) {
//                $user = model('app\common\model\User')->where('mobile', $v)->find();
//                echo ($k+1)."Name:".$user->username."Mobile:".$user->mobile."\n";
//                model('app\common\model\User')->where('mobile', $v)->update(['is_pw' => 1, 'pw_no' => $k+1]);
//                $this->actAccount($user->id, $user->id+1);
//            }
//            exit;
        }
        if($params['action'] == 'handBonusRefund'){
            $this->handBonusRefund();
        }
        if($params['action'] == 'BonusCount'){
            $this->autoBonusCount();
        }
        if($params['action'] == 'Net2DPOne'){
            exit;
            $this->Net2DPOne(30084, '****************', 4000, 0, 0, 1);
            $this->Net2DPOne(30081, '****************', 4000, 0, 0, 1);
        }
        if($params['action'] == 'doRefund'){
//            user length 5 order old 11+ul order new 18+ul
            $isDo = false;
            $userR = true;
            $moneyBk = true;

            $length = strlen($params['id']);
            $check = false;
            if($length ==  5){
                $user = model('app\common\model\User')->where('id', $params['id'])->find();
            }else if($length == 16){
                $order = model('app\api\model\wanlshop\Order')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
//                    ->where('state', '<', 7)
//                    ->where('statusb', '1')
                    ->where('order_no',$params['id'])
                    ->find();
                if($order) {
                    $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
                    $check = true;
                }
            }else if($length == 23){
                $order = model('app\api\model\wanlshop\OrderSync')
                    ->where('status', '<>', 0)
                    ->where('status', '<>', 3)
//                    ->where('goodspv', '>, 0')
                    ->where('order_no',$params['id'])
                    ->find();
                if($order) {
                    $user = model('app\common\model\User')->where('id', $order['user_id'])->find();
                    $check = true;
                }
            }
            if($check && $user){
                $order_nos = array();
                $orders1 = model('app\api\model\wanlshop\Order')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('statusb', '1')
//                    ->where('goodspv', '>', 0)
                    ->where('user_id',$user->id)->select();
                $orders2 = model('app\api\model\wanlshop\OrderSync')
                    ->where('user_id', $user->id)
                    ->where('status', '<>', 0)
                    ->where('status', '<>', 3)
//                    ->where('goodspv', '>', 0)
                    ->order('id', 'asc')
                    ->select();
                foreach($orders1 as $order){
                    $order_nos[$order->id]['order_no'] = $order->order_no;
                    $order_nos[$order->id]['goodsPv'] = $order->goodspv;
                }
                foreach($orders2 as $order){
                    $order_nos[$order->id]['order_no'] = $order->order_no;
                    $order_nos[$order->id]['goodsPv'] = $order->goodspv;
                }
                if($user && $order_nos){
                    echo $user->id."\n";
                    if($userR && ($user->pam1 >0 || $user->pam2 >0 || $user->pnums1 > 0 || $user->pnums2 > 0)) echo "网体下有人 请手动退单 \n";
                    else{
                        if(count($order_nos) == 1) {
                            foreach($order_nos as $k => $v) {
                                echo $v['order_no']." doRefund \n";
                                var_dump($v);
                                $checkOrder = model("app\common\model\CurrencyNsLog")
                                    ->where('service_ids', $v['order_no'])
                                    ->where('memo', '推荐奖')
                                    ->where('type', 'subsidy')
                                    ->where('status', '0')
                                    ->order('id', 'desc')
                                    ->find();
                                if ($checkOrder) {
                                    $userIv = model('app\common\model\User')->where('id', $checkOrder->user_id)->find();
                                    if ($userIv) {
                                        var_dump($v['goodsPv'] * $this->planrate[$user['plan']]['tj'][$userIv['vip_level']]);
                                        echo $checkOrder->money."<==>".$v['goodsPv'] . "\n";
                                        if($checkOrder->money == $v['goodsPv']){
                                            echo "第三单 \n";
                                            $iv = 1;
                                            $am = 0;
                                        }else{
                                            echo "非第三单 \n";
                                            $iv = 1;
                                            $am = $v['goodsPv'];
                                            $memo = 'NO';
//                                            判断是否复投
                                            foreach (model("app\common\model\CurrencyNsLog")
                                                         ->where('service_ids', $v['order_no'])
                                                         ->where('type', 'subsidy')
                                                         ->where('status', 0)
                                                         ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                                         ->order('id', 'asc')
                                                         ->select() as $log) {
                                                echo $v['goodsPv']."==>".$memo."==>".$log->money."\n";
                                                if (intval($log->money) == intval($v['goodsPv'] * 0.14)) $memo = '首单';
                                                if ($memo == 'NO' && intval($log->money) == intval($v['goodsPv'] * 0.07)) $memo = '复投';
                                            }
                                            echo "Memo:$memo \n";
                                            if($memo == 'NO'){
                                                echo "绩效奖记录异常 \n";
                                                $idDo = false;
                                            }else if($memo == '复投'){
                                                $am = $am / 2 ;
                                            }
                                            //判断是否存在多单
                                            if($userR){
                                                $count1 = model('app\api\model\wanlshop\Order')
                                                    ->where('status', 'normal')
                                                    ->where('deletetime', null)
                                                    ->where('state', '>', 1)
                                                    ->where('state', '<', 7)
                                                    ->where('statusb', '1')
                                                    ->where('user_id',$user->id)
                                                    ->count();
                                                $count2 = model('app\api\model\wanlshop\OrderSync')
                                                    ->where('user_id', $user->id)
                                                    ->where('status', '<>', 0)
                                                    ->where('status', '<>', 3)
                                                    ->order('id', 'asc')
                                                    ->count();
                                                if(($count1+$count2) > 1){
                                                    echo "存在多单 请手动退单 \n";
                                                    $idDo = false;
                                                }
                                            }
                                            //判断提货券是否足够
                                            if (strpos($v['order_no'], "YFL") !== false) {
                                                $rmb = $v['goodsPv'] / 800 * 2000;
                                                if($user->currency_rmb < $rmb){
                                                    echo "提货券不足 请手动退单 \n";
                                                    $idDo = false;
                                                }
                                            }
                                        }
                                        $uid = $user->id;
                                        $order_no = $v['order_no'];

                                        $teamAct = 3;
                                        $orderGL = true;

                                        echo "Uid:$uid RMB:$user->currency_rmb OrderNo:$order_no IV:$iv AM:$am \n";
                                        if($isDo) $this->Net2Modify($uid,$order_no,$am,$iv,1, $teamAct, $orderGL, $userR, $moneyBk);
                                    }
                                }else{
                                    echo "未找到对应的未结算订单 \n";
                                }
                            }
                        }else{
                            echo "存在多个未结算订单 请手动退单 \n";
                        }
                    }
                }
            }else{
                echo "输入信息识别异常 \n";
            }
        }
        if($params['action'] == 'hor'){
//        0 notdo 1showAll 2ShowDP 3Do
            $teamAct = 3;
            $orderGL = true;
            $userR = true;
            $moneyBk = true;


            $uid = 75804;
            $order_no = 'YFL48595408000758044535';
            $am = 4000;
            $iv=1;

            $this->Net2Modify($uid	,$order_no,$am,$iv,1, $teamAct, $orderGL, $userR, $moneyBk);
        }
        if($params['action'] == 'Net2Show'){
//            YFL19308267 这个号码，挪到YFL79641205这个市场的A区
            $act = 'up';
//            $act = 'downL';
//            $relation = 'inviter_id';
            $relation = 'pid';
            $upIds = array(73299);
            foreach($upIds as $upId){
                $this->Net2Show($upId, $act, $relation, 1);
            }
//            $this->Net2Show(41723,'up', 1);
//            $this->Net2Show(43894,'up', 1);
//            $this->Net2Show(37346,'up', 1);
//            $this->Net2Show(43506,'up', 1);
//            $this->Net2Show(41919,'up', 1);
//            $this->Net2Show(41363,'up', 1)t2;
//            $this->Net2Show(41365,'up', 1);
//            $this->Net2Show(3150,'up', 1);
//            $this->Net2Show(30679,'up', 1);
//            $this->Net2Show(41505,'up', 1);
//            $this->Net2Show(38199,'down', 34033);
//            $this->Net2Show(30049,'downL', 30000);
//            $this->Net2Show(32272,'downL', 30000);
//            $this->Net2Show(7091,'downL', 30000);
//            $this->Net2Show(41932,'up', 1);
//            $this->Net2Show(41894,'up', 1);
//            $this->Net2Show(37483,'downL', 1);
//            $this->Net2Show(39149,'downL', 1);
//            $this->Net2Show(41702,'up', 1);
//            $this->Net2Show(7587,'up', 1);
//            $this->Net2Show(43594,'up', 1);
//            $this->Net2Show(43628,'up', 1);
//            $this->Net2Show(43968,'up', 1);
//            $this->Net2Show(43860,'up', 1);
//            $this->Net2Show(39726,'up', 1);
//            $this->Net2Show(1702,'up', 1);
//            $this->Net2Show(44014,'up', 1);
//            $this->Net2Show(38869,'downL', 1);
//            $this->Net2Show(1703,'downL', 1);
//            $this->Net2Show(43019,'up', 1);
//            $this->Net2Show(44039,'up', 1);
//            $this->Net2Show(37357,'up', 1);
//            $this->Net2Show(39469,'up', 1);
//            $this->Net2Show(37326,'up', 1);
//            $this->Net2Show(31960,'up', 1);
//            $this->Net2Show(37202,'up', 1);

//            $teamAct = 2;
//            $orderGL = false;
//            $userR = false;
//            $this->Net2Modify(31960	,'NA',0,1,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(37202	,'NA',0,-1,0, $teamAct, $orderGL, $userR);
        }
        if($params['action'] == 'Net2Move'){
            $teamAct = 3;
            $orderGL = false;
            $userR = false;
            $user_id = 74694;
            $am = 1400;
            $nums = 4;
//            $this->Net2Modify(31960	,'NA',13400,$nums,0, $teamAct, $orderGL, $userR);
//            $this->Net2Modify(31960	,'NA',-13400,-1*$nums,0, $teamAct, $orderGL, $userR);
            $this->Net2Modify($user_id	,'NA', $am, $nums, 0, $teamAct, $orderGL, $userR);
            if($teamAct == 3) {
                $am = $am * -1;
                $nums = $nums * -1;
//                $am = -12800;
//                old: 8134 1
                model('app\common\model\User')->where('id', $user_id)->update(['pid' => 6726, 'pnet' => 2]);
//                model('app\common\model\User')->where('id', $user_id)->update(['pid' => 49887, 'pnet' => 1]);
                $this->Net2Modify($user_id, 'NA', $am, $nums, 0, $teamAct, $orderGL, $userR);
            }

        }

        if($params['action'] == 'NetIvFind'){
            $this->NetIvFind();
        }
        if($params['action'] == 'Supplement'){
            $this->calcOrderConfirmSupplement(true,true);
        }
        if($params['action'] == 'dpSupplement'){
//            $this->dpSupplement();
            $this->dpSupplementFH();
        }
        if($params['action'] == 'Net2NumsCounts'){
            $this->Net2NumsCounts();
        }
        if($params['action'] == 'daily'){
            // $this->autoCalcDaliy();
            $year = date('Y');
            $month = date('m');
            $day = date('d');
            $hour = date('H');
            $settletime = model('app\common\model\Settletime')->where('year', $year)->where('month', $month)->where('day', $day)->find();
            if (!$settletime) {
                model('app\common\model\Settletime')->create([
                    'year' => $year,
                    'month' => $month,
                    'day' => $day, // 备注
                    'hour' => $hour
                ]);
                $this->autoCalcDaliy();
//                $this->autoCalcDaliyYY();
            }
        }

        if($params['action'] == 'handDaily'){
//            $this->calcOrderConfirmDaily(false);
            $this->OrderDailyCount();
        }
        if($params['action'] == 'weekly'){
            $this->autoWeekly();
        }
        if($params['action'] == 'monthly'){
            $this->autoMonthly();
        }
        if($params['action'] == 'sandpay_out'){
//            var_dump($this->checkMerchant());
            $bl = $this->checkBalance($params['gateway']?$params['gateway']:'sandpay');
            if($bl['code'] == 200){
                return $this->OutTransfer($params['ids'], $params['gateway']?$params['gateway']:'sandpay');
            }else{
//                var_dump($bl);
                return $bl;
            }
        }
        if($params['action'] == 'sandpay_bl'){
//            var_dump($this->checkMerchant());
            $gateway = 'yeepay';
            $bl = $this->checkBalance();
//            var_dump($bl);
        }
        if($params['action'] == 'TestClear'){
            $this->YTest('Clear');
        }
        if($params['action'] == 'TestNew'){
            $this->YTest('New');
        }
        if($params['action'] == 'userOrder'){
            $this->userOrder();
        }
        if($params['action'] == 'cUsers'){
//            $this->cUsers(5019, 1, 0, 2);
//            $this->cUsers(15045, 1);
//            $this->cUsers(15045, 1, 0, 2);
//            $this->cUsers(15046, 1);
//            $this->cUsers(15046, 1, 0, 2);
//            $this->cUsers(15047, 1);
//            $this->cUsers(15047, 1, 0, 2);
//            $this->cUsers(15048, 1);
//            $this->cUsers(15048, 1, 0, 2);
//            $this->cUsers(15049, 1);
//            $this->cUsers(15049, 1, 0, 2);
//            $this->cUsers(15050, 1);
//            $this->cUsers(15050, 1, 0, 2);
//            $this->cUsers(15051, 1);
//            $this->cUsers(15051, 1, 0, 2);
//            $this->cUsers(15052, 10);
//            $this->cUsers(15052, 10, 0, 2);
//            $this->cUsers(15053, 10);
//            $this->cUsers(15053, 10, 0, 2);
//            $this->cUsers(15054, 10);
//            $this->cUsers(15054, 10, 0, 2);
//            $this->cUsers(15055, 10);
//            $this->cUsers(15055, 10, 0, 2);
//            $this->cUsers(15056, 10);
//            $this->cUsers(15056, 10, 0, 2);
//            $this->cUsers(15057, 10);
//            $this->cUsers(15057, 10, 0, 2);
//            $this->cUsers(15058, 10);
//            $this->cUsers(15058, 10, 0, 2);
//            $this->cUsers(15059, 10);
//            $this->cUsers(15059, 10, 0, 2);
        }
        if($params['action'] == 'bonusCancel'){
//            var_dump($params['id']);
            $this->bonusCancel($params['id']);
        }
        if($params['action'] == 'syncLv'){
//            var_dump($params['id']);
            $this->syncLv();
        }
        if($params['action'] == 'Test'){
//            $loop = 0;
//            model('app\api\model\wanlshop\Order')->where('fany', '0')->where('createtime', '>=', 1743436800)->where('goodspv', '>', 0)->update(['fany' => '1']);
//            exit;
//            foreach(model('app\api\model\wanlshop\Order')
//                        ->where('status', 'normal')
//                        ->where('deletetime', null)
//                        ->where('goodspv', 0)
//                        ->where('state', '>', 1)
//                        ->where('state', '<', 7)
////                        ->where('createtime', '>=', 1743436800)
//                        ->where('createtime', '>=', 1746028800)
//                        ->order('id asc')->select() as $os) {
////                if($loop > 0 && $loop % 10 < 2) model('app\api\model\wanlshop\Order')->where('id', $os->id)->update(['fany' => '1']);
//                if($loop > 0 && $loop % 10 < 4) model('app\api\model\wanlshop\Order')->where('id', $os->id)->update(['fany' => '1']);
//                $loop++;
//            }
//            foreach(model('app\common\model\User')->where('currency_gp','>',0)->order('id asc')->select() as $user){
//                echo $user->id."==>".$user->currency_gp."==>"."\n";
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$user->currency_gp, $user->id, '兑换', 'sys', '', 'currency_gp');
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($user->currency_gp*1.1, $user->id, '兑换', 'sys', '', 'currency_ns');
//            }
//            foreach(model('app\common\model\User')->where('id','<>', 521)->where('id','<>', 528)->where('currency_blc','>',0)->order('id asc')->select() as $user){
//                echo $user->id."==>".$user->currency_blc."==>"."\n";
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$user->currency_blc, $user->id, '兑换', 'sys', '', 'currency_blc');
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($user->currency_blc*1.1, $user->id, '兑换', 'sys', '', 'currency_ns');
//            }
//            foreach(model('app\common\model\User')->where('currency_bd','>',0)->order('id asc')->select() as $user){
//                echo $user->id."==>".$user->currency_bd."==>"."\n";
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$user->currency_bd, $user->id, '兑换', 'sys', '', 'currency_bd');
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($user->currency_bd, $user->id, '兑换', 'sys', '', 'currency_blc');
//            }
//            foreach(model('app\common\model\User')->where('currency_gp','>',0)->order('id asc')->select() as $user){
//                echo $user->id."==>".$user->currency_gp."==>"."\n";
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-$user->currency_gp, $user->id, '兑换', 'sys', '', 'currency_gp');
//                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($user->currency_gp, $user->id, '兑换', 'sys', '', 'currency_blc');
//            }

//            $this->OrderCfGd();
//            $this->myFriends(6089);
            exit;
            $userModel = model('app\common\model\User');
            $userinfo = $userModel->where('id',68099)->find();
            $order_info = array();
            $order_info['user_id'] = $userinfo->id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $userinfo->username;
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
//            $order_info['act'] = 'newpurchase';
            $this->calcOrderAction($order_info);
            $arr = array();
            $arr['action'] = 'orderAction';
            $arr['order'] = $order_info;
            Hook::listen("com_auto_settlement", $arr);
//            $this->pMarketAct();
//            $this->pMarketAm(30027);
//            $this->pMarketAm(30004);
//            $this->oneToThree(14);
//            $this->pxMarketAct();
//            $this->pxMarketAm(30909);
//            $this->calcOrderPay(5431);
//            $this->Net2DP(47587,3398996053247587,800,0,2,1);
//            $this->Net2DP(47314,3380072853247314,800,1,2,1);
//            $this->Net2DP(47326,3381101153247326,800,1,2,1);
//            $this->Net2DP(47366,3382099773247366,800,1,2,1);
//            $this->Net2DP(47375,3383496803247375,800,1,2,1);
//            $this->Net2DP(47415,3384294123247415,800,1,2,1);
//            $this->Net2DP(47423,3384494593247423,800,1,2,1);
//            $this->Net2DP(47424,3388272803247424,800,1,2,1);
//            $this->Net2DP(47381,3391445073247381,800,1,2,1);
//            $this->Net2DP(47445,3391699673247445,800,1,2,1);
//            $this->Net2DP(47459,3391809793247459,800,1,2,1);



//            $this->test();
//            $order_info = array();
//            $order_info['user_id'] = 43006;
//            $order_info['order_id'] = 1;
//            $order_info['order_no'] = 'YFL31323107000430068562';
//            $order_info['price'] = 1300;
//            $order_info['number'] = 1;
//            $order_info['category_id'] = 108;
//            $order_info['flq'] = 3;
//            $order_info['cny_rate'] = 0.06;
//            $order_info['status'] = '1';
//            $order_info['activity_type'] = 'shopgoods';
//            $order_info['act'] = 'newpurchase';
//            $this->calcOrderAction($order_info);


//            $this->calcOrderPay(1771);
//            $this->calcOrderPay(1588);
//            $this->calcOrderPay(1589);
            echo 'Test';
            exit;
//            $this->test();
//            $this->calcOrderPay(972);
//            $this->calcOrderPay(972);
//            $this->wslvUp(139, 3);
//            $this->intonet(168,$order_no='1206242000531168', $calc=true);
//            $this->calcOrderPay(16);
//            $this->shopApply(106);
//            $this->calcOrderConfirm(63);
//            $this->intonet(1,1);
//            $bl = $this->checkBalance('yeepay');
//            var_dump($bl);
//            $msg = $this->OutTransfer(186974, 'yeepay');
//            $msg = $this->checkOrderOut(106078);
//            $msg = $this->checkOrderIn('202301282316531255493153100519');
//            $this->autoCalcDaliyYY();
//            $this->calcOrder(25359);
//             var_dump($msg);
        }
    }
    /**
     * 确认货
     * @param $order_id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function calcOrderConfirm($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans', // 爱多米诺-永福莱豆
            6 => 'currency_gfz', // 爱多米诺-永福莱豆
        );
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['days' => 1]);
//                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
//                $cl_arr = array();
//                $cl_arr['status'] = '1';
//                $cl_arr['before'] = $user[$mtype];
//                $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                $cl_arr['updatetime'] = strtotime('now');
//                model("app\common\model\Currency{$mtypelog}Log")
//                    ->where('id', $cl['id'])
//                    ->update($cl_arr);
//
//                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }
    }

    public function handleOrderConfirmDaily($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans', // 爱多米诺-永福莱豆
            6 => 'currency_gfz', // 爱多米诺-永福莱豆
        );
        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id', $order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['days' => 1]);
//                $user = model('app\common\model\User')->where('id', $order['user_id'])->field($mtype)->find();
//                $cl_arr = array();
//                $cl_arr['status'] = '1';
//                $cl_arr['before'] = $user[$mtype];
//                $cl_arr['after'] = $user[$mtype] + $cl['money'];
//                $cl_arr['updatetime'] = strtotime('now');
//                model("app\common\model\Currency{$mtypelog}Log")
//                    ->where('id', $cl['id'])
//                    ->update($cl_arr);
//
//                model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mtype, $cl['money']);
            }
        }
    }

    public function calcOrderRefund($order_id){
        $mtype_arr = array(
            0 => 'currency_nfr',
            1 => 'currency_xnb',
            2 => 'currency_cny',
            3 => 'currency_rmb',
            4 => 'currency_ns',
            5 => 'currency_ans',
            6 => 'currency_flq',
            7 => 'currency_cny',
        );

        $order = model('app\api\model\wanlshop\Order')
            ->where('status', 'normal')
            ->where('deletetime', null)
            ->where('id',$order_id)->find();
        foreach($mtype_arr as $mtype){
            $mtypeex = explode("_", $mtype);
            $mtypelog = ucfirst($mtypeex[1]);
            foreach (model("app\common\model\Currency{$mtypelog}Log")
                         ->where('service_ids', $order['order_no'])
                         ->where('type', 'subsidy')
                         ->order('id', 'desc')
                         ->select() as $cl){
                model("app\common\model\Currency{$mtypelog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
            }
            //支付券退回
            $cl = model("app\common\model\Currency{$mtypelog}Log")
                ->where('service_ids', $order['order_no'])
                ->where('type', 'pay')
                ->find();
            if($cl){
                // 归还提货券,福利券等
                $money = abs($cl['money']);
                Log::info("orderRefund: oid=$order_id,mtype=$mtype, money=$money");
                controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(+($money), $cl['user_id'], '退款后归还', 'refund', $order['order_no'], $mtype, '1');
            }
        }
    }

    public function handBonusRefund(){
//        return true;
        foreach (model("app\common\model\CurrencyNsLog")
                     ->where('calc', '0')
                     ->where('status', '1')
                     ->where('memo', '管理费')
                     ->where('type', 'subsidy')
                     ->where('id', '<=', 26106)
                     ->order('id', 'asc')
                     ->select() as $cl) {
            $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
            echo "ID:".$cl['id'].":".$user['id']."===>".$user['username']."===>".$user['mobile']."===>".$user['currency_ns']."===>".$cl['money'];
            $cl['money'] = floor($cl['money'] * 100) / 100;
//                if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc('currency_ns', $cl['money']);
            if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec('currency_ns', abs($cl['money']));
            model("app\common\model\CurrencyNsLog")
                ->where('id', $cl['id'])
                ->update(['calc' => '1']);
            $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
            echo "===>".$user['currency_ns'];
            echo "\n";
//            exit;
        }
        exit;
//        31278=>1 -未加
//        $mType = 'currency_ns';
//        $idB = 23485;
//        $idE = 23503;
//        $order_no = 'FH2666050203232207';
//        31278=>2 -未加
//        $mType = 'currency_ns';
//        $idB = 23765;
//        $idE = 23783;
//        $order_no = 'FH2672470713233427';
//        31278=>3
//        $mType = 'currency_ns';
//        $idB = 24511;
//        $idE = 24529;
//        $order_no = 'FH2673403453232799';
//        31278=>4
//        $mType = 'currency_ns';
//        $idB = 26109;
//        $idE = 26127;
//        $order_no = 'FH2689119923232798';
//        31278=>5
//        $mType = 'currency_ns';
//        $idB = 26277;
//        $idE = 26295;
//        $order_no = 'FH2689186693232798';
//        31278=>6
//        $mType = 'currency_ns';
//        $idB = 30024;
//        $idE = 30042;
//        $order_no = 'FH2722368153231620';



//        31262=>1 -未加
//        $mType = 'currency_ns';
//        $idB = 23503;
//        $idE = 23521;
//        $order_no = 'FH2666050203232207';
//        31262=>2 -未加
//        $mType = 'currency_ns';
//        $idB = 23783;
//        $idE = 23801;
//        $order_no = 'FH2672470713233427';
//        31262=>3
//        $mType = 'currency_ns';
//        $idB = 24529;
//        $idE = 24547;
//        $order_no = 'FH2673403453232799';
//        31262=>4
//        $mType = 'currency_ns';
//        $idB = 26127;
//        $idE = 26145;
//        $order_no = 'FH2689119923232798';
//        31262=>5
//        $mType = 'currency_ns';
//        $idB = 26295;
//        $idE = 26313;
//        $order_no = 'FH2689186693232798';
//        31262=>6
//        $mType = 'currency_ns';
//        $idB = 30042;
//        $idE = 30060;
//        $order_no = 'FH2722368153231620';
//        31262=>7
//        $mType = 'currency_ns';
//        $idB = 31533;
//        $idE = 31551;
//        $order_no = 'FH2733456553232309';
//        31262=>8
        $mType = 'currency_ns';
        $idB = 33086;
        $idE = 33104;
        $order_no = 'FH2742898553232827';

        $mTypeEx = explode("_", $mType);
        $mTypeLog = ucfirst($mTypeEx[1]);
        foreach (model("app\common\model\Currency{$mTypeLog}Log")
                     ->where('service_ids', $order_no)
                     ->where('id', '>=', $idB)
                     ->where('id', '<', $idE)
                     ->where('type', 'subsidy')
                     ->order('id', 'desc')
                     ->select() as $cl){
            echo "ID".$cl['id'].":";
            if($cl['status'] == '0' || $cl['status'] == '1') {
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
                echo $user['id']."===>".$user['username']."===>".$user['mobile']."===>".$user[$mType]."===>".$cl['money'];
                if ($cl['status'] == '1') {
                    $cl['money'] = floor($cl['money'] * 100) / 100;
                    if($cl['money'] > 0) model('app\common\model\User')->where('id', $cl['user_id'])->setDec($mType, $cl['money']);
                    if($cl['money'] < 0) model('app\common\model\User')->where('id', $cl['user_id'])->setInc($mType, abs($cl['money']));
                }
                model("app\common\model\Currency{$mTypeLog}Log")
                    ->where('id', $cl['id'])
                    ->update(['status' => '2']);
                $user = model('app\common\model\User')->where('id', $cl['user_id'])->find();
                echo "===>".$user[$mType];
            }
            echo "END\n";
        }
    }


    public function Net2Modify($uid,$order_no,$am=0,$iv=0,$calc=1, $teamAct=0, $orderGL=false, $userR=false, $moneyBk=false){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user && $teamAct > 0) {
            $nowNet = $user['pnet'];
            $userP = $user;
            $loop = 0;
            $loopDp = 1;
            while ($userP && $user['plan'] == $userP['plan']) {
                $userO = array(
                    'pam1' => $userP['pam1'],
                    'pam1n' => $userP['pam1n'],
                    'pam2' => $userP['pam2'],
                    'pam2n' => $userP['pam2n']
                );
                $idDP = false;
                if($loop > 0 && $nowNet < 3) {
                    $update_array = array();
                    if ($am != 0) {
                        $update_array['pam' . $nowNet] = $userP['pam' . $nowNet] - $am;
                        $update_array['pam' . $nowNet . 'n'] = $userP['pam' . $nowNet . 'n'] - $am;
                        $userP['pam' . $nowNet . 'n'] = $update_array['pam' . $nowNet . 'n'];
                    }
                    if ($iv != 0) {
                        $update_array['pnums' . $nowNet] = $userP['pnums' . $nowNet] - $iv;
                    }
                    if($calc > 0 && $userP['vip_level'] > 1 && $am > 0) {
                        $amDp = 0;
                        $usersDp = array();
//                        判断是否对碰
                        $checkorder = model("app\common\model\CurrencyGpLog")
                            ->where('user_id', $userP['id'])
                            ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                            ->where('service_ids', $order_no)
                            ->where('type', 'subsidy')
                            ->order('id', 'desc')
                            ->find();
                        if($checkorder) {
                            $idDP = true;
//                            if ($loopDp <= $this->planrate[$user['plan']]['dpMax']) {
                                $amDp = $checkorder['money'];
                                $amountdp = $amDp / $this->planrate[$user['plan']]['ratePv'] / $this->planrate[$user['plan']]['dp'][$userP['vip_level']];
//                                if ($loopDp > 4) $amountdp = 160;
//                                else {
//                                    if ($amountdp > 800) $amountdp = $amountdp - 800;
//                                    else $amountdp = 0;
//                                }
//                                $amountdp = $amountdp / 2;
//                                $amountdp = 2000;
//                                if($uid == 39495) $amountdp = 400;
//                                if($uid == 31089) $amountdp = 400;
//                                if($uid == 30901) $amountdp = 400;

//                                if($uid == 30076) $amountdp = 400;
//                                if($uid == 30004) $amountdp = 400;
//                                $amountdp = 800;
                                echo $userP->id."==>".$amountdp."==>".$checkorder->money."\n";
                                if(abs($am-$amountdp) < 10) $amountdp = $am;
                                $update_array['pam1n'] = $userP['pam1n'] + $amountdp;
                                $update_array['pam2n'] = $userP['pam2n'] + $amountdp;
//                            }
//                            if (($amDp + $userP['weekfd']) >= $this->planrate[$user['plan']]['fd'][$userP['vip_level']]) {
//                                $amDp = $this->planrate[$user['plan']]['fd'][$userP['vip_level']] - $userP['weekfd'];
//                            }

                            $loopDp++;
                        }
                    }
                    if (!empty($update_array)) {
                        if($teamAct == 1) {
                            echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                            echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                        }
                        if($teamAct == 2) {
                            if($idDP) {
                                echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                            }else{
                                if(array_key_exists('pam1n',$update_array)){
                                    if($update_array['pam1n'] < 0){
                                        echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                        echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                                    }
                                }
                                if(array_key_exists('pam2n',$update_array)){
                                    if($update_array['pam2n'] < 0){
                                        echo $userP->id . ":左:" . $userO['pam1'] . "===>" . $userO['pam1n'] . "  右:" . $userO['pam2'] . "===>" . $userO['pam2n'] . "\n";
                                        echo ($idDP?'碰':' ').($nowNet==1?'左':'右') . ":左:" . (array_key_exists('pam1',$update_array)?$update_array['pam1']:$userP['pam1']) . "===>" . (array_key_exists('pam1n',$update_array)?$update_array['pam1n']:$userP['pam1n']) . "  右:" . (array_key_exists('pam2',$update_array)?$update_array['pam2']:$userP['pam2']) . "===>" . (array_key_exists('pam2n',$update_array)?$update_array['pam2n']:$userP['pam2n']) . "\n";
                                    }
                                }
                            }
                        }
//                        var_dump($update_array);
//                        if($userP['pam' . $nowNet . 'n'] < 0) echo $userP->id.":".$userP['pam' . $nowNet]."===>".($userP['pam' . $nowNet . 'n']+$am)."===>-".$am."===>".$update_array['pam' . $nowNet]."===>".$update_array['pam' . $nowNet . 'n']."\n";
                        if($teamAct == 3) model('app\common\model\User')->where('id', $userP['id'])->update($update_array);
                    }
                }
                $nowNet = $userP['pnet'];
                $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                $loop++;
            }
        }
        if($orderGL) {
            if($userR) {
                model('app\common\model\User')
                    ->where('id', $uid)
                    ->update(['vip_status' => '0', 'vip_level' => 1, 'self_amd0' => 0, 'manager_id' => 0, 'pid' => 0, 'pnet' => 0]);
            }
            model('app\api\model\wanlshop\OrderSync')
                ->where('order_no', $order_no)
                ->update(['status' => '3']);
            $mType_arr = array(
                0 => 'currency_ns',
                1 => 'currency_rmb',
                2 => 'currency_cny',
                3 => 'currency_pu',
                4 => 'currency_bd',
                5 => 'currency_gp',
                6 => 'currency_nsd',
                7 => 'currency_blc'
            );
            foreach ($mType_arr as $mType) {
                $mTypeEx = explode("_", $mType);
                $mTypeLog = ucfirst($mTypeEx[1]);
                foreach(model("app\common\model\Currency{$mTypeLog}Log")->where('service_ids', $order_no)->select() as $log) {
                    if($moneyBk && $log->status == '1' && $log->type == 'pay'){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-1*$log->money, $log->user_id, '退单', 'refund', $order_no, $mType);
//                        if ($log->money > 0) model('app\common\model\User')->where('id', $log->user_id)->setDec($mType, $log->money);
//                        if ($log->money < 0) model('app\common\model\User')->where('id', $log->user_id)->setInc($mType, abs($log->money));
                    }
                    if($moneyBk && $log->status == '1' && $log->type == 'sys' && strstr($log->memo, 'K3绿色消费券')){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-1*$log->money, $log->user_id, '退单', 'subsidy', $order_no, $mType);
//                        if ($log->money > 0) model('app\common\model\User')->where('id', $log->user_id)->setDec($mType, $log->money);
//                        if ($log->money < 0) model('app\common\model\User')->where('id', $log->user_id)->setInc($mType, abs($log->money));
                    }
                    if($moneyBk && $log->status == '1' && $log->type == 'sys' && strstr($log->memo, 'K3永福莱豆')){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-1*$log->money, $log->user_id, '退单', 'subsidy', $order_no, $mType);
//                        if ($log->money > 0) model('app\common\model\User')->where('id', $log->user_id)->setDec($mType, $log->money);
//                        if ($log->money < 0) model('app\common\model\User')->where('id', $log->user_id)->setInc($mType, abs($log->money));
                    }
                    if($moneyBk && $log->status == '1' && $log->type == 'sys' && strstr($log->memo, 'K3绿色积分')){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-1*$log->money, $log->user_id, '退单', 'subsidy', $order_no, $mType);
//                        if ($log->money > 0) model('app\common\model\User')->where('id', $log->user_id)->setDec($mType, $log->money);
//                        if ($log->money < 0) model('app\common\model\User')->where('id', $log->user_id)->setInc($mType, abs($log->money));
                    }
                    if($moneyBk && $log->status == '1' && $log->type == 'subsidy'){
                        controller('addons\wanlshop\library\WanlPay\WanlPay')->currency(-1*$log->money, $log->user_id, '退单', 'subsidy', $order_no, $mType);
                    }
                    model("app\common\model\Currency{$mTypeLog}Log")
                        ->where('service_ids', $order_no)
                        ->where('id', $log['id'])
                        ->update(['status' => '2']);
                }
            }
//            foreach(model("app\common\model\CurrencyNsLog")->where('service_ids', $order_no)->select() as $log) {
//                if($log->memo == '绩效奖' || $log->memo == '绩效奖D'){
//                    model('app\common\model\UserBonusTotal')->where('user_id', $log->user_id)->setDec('bonus_jx', $log->money);
//                }else if($log->memo == '管理奖'){
//                    model('app\common\model\UserBonusTotal')->where('user_id', $log->user_id)->setDec('bonus_gl', $log->money);
//                }else if($log->memo == '推荐奖'){
//                    model('app\common\model\UserBonusTotal')->where('user_id', $log->user_id)->setDec('bonus_iv', $log->money);
//                }else if($log->memo == '复购奖'){
//                    model('app\common\model\UserBonusTotal')->where('user_id', $log->user_id)->setDec('bonus_ivr', $log->money);
//                }
//            }
        }
        return true;
    }


    public function dpSupplement(){
        $sid = 977;
        $doDp = true;
        $max = 11;
        $loop = 1;
        foreach(model('app\api\model\wanlshop\Order')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('id', '>=', $sid)
                    ->where('id', '<>', 938)
                    ->where('id', '<', 978)
                    ->where('statusb', '1')
                    ->order('id asc')->select() as $os) {
            echo $loop."YFL==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            $clogB = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'pay')
                ->where('status', '1')
                ->order('id', 'asc')
                ->find();
            echo 'Amount:'.$clogB->money*-1;
            $clogIv = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['推荐奖','复购奖'])
                ->order('id', 'asc')
                ->find();
            echo 'IvAm:'.$clogIv->memo.$clogIv->money;
            $userIv = model('app\common\model\User')->where('id', $clogIv['user_id'])->find();
            $user = model('app\common\model\User')->where('id', $os['user_id'])->find();
            echo 'SelfAm:'.$user->self_amd0."\n";
            $am = $clogIv->money / $this->planrate[$userIv['plan']]['tj'][$userIv['vip_level']];
            if($clogIv->memo == '复购奖') echo 'PV:XXXX';
            else if ((($clogB->money * -1) / 1300 * 800) == $am && $am == $user->self_amd0) echo 'PV:'.$am;
            else echo 'PV:XXXX';
            if ($user->id == 38869) $am = 2000;
            if ($user->id == 39331) $am = 2000;
            if ($user->id == 43543) $am = 0;
            if ($user->id == 37876) $am = 0;
            echo "\n";
            if($am > 0) {
                $userXL = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 1)->find();
                $userXR = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 2)->find();
                if ($user) echo $user->self_amd0 . 'MY:' . $user->pam1 . "===>" . $user->pam1n . "R:" . $user->pam2 . "===>" . $user->pam2n . "\n";
                if ($userXL) echo $userXL->self_amd0 . 'Pam L:' . $userXL->pam1 . "===>" . $userXL->pam1n . "R:" . $userXL->pam2 . "===>" . $userXL->pam2n . "\n";
                if ($userXR) echo $userXR->self_amd0 . 'Pam R:' . $userXR->pam1 . "===>" . $userXR->pam1n . "R:" . $userXR->pam2 . "===>" . $userXR->pam2n . "\n";
                $nowNet = $user['pnet'];
                $userP = $user;
                $loopDp = 0;
                while ($userP) {
                    if ($loopDp > 0) {
                        if ($loopDp == 1) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pnums1'] . "  右:" . $userP['pnums2'] . "\n";
                            echo '=============>' . "\n";
                        }
                        if ($userP->monthly_fg == 0 && $userP->pam1 > 0 && $userP->pam2 > 0 && $userP['pam' . $nowNet . 'n'] < $am) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";

                            $clogDp = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $os->order_no)
                                ->where('type', 'subsidy')
                                ->where('user_id', $userP->id)
                                ->where('status', '0')
                                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                ->order('id', 'asc')
                                ->find();
                            if (!$clogDp && $doDp) {
                                if ($user->id == 43529 && $loopDp == 3) $am = $am/2;
                                $this->Net2DPOne($userP->id, $os->order_no, $am, 0, 0, 1);
                            }
                        }
                    }
                    $nowNet = $userP['pnet'];
                    $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                    $loopDp++;
                }
            }
            echo "ID:$os->id,结束\n";
            $loop++;
            if ($loop == $max) exit;
        }
    }

    public function dpSupplementFH(){
        $sid = 12436;
        $doDp = false;
        $max = 11;
        $loop = 1;
        foreach(model('app\api\model\wanlshop\OrderSync')
//                    ->where('id', 6587)
                    ->where('status', '1')
                    ->where('createtime', '>', 1730390400)
                    ->where('createtime', '<', 1730455200)
                    ->where('id', '>=', $sid)
                    ->where('id', '<', 12441)
                    ->order('id', 'asc')
                    ->select() as $os){
            echo $loop."FH==ID:$os->id,UserID:$os->user_id,OrderNO:$os->order_no\n";
            if($os->price==1300) $goodsPV = $os->number*800*($os->flq==2?1:3/8);
            if($os->price==999) $goodsPV = $os->number*700*($os->flq==2?1:3/8);
            echo 'GoodsPV:'.$goodsPV;
            $clogIv = model("app\common\model\CurrencyNsLog")
                ->where('service_ids', $os->order_no)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['推荐奖','复购奖'])
                ->order('id', 'asc')
                ->find();
            echo 'IvAm:'.$clogIv->memo.$clogIv->money;
            $userIv = model('app\common\model\User')->where('id', $clogIv['user_id'])->find();
            $user = model('app\common\model\User')->where('id', $os['user_id'])->find();
            echo 'SelfAm:'.$user->self_amd0."\n";
            $am = $clogIv->money / $this->planrate[$userIv['plan']]['tj'][$userIv['vip_level']];
            if($clogIv->memo == '复购奖') echo 'PV:XXXX';
            else if ($goodsPV == $am && $am == $user->self_amd0) echo 'PV:'.$am;
            else echo 'PV:XXXX';
            if ($os->id == 12436) $am = 150;
            if ($os->id == 12438) $am = 300;
            if ($os->id == 12439) $am = 300;
            if($doDp && $am == 300 && $clogIv->memo == '推荐奖' && $user->self_amd0 == 800){
                model('app\common\model\User')->where('id', $os['user_id'])->update(['self_amd0'=>300]);
            }
            echo "\n";
            if($am > 0) {
                $userXL = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 1)->find();
                $userXR = model('app\common\model\User')->where('pid', $user->id)->where('pnet', 2)->find();
                if ($user) echo $user->self_amd0 . 'MY:' . $user->pam1 . "===>" . $user->pam1n . "R:" . $user->pam2 . "===>" . $user->pam2n . "\n";
                if ($userXL) echo $userXL->self_amd0 . 'Pam L:' . $userXL->pam1 . "===>" . $userXL->pam1n . "R:" . $userXL->pam2 . "===>" . $userXL->pam2n . "\n";
                if ($userXR) echo $userXR->self_amd0 . 'Pam R:' . $userXR->pam1 . "===>" . $userXR->pam1n . "R:" . $userXR->pam2 . "===>" . $userXR->pam2n . "\n";
                $nowNet = $user['pnet'];
                $userP = $user;
                $loopDp = 0;
                while ($userP) {
                    if ($loopDp > 0) {
                        if ($loopDp == 1) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pnums1'] . "  右:" . $userP['pnums2'] . "\n";
                            echo '=============>' . "\n";
                        }
                        if ($userP->monthly_fg == 0 && $userP->pam1 > 0 && $userP->pam2 > 0 && $userP['pam' . $nowNet . 'n'] < $am) {
                            echo ($nowNet == 1 ? '左' : '右') . $userP->id . ":左:" . $userP['pam1'] . "===>" . $userP['pam1n'] . "  右:" . $userP['pam2'] . "===>" . $userP['pam2n'] . "\n";

                            $clogDp = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $os->order_no)
                                ->where('type', 'subsidy')
                                ->where('user_id', $userP->id)
                                ->where('status', '0')
                                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                ->order('id', 'asc')
                                ->find();
                            if (!$clogDp && $doDp) {
                                $amPv = $am;
                                if ($os->id == 12418 && $userP->id == 32683) $amPv = $am/2;
//                                if ($userP->id == 43529 && $loopDp == 3) $am = $am/2;
                                $this->Net2DPOne($userP->id, $os->order_no, $amPv, 0, 0, 1);
                            }
                        }
                    }
                    $nowNet = $userP['pnet'];
                    $userP = model('app\common\model\User')->where('id', $userP['pid'])->find();
                    $loopDp++;
                }
                model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $os->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '0')
                    ->update(['days' => 1]);
                model("app\common\model\CurrencyRmbLog")
                    ->where('service_ids', $os->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '0')
                    ->update(['days' => 1]);
            }
            echo "ID:$os->id,结束\n";
            $loop++;
            if ($loop == $max) exit;
        }
    }

    public function bonusCancel($uid){
        $user = model('app\common\model\User')->where('id', $uid)->find();
        $bonusDpD = model("app\common\model\CurrencyNsLog")
            ->where('user_id', $uid)
            ->where('type', 'subsidy')
            ->where('status', '1')
            ->where('memo', 'in', ['绩效奖','绩效奖D'])
            ->order('id', 'desc')
            ->sum('money');
        $bonusDpC = model("app\common\model\CurrencyNsLog")
            ->where('user_id', $uid)
            ->where('type', 'subsidy')
            ->where('status', '0')
            ->where('memo', 'in', ['绩效奖','绩效奖D'])
            ->order('id', 'desc')
            ->sum('money');
        echo $user->username;
        var_dump($bonusDpD);
        var_dump($bonusDpC);
        $loop=10;
        $doCancel = true;
        while($loop > 0) {
            $loop--;
            $ob = model("app\common\model\CurrencyNsLog")
                ->where('user_id', $uid)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                ->order('id', 'asc')
                ->find();
            echo 'NS:' . $ob->id . "===>" . $ob->service_ids . "\n";
            foreach (model("app\common\model\CurrencyNsLog")
                         ->where('service_ids', $ob->service_ids)
                         ->where('type', 'subsidy')
                         ->where('status', '0')
                         ->where('id', '>=', $ob->id)
                         ->order('id', 'asc')
                         ->select() as $obm) {
                if (($obm->memo == '绩效奖' || $obm->memo == '绩效奖D') && $obm->id > $ob->id) break;
                else {
                    if ($doCancel) model("app\common\model\CurrencyNsLog")->where('id', $obm->id)->update(['status' => '2']);
                    echo $obm->id . "===>" . $obm->service_ids . "\n";
                }
            }
            $ob = model("app\common\model\CurrencyRmbLog")
                ->where('user_id', $uid)
                ->where('type', 'subsidy')
                ->where('status', '0')
                ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                ->order('id', 'asc')
                ->find();
            echo 'RMB:' . $ob->id . "===>" . $ob->service_ids . "\n";
            foreach (model("app\common\model\CurrencyRmbLog")
                         ->where('service_ids', $ob->service_ids)
                         ->where('type', 'subsidy')
                         ->where('status', '0')
                         ->where('id', '>=', $ob->id)
                         ->order('id', 'asc')
                         ->select() as $obm) {
                if (($obm->memo == '绩效奖' || $obm->memo == '绩效奖D') && $obm->id > $ob->id) break;
                else {
                    if ($doCancel) model("app\common\model\CurrencyRmbLog")->where('id', $obm->id)->update(['status' => '2']);
                    echo $obm->id . "===>" . $obm->service_ids . "\n";
                }
            }
        }
    }

    public function Net2NumsCounts(){
        foreach(model('app\common\model\User')
                    ->where('vip_status', '1')
                    ->select() as $user){
            $count = model('app\common\model\User')->where('pid', $user->id)->count();
            $pnums1 = model('app\common\model\User')->where('pid', $user->id)->sum('pnums1');
            $pnums2 = model('app\common\model\User')->where('pid', $user->id)->sum('pnums2');
            $pnums = $user->pnums1+$user->pnums2 - $count-$pnums1-$pnums2;
            model('app\common\model\User')->where('id', $user->id)->update(['pnums'=>$pnums]);
        }
    }

    public function Net2Show($uid, $direction, $relation='pid', $stopId){
        $ids = array();
        $fields = 'id,username,mobile,truename,self_amd0,plan,vip_status,vip_level,inviter_id,manager_id,id,pid,pnet,pam1,pam2,pam1n,pam2n,pnums1,pnums2,invites_nums,teams_nums,pam1s,pam2s';
        $fields_arr = explode(",", $fields);
        $user = model('app\common\model\User')->where('id', $uid)->field($fields)->find();
        if($user){
            if ($direction == 'up') {
                $loop = 0;
                $userP = $user;
//                $nowNet = $userP['pnet'];
                while ($userP && $user['plan'] == $userP['plan']) {
                    $ids[] = $userP->id;
//                    echo "L:".$loop;
//                    foreach($fields_arr as $val){
//                        echo $val.":".$userP[$val]."==>";
//                    }
//                    echo "\n";
//                    $nowNet = $userP['pnet'];
                    if($userP->id == $stopId) break;
                    $userP = model('app\common\model\User')->where('id', $userP[$relation])->field($fields)->find();
                    $loop++;
                }
            }
            if ($direction == 'down' || $direction == 'downL') {
                $loop = 0;
                $userP = $user;
//                $nowNet = $userP['pnet'];
                while ($userP && $user['plan'] == $userP['plan']) {
                    $ids[] = $userP->id;
//                    echo "L:".$loop;
//                    foreach($fields_arr as $val){
//                        echo $val.":".$userP[$val]."==>";
//                    }
//                    echo "\n";
//                    $nowNet = $userP['pnet'];
                    if($userP->id == $stopId) break;
                    if($direction == 'downL'){
                        $userP = model('app\common\model\User')->where($relation, $userP['id'])->field($fields)->order('pnet asc')->find();
                    }else {
                        if ($loop == 0) $userP = model('app\common\model\User')->where($relation, $userP['id'])->field($fields)->order('pam1 asc')->find();
                        else $userP = model('app\common\model\User')->where($relation, $userP['id'])->field($fields)->order('pam1 desc')->find();
                    }
                    $loop++;
                }
            }
            foreach($ids as $v){
                echo ' or id='.$v;
            }
            echo "\n";
        }
    }

    public function NetIvFind(){
//        $idsNot = [30913, 30922, 31493, 31495, 34034, 34035, 34036, 34037, 34038, 34039];
        $idsNot = array(30913, 30922, 31493, 31495, 34034, 34035, 34036, 34037, 34038, 34039, 34040, 34274, 34276, 34287, 34309, 34310, 34486,36233,34489,37453,37500,37625,37689,38031,38032,38151,38241,38313,38373,38436,38660,38718,39996,41545,41584);

//        36233
        $loop = 1;
        $count = 0;
        $uid = 0;
        $userinfo = model('app\common\model\User')
            ->where('pid', '>', 0)
            ->where('pnet_iv', 0)
            ->where('id', 'not in', $idsNot)
            ->order('id', 'asc')
            ->find();
        echo $userinfo->id."\n";
//        exit;
        while($userinfo){
            $pnetIv=0;
            if($uid == $userinfo->id) {
                $count++;
            } else {
                $uid = $userinfo->id;
                $count = 0;
            }
            $userUp = $userinfo;
            while($userUp){
                echo $userUp['id']."|".$userUp['pnet']."--->";
                if($userinfo['manager_id'] == $userUp['pid']){
                    $pnetIv = $userUp['pnet'];
                    model('app\common\model\User')->where('id', $userinfo['id'])->update(['pnet_iv' => $userUp['pnet']]);
                    $userUp = false;
                }
                $userUp = model('app\common\model\User')->where('id', $userUp['pid'])->find();
            }
            echo "\n".$userinfo->id."===>".$uid."===>"."Pnet:".$pnetIv."===>".$count;
            if($count > 0) {
                echo "Error\n";
//                break;
                $idsNot[] = $userinfo->id;
//                var_dump($idsNot);
                $loop++;
            }else{
                echo "Done\n";
            }
            $userinfo = model('app\common\model\User')
                ->where('pid', '>', 0)
                ->where('pnet_iv', 0)
                ->where('id', 'not in', $idsNot)
                ->order('id', 'asc')
                ->find();

            if($loop > 0) {
                var_dump($idsNot);
                break;
            }
        }
    }

    public function doconfirm($params){
        $remitinfo = model('app\admin\model\wanlshop\RemitOrder')
            ->where('id', $params['id'])
            ->find();
        if ($remitinfo) {
            if($remitinfo['uplogid'] > 0){
                if($params['action'] == 'received'){
                    $count = model('app\admin\model\wanlshop\RemitOrder')
                        ->where('status', '<>', 'confirm')
                        ->where('uplogid', $remitinfo['uplogid'])
                        ->count();
                    if($count == 0){
                        model('app\admin\model\UserUpgradeLog')
                            ->where('id', $remitinfo['uplogid'])
                            ->update(['state' => '1']);
                        if($remitinfo['rtype'] > 0){
                            $uul = model('app\admin\model\UserUpgradeLog')
                                ->where('id', $remitinfo['uplogid'])
                                ->find();
                            if ($uul['lv_new'] > 2) {
                                $this->wslvUp($uul['pay_user_id'], $uul['lv_new']);
                            }
                        }
                    }
                }
                if($params['action'] == 'cancel'){
                    model('app\admin\model\UserUpgradeLog')
                        ->where('id', $remitinfo['uplogid'])
                        ->update(['state' => '2']);
                }
            }
            if($remitinfo['rtype'] < 2){
                if($params['action'] == 'received'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['user_id'], '进货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                    if($remitinfo['rtype'] == 0){
                        $user = model('app\common\model\User')->where('id', $remitinfo['user_id'])->find();
                        $userr = model('app\common\model\User')->where('id', $user['inviter_id'])->find();
                        if($userr['svip_level'] >= $user['svip_level']){
                            $pjrate = 0.05;
                            if($userr['svip_level'] == 4) $pjrate = 0.03;
                            if($userr['svip_level'] > 4) $pjrate = 0.02;
                            controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount']*$remitinfo['discount']*$pjrate, $userr['id'], '平级奖励', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                        }
                        if($user['svip_level'] < 11) $this->wslvUp($user['id'], $user['svip_level'], false);
                    }
                }
                if($params['action'] == 'cancel'){
                    controller('addons\wanlshop\library\WanlPay\WanlPay')->currency($remitinfo['amount'], $remitinfo['rec_user_id'], '批发商退货', 'subsidy', $remitinfo['pay_sn'], 'currency_rmb');
                }
            }
        }
    }

    public function checkOrderOut($orderid){
        $data = [
            'merchantId' => $this->merchantId,
            'merchantOrderId' => $orderid,
        ];
        $signbf = 'merchantId='.$this->merchantId.'&merchantOrderId='.$data['merchantOrderId'].'&key='.$this->key;
        $sign = strtoupper(md5($signbf));
        $result = $this->sendRequest("https://pay.s100mi.com/api/v1/out-order/transfer/query", $data, 'POST', $option=[], $sign);
        if ($result['ret']) {
            $result['msg'] = (array)json_decode($result['msg'], true);
            if($result['msg']['code'] == 200){
                if( $result['msg']['data']['status'] == 'SUCCESS') return true;
                else return false;
//                var_dump($result['msg']);
            }
            return false;//$result['msg'];
        }else{
            return false;//['code' => 10005 ,'msg' => '接口异常'];
        }
    }

    public function pxMarketAm($uid)
    {
        $loop = 1;
        $tzAm = 0;
        $pvAm = 0;
        $drqAm = 0;
//        $createtime_s = strtotime('2024-11-30');
//        $createtime_e = strtotime('2024-12-31');
//        $createtime_s = strtotime('2024-12-31');
//        $createtime_e = strtotime('2025-01-31');
//        6071
//        $createtime_s = strtotime('2025-01-24');
//        $createtime_e = strtotime('2025-02-24');
        $createtime_s = strtotime('2025-02-06');
        $createtime_e = strtotime('2025-03-01');
        foreach (model('app\common\model\User')
                     ->where('px_id', $uid)
                     ->where('vip_level', '>', 1)
                     ->order('id', 'desc')->select() as $user) {
            $msg = '';
            foreach (model('app\api\model\wanlshop\OrderSync')
                         ->where('user_id', $user->id)
                         ->where('createtime', '>', $createtime_s)
                         ->where('createtime', '<', $createtime_e)
                         ->where('pv', '<', 0)
//                         ->where('flq', '<', '3')
                         ->where('status', '<', '3')
                         ->order('id', 'desc')
                         ->select() as $order) {
                $tz = 0;
                $pv = 0;
                $dkq = 0;
                $memo = 'NO';
                $tz = $order->price * $order->number;
                $msg .= "UID:" . $user->id . "OrderNO:" . $order->order_no. "FLQ:" . $order->flq . "Price:" . $order->price . "Number:" . $order->number;
                if ($order->price == 1300) {
                    $flqPv = $order->number * 300;
                    $pv = $order->number * 800;
                } else {
                    $flqPv = $order->number * 200;
                    $pv = $order->number * 700;
                }
                if ($order->flq == 1) $pv = $flqPv;
                $clogTj = model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $order->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '<>', 3)
                    ->where('memo', 'in',['推荐奖', '复购奖'])
                    ->order('id', 'asc')
                    ->find();
                if($clogTj) {
                    if ($pv == $clogTj->money) {
                        $pv = 0;
                        $memo = '第三单';
                    } else {
                        foreach (model("app\common\model\CurrencyNsLog")
                                     ->where('service_ids', $order->order_no)
                                     ->where('type', 'subsidy')
                                     ->where('status', '<>', 3)
                                     ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                     ->order('id', 'asc')
                                     ->select() as $log) {
                            if (intval($log->money) == intval($pv * 0.14)) $memo = '首单';
                            if ($memo == 'NO' && intval($log->money) == intval($pv * 0.07)) $memo = '复投';
                        }
                    }
                }
                if ($order->flq == 3) {
                    $dkq = $pv;
                    $drqAm += $pv;
                    $pv = 0;
                }
                if ($memo == 'NO') {
                    $pv = 0;
                    $memo = 'NA';
                }
                $msg .= "AM:" . $tz . "PV:" . $pv. "DKQ:" . $dkq;
                $msg .= "[$memo]";
                $msg .=  "\n";
//                echo $msg;
                if($memo == 'NA' && $order->flq != '3') echo $msg;
                $tzAm += $tz;
                $pvAm += $pv;
            }
            foreach (model('app\api\model\wanlshop\Order')
                         ->where('status', 'normal')
                         ->where('deletetime', null)
                         ->where('state', '>', 1)
                         ->where('state', '<', 7)
                         ->where('statusb', '1')
                         ->where('paymenttime', '>', $createtime_s)
                         ->where('paymenttime', '<', $createtime_e)
                         ->where('pv', '<', 0)
                         ->where('flq', '<', '3')
                         ->where('user_id', $user->id)
                         ->order('id asc')->select() as $order) {
                foreach (model('app\api\model\wanlshop\OrderGoods')
                             ->where('order_id', $order->id)
                             ->select() as $goods) {
                    $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                    if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] || $goodsData['activity_type'] == 'vipgoods' || $goodsData['activity_type'] == 'shopgoods') {
                        if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods') {
                            $msg = "UID:" . $user->id . "OrderNO:" . $order->order_no. "FLQ:" . $order->flq;
                            $tz = 0;
                            $pv = 0;
                            $dkq = 0;
                            $tz = $goods['price'] * $goods['number'];
                            $price = 999;
                            if ($tz % 999 == 0) {
                                $nums = floor($tz / 999);
                            } else if ($tz % 1300 == 0) {
                                $price = 1300;
                                $nums = floor($tz / 1300);
                            }
                            $memo = 'NO';
                            $msg .= "Price:" . $price . "Number:" . $nums;
                            if ($price == 1300) {
                                $flqPv = $nums * 300;
                                $pv = $nums * 800;
                            } else {
                                $flqPv = $nums * 200;
                                $pv = $nums * 700;
                            }
                            if ($order->flq == 1) $pv = $flqPv;
                            $clogTj = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $order->order_no)
                                ->where('type', 'subsidy')
                                ->where('status', '<>', 3)
                                ->where('memo', 'in',['推荐奖', '复购奖'])
                                ->order('id', 'asc')
                                ->find();
                            if ($pv == $clogTj->money) {
                                $pv = 0;
                                $memo = '第三单';
                            } else {
                                foreach (model("app\common\model\CurrencyNsLog")
                                             ->where('service_ids', $order->order_no)
                                             ->where('type', 'subsidy')
                                             ->where('status', '<>', 3)
                                             ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                             ->order('id', 'asc')
                                             ->select() as $log) {
                                    if (intval($log->money) == intval($pv * 0.14)) $memo = '首单';
                                    if ($memo == 'NO' && intval($log->money) == intval($pv * 0.07)) $memo = '复投';
                                }
                            }
                            if ($order->flq == 3) {
                                $dkq = $pv;
                                $drqAm += $pv;
                                $pv = 0;
                            }
                            if ($memo == 'NO') {
                                $pv = 0;
                                $memo = 'NA';
                            }
                            $tzAm += $tz;
                            $pvAm += $pv;
                            $msg .= "AM:" . $tz . "PV:" . $pv. "DKQ:" . $dkq;
                            $msg .= "[$memo]";
                            $msg .= "\n";
//                            echo $msg;
                            if($memo == 'NA' && $order->flq != '3') echo $msg;
                        }
                    }
                }
            }
//            echo "SUM:".$user->id." TOTAL AM:" . $tzAm . "PV:" . $pvAm . "\n";
//            if($loop > 5) break;
            $loop++;
        }
        echo "PID:".$uid." TOTAL AM:" . $tzAm . "PV:" . $pvAm . "DRQ:" . $drqAm . "\n";
    }

    public function pMarketAm($uid)
    {
        $loop = 1;
        $tzAm = 0;
        $pvAm = 0;
        $drqAm = 0;
//        $createtime_s = strtotime('2024-11-30');
//        $createtime_e = strtotime('2024-12-31');
//        $createtime_s = strtotime('2024-12-31');
//        $createtime_e = strtotime('2025-01-31');
//        6071
//        $createtime_s = strtotime('2025-01-24');
//        $createtime_e = strtotime('2025-02-24');
//        $createtime_s = strtotime('2024-11-30');
//        $createtime_e = strtotime('2025-03-01');
        $createtime_s = strtotime('2025-03-31');
        $createtime_e = strtotime('2025-06-01');
        foreach (model('app\common\model\User')
                     ->where('p_id', $uid)
                     ->where('vip_level', '>', 1)
                     ->order('id', 'desc')->select() as $user) {
            $msg = '';
            foreach (model('app\api\model\wanlshop\OrderSync')
                         ->where('user_id', $user->id)
                         ->where('createtime', '>', $createtime_s)
                         ->where('createtime', '<', $createtime_e)
                         ->where('pv', '<', 0)
//                         ->where('flq', '<', '3')
                         ->where('status', '<', '3')
                         ->order('id', 'desc')
                         ->select() as $order) {
                $tz = 0;
                $pv = 0;
                $dkq = 0;
                $memo = 'NO';
                $tz = $order->price * $order->number;
                $msg .= "UID:" . $user->id . "OrderNO:" . $order->order_no. "FLQ:" . $order->flq . "Price:" . $order->price . "Number:" . $order->number;
                if ($order->price == 1300) {
                    $flqPv = $order->number * 300;
                    $pv = $order->number * 800;
                } else {
                    $flqPv = $order->number * 200;
                    $pv = $order->number * 700;
                }
                if ($order->flq == 1) $pv = $flqPv;
                $clogTj = model("app\common\model\CurrencyNsLog")
                    ->where('service_ids', $order->order_no)
                    ->where('type', 'subsidy')
                    ->where('status', '<>', 3)
                    ->where('memo', 'in',['推荐奖', '复购奖'])
                    ->order('id', 'asc')
                    ->find();
                if($clogTj) {
                    if ($pv == $clogTj->money) {
                        $pv = 0;
                        $memo = '第三单';
                    } else {
                        foreach (model("app\common\model\CurrencyNsLog")
                                     ->where('service_ids', $order->order_no)
                                     ->where('type', 'subsidy')
                                     ->where('status', '<>', 3)
                                     ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                     ->order('id', 'asc')
                                     ->select() as $log) {
                            if (intval($log->money) == intval($pv * 0.14)) $memo = '首单';
                            if ($memo == 'NO' && intval($log->money) == intval($pv * 0.07)) $memo = '复投';
                        }
                    }
                }
                if ($order->flq == 3) {
                    $dkq = $pv;
                    $drqAm += $pv;
                    $pv = 0;
                }
                if ($memo == 'NO') {
                    $pv = 0;
                    $memo = 'NA';
                }
                $msg .= "AM:" . $tz . "PV:" . $pv. "DKQ:" . $dkq;
                $msg .= "[$memo]";
                $msg .=  "\n";
//                echo $msg;
                if($memo == 'NA' && $order->flq != '3') echo $msg;
                $tzAm += $tz;
                $pvAm += $pv;
            }
            foreach (model('app\api\model\wanlshop\Order')
                         ->where('status', 'normal')
                         ->where('deletetime', null)
                         ->where('state', '>', 1)
                         ->where('state', '<', 7)
                         ->where('statusb', '1')
                         ->where('paymenttime', '>', $createtime_s)
                         ->where('paymenttime', '<', $createtime_e)
                         ->where('pv', '<', 0)
                         ->where('flq', '<', '3')
                         ->where('user_id', $user->id)
                         ->order('id asc')->select() as $order) {
                foreach (model('app\api\model\wanlshop\OrderGoods')
                             ->where('order_id', $order->id)
                             ->select() as $goods) {
                    $goodsData = model('app\api\model\wanlshop\Goods')->get($goods['goods_id']);
                    if ($goods['shop_id'] == $this->saas_info[$user['saas_id']]['shop_id_vip'] || $goodsData['activity_type'] == 'vipgoods' || $goodsData['activity_type'] == 'shopgoods') {
                        if ($goodsData['category_id'] == $this->calcData[0]['category_id_lv2'] || $goodsData['activity_type'] == 'shopgoods') {
                            $msg = "UID:" . $user->id . "OrderNO:" . $order->order_no. "FLQ:" . $order->flq;
                            $tz = 0;
                            $pv = 0;
                            $dkq = 0;
                            $tz = $goods['price'] * $goods['number'];
                            $price = 999;
                            if ($tz % 999 == 0) {
                                $nums = floor($tz / 999);
                            } else if ($tz % 1300 == 0) {
                                $price = 1300;
                                $nums = floor($tz / 1300);
                            }
                            $memo = 'NO';
                            $msg .= "Price:" . $price . "Number:" . $nums;
                            if ($price == 1300) {
                                $flqPv = $nums * 300;
                                $pv = $nums * 800;
                            } else {
                                $flqPv = $nums * 200;
                                $pv = $nums * 700;
                            }
                            if ($order->flq == 1) $pv = $flqPv;
                            $clogTj = model("app\common\model\CurrencyNsLog")
                                ->where('service_ids', $order->order_no)
                                ->where('type', 'subsidy')
                                ->where('status', '<>', 3)
                                ->where('memo', 'in',['推荐奖', '复购奖'])
                                ->order('id', 'asc')
                                ->find();
                            if ($pv == $clogTj->money) {
                                $pv = 0;
                                $memo = '第三单';
                            } else {
                                foreach (model("app\common\model\CurrencyNsLog")
                                             ->where('service_ids', $order->order_no)
                                             ->where('type', 'subsidy')
                                             ->where('status', '<>', 3)
                                             ->where('memo', 'in', ['绩效奖', '绩效奖D'])
                                             ->order('id', 'asc')
                                             ->select() as $log) {
                                    if (intval($log->money) == intval($pv * 0.14)) $memo = '首单';
                                    if ($memo == 'NO' && intval($log->money) == intval($pv * 0.07)) $memo = '复投';
                                }
                            }
                            if ($order->flq == 3) {
                                $dkq = $pv;
                                $drqAm += $pv;
                                $pv = 0;
                            }
                            if ($memo == 'NO') {
                                $pv = 0;
                                $memo = 'NA';
                            }
                            $tzAm += $tz;
                            $pvAm += $pv;
                            $msg .= "AM:" . $tz . "PV:" . $pv. "DKQ:" . $dkq;
                            $msg .= "[$memo]";
                            $msg .= "\n";
//                            echo $msg;
                            if($memo == 'NA' && $order->flq != '3') echo $msg;
                        }
                    }
                }
            }
//            echo "SUM:".$user->id." TOTAL AM:" . $tzAm . "PV:" . $pvAm . "\n";
//            if($loop > 5) break;
            $loop++;
        }
        echo "PID:".$uid." TOTAL AM:" . $tzAm . "PV:" . $pvAm . "DRQ:" . $drqAm . "\n";
    }

    public function newAccount(){
        $mobile = ***********;
        $user = model('app\common\model\User');
        $mUserId = 31928;
        $userinfo = $user->where('id', '>=', $mUserId)
            ->where('m_id', $mUserId)
            ->where('is_pw', 1)
            ->order('pw_no', 'asc')
            ->find();
        while($userinfo) {
            // create Account
            $userNew = $userinfo->toArray();
            if($userinfo['id'] == $mUserId) $userNew['inviter_id'] = 1050;
            else{
                $userIvo = $user->where('id', $userinfo['pw_iv'])->field('mobile2')->find();
                $userIvn = $user->where('mobile', $userIvo['mobile2'])->field('id')->find();
                $userNew['inviter_id'] = $userIvn['id'];
                $userNew['pw_iv'] = $userIvn['id'];
            }
            $userNew['vip_status'] = '0';
            $userNew['vip_level'] = 1;
            $userNew['manager_id'] = 0;
            $userNew['pid'] = 0;
            $userNew['pnet'] = 0;
            $userNew['pnums1'] = 0;
            $userNew['pnums2'] = 0;
            $userNew['pam1'] = 0;
            $userNew['pam2'] = 0;
            $userNew['pam1n'] = 0;
            $userNew['pam2n'] = 0;
            $userNew['invites_nums'] = 0;
            $userNew['teams_nums'] = 0;
            if(strlen($userNew['avatar']) > 255){
                unset($userNew['avatar']);
            }
            unset($userNew['id']);
            unset($userNew['m_id']);
            $username = '';
            $hasUser = true;
            while($hasUser){
                $username = 'YFL'.Random::numeric(8);
                // 检测用户名、昵称、邮箱、手机号是否存在
                $userCheck = $user->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
                if(!$userCheck) $hasUser = false;
            }
            $userNew['username'] = $username;
            $id = $user->allowField(true)->create($userNew,true)->id;
            if($id > 0){
                $this->invitationCode($id);
                $user->where('id', $userinfo->id)->update(['mobile' =>  $mobile, 'is_pw' => 4]);
                $mobile++;
            }
            // update account
            echo $id."===>".$userNew['nickname']."===>".$mobile."\n";
//            var_dump($id);
//            exit;
            $userinfo = $user->where('id', '>=', $mUserId)
                ->where('m_id', $mUserId)
                ->where('is_pw', 1)
                ->order('pw_no', 'asc')
                ->find();
        }
    }
    public function actAccount($idF, $idE){
        $userinfo = model('app\common\model\User')
            ->where('is_pw', 1)
            ->where('id', '>=', $idF)
            ->where('id', '<=', $idE)
//            ->order('pw_no', 'asc')
            ->order('pw_no,id', 'asc')
            ->find();
//        echo $userinfo->id;exit;
        while($userinfo) {
            $order_info = array();
            $order_info['user_id'] = $userinfo->id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $userinfo->username;
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
//            $order_info['act'] = 'newpurchase';
            $arr = array();
            $arr['action'] = 'orderAction';
            $arr['order'] = $order_info;
            $arr['isOrderNet'] = true;
            Hook::listen("com_auto_settlement", $arr);
            model('app\common\model\User')->where(['id' => $userinfo->id])->update(['is_pw'=>3]);
            echo $userinfo['id']."===>".$userinfo['mobile']."===>".$userinfo->inviter_id."\n";
//            sleep(5);
//            exit;
            $userinfo = model('app\common\model\User')
                ->where('is_pw', 1)
                ->where('id', '>', $idF)
                ->where('id', '<', $idE)
                ->order('pw_no,id', 'asc')
                ->find();
        }

    }

    public function userToGd($sid){
        $userinfo = model('app\common\model\User')
//                        ->where('form_app', NULL)
            ->where('id_gd', 0)
            ->where('id', '>=', $sid)
            ->where('id', '<', 20000)
            ->order('id', 'asc')
//                    ->limit(1)
            ->find();
        while($userinfo){
            $res = controller('app\api\controller\exten\UserOrderReq')->registerBatch([$userinfo->id], true);
            var_dump($res);
            $userinfo = model('app\common\model\User')
//                        ->where('form_app', NULL)
                ->where('id_gd', 0)
                ->where('id', '>=', $sid)
                ->where('id', '<', 20000)
                ->order('id', 'asc')
//                    ->limit(1)
                ->find();
        }
    }

    public function cUsers($ivid=0, $nums=0, $n2=0, $dz=0) {
        if($ivid == 0 || $nums == 0) return false;
//        $password = 'MKap2LtjHOBg3_9yVR1gaNY4sy3DxLf1dr5';
//        $salt = 'CagZjx';
//        $paypwd = 'Yco8IefQyaiVvELD2wehdNwMvlM';
//        $psalt = 'WEK9cB';
        $password = 'o9FknF1N9w2ksEuA4Wkf5qvyc_tdKgy';
        $salt = 'rWK40U';
        $paypwd = 'tFTh-I48QnEJOmC1QxgtVDk-IMg';
        $psalt = '7rH2w6';
        $user_key = ['id', 'saas_id', 'm_id', 'plan', 'group_id', 'username', 'nickname', 'truename', 'idcard', 'alipay', 'password', 'salt', 'paypwd', 'psalt', 'ivcode', 'email', 'mobile', 'avatar', 'level', 'vip_status', 'ns_status', 'trident11', 'trident21', 'level_id', 'vip_level', 'vipv_level', 'vip_levels', 'vip_levelm', 'svip_level', 'svip_levels', 'vip_statusu', 'vip_levelu', 'svip_levelu', 'svip_levelum', 'is_balance', 'is_remit', 'is_withdraw', 'is_certified', 'gender', 'birthday', 'bio', 'money', 'currency_old', 'currency_cny', 'currency_rmb', 'currency_bd', 'currency_xnb', 'currency_nfr', 'currency_ns', 'currency_ans', 'currency_usdt', 'currency_fil', 'currency_lmt', 'currency_points', 'currency_gq', 'currency_tz', 'currency_pu', 'currency_tzu', 'currency_adv', 'currency_love', 'currency_pow', 'currency_spow', 'currency_ric', 'riches', 'currency_flq', 'currency_fhq', 'currency_gfz', 'score', 'successions', 'maxsuccessions', 'prevtime', 'logintime', 'loginip', 'loginfailure', 'joinip', 'jointime', 'createtime', 'updatetime', 'uptime', 'token', 'status', 'verification', 'inviter_id', 'pid', 'pnet', 'pam1', 'pam2', 'pam1c', 'pam2c', 'pam1n', 'pam2n', 'pnets', 'self_am0', 'self_amt', 'self_times', 'self_amd0', 'self_amdt', 'invite_nums', 'invite_numsns', 'invites_loop', 'invites_nums', 'invite_numsw', 'invites_numsw', 'team_nums', 'team_numsns', 'teams_nums', 'invite_am', 'team_am', 'team_amm', 'invite_numsu', 'invite_amu', 'team_numsu', 'team_amu', 'team_amu_new', 'invite_numsu_new', 'team_numsu_new', 'static_m', 'static_bonus', 'share_bonus', 'share2_bonus', 'dynamic_bonus', 'dynamic_am', 'svip_bonus', 'autopay', 'purelease', 'pu_static', 'pu_share', 'pu_dynamic', 'pu_todaytt', 'wslv3', 'wslv4', 'monthly_ns', 'monthly_nsn', 'monthly_ns_am', 'ns_pay', 'ns_dynamic', 'ns_static', 'ns_fhq', 'adv_nums', 'adv_num_time', 'parent_code', 'is_securities_firms', 'is_distributor', 'vipd_level', 'invited_nums', 'teamd_nums', 'top_lvd', 'dist_address', 'city_server', 'city_address', 'city_info', 'monthly_fg', 'monthly_fgn', 'viplc_level', 'lc_lam', 'up_state'];
        $user_val = [6586, 4, 0, 'C', 0, '13000000030', '130****0030', NULL, NULL, NULL, $password, $salt, $paypwd, $psalt, 'FHXQyA01', '', '13000000030', '', 1, '1', '0', '0', '0', 0, 3, 1, 1, 1, 1, 1, '0', 1, 1, 1, '1', '1', '1', '1', 0, NULL, '', '0.00', '0.00000', '0.00', '0.00', '0.00', '0', '0', '0.00', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', 0, 1, 1, 1725926400, 1712742196, '***************', 0, '***************', 1725926400, 1725926400, 1712742196, NULL, '', 'normal', '', 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', 0, '0.00000', '0', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', 0, 0, 0, 0, '0.00', '0.00000', '0.00000', '0.00000', 0, 0, NULL, '0,1,150,152,', 0, 0, 0, 0, 0, 0, NULL, '0', NULL, NULL, 0, 0, 1, 0, 0];
        $count = count($user_key);

        $userRegister = true;
        $pnet_dz = 1;
        $ivUser = model('app\common\model\User')->where('id', $ivid)->find();
        $password = $ivUser->password;
        $salt = $ivUser->salt;
        $paypwd = $ivUser->paypwd;
        $psalt = $ivUser->psalt;
        $user_val = [6586, 4, 0, 'C', 0, '13000000030', '130****0030', NULL, NULL, NULL, $password, $salt, $paypwd, $psalt, 'FHXQyA01', '', '13000000030', '', 1, '1', '0', '0', '0', 0, 3, 1, 1, 1, 1, 1, '0', 1, 1, 1, '1', '1', '1', '1', 0, NULL, '', '0.00', '0.00000', '0.00', '0.00', '0.00', '0', '0', '0.00', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', 0, 1, 1, 1725926400, 1712742196, '***************', 0, '***************', 1725926400, 1725926400, 1712742196, NULL, '', 'normal', '', 210, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '0.00000', '0.00000', '0.00000', '0.00000', '0.00000', 0, '0.00000', '0', '0.00', '0.00000', '0.00000', '0.00000', '0.00000', 0, 0, 0, 0, '0.00', '0.00000', '0.00000', '0.00000', 0, 0, NULL, '0,1,150,152,', 0, 0, 0, 0, 0, 0, NULL, '0', NULL, NULL, 0, 0, 1, 0, 0];
        $count = count($user_key);
        if($userRegister) {
            $endUser = model('app\common\model\User')->where('id', '<', 30000)->order('id desc')->find();
            $sid = $endUser->id + 1;
//            $ivc = $endUser->mobile[7].$endUser->mobile[8].$endUser->mobile[9].$endUser->mobile[10] + 1;
            $ivc = $endUser->username[7].$endUser->username[8].$endUser->username[9].$endUser->username[10] + 1;
            $no1 = '1330000'.$ivc;
            $ivn = 133;
            $isIv = true;
            $search = "1330000";
            $replace = "133****";
            $replaceY = "YFL3000";
            for ($k = 0; $k < $nums; $k++) {
                $loop = 0;
                $user_array = array();
                for ($j = 0; $j < $count; $j++) {
                    $user_array[$user_key[$j]] = $user_val[$j];
                    if (in_array($user_key[$j], array('id'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $sid + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if (in_array($user_key[$j], array('username', 'mobile'))) {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $no1 + $k;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'inviter_id') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = $ivid;
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                    if ($user_key[$j] == 'ivcode') {
                        print($user_key[$j]);
                        echo ':';
                        $ic = $ivc + $k;
                        $user_array[$user_key[$j]] = 'FHXQ' . $ivn . 'Cn' . $ic . 'M';
                        echo "===>";
                    }
                    if ($user_key[$j] == 'nickname') {
                        print($user_key[$j]);
                        echo ':';
                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
//                        $search = "1340000";
//                        $replace = "134****";
//                        $user_array[$user_key[$j]] = str_replace($search, $replace, $user_array['username']);
                        print($user_array[$user_key[$j]]);
                        echo "===>";
                    }
                }

                $user_array['vip_status'] = '0';
                $user_array['vip_level'] = 1;
                $user_array['pnet_dz'] = $pnet_dz;
                $user_array['is_pw'] = 1;
                if($n2 > 0) {
                    if ($k < $n2) $user_array['pnet_dz'] = 2;
                    else $user_array['pnet_dz'] = 1;
                }
                if($dz == 1 || $dz == 2) $user_array['pnet_dz']=$dz;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
//                    $isIv = !$isIv;
//                $user_array['pnet_dz'] = 2;
                $user_array['inviter_id'] = $ivid;
//                if($k == 0) $user_array['inviter_id'] = $ivid;
//                else $user_array['inviter_id'] = $sid;
//                if($isIv){
//                    $user_array['inviter_id'] = $ivid;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
//                if ($k > 0 && $k < 10) {
//                    $user_array['pnet_dz'] = 1;
//                }
//                if($isIv){
//                    $user_array['inviter_id'] = 7906;
//                }else{
//                    $user_array['inviter_id'] = $user_array['id'] - 1;
//                }
////                if($k != 5 && $k != 6 && $k != 13 && $k != 16 && $k != 17 && $k != 32)
//                $isIv = !$isIv;
                $user_array['username'] = str_replace($search, $replaceY, $user_array['username']);
                model('app\common\model\User')->create($user_array);
                echo "\n";
            }
        }
        $this->actAccount($sid, 20000);
        $this->userToGd($sid);
    }

    public function oneToThree($uid){
        $userModel = model('app\common\model\User');
        $userinfo = $userModel->where('id',$uid)->find();
        $userNew = $userinfo->toArray();
        $userNew['inviter_id'] = $userinfo->id;
        $userNew['parent_code'] = $userinfo->parent_code.",".$userinfo->id;
        $userArr['ns_status'] = '0';
        $userNew['vip_status'] = '0';
        $userNew['vip_level'] = 1;
        $userNew['vip_level'] = 1;
        $userNew['vip_levels'] = 1;
        $userNew['self_amd0'] = 0;
        $userNew['viplc_level'] = 1;
        $userNew['svip_level'] = 1;
        $userNew['manager_id'] = 0;
        $userNew['pid'] = 0;
        $userNew['pnet'] = 0;
        $userNew['pnet_dz'] = 0;
        $userNew['pnet_iv'] = 0;
        $userNew['pnums1'] = 0;
        $userNew['pnums2'] = 0;
        $userNew['pam1'] = 0;
        $userNew['pam2'] = 0;
        $userNew['pam1n'] = 0;
        $userNew['pam2n'] = 0;
        $userNew['pam1c'] = 0;
        $userNew['pam2c'] = 0;
        $userNew['invites_nums'] = 0;
        $userNew['teams_nums'] = 0;
        $userNew['successions'] = 0;
        $userNew['maxsuccessions'] = 0;
        if(strlen($userNew['avatar']) > 255){
            unset($userNew['avatar']);
        }
        unset($userNew['id']);
        unset($userNew['money']);
        unset($userNew['currency_rmb']);
        unset($userNew['currency_xnb']);
        unset($userNew['currency_cny']);
        unset($userNew['currency_bd']);
        unset($userNew['currency_ns']);
        unset($userNew['currency_tz']);
        unset($userNew['currency_nfr']);
        unset($userNew['currency_adv']);
        unset($userNew['currency_pu']);
        unset($userNew['vipv_level']);
        unset($userNew['form_app']);
        unset($userNew['form_id']);
        unset($userNew['username_gd']);
        unset($userNew['id_gd']);
        unset($userNew['username_fh']);
        unset($userNew['id_fh']);
        unset($userNew['activetime']);

        $userNew['pnet_dz'] = 1;
        $username = '';
        $flag = true;
        while($flag){
            $username = 'YFL'.Random::numeric(8);
            $flag = (bool)$userModel->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
        }
        $userNew['username'] = $username;
        $userNew['nickname'] = $username;
        $id = $userModel->allowField(true)->create($userNew,true)->id;
        if($id > 0){
            $this->invitationCode($id);
            $order_info = array();
            $order_info['user_id'] = $id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $username;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $this->calcOrderAction($order_info);
        }


        $userNew['pnet_dz'] = 2;
        $username = '';
        $flag = true;
        while($flag){
            $username = 'YFL'.Random::numeric(8);
            $flag = (bool)$userModel->get(['saas_id' => $userinfo['saas_id'], 'username' => $username]);
        }
        $userNew['username'] = $username;
        $userNew['nickname'] = $username;
        $id = $userModel->allowField(true)->create($userNew,true)->id;
        if($id > 0){
            $this->invitationCode($id);
            $order_info = array();
            $order_info['user_id'] = $id;
            $order_info['order_id'] = 0;
            $order_info['order_no'] = 'HT' . $username;
            $order_info['category_id'] = 108;
            $order_info['flq'] = 2;
            $order_info['activity_type'] = 'shopgoods';
            $order_info['act'] = 'emptyorder';
            $order_info['price'] = 1300;
            $order_info['number'] = 10;
            $this->calcOrderAction($order_info);
        }
    }

    public function syncLv(){
        $user = model('app\common\model\User')
//            ->where('islvsync', '<', '2')
            ->where('islvsync', '0')
            ->where('vip_level', '>', 2)
            ->order('id', 'asc')
            ->find();
        while($user) {
            $userArr = array(
                'id' => $user->id,
                'saas_id' => $user->saas_id,
                'lv' => $user->vip_level
            );
            $res = controller('app\api\controller\exten\UserOrderReq')->syncLv($userArr);
            var_dump($res);
            $user = model('app\common\model\User')
//                ->where('islvsync', '<', '2')
                ->where('islvsync', '0')
                ->where('vip_level', '>', 2)
                ->order('id', 'asc')
                ->find();
        }
    }

    public function test($auction_id = 1)
    {
        foreach(model('app\common\model\User')
                    ->where('id', 37473)
                    ->where('vip_level', '>', 1)->select() as $user){
            $starttime = strtotime("2023-12-30");
            $userBonus = model('app\common\model\UserBonusTotal')->where('user_id', $user->id)->find();
            if($userBonus){
                if($userBonus->updatetime > 0 && $userBonus->updatetime != '') $starttime = $userBonus->updatetime;
            }
            $uBonus = model('app\common\model\CurrencyNsLog')
                ->where('user_id', $user->id)
                ->where('createtime', '>', $starttime)
                ->field("user_id
                                    ,SUM( CASE WHEN memo in ('推荐奖') and status <> '2' THEN money ELSE 0 END ) AS bonus_iv
                                    ,SUM( CASE WHEN memo in ('复购奖') and status <> '2' THEN money ELSE 0 END ) AS bonus_ivr
                                    ,SUM( CASE WHEN memo in ('绩效奖','绩效奖D') and status <> '2' THEN money ELSE 0 END ) AS bonus_jx
                                    ,SUM( CASE WHEN memo in ('管理奖') and status <> '2' THEN money ELSE 0 END ) AS bonus_gl
                                    ,SUM( CASE WHEN memo in ('总监奖励') and status <> '2' THEN money ELSE 0 END ) AS bonus_zj
                                    ,SUM( CASE WHEN memo in ('达人补贴') and status <> '2' THEN money ELSE 0 END ) AS bonus_dr")
                ->find();
            if($uBonus && ($uBonus->bonus_iv || $uBonus->bonus_ivr || $uBonus->bonus_jx || $uBonus->bonus_gl)){
                if($userBonus){
                    model('app\common\model\UserBonusTotal')
                        ->where('id', $userBonus->id)
                        ->update(['bonus_iv' => $userBonus->bonus_iv + $uBonus->bonus_iv, 'bonus_ivr' => $userBonus->bonus_ivr + $uBonus->bonus_ivr, 'bonus_jx' => $userBonus->bonus_jx + $uBonus->bonus_jx, 'bonus_gl' => $userBonus->bonus_gl + $uBonus->bonus_gl, 'bonus_zj' => $userBonus->bonus_zj + $uBonus->bonus_zj, 'bonus_dr' => $userBonus->bonus_dr + $uBonus->bonus_dr, 'updatetime' => time()]);
                }else{
                    $userBonusArray = array();
                    $userBonusArray['user_id'] = $user->id;
                    $userBonusArray['saas_id'] = $user->saas_id;
                    $userBonusArray['bonus_iv'] = $uBonus->bonus_iv??0;
                    $userBonusArray['bonus_ivr'] = $uBonus->bonus_ivr??0;
                    $userBonusArray['bonus_jx'] = $uBonus->bonus_jx??0;
                    $userBonusArray['bonus_gl'] = $uBonus->bonus_gl??0;
                    $userBonusArray['bonus_zj'] = $uBonus->bonus_zj??0;
                    $userBonusArray['bonus_dr'] = $uBonus->bonus_dr??0;
                    $userBonusArray['createtime'] = time();
                    $userBonusArray['updatetime'] = time();
                    model('app\common\model\UserBonusTotal')->create($userBonusArray);
                }
                echo "User ID:".$user->id."==>Bonus IV:".$uBonus->bonus_iv."==>Bonus IVR:".$uBonus->bonus_ivr."==>Bonus JX:".$uBonus->bonus_jx."==>Bonus GL:".$uBonus->bonus_gl."\n";
            }
        }
        exit;
        echo "================================\n";
        $list = model('app\common\model\CurrencyNsLog')
            ->field("user_id
                                    ,SUM( CASE WHEN memo in ('推荐奖') THEN money ELSE 0 END ) AS iv_am
                                    ,SUM( CASE WHEN memo in ('复购奖') THEN money ELSE 0 END ) AS ivr_am
                                    ,SUM( CASE WHEN memo in ('绩效奖','绩效奖D') THEN money ELSE 0 END ) AS jx_am
                                    ,SUM( CASE WHEN memo in ('管理奖') THEN money ELSE 0 END ) AS gl_am")
            ->order('user_id ASC')
            ->group('user_id')
            ->limit(10)
            ->select();
        foreach($list as $v){
            echo "ID:".$v['user_id']."==>IV:".$v['iv_am']."==>IVR:".$v['ivr_am']."==>JX:".$v['jx_am']."==>GL:".$v['gl_am'];
            echo "\n";
        }
        exit;
        $moneyBk = true;
        $mType_arr = array(
            0 => 'currency_pu'
        );
        foreach ($mType_arr as $mType) {
            $mTypeEx = explode("_", $mType);
            $mTypeLog = ucfirst($mTypeEx[1]);
            foreach(model("app\common\model\Currency{$mTypeLog}Log")
                        ->where('user_id', 46433)
                        ->where('id', 200)
                        ->select() as $log) {
                echo $log->user_id."==>".$log->money."\n";
                if($moneyBk){
                    if ($log->money > 0) model('app\common\model\User')->where('id', $log->user_id)->setDec($mType, $log->money);
                    if ($log->money < 0) model('app\common\model\User')->where('id', $log->user_id)->setInc($mType, abs($log->money));
                }
                model("app\common\model\Currency{$mTypeLog}Log")
                    ->where('id', $log['id'])
                    ->update(['status' => '2']);
            }
        }
        exit;
        foreach(model('app\common\model\User')
//                    ->where('id', 'in', [528,6066,7077])
                    ->where('id', '>', 7076)
                    ->where('id', '<', 7093)
//                    ->where('id', '<', 7493)
                    ->field('id,username,truename,mobile,inviter_id,pid,pnet,is_pw')
                    ->select() as $user){
            echo "ID:".$user->id."==>".$user->username."==>".$user->truename."==>".$user->mobile;
            echo "==>IV:".$user->inviter_id."==>PID:".$user->pid."==>NET:".$user->pnet;
            echo "==>PW:".$user->is_pw;
            echo "\n";
        }
        exit();
        foreach(model('app\admin\model\UserTrident2n')
                    ->where('state', '1')
                    ->where('status', '0')
                    ->where('px_nums', 2)
                    ->order('id', 'asc')->select() as $ut2n){
            $loopm = 1;
            foreach(model('app\admin\model\UserTrident2n')
                        ->where('state', '1')
                        ->where('status', '0')
                        ->where('rid', $ut2n['user_id'])
                        ->order('team_nums desc,id asc')->select() as $ut2n2){
//                if($loopm == 1)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
//                if($loopm == 3)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
//                if($loopm == 2)
//                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);

                if($loopm == 1)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 3]);
                if($loopm == 2)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 1]);
                if($loopm == 3)
                    model('app\admin\model\UserTrident2n')->where('id', $ut2n2['id'])->update(['market_kh' => 2]);
                $loopm++;
            }
        }

        exit();
        foreach (model("app\common\model\CurrencyRmbLog")
                     ->where('type', 'subsidy')
                     ->where('money',999)
                     ->order('id', 'desc')
                     ->select() as $cl) {
            $count = model("app\common\model\CurrencyRmbLog")
                ->where('type', 'subsidy')
                ->where('money',999)
                ->where('service_ids',$cl['service_ids'])
                ->order('id', 'desc')
                ->count();
            if($count > 1){
                echo $cl['id']."===>".$cl['user_id']."===>".$cl['service_ids']."\n";
            }
        }
        exit();
        foreach(model('app\api\model\wanlshop\Order')
                    ->where('id', '>', '622')
                    ->where('status', 'normal')
                    ->where('deletetime', null)
                    ->where('state', '>', 1)
                    ->where('state', '<', 7)
                    ->where('state', '<', 7)
                    ->where('statusb', '0')->select() as $order){
            $user = model('app\common\model\User')
                ->where('id', $order['user_id'])
                ->find();
            $u2info = model('app\admin\model\UserTrident2n')
                ->where('saas_id', $order['saas_id'])
                ->where('state', '1')
                ->where('status', '0')
                ->where('user_id', $order['user_id'])
                ->order('id', 'asc')->find();
            $hvn2 = False;
            if($u2info) $hvn2 = True;
            echo 'ID:'.$user['id'].';'.$user['username'].'入网：'.($hvn2?'Y':'N')."\n";
//            if (!$hvn2) $this->calcOrderPayTest($order['id']);
        }

    }

    public function YTest($act){
        exit();
//        SELECT id,username,saas_id,vip_status,vip_level,vip_levelm,inviter_id,manager_id,self_amd0,pid,pnet,pnums1,pnums2,pam1,pam2,pam1n,pam2n FROM `fa_user` WHERE id>100 and id < 200
        $userlist = [102,103,106];
        if($act == 'Clear') $userlist = [101,102,103,106];
        if($act == 'New') model('app\common\model\User')->where('id', 101)->update(['vip_level' => 2]);
        foreach($userlist as $user_id){
            if($act == 'Clear')
                model('app\common\model\User')
                    ->where('id', $user_id)
                    ->update([
                        'vip_level' => 1,
                        'vip_levelm' => 1,
                        'self_amd0' => 0,
                        'pid' => 0,
                        'pnet' => 0,
                        'pam1' => 0,
                        'pam2' => 0,
                        'pam1n' => 0,
                        'pam2n' => 0,
                        'pnums1' => 0,
                        'pnums2' => 0
                    ]);
            if($act == 'New') {
                $this->intoNet2($user_id, '1000000' . $user_id, 100, 1, 2, 1);
                model('app\common\model\User')
                    ->where('id', $user_id)
                    ->update([
                        'vip_level' => 2,
                        'self_amd0' => 100
                    ]);
            }
        }
    }
}
