<?php

return [
    'autoload' => false,
    'hooks' => [
        'app_init' => [
            'alioss',
            'qrcode',
            'wanlshop',
        ],
        'upload_config_init' => [
            'alioss',
        ],
        'upload_delete' => [
            'alioss',
        ],
        'lakala' => [
            'lakala',
        ],
        'payment' => [
            'lakala',
        ],
        'trade' => [
            'lakala',
        ],
        'refund' => [
            'lakala',
        ],
        'verify' => [
            'lakala',
        ],
        'apply' => [
            'lakala',
        ],
        'query_contract' => [
            'lakala',
        ],
        'download_contract' => [
            'lakala',
        ],
        'balance_query' => [
            'lakala',
        ],
        'area' => [
            'lakala',
        ],
        'bank' => [
            'lakala',
        ],
        'upload' => [
            'lakala',
        ],
        'category' => [
            'lakala',
        ],
        'reg_merchant' => [
            'lakala',
        ],
        'add_term' => [
            'lakala',
        ],
        'query_merchant' => [
            'lakala',
        ],
        'query_update' => [
            'lakala',
        ],
        'update_info' => [
            'lakala',
        ],
        'update_settle' => [
            'lakala',
        ],
        'query_merchant_no' => [
            'lakala',
        ],
        'reconsider' => [
            'lakala',
        ],
        'mrch_auth_state_query' => [
            'lakala',
        ],
        'mqtt' => [
            'mqtt',
        ],
        'send_msg' => [
            'mqtt',
        ],
        'config_init' => [
            'qcloudsms',
            'simditor',
        ],
        'sms_send' => [
            'qcloudsms',
        ],
        'sms_notice' => [
            'qcloudsms',
        ],
        'sms_check' => [
            'qcloudsms',
        ],
        'user_sidenav_after' => [
            'signin',
            'wanlshop',
        ],
        'upgrade' => [
            'simditor',
            'wanlshop',
        ],
        'weipinshang' => [
            'weipinshang',
        ],
    ],
    'route' => [
        '/qrcode$' => 'qrcode/index/index',
        '/qrcode/build$' => 'qrcode/index/build',
    ],
    'priority' => [],
    'domain' => '',
];
