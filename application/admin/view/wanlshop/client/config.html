<div class="panel panel-default panel-intro">
	<div class="panel-heading">
		<div class="panel-lead"><em>系统设置</em>全局配置实时同步客户端，用户重启客户端后生效</div>
		<ul class="nav nav-tabs">
			<li class="{:$Think.get.use === null ? 'active' : ''}"><a href="#system" data-toggle="tab">系统配置</a></li>
			<li class="{:$Think.get.use === 'redis' ? 'active' : ''}"><a href="#poster" data-toggle="tab">海报分享配置</a></li>
			<li class="{:$Think.get.use === 'redis' ? 'active' : ''}"><a href="#redis" data-toggle="tab">Redis配置</a></li>
			<li class="{:$Think.get.use === 'captcha' ? 'active' : ''}"><a href="#captcha" data-toggle="tab">人机验证配置</a></li>
			<li class="{:$Think.get.use === 'withdraw' ? 'active' : ''}"><a href="#withdraw" data-toggle="tab">提现配置</a></li>
			<li class="{:$Think.get.use === 'order' ? 'active' : ''}"><a href="#order" data-toggle="tab">订单配置</a></li>
			<li class="{:$Think.get.use === 'return' ? 'active' : ''}"><a href="#return" data-toggle="tab">退货配置</a></li>
			<li class="{:$Think.get.use === 'find' ? 'active' : ''}"><a href="#find" data-toggle="tab">发现页管理</a></li>
			<li class="{:$Think.get.use === 'live' ? 'active' : ''}"><a href="#live" data-toggle="tab">阿里直播</a></li>
			<li class="{:$Think.get.use === 'video' ? 'active' : ''}"><a href="#video" data-toggle="tab">阿里点播</a></li>
			<li class="{:$Think.get.use === 'im' ? 'active' : ''}"><a href="#im" data-toggle="tab">IM即时通讯</a></li>
			<li class="{:$Think.get.use === 'kuaidi' ? 'active' : ''}"><a href="#kuaidi" data-toggle="tab">快递100推送</a></li>
		</ul>
	</div>
	<div class="panel-body">
		<div id="myTabContent" class="tab-content">
			<div class="tab-pane fade {:$Think.get.use === null ? 'active in' : ''}" id="system">
				<div class="widget-body no-padding">
					<form id="ini-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>新店铺审核</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[config][store_audit]',
												$wanlshop.config.store_audit, ['color'=>'success', 'yes'=>'Y',
												'no'=>'N'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="关闭此功能后,商家申请店铺无需后台审核直接开通"> <i class="fa fa-info-circle"></i> 此功能有什么作用</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>第三方支付退款原路返回</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[config][refund_switch]',
												$wanlshop.config.refund_switch, ['color'=>'success', 'yes'=>'Y',
												'no'=>'N'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="此功能会直接调用第三方支付退款API接口，会直接从第三方退款用户，退款后不可逆，不建议开启此功能，建议退款至余额！！"> <i class="fa fa-info-circle"></i> 开启注意事项（不建议开启）</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>帮助中心类目CID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][help_category]',
												$wanlshop.config.help_category,
												['data-rule'=>'required','data-tip'=>'请填写帮助中心类目ID','placeholder'=>'类目ID，客户端读取帮助中心类目列表'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="CID为后台【内容管理】-【分类管理】类目的ID"> 
														<i class="fa fa-info-circle"></i> CID要如何获取？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>头条新闻类目CID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][new_category]', $wanlshop.config.new_category,
												['data-rule'=>'required','data-tip'=>'请填写头条新闻类目ID','placeholder'=>'类目ID，客户端读取头条新闻类目列表'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>系统消息类目CID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][sys_category]', $wanlshop.config.sys_category,
												['data-rule'=>'required','data-tip'=>'请填写系统消息类目ID','placeholder'=>'类目ID，客户端读取系统消息类目列表'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>用户协议文章ID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][user_agreement]',
												$wanlshop.config.user_agreement,
												['data-rule'=>'required','data-tip'=>'请填写用户协议文章ID','placeholder'=>'用户协议的文章ID'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="CID为后台【内容管理】-【文章列表】文章的ID"> 
														<i class="fa fa-info-circle"></i> 文章ID要如何获取？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>隐私保护文章ID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][privacy_protection]',
												$wanlshop.config.privacy_protection,
												['data-rule'=>'required','data-tip'=>'请填写隐私保护文章ID','placeholder'=>'隐私保护文章ID'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								
								<tr>
									<td>客服头像</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::image('row[config][service_logo]', $wanlshop.config.service_logo,
												['data-rule'=>'required','data-tip'=>'请上传客服头像','placeholder'=>'请上传客服头像，建议尺寸300x300像素'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								
								<tr>
									<td>客服电话</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][tel_phone]', $wanlshop.config.tel_phone,
												['data-rule'=>'required','data-tip'=>'请填写客服电话','placeholder'=>'电话、手机号'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>工作时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][working_hours]',
												$wanlshop.config.working_hours,
												['data-rule'=>'required','data-tip'=>'请填写工作时间','placeholder'=>'09:00~22:00'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<!-- 1.1.2升级 -->
								<tr>
									<td>商家后台文档地址</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][shop_document]',
												$wanlshop.config.shop_document,
												['data-tip'=>'商家后台文档地址','placeholder'=>'https://doc.example.com'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>商家后台QQ群</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][shop_qun]',
												$wanlshop.config.shop_qun,
												['data-tip'=>'商家后台QQ交流群','placeholder'=>'http://shang.qq.com/wpa/qunwpa?idkey=7a1**********a4'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>小程序ID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][mp_weixin_id]', $wanlshop.config.mp_weixin_id,
												['data-rule'=>'required','data-tip'=>'请填写小程序推广ID','placeholder'=>'小程序ID'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'captcha' ? 'active in' : ''}" id="captcha">
				<div class="widget-body no-padding">
					{if condition="$Think.get.use eq 'captcha'"}
					<a href="../../wanlshop/captcha" class="btn btn-warning " style="margin: 7px 0;"  title="返回人机验证"><i class="fa fa-angle-double-left"></i> 返回</a>
					{/if}
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>是否开启人机验证</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[captcha][captcha_switch]', $wanlshop.captcha.captcha_switch,
												['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
												<div style="margin-top:5px;"><a href="javascript:" data-toggle="tooltip" data-title="开启务必保障node正常运行，此功能会用于客户端关键位置人机识别,避免机器人自动操作,如：短信发送页面人机验证"> <i class="fa fa-info-circle"></i> 人机验证使用场景？</a> 
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>生成验证码图像服务端</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::selectpicker('row[captcha][captchaService]', ['php'=>'PHP服务端生成验证图片', 'node'=>'Node服务端生成验证图片'], $wanlshop.captcha.captchaService, ['data-rule'=>'required'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="选择php服务端无需任何配置,缺点消耗内存多裁剪图像过大,优点无需配置选择即可以使用; 选择node服务端,需要按文档配置node,缺点安装繁琐并且还要服务器开启shell_exec函数,优点消耗内存极小,裁切图片小基本原图"> <i class="fa fa-info-circle"></i> php服务端和node服务端有什么区别？</a> 
													<a href="https://doc.fastadmin.net/wanlshop/2479.html" target="_blank"><i class="fa fa-question-circle"></i> 如何配置node服务端?</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>服务器Node路径</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][nodePath]', $wanlshop.captcha.nodePath, ['data-rule'=>'required','data-tip'=>'请填写NodeJs路径','placeholder'=>'3'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证图像生成大小 (px)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][canvasSize]', $wanlshop.captcha.canvasSize, ['data-rule'=>'required','data-tip'=>'请填写默认正方形验证码像素大小默认480px','placeholder'=>'480'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证密码盐Key</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][seKey]', $wanlshop.captcha.seKey, ['data-rule'=>'required','data-tip'=>'请填写验证密码盐','placeholder'=>'3'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>拖动角度允许误差阈值 (度)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][errorAccuracy]', $wanlshop.captcha.errorAccuracy, ['data-rule'=>'required','data-tip'=>'请填写左右误差度数允许值，默认10度','placeholder'=>'10'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="此数值为度数，在旋转过程中相差度数则可以通过验证，降低验证难度" 
													data-original-title="" title=""> <i class="fa fa-info-circle"></i> 如何配置左右误差度数允许值？</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>效验成功免效验次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][captchaUseMaxNum]', $wanlshop.captcha.captchaUseMaxNum, ['data-rule'=>'required','data-tip'=>'请填写验证码效验完，允许使用的最大次数，默认3次','placeholder'=>'3'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证图像随机干扰点数 </td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][randomPoint]', $wanlshop.captcha.randomPoint, ['data-rule'=>'required','data-tip'=>'请填写随机干扰点数量 ，默认200个','placeholder'=>'200'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证图像随机干扰线数 </td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][randomLine]', $wanlshop.captcha.randomLine, ['data-rule'=>'required','data-tip'=>'请填写随机干扰线数量，默认50个','placeholder'=>'50'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证图像随机干扰矩块数 </td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][randomBlock]', $wanlshop.captcha.randomBlock, ['data-rule'=>'required','data-tip'=>'请填写随机干扰矩块数量，默认3个','placeholder'=>'3'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>人机验证限制完成时间(秒)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][captchaUseMaxTime]', $wanlshop.captcha.captchaUseMaxTime, ['data-rule'=>'required','data-tip'=>'请填写验证码效验完，有效期(秒)，默认3600秒','placeholder'=>'3600'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>验证图像有效期</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][checkTimeOut]', $wanlshop.captcha.checkTimeOut, ['data-rule'=>'required','data-tip'=>'请填写验证码有效期，默认600ms','placeholder'=>'600'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="验证码有效期是指用户在客户端弹出人机效验界面后可操作时间" 
													data-original-title="" title=""> <i class="fa fa-info-circle"></i> 什么是验证码有效期？</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>鼠标轨迹间隔时间(ms)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][dragInterval]', $wanlshop.captcha.dragInterval, ['data-rule'=>'required','data-tip'=>'请填写鼠标轨迹间隔时间(ms)，默认200ms','placeholder'=>'200'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>拖拽至少用时(ms)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][dragTimeMin]', $wanlshop.captcha.dragTimeMin, ['data-rule'=>'required','data-tip'=>'请填写拖拽至少用时(ms)，默认500ms','placeholder'=>'500'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>拖拽最多用时(ms)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][dragTimeMax]', $wanlshop.captcha.dragTimeMax, ['data-rule'=>'required','data-tip'=>'请填写拖拽最多用时(ms)，默认10000ms','placeholder'=>'10000'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>每次验证最多失误次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][oneCapErrNum]', $wanlshop.captcha.oneCapErrNum, ['data-rule'=>'required','data-tip'=>'请填写每个验证码最多错误几次失效，默认3次','placeholder'=>'3'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>一天允许生成多少次验证码</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][ipDayAll]', $wanlshop.captcha.ipDayAll, ['data-rule'=>'required','data-tip'=>'请填写单IP一天允许生成多少次验证码，默认300次','placeholder'=>'300'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>一天允许验证失败次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][ipDayError]', $wanlshop.captcha.ipDayError, ['data-rule'=>'required','data-tip'=>'请填写单IP一天允许验证失败次数，默认100次','placeholder'=>'100'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>1小时允许生成多少次验证码</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][ipHourAll]', $wanlshop.captcha.ipHourAll, ['data-rule'=>'required','data-tip'=>'请填写单IP一小时内允许生成多少张验证码，默认100张','placeholder'=>'100'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>1小时内允许失败次数 </td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[captcha][ipHourError]', $wanlshop.captcha.ipHourError, ['data-rule'=>'required','data-tip'=>'请填写单IP一小时内允许出错多少次，默认30次','placeholder'=>'30'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'redis' ? 'active in' : ''}" id="redis">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>Redis主机地址</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[redis][host]', $wanlshop.redis.host,
												['data-rule'=>'required','data-tip'=>'请填写Redis主机地址，在服务器安装一般填写127.0.0.1','placeholder'=>'127.0.0.1'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/997.html" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置Redis主机地址?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>Redis服务端口</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[redis][port]', $wanlshop.redis.port,
												['data-rule'=>'required','data-tip'=>'请填写Redis服务端口，在服务器安装一般填写6379','placeholder'=>'6379'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/997.html" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置Redis服务端口?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>Redis密码 (一般为空)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[redis][password]', $wanlshop.redis.password,
												['data-tip'=>'通常Redis没有密码，如果是第三方Redis请查看第三方Redis密码填写此处'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>操作库</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[redis][select]', $wanlshop.redis.select,
												['data-rule'=>'required','data-tip'=>'请填写Redis操作库','placeholder'=>'0'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>超时时间(秒)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[redis][timeout]', $wanlshop.redis.timeout,
												['data-rule'=>'required','data-tip'=>'请填写Redis超时时间(秒)，设置为0则默认超时时间','placeholder'=>'0'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>是否长连接</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[redis][persistent]', $wanlshop.redis.persistent,
												['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			
			
			
			
			<div class="tab-pane fade {:$Think.get.use === 'redis' ? 'active in' : ''}" id="poster">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>生成海报尺寸</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][poster_width]',
												$wanlshop.config.poster_width,
												['data-rule'=>'required','data-tip'=>'生成海报尺寸rpx','placeholder'=>'请填写生成海报宽度'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>生成海报背景颜色</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="form-inline">
													<input name="row[config][poster_background]" class="form-control" type="text" v-model="row.config.poster_background">
													<input class="form-control wanl-color" type="color" v-model="row.config.poster_background">
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							    <tr>
									<td>默认分享图</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::image('row[config][poster_image]', $wanlshop.config.poster_image,
												['data-rule'=>'required','data-tip'=>'请上传默认分享图','placeholder'=>'请上传默认分享图，建议尺寸500x650像素，图片大小不超过100Kb'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/296.html#toc-22" target="_blank">
														<i class="fa fa-question-circle"></i> 海报无法生成，存在跨域？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>分享链接H5域名</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[h5][domain]',
												$wanlshop.h5.domain,
												['data-rule'=>'required','data-tip'=>'请填写分享链接H5域名','placeholder'=>'海报分享链接H5域名'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>二维码展示</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												 {:Form::radios('row[config][poster_qrcode]', [0=>'全部', 1=>'微信小程序码', 2=>'普通二维码'], $wanlshop.config.poster_qrcode, ['data-rule'=>'required'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="1.仅App、微信小程序、H5客户端生效其他仅显示普通二维码 2.小程序尚未正确配置请勿勾选小程序，否则会生成海报失败 3.普通二维码生成的为H5 url路径,请在后台UNIAPP客户端管理配置正确H5地址"> 
														<i class="fa fa-info-circle"></i> 注意！ 
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>要打开的小程序版本</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												 {:Form::select('row[config][poster_env]', ['release'=>'正式版', 'trial'=>'体验版', 'develop'=>'开发版'], $wanlshop.config.poster_env, ['data-rule'=>'required'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>分享默认标题</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][poster_title]',
												$wanlshop.config.poster_title,
												['data-rule'=>'required','data-tip'=>'请填写分享标题','placeholder'=>'海报分享标题'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>标题文字颜色</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="form-inline">
													<input name="row[config][poster_title_color]" class="form-control" type="text" v-model="row.config.poster_title_color">
													<input class="form-control wanl-color" type="color" v-model="row.config.poster_title_color">
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>分享默认详情</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][poster_details]',
												$wanlshop.config.poster_details,
												['data-rule'=>'required','data-tip'=>'请填写分享默认详情','placeholder'=>'海报分享默认详情'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>价格文字颜色</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="form-inline">
													<input name="row[config][poster_price_color]" class="form-control" type="text" v-model="row.config.poster_price_color">
													<input class="form-control wanl-color" type="color" v-model="row.config.poster_price_color">
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>默认推荐人</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][poster_user]',
												$wanlshop.config.poster_user,
												['data-rule'=>'required','data-tip'=>'请填写默认推荐人','placeholder'=>'海报左下角默认推荐人'])}
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="如果用户未登录情况下分享出来的均为默认推荐人"> 
														<i class="fa fa-info-circle"></i> 如何配置 ？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>默认推荐描述</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][poster_viewDetails]',
												$wanlshop.config.poster_viewDetails,
												['data-rule'=>'required','data-tip'=>'请填写默认推荐描述','placeholder'=>'海报左下角默认推荐描述'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>默认推荐描述颜色</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="form-inline">
													<input name="row[config][poster_original_color]" class="form-control" type="text" v-model="row.config.poster_original_color">
													<input class="form-control wanl-color" type="color" v-model="row.config.poster_original_color">
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			
			
			
			
			
			
			
			
			
			
			
			
			<div class="tab-pane fade {:$Think.get.use === 'withdraw' ? 'active in' : ''}" id="withdraw">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>是否开启提现</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[withdraw][state]', $wanlshop.withdraw.state,
												['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>最低提现金额</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][minmoney]', $wanlshop.withdraw.minmoney,
												['data-rule'=>'required','data-tip'=>'设置0则不限制提现金额','placeholder'=>'设置0则不限制提现金额'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>提现金额倍数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][multiple]', $wanlshop.withdraw.multiple,
												['data-rule'=>'required','data-tip'=>'设置0则不限制提现金额倍数','placeholder'=>'设置0则不限制提现金额倍数'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>每日可提现次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][daylimit]', $wanlshop.withdraw.daylimit,
												['data-rule'=>'required','data-tip'=>'每日可提现次数，设置0不限提现次数','placeholder'=>'每日可提现次数'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>每周可提现次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][weeklimit]', $wanlshop.withdraw.weeklimit,
												['data-rule'=>'required','data-tip'=>'每周可提现次数，设置0不限提现次数','placeholder'=>'每周可提现次数'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>每月可提现次数</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][monthlimit]', $wanlshop.withdraw.monthlimit,
												['data-rule'=>'required','data-tip'=>'每月可提现次数，设置0不限提现次数','placeholder'=>'每月可提现次数'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>提现手续费(‰)</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[withdraw][servicefee]', $wanlshop.withdraw.servicefee,
												['data-rule'=>'required','data-tip'=>'每笔提现扣除手续费千分之几？','placeholder'=>'手续费‰'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'order' ? 'active in' : ''}" id="order">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数（天）</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>取消未支付时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][cancel]', $wanlshop.order.cancel,
												['data-rule'=>'required','data-tip'=>'几天后取消未支付订单','placeholder'=>'订单下单未付款，n天后自动关闭，设置0天不自动关闭'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>自动收货时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][receiving]', $wanlshop.order.receiving,
												['data-rule'=>'required','data-tip'=>'订单提交后几天自动收货','placeholder'=>'如果在期间未确认收货，系统自动完成收货，设置0天不自动收货'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>自动评论时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][comment]', $wanlshop.order.comment,
												['data-rule'=>'required','data-tip'=>'收货后几天自动评论','placeholder'=>'如果在期间未自动评论，系统自动完成评论，设置0天不自动评论'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>最后售后时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][customer]', $wanlshop.order.customer,
												['data-rule'=>'required','data-tip'=>'几天内支持售后','placeholder'=>'订单完成后
												，用户在n天内可以发起售后申请，设置0天不允许申请售后'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'return' ? 'active in' : ''}" id="return">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数（天）</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>卖家自动同意时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][autoagree]', $wanlshop.order.autoagree,
												['data-rule'=>'required','data-tip'=>'自动同意售后时间','placeholder'=>'买家提交退款后商家处理时间，超出n天后自动同意，设置0天不自动关闭'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>买家退货时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][returntime]', $wanlshop.order.returntime,
												['data-rule'=>'required','data-tip'=>'卖家同意售后，退货时间','placeholder'=>'退货时间，如果超过则关闭售后'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>卖家收货时间</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[order][receivingtime]', $wanlshop.order.receivingtime,
												['data-rule'=>'required','data-tip'=>'买家退回宝贝，自动收货时间','placeholder'=>'买家退货后，超出指定天数后自动完成售后'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'find' ? 'active in' : ''}" id="find">
				<div class="widget-body no-padding">
					<form id="ini-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>发现启用社交评论</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[config][comment_switch]', $wanlshop.config.comment_switch, ['color'=>'success', 'yes'=>'Y', 'no'=>'N'])} 
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>APP功能开关</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::checkboxs('row[find][app_switch]', ['new'=>'上新',
												'live'=>'直播', 'video'=>'短视频（取消则关闭此功能）', 'want'=>'种草', 'show'=>'买家秀'], $wanlshop.find.app_switch)}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>小程序功能开关</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::checkboxs('row[find][mp_switch]', ['new'=>'上新',
												'live'=>'直播', 'video'=>'短视频（取消则关闭此功能）', 'want'=>'种草', 'show'=>'买家秀'], $wanlshop.find.mp_switch)}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>H5功能开关</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::checkboxs('row[find][h5_switch]', ['new'=>'上新',
												'live'=>'直播', 'video'=>'短视频（取消则关闭此功能）', 'want'=>'种草', 'show'=>'买家秀'], $wanlshop.find.h5_switch)}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>微信内置浏览器</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::checkboxs('row[find][wechat_switch]', ['new'=>'上新',
												'live'=>'直播', 'video'=>'短视频（取消则关闭此功能）', 'want'=>'种草', 'show'=>'买家秀'], $wanlshop.find.wechat_switch)}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>个人发布需审核</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[find][personalExamine_switch]', $wanlshop.find.personalExamine_switch, ['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>店铺个人发布均审核</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[find][allExamine_switch]', $wanlshop.find.allExamine_switch, ['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>

							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'live' ? 'active in' : ''}" id="live">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped" id="app">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>直播AppName</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][appName]', $wanlshop.live.appName,
												['data-rule'=>'required','data-tip'=>'具体请看文档','placeholder'=>'例如：wanlshop'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/365.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置直播AppName?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>域名是否已配置SSL</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::switcher('row[live][sslSwitch]', $wanlshop.live.sslSwitch,
												['color'=>'success', 'yes'=>'Y', 'no'=>'N'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>直播推流域名</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][pushDomain]', $wanlshop.live.pushDomain,
												['data-rule'=>'required','data-tip'=>'推流流域名','placeholder'=>'例如：rtmp.example.com，不要添加http和/'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/365.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置直播推流域名?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>直播播放域名</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][liveDomain]', $wanlshop.live.liveDomain,
												['data-rule'=>'required','data-tip'=>'播流域名','placeholder'=>'例如：live.example.com，不要添加http和/'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/365.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置直播播放域名?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<!-- 1.1.2升级 -->
								<tr>
									<td>直播录制OSS域名</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][liveCnd]', $wanlshop.live.liveCnd,
												['data-rule'=>'required','data-tip'=>'请在阿里云OSS配置或使用OSS提供域名','placeholder'=>'例如：play.example.com'])}
												<div style="margin-top:5px;">
													<a href="https://help.aliyun.com/document_detail/84932.htm" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置直播录制OSS域名?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<!-- 1.1.2升级 -->
								<tr>
									<td>直播是否鉴权</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<select name="row[live][authSwitch]" data-rule="required" id="authSwitch" v-model="authSwitch" class="form-control">
													<option v-for="(item, index) in authList" :key="index" :value="index" v-text="item" :selected="authSwitch === index"></option>
												</select>
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="无法直播时请检查IM和尝试关闭鉴权"> 
														<i class="fa fa-info-circle"></i> 无法直播？鉴权不生效？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr v-show="authSwitch == 'Y'">
									<td>播放域名鉴权KEY</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][liveKey]', $wanlshop.live.liveKey,
												['data-rule'=>'required','data-tip'=>'请在阿里云直播后台获取','placeholder'=>'例如：323We1sdf1'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr v-show="authSwitch == 'Y'">
									<td>推流域名鉴权KEY</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][pushKey]', $wanlshop.live.pushKey,
												['data-rule'=>'required','data-tip'=>'请在阿里云直播后台获取','placeholder'=>'例如：323We1sdf1'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr v-show="authSwitch == 'Y'">
									<td>鉴权有效分钟</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][builderTime]', $wanlshop.live.builderTime,
												['data-rule'=>'required','data-tip'=>'请输入分钟数，推荐60分钟最多360分钟','placeholder'=>'例如：60'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<!-- 1.1.2升级 -->
								<tr>
									<td>是否启用转码模板</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<select name="row[live][transTemplateSwitch]" data-rule="required" id="transTemplateSwitch" v-model="transTemplateSwitch" class="form-control">
													<option v-for="(item, index) in transTemplateList" :key="index" :value="index" v-text="item" :selected="transTemplateSwitch === index"></option>
												</select>
												<div style="margin-top:5px;">
													<a href="javascript:" data-toggle="tooltip" data-title="开启转码模板后，则压缩播放节省流量"> 
														<i class="fa fa-info-circle"></i> 转码模板是什么？
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr v-show="transTemplateSwitch == 'Y'">
									<td>转码模板</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[live][transTemplate]', $wanlshop.live.transTemplate,
												['data-rule'=>'required','data-tip'=>'请填写模板英文名','placeholder'=>'例如：lld，为选择标清播放'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/365.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置转码模板?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>直播推流回调地址</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="input-group url">
													<input class="form-control" type="text"
														value="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/push"
														disabled>
													<span class="input-group-btn"> <span class="btn btn-info btn-copy"
															data-clipboard-text="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/push">
															<i class="fa fa-clipboard"></i></span> </span>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>直播录制OSS回调地址</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="input-group url">
													<input class="form-control" type="text"
														value="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/record"
														disabled>
													<span class="input-group-btn"> <span class="btn btn-info btn-copy"
															data-clipboard-text="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/record">
															<i class="fa fa-clipboard"></i></span> </span>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'video' ? 'active in' : ''}" id="video">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>访问控制RAM AccessKeyId</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[video][accessKeyId]', $wanlshop.video.accessKeyId,
												['data-rule'=>'required','data-tip'=>'同OSS AccessKeyId ![但需阿里云RAM访问用户组中添加VOB相关权限]','placeholder'=>'例:EIo37qBnBu7z8esyibrVZ4ds'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/309.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置访问控制RAM AccessKeyId?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>访问控制RAM AccessKeySecret</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[video][accessKeySecret]', $wanlshop.video.accessKeySecret,
												['data-rule'=>'required','data-tip'=>'同OSS AccessKeySecret ![但需阿里云RAM访问用户组中添加VOB相关权限]','placeholder'=>'例:vBN6Iq0GEzhRB4hRDVhtQlkqLp1g50'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/309.html#toc-3" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置访问控制RAM AccessKeySecret?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								
								<!-- 1.1.3升级 -->
								<tr>
									<td>阿里云点播地域</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::selectpicker('row[video][regionId]', ['cn-shanghai'=>'上海地域', 'cn-beijing'=>'北京地域'], $wanlshop.video.regionId, ['data-rule'=>'required'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/309.html#toc-7" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置阿里云点播地域，为什么没有其他地域?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								
								<tr>
									<td>媒体处理工作流ID</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[video][workflowId]', $wanlshop.video.workflowId,
												['data-rule'=>'required','data-tip'=>'阿里点播控制台 / 配置管理 / 媒体处理配置 -> 工作流','placeholder'=>'例:504co48o2nkpnc6rkod5krvc0hfcuw1s'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/309.html#toc-7" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置媒体处理工作流ID?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>媒体处理回调 Url</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="input-group url">
													<input class="form-control" type="text"
														value="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/vod"
														disabled>
													<span class="input-group-btn"> <span class="btn btn-info btn-copy"
															data-clipboard-text="{$wanlshop.ini.appurl|htmlentities}/wanlshop/callback/vod">
															<i class="fa fa-clipboard"></i></span> </span>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>媒体处理回调 鉴权秘钥</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[video][privateKey]', $wanlshop.video.privateKey,
												['data-rule'=>'required','data-tip'=>'阿里点播控制台 / 配置管理 / 媒体处理配置 -> 回调设置','placeholder'=>'例:HLmMJw1I8Gm0rbGEBdNepV09RgmOZkQo'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/309.html#toc-7" target="_blank">
														<i class="fa fa-question-circle"></i> 如何配置媒体处理回调 鉴权秘钥?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'im' ? 'active in' : ''}" id="im">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>客服初始消息</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][auth_reply]', $wanlshop.config.auth_reply,
												['data-rule'=>'required','data-tip'=>'请填写客服初始消息','placeholder'=>'例如：欢迎使用在线客服'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>人工未在线</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][not_online]', $wanlshop.config.not_online,
												['data-rule'=>'required','data-tip'=>'请填写人工未在线','placeholder'=>'例如：[汗]
												非工作时间8:00-22:00 或客服繁忙！请稍后再试~'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>客服首消息</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[config][service_initial]',
												$wanlshop.config.service_initial,
												['data-rule'=>'required','data-tip'=>'请填写客服首消息','placeholder'=>'例如：您好 [微笑] 请用一句话简短描述问题~'])}
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
			<div class="tab-pane fade {:$Think.get.use === 'kuaidi' ? 'active in' : ''}" id="kuaidi">
				<div class="widget-body no-padding">
					<form id="chat-form" class="edit-form form-horizontal" role="form" data-toggle="validator"
						method="POST" action="{:url('wanlshop.client/edit')}">
						{:token()}
						<table class="table table-striped">
							<thead>
								<tr>
									<th width="15%">配置项</th>
									<th width="85%">参数</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>快递100 Key</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												{:Form::text('row[kuaidi][secretKey]', $wanlshop.kuaidi.secretKey, ['data-tip'=>'请填写快递100Key','placeholder'=>'请填写快递100Key'])}
												<div style="margin-top:5px;">
													<a href="https://doc.fastadmin.net/wanlshop/611.html" target="_blank">
														<i class="fa fa-question-circle"></i> 如何申请快递100Key?
													</a>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
								<tr>
									<td>回调地址</td>
									<td>
										<div class="row">
											<div class="col-sm-8 col-xs-12">
												<div class="input-group url">
												<span
													class="input-group-addon">{$wanlshop.ini.appurl|htmlentities}</span>
												<input data-rule="required" class="form-control"
													name="row[kuaidi][callbackUrl]" data-tip="未二次开发不建议修改默认回调"
													type="text"
													value="{$wanlshop.kuaidi.callbackUrl|htmlentities}">
												<span class="input-group-btn"> <span class="btn btn-info btn-copy"
														data-clipboard-text="{$wanlshop.ini.appurl|htmlentities}{$wanlshop.kuaidi.callbackUrl|htmlentities}">
														<i class="fa fa-clipboard"></i></span> </span>
												</div>
											</div>
											<div class="col-sm-4"></div>
										</div>
									</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<th></th>
									<th>
										<button type="submit" class="btn btn-success btn-embossed">确定</button>
										<button type="reset" class="btn btn-default btn-embossed">重置</button>
									</th>
								</tr>
							</tfoot>
						</table>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<style type="text/css">
	[v-cloak] {display: none;}
	@media (max-width: 375px) {
		.edit-form tr td input {
			width: 100%;
		}

		.edit-form tr th:first-child,
		.edit-form tr td:first-child {
			width: 20%;
		}

		.edit-form tr th:nth-last-of-type(-n+2),
		.edit-form tr td:nth-last-of-type(-n+2) {
			display: none;
		}
	}

	.edit-form table>tbody>tr td a.btn-delcfg {
		visibility: hidden;
	}

	.edit-form table>tbody>tr:hover td a.btn-delcfg {
		visibility: visible;
	}
	.input-group.url .input-group-addon {
		background-color: #e8edf0;
		color: #868686;
		border-color: #d2d6de;
	}
	
	.wanl-color {
		-webkit-appearance: square-button;
		width: 29px !important;
		height: 31px;
		background-color: #fff;
		cursor: default;
		border-width: 1px;
		border-style: solid;
		border-color: #d2d6de;
		border-image: initial;
		padding: 0;
	}
</style>