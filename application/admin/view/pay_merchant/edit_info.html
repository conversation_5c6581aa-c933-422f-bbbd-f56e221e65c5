<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div id="app">
        <input id="c-channel" class="form-control hidden" name="row[channel]" type="text" value="{$row['channel']|htmlentities}">
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">商户类型:</label>
            <div class="col-xs-12 col-sm-8">
                <select id="c-merType" data-rule="required" class="form-control selectpicker" v-model="params.merType" name="row[merType]">
                    <option value="TP_PERSONAL" {neq name="row['body']['merType']" value="TP_PERSONAL"} disabled {/neq}>小微个人</option>
                    <option value="TP_MERCHANT" {neq name="row['body']['merType']" value="TP_MERCHANT"} disabled {/neq}>企业</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">商户名称:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-merRegName" {neq name="row['body']['merType']" value="TP_MERCHANT"} readonly {/neq} data-rule="required" class="form-control" name="row[merRegName]" type="text" placeholder="请输入商户名称" value="{$row.body['licenseName'] ? $row.body['licenseName'] : $row.body['merRegName']}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">商户简称:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-merName" {neq name="row['body']['merType']" value="TP_MERCHANT"} readonly {/neq} data-rule="required" minlength="4"  maxlength="20" class="form-control" name="row[merName]" type="text" placeholder="请输入商户简称" value="{$row.body['merName']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">法人姓名:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-larName" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" name="row[larName]" type="text" placeholder="请输入法人姓名" value="{$row.body['larName']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">法人手机号:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-contactMobile" {neq name="edit" value="1"} readonly {/neq} data-rule="required;mobile"  class="form-control" maxlength="11" name="row[contactMobile]" type="text" placeholder="请输入法人手机号" value="{$row.body['contactMobile']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">负责人邮箱:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-email" {neq name="edit" value="1"} readonly {/neq} data-rule="required;email"  class="form-control" name="row[email]" type="text" placeholder="请输入负责人邮箱" value="{$row.body['email']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">负责人身份证号:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-larIdCard" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" maxlength="18" name="row[larIdCard]" type="text" placeholder="请输入负责人身份证号" value="{$row.body['larIdCard']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">身份证有效期（始）:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-larIdCardStart" {neq name="edit" value="1"} readonly {/neq} data-rule="required" maxlength="10" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" name="row[larIdCardStart]" type="text" placeholder="请输入身份证有效期（始），如2024-01-30" value="{$row.body['larIdCardStart']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">身份证有效期（止）:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-larIdCardEnd" {neq name="edit" value="1"} readonly {/neq} data-rule="required" maxlength="10" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" v-model="params.larIdCardEnd" name="row[larIdCardEnd]" type="text" placeholder="请输入身份证有效期（止），如2024-01-30" value="{$row.body['larIdCardEnd']|htmlentities}">
                <span class="btn btn-success" @click="params.larIdCardEnd = '9999-12-31'">长期有效</span>
            </div>
        </div>
        <!-- <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">省市区/县:</label>
            <div class="col-xs-12 col-sm-8">
                <div class='control-relative'><input id="c-city" class="form-control" data-rule="required" data-toggle="city-picker" name="row[address]" type="text"  placeholder="请选择省市区/县"></div>
            </div>
        </div> -->
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">省市区/县:</label>
            <div class="col-sm-3">
                <input id="c-province" {neq name="edit" value="1"} disabled {/neq} data-rule="required" placeholder="请选择" data-source="{}" data-page-size="10" data-primary-key="code" data-field="name" class="form-control selectpage" name="row[provinceCode]" type="text" value="{$row.body['provinceCode']|htmlentities}">
            </div>
            <div class="col-sm-3">
                <input id="c-city" {neq name="edit" value="1"} disabled {/neq} data-rule="required" placeholder="请选择" data-source='[{"code":{$row.body["cityCode"]},"name":"{$row.body["cityName"]}"}]' data-primary-key="code" data-page-size="10" data-field="name" class="form-control selectpage" name="row[cityCode]" type="text" value="{$row.body['cityCode']|htmlentities}">
            </div>
            <div class="col-sm-3">
                <input id="c-county" {neq name="edit" value="1"} disabled {/neq} data-rule="required" placeholder="请选择" data-source='[{"code":{$row.body["countyCode"]},"name":"{$row.body["countyName"]}"}]' data-primary-key="code" 	data-page-size="10" data-field="name" class="form-control selectpage" name="row[countyCode]" type="text" value="{$row.body['countyCode']|htmlentities}">
            </div>
            <input id="c-provinceName" style="display: none;" name="row[provinceName]" type="text" value="{$row.body['provinceName']|htmlentities}">
            <input id="c-cityName" style="display: none;" name="row[cityName]" type="text" value="{$row.body['cityName']|htmlentities}">
            <input id="c-countyName" style="display: none;" name="row[countyName]" type="text" value="{$row.body['countyName']|htmlentities}">
        </div>
        
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">详细地址:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-merAddr" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" name="row[merAddr]" type="text" placeholder="请输入详细地址" value="{$row.body['merAddr']|htmlentities}">
            </div>
        </div>
        <span data-favisible="merType=TP_MERCHANT">
            <div class="form-group" >
                <label class="control-label col-xs-12 col-sm-2">营业执照注册号:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-licenseNo" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" name="row[licenseNo]" type="text" placeholder="请输入营业执照注册号" value="{$row.body['licenseNo']|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">执照有效期（始）:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-licenseDtStart" {neq name="edit" value="1"} readonly {/neq} data-rule="required" maxlength="10"  class="form-control datetimepicker" data-date-format="YYYY-MM-DD" name="row[licenseDtStart]" type="text" placeholder="请输入执照有效期（始），如2024-01-30" value="{$row.body['licenseDtStart']|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">执照有效期（止）:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-licenseDtEnd" {neq name="edit" value="1"} readonly {/neq} data-rule="required" maxlength="10"  class="form-control datetimepicker" data-date-format="YYYY-MM-DD" v-model="params.licenseDtEnd" name="row[licenseDtEnd]" type="text" placeholder="请输入执照有效期（止），如2024-01-30" value="{$row.body['licenseDtEnd']|htmlentities}">
                    <span class="btn btn-success" @click="params.licenseDtEnd = '9999-12-31'">长期有效</span>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">营业执照经营范围:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-businessContent" {neq name="edit" value="1"} readonly {/neq} maxlength="20" data-rule="required"  class="form-control" name="row[businessContent]" type="text" placeholder="请输入营业执照经营范围" value="{$row.body['businessContent']|htmlentities}">
                </div>
            </div>
        </span>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">行业MCC:</label>
            <div class="col-xs-12 col-sm-4">
                <input id="c-category-1" {neq name="edit" value="1"} disabled {/neq} placeholder="请选择" data-rule="required" data-source="pay_merchant/getCategory" 	data-page-size="10" data-primary-key="code" data-field="name" class="form-control selectpage" name="row[mcc1]" type="text" value="{$row.body['mcc1']|htmlentities}">
            </div>
            <div class="col-xs-12 col-sm-4">
                <input id="c-category-2" {neq name="edit" value="1"} disabled {/neq} placeholder="请选择" data-rule="required" data-source='[{"code":"{$row.body["mcc2"]}","name":"{$row.body["mcc2"]}"}]' data-primary-key="code" data-page-size="10" data-field="name" class="form-control selectpage" name="row[mcc2]" type="text" value="{$row.body['mcc2']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <h3 style="text-align: center;">— 结算信息 —</h3>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">结算账户类型:</label>
            <div class="col-xs-12 col-sm-8">
                <select  id="c-accountType" {neq name="edit" value="1"} disabled {/neq} data-rule="required" class="form-control selectpicker" v-model="params.accountType" name="row[accountType]">
                    <option value="57">对公</option>
                    <option value="58">对私</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">结算账户名称:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-accountName" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" name="row[accountName]" type="text" placeholder="请输入结算账户名称" value="{$row.body['accountName']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">结算卡号:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-accountNo" {neq name="edit" value="1"} readonly {/neq} data-rule="required"  class="form-control" name="row[accountNo]" type="text" placeholder="请输入结算卡号" value="{$row.body['accountNo']|htmlentities}">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">结算卡归属:</label>
            <div class="col-xs-12 col-sm-4">
                <input id="c-settleProvince" {neq name="edit" value="1"} disabled {/neq} placeholder="请选择" data-rule="required" data-source="pay_merchant/getArea?isBank=1" 	data-page-size="10" data-primary-key="code" data-field="name" class="form-control selectpage" name="row[settleProvinceCode]" type="text" value="{$row.body['settleProvinceCode']|htmlentities}">
            </div>
            <div class="col-xs-12 col-sm-4">
                <input id="c-settleCity" {neq name="edit" value="1"} disabled {/neq} placeholder="请选择" data-rule="required" data-source='[{"code":{$row.body["settleCityCode"]},"name":"{$row.body["settleCityName"]}"}]' data-primary-key="code" data-page-size="10" data-field="name" class="form-control selectpage" name="row[settleCityCode]" type="text" value="{$row.body['settleCityCode']|htmlentities}">
            </div>
            <input id="c-settleProvinceName" style="display: none;" name="row[settleProvinceName]" type="text" value="{$row.body['settleProvinceName']|htmlentities}">
            <input id="c-settleCityName" style="display: none;" name="row[settleCityName]" type="text" value="{$row.body['settleCityName']|htmlentities}">
        </div>
        <!-- <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">开户名:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-p26_bank_owner" data-rule="required" class="form-control" name="row[p26_bank_owner]" type="text" placeholder="请输入开户人姓名">
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">开户人身份证号:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-p27_bank_idcard" data-rule="required" maxlength="18" class="form-control" name="row[p27_bank_idcard]" type="text" placeholder="请输入开户人身份证号">
            </div>
        </div> -->
        <div class="form-group" data-favisible="settleCityCode>0">
            <label class="control-label col-xs-12 col-sm-2">开户行:</label>
            <div class="col-xs-12 col-sm-4">
                <input id="c-branchBank" {neq name="edit" value="1"} readonly {/neq} data-rule="required" data-order-by="branchBankNo" data-field="branchBankName" data-clearingBankCode="{clearNo}" data-format-item="{branchBankName}【{clearNo}】" data-source='[{"branchBankNo":{$row.body["openningBankCode"]},"branchBankName":"{$row.body["openningBankName"]}","clearNo":"{$row.body["clearingBankCode"]}"}]'  data-primary-key="branchBankNo" class="form-control selectpage" name="row[openningBankCode]" type="text" placeholder="请选择" value="{$row.body['openningBankCode']|htmlentities}">
            </div>
            <div class="col-xs-12 col-sm-4">
                <input id="c-branchBankQuery" {neq name="edit" value="1"} readonly {/neq} type="text" class="form-control" placeholder="没有找到可以在这输入看看">
            </div>
            <input id="c-openningBankName" style="display: none;" name="row[openningBankName]" type="text" value="{$row.body['openningBankName']|htmlentities}">
            <input id="c-clearingBankCode" style="display: none;" name="row[clearingBankCode]" type="text" value="{$row.body['clearingBankCode']|htmlentities}">
        </div>
        <!-- <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">预留手机号:</label>
            <div class="col-xs-12 col-sm-8">
                <input id="c-p28_bank_mobile" data-rule="required" maxlength="11"  class="form-control" name="row[p28_bank_mobile]" type="text" placeholder="请输入预留手机号">
            </div>
        </div> -->
        
        <div class="form-group">
            <h3 style="text-align: center;">— 附件资料 —</h3>
        </div>
        <div class="form-group">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">身份证正面照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-id_card" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][ID_CARD_FRONT]" type="text" value="{$row.body['images']['ID_CARD_FRONT']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-id_card" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-id_card"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-id_card" data-template="avatartpl"></ul>
            </div>
        </div>

        <div class="form-group">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">身份证反面照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-id_f_card" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][ID_CARD_BEHIND]" type="text" value="{$row.body['images']['ID_CARD_BEHIND']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-id_f_card" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-id_f_card"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-id_f_card" data-template="avatartpl"></ul>
            </div>
        </div>

        <div class="form-group">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">店铺门头照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-storefront" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][SHOP_OUTSIDE_IMG]" type="text" value="{$row.body['images']['SHOP_OUTSIDE_IMG']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-storefront" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-storefront"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-storefront" data-template="avatartpl"></ul>
            </div>
        </div>
        <div class="form-group">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">店铺内景照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-interior_1" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][SHOP_INSIDE_IMG]" type="text" value="{$row.body['images']['SHOP_INSIDE_IMG']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-interior_1" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-interior_1"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-interior_1" data-template="avatartpl"></ul>
            </div>
        </div>
        <div class="form-group">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">结算卡正面照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-bankCardImg" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][BANK_CARD]" type="text" value="{$row.body['images']['BANK_CARD']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-bankCardImg" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-bankCardImg"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-bankCardImg" data-template="avatartpl"></ul>
            </div>
        </div>
        
        <div class="form-group" data-favisible="merType=TP_MERCHANT">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">营业执照:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-license" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][BUSINESS_LICENCE]" type="text" value="{$row.body['images']['BUSINESS_LICENCE']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-license" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-license"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-license" data-template="avatartpl"></ul>
            </div>
        </div>
        <div class="form-group" data-favisible="accountType=57">
            <label for="c-image" class="control-label col-xs-12 col-sm-2">开户许可证:</label>
            <div class="col-xs-12 col-sm-8">
                <div class="input-group">
                    <input id="c-industryLicenseImg" data-rule="required" {neq name="edit" value="1"} readonly {/neq} class="form-control" name="row[images][OPENING_PERMIT]" type="text" value="{$row.body['images']['OPENING_PERMIT']|htmlentities}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="faupload-image" {neq name="edit" value="1"} disabled {/neq} class="btn btn-danger faupload" data-maxsize="3M" data-input-id="c-industryLicenseImg" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-industryLicenseImg"><i class="fa fa-upload"></i> 上传</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-industryLicenseImg" data-template="avatartpl"></ul>
            </div>
        </div>
    </div>
    {eq name="edit" value="1"}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
    {/eq}
</form>
<script type="text/html" id="avatartpl">
    <li class="col-xs-3">
        <a href="<%=url%>" data-url="<%=url%>" target="_blank" class="thumbnail">
            <img src="<%=url%>" class="img-responsive">
        </a>
        <a href="javascript:;" class="btn btn-danger btn-xs btn-trash"><i class="fa fa-trash"></i></a>
    </li>
</script>
<style>
    .sp_result_area {
        background-color: white;
        display: none;
        list-style: none;
        margin: 0;
        padding: 0;
        position: absolute;
        z-index: 100;
        width: 60%;
        border-radius: 2px;
    }    
</style>
<script>
    var merType = "<?php echo $row['body']['merType']; ?>";
    var accountType = "<?php echo $row['body']['accountType']; ?>";
    var larIdCardEnd = "<?php echo $row['body']['larIdCardEnd']; ?>";
    var licenseDtEnd = "<?php echo $row['body']['licenseDtEnd']; ?>";
    var accountIdDtEnd = "<?php echo isset($row['body']['accountIdDtEnd']) ? $row['body']['accountIdDtEnd'] : ''; ?>";
</script>