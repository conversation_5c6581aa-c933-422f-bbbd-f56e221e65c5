<style type="text/css">
    .teambody {
        border: 1px dotted grey;padding: 5px;
        position: static;
        margin-bottom: 20px;
      }
    @media (min-width: 768px) {
      .teambody {
        width: 300px;
        position: absolute;right: 50px;
      }
    }

</style>
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">
    <div class="teambody hidden">
        <div class="container" style="width: 300px;">

            <div class="col-12" style="font-size: 16px;font-weight: 800;">
                直推数据
            </div>

            <div class="row" style="margin-top: 10px;">
                <div class="col-xs-4">

                        <div>会员数</div>
                        <div>{$invites['invite_number']}</div>

                </div>
                <div class="col-xs-4">

                        <div>高级会员数</div>
                        <div>{$invites['invites_up_nums']}</div>

                </div>
                <div class="col-xs-4">

                        <div>云店数</div>
                        <div>{$invites['invites_nums']}</div>

                </div>
            </div>

            <div class="col-12" style="margin-top: 10px;font-size: 16px;font-weight: 800;">
                团队数据
            </div>

            <div class="row"  style="margin-top: 10px;">
                <div class="col-xs-4">

                        <div>团队人数</div>
                        <div>{$parent['parent_number']}</div>

                </div>
                <div class="col-xs-4">

                        <div>团队会员数</div>
                        <div>{$parent['upcon']}</div>

                </div>
                <div class="col-xs-4">

                        <div>团队云店数</div>
                        <div>{$parent['yuncon']}</div>

                </div>
            </div>
            <div class="row"  style="margin-top: 10px;">

                <div class="col-xs-3">

                        <div>星1</div>
                        <div>{$parent['xing1']}</div>

                </div>

                <div class="col-xs-3">

                        <div>星2</div>
                        <div>{$parent['xing2']}</div>

                </div>

                <div class="col-xs-3">

                        <div>星3</div>
                        <div>{$parent['xing3']}</div>

                </div>
                
                <div class="col-xs-3">

                        <div>董事</div>
                        <div>{$parent['xing4']}</div>

                </div>

            </div>

        </div>

    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">方案:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[plan]', ['A'=>'A方案', 'B'=>'B方案', 'C'=>'C方案', 'D'=>'D方案', 'E'=>'E方案', 'F'=>'F方案', 'G'=>'G方案', 'H'=>'H方案', 'Z'=>'Z方案'], $row['plan'])}
        </div>
    </div>
    <div class="form-group">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$groupList}
        </div>
    </div>
    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text"
                   value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-memo" class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-memo" class="form-control" name="row[memo]" type="text"
                   value="{$row.memo|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text"
                   value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-truename" class="control-label col-xs-12 col-sm-2">{:__('Truename')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-truename" class="form-control" name="row[truename]" type="text"
                   value="{$row.truename|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-idcard" class="control-label col-xs-12 col-sm-2">{:__('身份证号')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-idcard" class="form-control" name="row[idcard]" type="text" value="{$row.idcard|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-alipay" class="control-label col-xs-12 col-sm-2">{:__('Alipay')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-alipay" class="form-control" name="row[alipay]" type="text" value="{$row.alipay|htmlentities}">
        </div>
    </div>

    <div class="form-group hidden">
        <label for="content" class="control-label col-xs-12 col-sm-2">联创私董会:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_securities_firms]', ['0'=>'否', '1'=>'是'], $row['is_securities_firms'])}
        </div>
    </div>

    <div style="border: 1px dotted grey;margin:5px;" class="hidden">
        <div class="form-group">
            <label for="content" class="control-label col-xs-12 col-sm-2">是否4995:</label>
            <div class="col-xs-12 col-sm-8">
                {:build_radios('row[self_amd0]', ['999'=>'否', '4995'=>'是'], $row['self_amd0'])}
            </div>
        </div>

        <div class="form-group">
            <label for="content" class="control-label col-xs-12 col-sm-2">渠道分销商:</label>
            <div class="col-xs-12 col-sm-8">
                {:build_radios('row[is_distributor]', ['0'=>'否', '1'=>'是'], $row['is_distributor'])}
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">渠道分销商-区块:</label>
            <div class="col-xs-12 col-sm-8">
                <div class='control-relative'><input id="c-dist_address" class="form-control" data-toggle="city-picker"
                                                     name="row[dist_address]" type="text"
                                                     value="{$row.dist_address|htmlentities}"></div>
            </div>
        </div>
    </div>

    <div style="border: 1px dotted grey;margin:5px;">
        <div class="form-group">
            <label for="content" class="control-label col-xs-12 col-sm-2">城市服务商:</label>
            <div class="col-xs-12 col-sm-8">
                {:build_radios('row[city_server]', ['0'=>'否', '1'=>'县级', '2'=>'市级', '3'=>'省级', '4'=>'大区'],
                $row['city_server'])}
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">城市服务商-区域:</label>
            <div class="col-xs-12 col-sm-8">
                <div class='control-relative'><input id="c-city_address" class="form-control" data-toggle="city-picker"
                                                     name="row[city_address]" type="text"
                                                     value="{$row.city_address|htmlentities}"></div>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-xs-12 col-sm-2">服务站点-详细地址:</label>
            <div class="col-xs-12 col-sm-8">
                <div class='control-relative'><input id="c-city_info" class="form-control" data-toggle="" type="text"
                                                     name="row[city_info]" type="text"
                                                     value="{$row.city_info|htmlentities}"></div>
            </div>
        </div>
    </div>


    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">登陆{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="text" value=""
                   placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password"/>
        </div>
        {$row.password_text|htmlentities}
    </div>
    <div class="form-group">
        <label for="c-paypwd" class="control-label col-xs-12 col-sm-2">支付{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-paypwd" data-rule="paypwd" class="form-control" name="row[paypwd]" type="text" value=""
                   placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password"/>
        </div>
        {$row.paypwd_text|htmlentities}
    </div>
    <div class="form-group">
        <label for="c-email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-email" data-rule="" class="form-control" name="row[email]" type="text"
                   value="{$row.email|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="" class="form-control" name="row[mobile]" type="text"
                   value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text"
                       value="{$row.avatar}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload"
                                  data-input-id="c-avatar"
                                  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
                                  data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose"
                                  data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i
                            class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-level" class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-level" data-rule="required" class="form-control" name="row[level]" type="number"
                   value="{$row.level}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-gender" class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[gender]', ['1'=>__('Male'), '0'=>__('Female')], $row['gender'])}
        </div>
    </div>
    <div class="form-group">
        <label for="c-birthday" class="control-label col-xs-12 col-sm-2">{:__('Birthday')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-birthday" data-rule="" class="form-control datetimepicker" data-date-format="YYYY-MM-DD"
                   data-use-current="true" name="row[birthday]" type="text" value="{$row.birthday}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-bio" class="control-label col-xs-12 col-sm-2">{:__('Bio')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bio" data-rule="" class="form-control" name="row[bio]" type="text"
                   value="{$row.bio|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Vip_Level')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$levelList}
        </div>
    </div>

    <div class="form-group hidden">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('vipv_level')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$vlevelList}
        </div>
    </div>

    <div class="form-group hidden">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('viplc_level')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$levellcList}
        </div>
    </div>

    <div class="form-group hidden">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Vip_Levelu')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$leveluList}
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Svip_Level')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$svipList}
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Svip_Levelu')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$svipuList}
        </div>
    </div>
    <div class="form-group">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Vip_Levels')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$levelsList}
        </div>
        <!--        大于{:__('Vip_Level')}时生效-->
    </div>
    <div class="form-group">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Svip_Levels')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$svipsList}
        </div>
    </div>
    <div class="form-group">
        <label for="c-group_id" class="control-label col-xs-12 col-sm-2">{:__('Svip_lv_dkq')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$svipdkqList}
        </div>
    </div>
    <div class="form-group">
        <label for="c-score" class="control-label col-xs-12 col-sm-2">{:__('Days_dkq')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-days-dkq" data-rule="required" class="form-control" name="row[days_dkq_lv]" type="number"
                   value="{$row.days_dkq_lv}">
        </div>
        设置为0表示重置时间，>=89则第二天降级
    </div>
    <div class="form-group hidden">
        <label for="c-usdt" class="control-label col-xs-12 col-sm-2">{:__('USDT')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-usdt" data-rule="required" class="form-control" name="row[currency_usdt]" type="number"
                   value="{$row.currency_usdt}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-old" class="control-label col-xs-12 col-sm-2">{:__('Currency_Old')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-old" data-rule="required" class="form-control" name="row[currency_old]" type="number"
                   value="{$row.currency_old}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-bd" class="control-label col-xs-12 col-sm-2">{:__('Currency_Bd')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-bd" data-rule="required" class="form-control" name="row[currency_bd]" type="number"
                   value="{$row.currency_bd}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-bd" class="control-label col-xs-12 col-sm-2">{:__('Currency_Lmt')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-lmt" data-rule="required" class="form-control" name="row[currency_lmt]" type="number"
                   value="{$row.currency_lmt}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-points" class="control-label col-xs-12 col-sm-2">{:__('Currency_Points')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-points" data-rule="required" class="form-control" name="row[currency_points]" type="number"
                   value="{$row.currency_points}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-tz" class="control-label col-xs-12 col-sm-2">{:__('Currency_Tz')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-tz" data-rule="required" class="form-control" name="row[currency_tz]" type="number"
                   value="{$row.currency_tz}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-gq" class="control-label col-xs-12 col-sm-2">{:__('Currency_Gq')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-gq" data-rule="required" class="form-control" name="row[currency_gq]" type="number"
                   value="{$row.currency_gq}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number"
                   value="{$row.money}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-score" class="control-label col-xs-12 col-sm-2">{:__('Score')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-score" data-rule="required" class="form-control" name="row[score]" type="number"
                   value="{$row.score}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-successions" class="control-label col-xs-12 col-sm-2">{:__('Successions')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-successions" data-rule="required" class="form-control" name="row[successions]" type="number"
                   value="{$row.successions}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-maxsuccessions" class="control-label col-xs-12 col-sm-2">{:__('Maxsuccessions')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-maxsuccessions" data-rule="required" class="form-control" name="row[maxsuccessions]"
                   type="number" value="{$row.maxsuccessions}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-prevtime" class="control-label col-xs-12 col-sm-2">{:__('Prevtime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-prevtime" data-rule="required" class="form-control datetimepicker"
                   data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[prevtime]" type="text"
                   value="{$row.prevtime|datetime}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-logintime" class="control-label col-xs-12 col-sm-2">{:__('Logintime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-logintime" data-rule="required" class="form-control datetimepicker"
                   data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[logintime]" type="text"
                   value="{$row.logintime|datetime}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-loginip" class="control-label col-xs-12 col-sm-2">{:__('Loginip')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-loginip" data-rule="required" class="form-control" name="row[loginip]" type="text"
                   value="{$row.loginip}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-loginfailure" data-rule="required" class="form-control" name="row[loginfailure]" type="number"
                   value="{$row.loginfailure}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-joinip" class="control-label col-xs-12 col-sm-2">{:__('Joinip')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-joinip" data-rule="required" class="form-control" name="row[joinip]" type="text"
                   value="{$row.joinip}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-jointime" class="control-label col-xs-12 col-sm-2">{:__('Jointime')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-jointime" data-rule="required" class="form-control datetimepicker"
                   data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[jointime]" type="text"
                   value="{$row.jointime|datetime}">
        </div>
    </div>
    <div class="form-group hidden">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('团队业绩')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-team_am" data-rule="required" class="form-control" name="row[team_am]" type="text"
                   value="{$row.team_am}">
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('定制左右')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-pnet_dz" data-rule="required" class="form-control" name="row[pnet_dz]" type="text"
                   value="{$row.pnet_dz}">
        </div>
        仅在账号激活前修改有效针对推荐人1左2右
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('定制结点')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-ypid" data-rule="required" class="form-control" name="row[ypid]" type="text"
                   value="{$row.ypid}">
        </div>
        仅在账号激活前修改有效
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('默认部门')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-pnet_team" data-rule="required" class="form-control" name="row[pnet_team]" type="text"
                   value="{$row.pnet_team}">
        </div>
        修改之后推荐的会员默认在3或者以上部门V1及以上级别有效
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('激活')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[vip_status]', ['0'=>__('否'), '1'=>__('是')], $row['vip_status'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('回填')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_backfill]', ['0'=>__('否'), '1'=>__('是')], $row['is_backfill'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('现金支付')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_balance]', ['0'=>__('禁止'), '1'=>__('允许')], $row['is_balance'])}
        </div>
    </div>
    <div class="form-group hidden">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('汇款')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_remit]', ['0'=>__('禁止'), '1'=>__('允许')], $row['is_remit'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('提现')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_withdraw]', ['0'=>__('禁止'), '1'=>__('允许')], $row['is_withdraw'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('Net')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_net]', ['0'=>__('禁止'), '1'=>__('允许')], $row['is_net'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('注册')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_register]', ['0'=>__('禁止'), '1'=>__('允许')], $row['is_register'])}
        </div>
    </div>
    <!--    <div class="form-group">-->
    <!--        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('自动审核')}:</label>-->
    <!--        <div class="col-xs-12 col-sm-8">-->
    <!--            {:build_radios('row[autopay]', ['0'=>__('关闭'), '1'=>__('开启')], $row['autopay'])}-->
    <!--        </div>-->
    <!--    </div>-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('券商')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[dealer]', ['0'=>__('非'), '1'=>__('是')], $row['dealer'])}
        </div>
    </div>
    <div class="form-group">
        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
