<div class="page" id="change-abc-page-app" v-cloak>
    <form class="form-inline">
        <div class="form-group">
            <label for="username1">我的</label>
            <input type="text" id="username2" value="{:$userInfo.username}" disabled>
            <label for="username1">修改邀请人</label>
            <input type="text" v-model="queryData.username1" class="form-control" id="username1" placeholder="手机号上">

            <input type="hidden" id="uid" value="{:$uid}">
        </div>
        <button type="button" @click="queryUserInfo()" class="btn btn-default" >查询</button>
    </form>
    <hr>
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">会员关系</h3>
        </div>
        <div class="panel-body">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th>账号</th>
                    <th>注册时间</th>
                    <th>等级:旗舰/云店/会员</th>
                    <th>邀请人</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(item,index) in userList ">
                    <td>{{item.uname}}</td>
                    <td>{{item.jointime}}</td>
                    <td>{{item.self_amd0}}</td>
                    <td>{{item.upname}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <hr>
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">变更内容</h3>
        </div>
        <div class="panel-body">
            <form class="form-inline">
                <button type="button" @click="changeLine()" class="btn btn-success pull-right">确定变更</button>
            </form>
        </div>
    </div>
</div>